import React, { useState, useEffect, useRef } from "react";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";
import WebhookModal from "../common-files/WebhookModal";
import Analytics from "../../utils/analyticsTracking";

const ApiTab = () => {
  const [apiKey, setApiKey] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [copied, setCopied] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [showWebhookForm, setShowWebhookForm] = useState(false);
  const [isCreateApiKey, setIsCreateApiKey] = useState(false);
  const [displayCreditUsed, setDisplayCreditUsed] = useState(false);
  const [userCreditsShow, setUserCreditsShow] = useState("");
  const [isWebhookModalOpen, setIsWebhookModalOpen] = useState(false);
  const [copiedCode, setCopiedCode] = useState(false);
  const textAreaRef = useRef(null);

  // Track API actions
  const trackApiAction = (action, properties = {}) => {
    const user = JSON.parse(localStorage.getItem("user")) || {};
    Analytics.track(`API ${action}`, {
      user_email: user.email,
      user_role: user.role,
      ...properties
    });
  };

  // Fetch API key on component mount
  useEffect(() => {
    const getUserApiKey = async () => {
      try {
        const res = await PostWithTokenNoCache(ApiName.getApiKey);
        if (res?.data?.status === 200 && res.data.data) {
          const jsonObj = JSON.parse(res.data.data);
          setApiKey(jsonObj.api_key);
        }
      } catch (error) {
        console.error("Error fetching API key:", error);
      }
    };
    getUserApiKey();
  }, []);

  // Fetch credit usage
  useEffect(() => {
    const creditsUsageDetails = async () => {
      try {
        const res = await PostWithTokenNoCache(ApiName.APICreditUsed, {});
        if (res?.data?.status === 200 && res.data.data) {
          setUserCreditsShow(JSON.parse(res.data.data));
          trackApiAction('Credit Usage Fetched');
        }
      } catch (error) {
        console.error("Error fetching credit usage:", error);
        trackApiAction('Credit Usage Fetch Error', {
          error: error?.response?.data?.message || 'Unknown error'
        });
      }
    };
    creditsUsageDetails();
  }, []);

  const generateApiKey = async () => {
    setIsCreateApiKey(true);
    const isRegenerating = !!apiKey;

    trackApiAction(isRegenerating ? 'Key Regenerated' : 'Key Generated', {
      action: isRegenerating ? 'regenerate' : 'generate'
    });

    try {
      const res = await PostWithTokenNoCache(ApiName.createApiKey);
      if (res?.data?.status === 200 && res.data.data) {
        const jsonObj = JSON.parse(res.data.data);
        setApiKey(jsonObj.api_key);
        setHasGenerated(true);
        setDisplayCreditUsed(true);
        trackApiAction('Key Generation Success');
      }
    } catch (error) {
      console.error("Error generating API key:", error);
      trackApiAction('Key Generation Failed', {
        error: error?.response?.data?.message || 'Unknown error'
      });
    } finally {
      setIsCreateApiKey(false);
    }
  };

  const toggleVisibility = () => setIsVisible(!isVisible);

  const handleCopy = () => {
    navigator.clipboard.writeText(apiKey).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      trackApiAction('Key Copied');
    });
  };

  const toggleWebhookModal = () => {
    setIsWebhookModalOpen(!isWebhookModalOpen);
    trackApiAction('Webhook Modal Toggled', {
      state: !isWebhookModalOpen ? 'opened' : 'closed'
    });
  };

  const displayCreditUsedCount = () => {
    setDisplayCreditUsed(false);
  };

  const copyText = () => {
    const textToCopy = textAreaRef.current.textContent;
    navigator.clipboard.writeText(textToCopy).then(() => {
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
      trackApiAction('Key Copied');
    });
  };

  return (
    <div className="profile-box-2">
      <div className="row">
        <div className="col-md-2"></div>
        <div className="col-md-8">
          <div className="profile-box">
            <div className="my-api-section">
              <p className="my-api">API</p>

              <div className="Totals-1">
                <div className="availables">
                  <span className="creits-sell" onClick={displayCreditUsedCount}>
                    <div className="d-flex flex-row justify-content-between">
                      <div>
                        <span className="creits-used">Credits Used</span>
                      </div>
                      <div>
                        <h1 className="user-count">
                          {userCreditsShow?.api_credit_usage || 0}
                        </h1>
                      </div>
                    </div>
                  </span>
                </div>
              </div>

              <div className="d-flex flex-row justify-content-between">
                <div>
                  <p className="my-api-key">API Key:</p>
                </div>
                <div className="api-key-wrapper">
                  <input
                    type={isVisible ? 'text' : 'password'}
                    value={apiKey}
                    readOnly
                    placeholder="***************Z474v2"
                  />

                  <span
                    className="toggle-visibility"
                    onClick={toggleVisibility}
                    title="Show/Hide API Key"
                  >
                    <span style={{ color: "#707070" }}>|</span>&nbsp;
                    <img
                      src={
                        !apiKey
                          ? 'images/grey-eye.png'
                          : isVisible
                            ? 'images/unhide-eye.png'
                            : 'images/blue-eye.png'
                      }
                      alt="Toggle Visibility"
                    />&nbsp;
                  </span>
                  <span className="copy-api-key" onClick={handleCopy} title="Copy API Key">
                    <img src="images/copy-api-icon.png" alt="Copy Icon" />
                    <span className="copy-text">Copy</span>
                    {copied && <div className="tooltip">Copied Successfully</div>}
                  </span>
                </div>
              </div>
              <button
                className="generate-btn"
                onClick={generateApiKey}
                disabled={isCreateApiKey}
              >
                {isCreateApiKey ? "Generating..." : hasGenerated ? "Regenerate" : "Generate"}
              </button>
            </div>
          </div>
        </div>
        <div className="col-md-2"></div>
      </div>


      <div className="row">
        <div className="col-md-2"></div>
        <div className="col-md-8">
          <div className="profile-box mt-3">
            <div className="my-api-section">
              <div className="d-flex flex-row justify-content-between">
                <p className="my-api">Webhook</p>
                <button
                  type="button"
                  className="webhook-generate"
                  onClick={toggleWebhookModal}
                >
                  <img src="images/plus-sign.png" alt="plus" width="10" />&nbsp; Add webhook
                </button>
              </div>

              {copiedCode && (
                <div className="row">
                  <div className="col-5"></div>
                  <div className="col-5">
                    <p className="successmessage3">
                      <img src="./images/promocode-success.png" alt="Success" />
                      Copied Successfully
                    </p>
                  </div>
                  <div className="col-3"></div>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="col-md-2"></div>
      </div>


      {isWebhookModalOpen && (
        <WebhookModal
          closeModal={toggleWebhookModal}
          setIsWebhookModalOpen={setIsWebhookModalOpen}
        />
      )}
    </div>
  );
};

export default ApiTab;