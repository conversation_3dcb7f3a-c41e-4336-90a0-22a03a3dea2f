import React, { useState, useEffect } from 'react';
import AsyncSelect from 'react-select/async';
import { ApiName } from '../common-files/ApiNames.js';
import { PostWithTokenNoCache, postWithToken } from '../common-files/ApiCalls.js';
import UseTabStore from '../common-files/useGlobalState.js';
import { SearchJobtitleStyles } from '../common-files/FiltersData.js';

const Technology = () => {
	// State to store selected values
	const { selectedValues, setSelectedCompanyTechKeyWordsList, setCurrentPage, setSelectedValues, companyTechKeyWordsList } = UseTabStore();
	const [isTechnologyAutoFocus, setIsTechnologyAutoFocus] = useState(false); // Set one of them to autofocus initially

	useEffect(() => {
		if (Object.keys(companyTechKeyWordsList).length < 1) {
			setSelectedValues([]);
		} else {
			const selectedValues = Object.keys(companyTechKeyWordsList).map(key => {
				return { label: companyTechKeyWordsList[key], value: companyTechKeyWordsList[key] };
			});
			setSelectedValues(selectedValues);
		}
	}, [companyTechKeyWordsList])

	const loadOptions = (inputValue, callback) => {
		if (inputValue) {
			const dataPost = {
				searchPattern: inputValue // Pass the inputValue to your API request
			};
			// Make an API call here to fetch suggestions based on the inputValue
			PostWithTokenNoCache(ApiName.technologyKeywords, dataPost)
				.then((res) => {
					if (res.data.status === 200) {
						const technology = JSON.parse(res.data.data);
						// Convert the array to a Set to remove duplicates and then convert it back to an array
						const uniqueTechnology = [...new Set(technology)];

						const options = uniqueTechnology.map((option) => ({
							label: option,
							value: option
						}));
						callback(options);
					}
				})
				.catch((error) => {
					console.error('Error fetching data:', error);
					callback([]); // Clear options on error
				});
		}
	};

	// Handle value change when an option is selected or deselected
	const handleValueChange = (selectedOptions) => {
		setCurrentPage(1);
		setSelectedValues(selectedOptions);
		let updatedTechnology = {};

		// Update the company names based on selected options
		selectedOptions.forEach((item, index) => {
			updatedTechnology[index] = item.value;
		});
		setSelectedCompanyTechKeyWordsList(updatedTechnology);
	};

	const SearchTechnology = ({ autoFocus, onFocus, onBlur }) => {
		return (
			<div className="mb-2 mt-3 d-flex flex-column">
				<div className="">
					<AsyncSelect
						cacheOptions
						defaultOptions
						loadOptions={loadOptions}
						isMulti
						placeholder="Type to search and select"
						onChange={handleValueChange} // Handle selected value changes
						value={selectedValues} // Pass selected values
						styles={SearchJobtitleStyles}
						autoFocus={autoFocus}
						onFocus={onFocus}
						onBlur={onBlur}
						isClearable
					/>
				</div>
				<div className="search-icon">
					<i className="fas fa-search"></i>
				</div>
			</div>
		)
	}

	return (
		<div>
			<SearchTechnology
				autoFocus={isTechnologyAutoFocus}
				onFocus={() => setIsTechnologyAutoFocus(true)}
				onBlur={() => setIsTechnologyAutoFocus(false)}
			/>
		</div>
	)
}
export default Technology;