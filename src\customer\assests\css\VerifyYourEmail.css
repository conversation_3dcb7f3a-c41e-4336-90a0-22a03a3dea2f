@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap');
.body {
    background-color: #E8F7F7;
    height: 45rem;
}

.card {
    max-width: 30rem;
    border-radius: 21px;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    border: 0;
    margin: auto;
}



p.semi-header {
    text-align: center;
    font-size: 20px;
    /* font-weight: 500; */
    color: #000;
    margin: 0;

}

.card h3 {
    text-align: center;
    margin-top: 30px;
    color: #093D54;
    margin-bottom: 23px;
}

p.email-link {
    text-align: center;
}

p.email-link a {
    font-size: 19px;
    text-decoration: none;
    color: #55C2C3;
}

p.email-issue {
    text-align: center;
    color: #0C243C;
    margin: 13px 0 8px 0;
}

p.email-issue a {
    color: #55C2C3;
    text-decoration: none;
}

p.activate-account {
    text-align: center;
    font-size: 14px;
    margin: 11px 0 30px 0px;
    color: #7E8C9C;
}

.receive-link p {
    text-align: center;
    margin-top: 24px;
    line-height: 30px;
    color: #707070;
}

.receive-link a {
    text-decoration: none;
    color: #55C2C3;
}

.card>hr {
    margin-right: 15%;
    margin-left: 15%;
    color: #********;
}