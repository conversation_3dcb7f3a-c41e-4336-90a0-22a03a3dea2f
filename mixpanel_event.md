ReachStream Mixpanel Events Documentation
This document provides a comprehensive listing of all Mixpanel events implemented throughout the ReachStream application, organized by file path. Use this as a reference when modifying event names or properties.

Core Analytics Files
src/utils/mixpanel.js
Core implementation file that initializes Mixpanel and provides tracking methods.

No direct events are tracked in this file, but it contains the implementation for: - track() - Main tracking method - identify() - User identification - people.set() - User property setting - opt_in_tracking() - Consent opt-in - opt_out_tracking() - Consent opt-out

src/utils/analyticsTracking.js
Wrapper around Mixpanel with standardized tracking methods.

Generic events implemented: - track(name, props) - Generic event tracking with timestamp

User events: - trackSignUp(method, userInfo) - Event: "Signed Up via [method] for [planType] Plan" - trackSignIn(method, userInfo) - Event: "Signed In via [method] ([userRole])" - trackUserIdentify(userId, userProps) - Sets user properties

Email verification events: - trackEmailVerificationInitiated(verificationInfo) - Event: "Initiated Email Verification" - trackEmailVerified(verificationInfo) - Event: "Verified Email" - trackEmailVerificationFailed(verificationInfo) - Event: "Email Verification Failed"

Feature usage events: - trackFeatureUsage(featureName, properties) - Event: "[action] [featureName] [target]"

List-related events: - trackListCreated(listInfo) - Event: "Created List [listName]" or "Created List" - trackListDeleted(listInfo) - Event: "Deleted List [listName]" or "Deleted List" - trackListEdited(listInfo) - Event: "List Edited"

Contact-related events: - trackContactViewed(contactInfo) - Event: "Viewed Contact [contactName]" or "Viewed Contact" - trackCompanyViewed(companyInfo) - Event: "Company Viewed" - trackContactDownloaded(contactInfo) - Event: "Downloaded Contact [contactName]" or "Downloaded Contact" - trackContactsExported(exportInfo) - Event: "Exported Contacts from [source]" or "Exported Contacts"

Plan-related events: - trackPlanViewed(planInfo) - Event: "Plan Viewed" - trackPlanUpgrade(upgradeInfo) - Event: "Plan Upgraded from [fromPlan] to [toPlan]" or "Plan Upgraded" - trackPlanDowngrade(downgradeInfo) - Event: "Plan Downgraded from [fromPlan] to [toPlan]" or "Plan Downgraded"

Customer journey events: - trackIncompleteSignup(formData) - Event: "Incomplete Signup" - trackInactivity(inactivityData) - Event: "User Inactive" - trackCreditUsage(creditData) - Event: "Credit Usage" - trackFilterUsage(filterData) - Event: "Filter Usage" - trackEmailReveal(contactInfo) - Event: "Email Revealed" - trackABMListFeature(featureInfo) - Event: "ABM List Feature Used" - trackReferralActivity(referralInfo) - Event: "Referral Activity" - trackPricingPageView(viewInfo) - Event: "Pricing Page Viewed" - updateUserLifecycleStage(userId, stage, properties) - Updates user properties with lifecycle stage - trackEngagementMilestone(userId, milestone, properties) - Event: "Engagement Milestone" - trackNoSearchResults(searchInfo) - Event: "No Search Results"

Page view events: - trackPageView(pageInfo) - Event: "Page Viewed: [pageName]" or "Page Viewed"

src/utils/customerJourneyTracking.js
Implements customer lifecycle tracking.

trackSignupAbandonment(formData) - Calls Analytics.trackIncompleteSignup()
trackCreditUsage(userEmail) - Calls Analytics.trackCreditUsage()
trackUserActivity(user) - Updates user profile with activity data
checkUserInactivity(user) - Calls Analytics.trackInactivity() when applicable
trackPricingPageView(user) - Calls Analytics.trackPricingPageView()
trackSearchAndFilterUsage(filterData, resultsCount, user) - Calls Analytics.trackFilterUsage()
trackReferralSignup(referralCode, newUserEmail) - Calls Analytics.trackReferralActivity()
trackEmailReveal(contactInfo, isAddedToList) - Calls Analytics.trackEmailReveal()
trackContactDownload(contacts, listName) - Calls Analytics.trackContactsExported()
Authentication & User Management
src/customer/register/SignIn.js
Analytics.track('Page Viewed', { page: 'signin' }) - Tracks signin page view
Analytics.track('Google Login Attempt') - Tracks Google login attempt
Mixpanel.track('Google Login Attempt') - Duplicate tracking for Google login
Analytics.track('Microsoft Login Attempt') - Tracks Microsoft login attempt
Mixpanel.track('Microsoft Login Attempt') - Duplicate tracking for Microsoft login
Analytics.track('LinkedIn Login Attempt') - Tracks LinkedIn login attempt
Mixpanel.track('LinkedIn Login Attempt') - Duplicate tracking for LinkedIn login
src/customer/register/SignUp.js
Analytics.track('Paid Sign Up Started', { plan_type, referral_code }) - Tracks start of paid signup
Analytics.track('Incomplete Signup', { formData }) - Tracks abandoned signup
Mixpanel.track('Social Auth Initiated', { provider, email }) - Tracks social auth start
Mixpanel.track('Social Auth Failed', { provider, error }) - Tracks social auth failure
Mixpanel.track('Signup Completed', { email, plan_type, etc. }) - Tracks completed signup
src/customer/register/FreeTrialSignUp.js
Analytics.track('Incomplete Signup', { formData }) - Tracks abandoned signup
Analytics.track('Social Auth Initiated', { provider }) - Tracks social auth start
Analytics.track('Social Auth Failed', { provider, error }) - Tracks social auth failure
Mixpanel.track('Subscription Complete', { email, plan: 'Icebreaker' }) - Tracks free subscription completion
Analytics.track('Icebreaker Subscription Complete', { email }) - Duplicate tracking for Icebreaker completion
Mixpanel.track('Referral Redeemed', { referral_code, email }) - Tracks referral usage
src/customer/register/VerifyEmail.js
Analytics.track('Email Verification Page Viewed', { email }) - Tracks verification page view
Analytics.track('OTP Expired', { email }) - Tracks OTP expiration
Analytics.track('Email Verification Attempted', { email }) - Tracks verification attempt
Analytics.track('Email Verification Successful', { email }) - Tracks successful verification
Analytics.track('Email Verification Failed', { email, error }) - Tracks failed verification
src/customer/register/CreateNewPassword.js
Analytics.track('Create New Password Page Viewed', { email }) - Tracks password creation page view
Analytics.track('Password Reset Attempted', { email }) - Tracks password reset attempt
Analytics.track('Password Reset Successful', { email }) - Tracks successful password reset
Analytics.track('Password Reset Failed', { email, error }) - Tracks failed password reset
src/customer/register/PasswordCreatedSuccessfully.js
Analytics.track('Password Reset Completion Page Viewed', { email }) - Tracks completion page view
Analytics.track('Password Reset Sign In Clicked', { email }) - Tracks sign in click after reset
Mixpanel.identify(location.state.email) - Identifies user by email
Mixpanel.people.set({ '$email': location.state.email }) - Sets user email property
Email Verification Events
src/customer/register/SignUp.js and src/customer/register/FreeTrialSignUp.js
Analytics.trackEmailVerificationInitiated({ email, plan_name, etc. }) - Tracks when verification code is requested
Analytics.trackEmailVerified({ email, plan_name, etc. }) - Tracks when email is successfully verified
Analytics.trackEmailVerificationFailed({ email, error, etc. }) - Tracks when email verification fails
Pricing & Payment
src/customer/pricing/UpgradePlan.js
Mixpanel.track('Promo Code Failed', { promo_code, error, package_id, package_name }) - Tracks invalid promo code
Mixpanel.track('Promo Code Applied', { promo_code, discount_percentage, original_price, discounted_price, package_id, package_name, coupon_id }) - Tracks successful promo code
Mixpanel.track('Promo Code Error', { promo_code, error, package_id, package_name }) - Tracks promo code error
Mixpanel.track('Upgrade Payment Initiated', { package_id, package_name, price, discounted_price, has_discount, discount_percentage, promo_code, user_email, deviceInfo, utmParams }) - Tracks payment initiation
Mixpanel.track('Upgrade Order Created', { order_id, package_id, package_name, price, discounted_price, promo_code }) - Tracks order creation
Mixpanel.track('Upgrade Payment Redirect', { order_id, package_id, package_name }) - Tracks redirect to payment page
Mixpanel.track('Upgrade Payment Error', { error, order_id, package_id, package_name }) - Tracks payment error
Mixpanel.track('Upgrade Order Creation Error', { error, package_id, package_name }) - Tracks order creation error
src/customer/pricing/ProceedToPay.js
Mixpanel.track('Promo Code Failed', { promo_code, error, package_id, package_name }) - Tracks invalid promo code
Mixpanel.track('Promo Code Applied', { promo_code, discount_percentage, original_price, discounted_price, package_id, package_name, coupon_id }) - Tracks successful promo code
Mixpanel.track('Promo Code Error', { promo_code, error, package_id, package_name }) - Tracks promo code error
Mixpanel.track('Payment Initiated', { package_id, package_name, price, discounted_price, has_discount, discount_percentage, promo_code, user_email }) - Tracks payment initiation
Mixpanel.track('Order Created', { order_id, package_id, package_name, price, discounted_price, promo_code }) - Tracks order creation
Mixpanel.track('Payment Redirect', { order_id, package_id, package_name }) - Tracks redirect to payment page
Mixpanel.track('Payment Error', { error, order_id, package_id, package_name }) - Tracks payment error
Mixpanel.track('Order Creation Error', { error, package_id, package_name }) - Tracks order creation error
src/customer/pricing/PaymentSuccessfull.js
Mixpanel.track('Payment Successful', { order_id, package_id, package_name, price }) - Tracks successful payment
Mixpanel.track('Purchase', { order_id, package_id, package_name, price, currency: 'USD' }) - Tracks purchase event
src/customer/pricing/PaymentFailed.js
Mixpanel.track('Payment Failed', { order_id, error_message }) - Tracks payment failure
Analytics.track('Payment Failed', { order_id, error_message }) - Duplicate tracking for payment failure
Mixpanel.track('Payment Retry Initiated', { order_id }) - Tracks payment retry
List Management
src/customer/list/ListPopup.js
Analytics.track('User Updates Existing List', { list_id, list_name, list_size }) - Tracks list updates
Analytics.track('User Creates a List', { list_name, list_size, filters_used }) - Tracks list creation
src/customer/list/SavedList.js
Analytics.track('User Views List Details', { list_id, list_name, list_size }) - Tracks list view
Analytics.track('User Deletes List', { list_id, list_name }) - Tracks list deletion
Analytics.track('User Download Empty List', { list_id, list_name }) - Tracks empty list download attempt
Analytics.track('User Downloads List', { list_id, list_name, download_size }) - Tracks list download
Analytics.track('List Download Error', { list_id, list_name, error }) - Tracks list download error
Privacy & Consent
src/components/CookieConsent.js
mixpanel.opt_out_tracking() - Opts user out of tracking when consent declined
Current Active Events
The following events are currently active in the application:

Authentication & Registration
Analytics.track('Page Viewed', {...}) - In src/customer/register/SignIn.js
Analytics.track('Google/Microsoft/LinkedIn Login Attempt') - In src/customer/register/SignIn.js
Analytics.track('Email Verification Page Viewed', {...}) - In src/customer/register/VerifyEmail.js
Analytics.track('Email Verification Attempted/Successful/Failed', {...}) - In src/customer/register/VerifyEmail.js
Analytics.track('Password Reset Completion Page Viewed', {...}) - In src/customer/register/PasswordCreatedSuccessfully.js
Contact & Company Interaction
Analytics.track('User Reveals Email', {...}) - In src/customer/filters/ContactModal.js
Analytics.track('Email Reveal Success', {...}) - In src/customer/filters/ContactModal.js
Analytics.track('User Views Company Website/Phone', {...}) - In src/customer/filters/ContactModal.js
List Management
Analytics.track('User Views/Creates/Updates/Deletes List', {...}) - In src/customer/list/SavedList.js and src/customer/list/ListPopup.js
Analytics.track('User Downloads List', {...}) - In src/customer/list/SavedList.js
Account Management
trackEvent('Account Details Navigation', {...}) - In src/customer/layouts/Header.js
trackAccountAction('Profile Updated', {...}) - In src/customer/layouts/Account_details.js
trackAccountAction('Account Deactivation', {...}) - In src/customer/layouts/Account_details.js
trackAccountAction('API Key Copied', {...}) - In src/customer/layouts/Account_details.js
Payment Processing
Mixpanel.track('Promo Code Applied/Failed', {...}) - In src/customer/pricing/UpgradePlan.js and src/customer/pricing/ProceedToPay.js
Mixpanel.track('Payment Initiated/Successful/Failed', {...}) - In pricing-related files
Tour Navigation
Analytics.track('Tour Skipped', {...}) - In all src/customer/tour-guide/*.js files
Analytics.track('Tour Completed', {...}) - In src/customer/tour-guide/PageFive.js
Enrichment
Only essential event tracking in submitEnrichment function in src/customer/enrichment/EnrichTabContacts.js
Common Event Properties
Most events include these standard properties:

timestamp: ISO timestamp of when the event occurred
environment: Development, staging, or production
user_email: Email of the logged-in user (when available)
device_info: Browser and device information
viewport_size: User's screen dimensions
Location Data
All events automatically include the following location data:

location.url: Full URL of the current page
location.path: Path portion of the URL (without domain)
location.hostname: Domain name of the current site
location.page_title: Title of the current page
location.referrer: URL of the referring page
location.referrer_domain: Domain of the referring page
Geo-Location Data
If the user grants permission, the following geo-location data may be included:

geo_latitude: User's latitude coordinate
geo_longitude: User's longitude coordinate
geo_accuracy: Accuracy of the geo-location in meters
geo_timestamp: When the geo-location was captured
To request geo-location data, use the Analytics.trackUserGeoLocation() method. This returns a Promise that resolves with the location data or false if unavailable.

Event Naming Conventions
ReachStream uses these event naming patterns:

Feature Usage: "Feature Used: [Feature Name]"
User Actions: "[Action] [Object]" (e.g., "Created List")
Page Views: "Page Viewed" or "[Page Name] Page Viewed"
Process Steps: "[Process] [Step]" (e.g., "Email Verification Attempted")
Outcomes: "[Process] [Outcome]" (e.g., "Payment Successful")
When modifying event names, maintain these conventions for consistency and easier analysis.