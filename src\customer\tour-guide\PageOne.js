import React from "react";
import "../assests/css/layouts/demo.css";
import { useNavigate, useParams } from "react-router-dom";
import Header from "../layouts/Header";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../customer/common-files/ApiCalls.js";
const PageOne = () => {
  let data_params = useParams();
  const navigate = useNavigate();

  const goNext = () => {
    navigate("/tour-guide-sort-by/" + data_params.id);
  };

  const skipTour = async () => {
    const params = JSON.stringify({
      id: data_params.id,
      actionType: "SKIP",
      repeatTimeDefault: 1,
      currentRepeatedTime: 1,
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.appTourUpdate, params);
      if (res.data.status == "200") {
        navigate("/dashboard");
      }
    } catch (errors) {}
  };

  return (
    <>
      <div>
        <Header />
        <div class="example">
          <img
            src="../../images/demo-image.png"
            class="img-fluid"
            alt="Responsive image"
          />
          <img
            src="../../images/demo-image-2.png"
            class="img-fluid"
            alt="Responsive image"
            style={{ display: "none" }}
          />
          <div>
            <p className="skiptour" onClick={skipTour}>
              Skip Tour
            </p>
          </div>
          {/* <p className="SearchbyFilters">Search by Filters</p>
          <p className="Youcansearch">
            You can search for either contact or company profiles. Use 15+
            filters to search and shortlist your ICP.
          </p> */}
          <button type="button" onClick={goNext} className="nextbutton">
            Next
          </button>
        </div>
      </div>
    </>
  );
};

export default PageOne;
