import React, { useState, useEffect } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import AdminHeader from "../../layouts/AdminHeader.js";
import LeftSideNav from '../../layouts/LeftSideNav.js';
import {
    postWithToken,
    PostWithTokenNoCache,
} from "../../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { useNavigate, useParams } from "react-router-dom";

const CreateDomainBlock = () => {

    const navigate = useNavigate();
    const [data, setData] = useState({ blockStatus: "True" });

    const [validDomainMsg, setValidDomainMsg] = useState();
    const [validBlockStatusReason, setValidBlockStatusReason] = useState();

    const handleInputDomainChange = (e) => {
        var { id, value, type } = e.target;

        if (value) {
            setValidDomainMsg("")
        } else {
            setValidDomainMsg("Domain could not be a empty")
        }
        setData((prevData) => ({
            ...prevData,
            [id]: type === 'checkbox' ? e.target.checked : value.toLowerCase(),
        }));
    };

    const handleInputBlockStatusChange = (e) => {
        var { id, value, type } = e.target;

        // check value and convert to boolean only if value is string TRUE or FALSE
        value = value === "True" ? true : value === "False" ? false : value;

        setData((prevData) => ({
            ...prevData,
            [id]: type === 'checkbox' ? e.target.checked : value,
        }));
    };

    const handleInputBlockStatusReasonChange = (e) => {
        var { id, value } = e.target;

        if (value) {
            setValidBlockStatusReason("")
        } else {
            setValidBlockStatusReason("Reason could not be a empty")
        }
        setData((prevData) => ({
            ...prevData,
            [id]: value,
        }));
    };


    const submitHandler = async () => {
        const res = await PostWithTokenNoCache(ApiName.createUsersDomainBlockList, data);
        if (res && "status" in res) {
            if (res.status == 200) {
                navigate("/admin/users-domain-block-list")
            }
        }
    }

    return (
        <div className={S.app}>
            <AdminHeader />
            <div className={S.main_container}>
                <div className="d-flex flex-row">
                    <div style={{ width: "350px" }}>
                        <LeftSideNav />
                    </div>

                    <div className={S.formlayout}>
                        <form className="mb-3">
                            <h3 className={S.adminsignup}>Create Users Block</h3>

                            <div className="row">
                                <div className="col-md-12">
                                    <div className="form-group">
                                        <label>Domain</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            id="domain"
                                            onChange={handleInputDomainChange}
                                            required
                                        />
                                        {validDomainMsg ?
                                            (<p className="invalid">{validDomainMsg} </p>)
                                            : ("")}
                                    </div>
                                </div>

                                <div className="col-md-12" style={{ display: 'none' }}>
                                    <div className="form-group">
                                        <label>Block Status</label>
                                        <select
                                            id="blockStatus"
                                            className="form-control"
                                            onChange={handleInputBlockStatusChange}
                                        // value={data && data.blockStatus ? "True" : "False"}
                                        >
                                            <option value="True" >True</option>
                                            <option value="False">False</option>
                                        </select>
                                    </div>
                                </div>

                                <div className="col-md-12">
                                    <div className="form-group">
                                        <label>Block Reason</label>
                                        <textarea
                                            type="text"
                                            className="form-control"
                                            id="blockReason"
                                            onChange={handleInputBlockStatusReasonChange}
                                            required
                                        >
                                        </textarea>
                                        {validBlockStatusReason ?
                                            (<p className="invalid">{validBlockStatusReason} </p>)
                                            : ("")}
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div className="mb-3">
                            <button
                                type="submit"
                                className={`${S.adminsubmit} ${validDomainMsg !== "" || validBlockStatusReason !== "" ? 'cust-disabled' : ''}`}
                                onClick={submitHandler}
                            >Submit</button>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    )


}

export default CreateDomainBlock;