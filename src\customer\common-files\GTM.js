import React, { useEffect, useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';
import { envConfig } from "../../config";

const TrackingManager = () => {
  const location = useLocation();
  const trackedElements = useRef(new Set());
  const observerRef = useRef(null);

  const gtmId = process.env.REACT_APP_GTM_ID;
  const targetPaths = ['/payment-successful', '/sign-up','/proceed-to-pay'];
  
  // Enhanced pattern matching for scripts
  const forbiddenPatterns = {
    urls: [
      'googletagmanager.com/gtm.js',
      'googletagmanager.com/gtag/js',
      'googletagmanager.com/gtag/destination',
      'snap.licdn.com/li.lms-analytics',
      'clarity.ms/tag',
      'clarity.ms/s/0.8.0/clarity.js',
      'googleadservices.com/pagead/conversion_async.js'
    ],
    inline: [
      'window.dataLayer=window.dataLayer||[]',
      'gtag("js",new Date)',
      'gtag("config","G-7S0MSDMD95")'
    ]
  };

  const nuclearCleanup = () => {
    // Remove DOM elements
    document.querySelectorAll('script').forEach(script => {
      const isForbidden = 
        forbiddenPatterns.urls.some(url => script.src.includes(url)) ||
        forbiddenPatterns.inline.some(text => script.textContent.includes(text));
      
      if (isForbidden && script.parentNode) {
        script.parentNode.removeChild(script);
        trackedElements.current.add(script);
      }
    });

    // Nuclear global reset
    window.dataLayer = [];
    window.gtag = () => {}; // Neutralize instead of delete
    delete window.clarity;
    window.lintrk = undefined;
  };

  useEffect(() => {
    const isTargetPage = targetPaths.some(path => 
      location.pathname.includes(path)
    );

    observerRef.current = new MutationObserver(mutations => {
      mutations.forEach(({ addedNodes }) => {
        addedNodes.forEach(node => {
          if (node.tagName === 'SCRIPT') {
            const isForbidden = 
              forbiddenPatterns.urls.some(url => node.src.includes(url)) ||
              forbiddenPatterns.inline.some(text => node.textContent.includes(text));

            if (isForbidden) {
              trackedElements.current.add(node);
              if (!isTargetPage && node.parentNode) {
                node.parentNode.removeChild(node);
              }
            }
          }
        });
      });
    });

    // Start observing entire document
    observerRef.current.observe(document.documentElement, {
      childList: true,
      subtree: true
    });

    // Inject only on target pages
    if (isTargetPage) {
      const scripts = {
        gtm: `https://www.googletagmanager.com/gtm.js?id=${gtmId}`,
        linkedin: 'https://snap.licdn.com/li.lms-analytics/insight.min.js',
        clarity: 'https://www.clarity.ms/tag.js'
      };

      Object.entries(scripts).forEach(([key, src]) => {
        const script = document.createElement('script');
        script.async = true;
        script.src = src;
        trackedElements.current.add(script);
        document.head.appendChild(script);
      });
    }

    // Initial cleanup
    nuclearCleanup();

    return () => {
      nuclearCleanup();
      if (observerRef.current) observerRef.current.disconnect();
    };
  }, [location.pathname]);

  return (
    <Helmet>
      {targetPaths.some(path => location.pathname.includes(path)) && (
          <noscript>
            {`<!-- Google Tag Manager (noscript) -->
            <iframe src="https://www.googletagmanager.com/ns.html?id=${gtmId}"
              height="0" width="0" 
              style="display:none;visibility:hidden">
            </iframe>
            <!-- End Google Tag Manager (noscript) -->`}
          </noscript>
        )}
    </Helmet>
  );
};

export default TrackingManager;