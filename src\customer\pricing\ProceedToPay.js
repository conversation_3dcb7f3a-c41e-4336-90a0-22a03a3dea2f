import React, { useState, useContext, useEffect } from "react";
import "../../customer/assests/css/paymentdetails.css";
import "../../customer/assests/css/password-eye.css";
import {
  PostWithTokenNoCache,
} from "../common-files/ApiCalls.js";
import { ApiName } from "../common-files/ApiNames.js";
import DashboardContext from "../common-files/ContextDashboard.js";
import { getSessionItem } from "../common-files/LocalStorage.js";
import Footer from "../layouts/Footer.js";
import { envConfig } from "../../config.js";
import GTM from '../common-files/GTM.js';
import DWINTracker from "../common-files/DWINTracker.js";
import Mixpanel from '../../utils/mixpanel';
import Analytics from '../../utils/analyticsTracking';

const ProceedToPay = () => {
  const gtmId = process.env.REACT_APP_GTM_ID;
  const { dataDC, setDataDC } = useContext(DashboardContext);
  const [promocode, setPromocode] = useState();
  const [error, setError] = useState(false);
  const [afterDiscount, setAfterDiscount] = useState(0);
  const [promocodeInvalid, setPromocodeInvalid] = useState(true);

  const [btnVisible, setBtnVisible] = useState(false);
  const [promocodeMsg, setPromocodeMsg] = useState(false);
  const [inputDisable, setInputDisable] = useState(false);
  const [couponId, setCouponId] = useState(null);
  const [loading, setLoading] = useState(false);
  const yearlyPlanIds = envConfig.yearlyPlanIds;

  const priceData = {
    access_database: "",
    custom_filter: "",
    data_fields: "",
    export: "",
    no_of_contact_views: "",
    no_of_users: "",
    price: "",
    verified_email_phone: "",
  };
  const [dollarData, setDollorData] = useState(priceData);

  let email = dataDC.loggedUserEmail || null;
  let token = dataDC.token || null;
  let packageId = dataDC.packageId || null;

  let user = localStorage.getItem("user");
  if (user) {
    const parsedData = JSON.parse(user);
    packageId = parsedData.packageId || null;
    token = parsedData.token || null;
    email = parsedData.email || null;
  }

  if (!user) {
    window.location.href = "/";
  }

  useEffect(() => {
    setLoading(false);
    
    // Track "Paid Checkout Started" event when component loads
    const trackCheckoutStarted = () => {
      try {
        // Get UTM parameters from localStorage
        const utmParams = localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {};
        
        // Gather device and location info
        const deviceInfo = {
          browser: navigator.userAgent,
          device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
          viewport_size: `${window.innerWidth}x${window.innerHeight}`,
          language: navigator.language || navigator.userLanguage
        };
        
        // Track checkout started event
        Analytics.trackPaymentStarted({
          package_id: packageId,
          user_email: email,
          location: window.location.href,
          page_title: document.title,
          referrer: document.referrer || 'direct',
          ...deviceInfo,
          ...utmParams
        });
      } catch (error) {
        console.warn('Error tracking checkout started:', error);
      }
    };
    
    trackCheckoutStarted();
  }, [])

  useEffect(() => {
    const postData = async () => {
      try {
        const response = await fetch(ApiName.packagePricePlan, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ method: "GET" }), // Replace with your payload
        });
        const data = await response.json();
        if (data.status == 200) {
          const jsonData = JSON.parse(data.data);
          let filterPlan = jsonData.filter(value => {
            return parseInt(value?.id) === parseInt(packageId);
          });
          if (filterPlan[0] !== undefined || null) {
            setDollorData((prevData) => ({
              ...prevData,
              ...filterPlan[0],
            }));
            setError("");
          }
        }
      } catch (error) {
        console.error("Error fetching package price plan:", error);
        setError("Failed to fetch package price plan. Please try again later.");
      }
    };
    if (packageId) {
      postData();
    }
  }, []);

  function calculateDiscount(price, discount) {
    return (price - price * (discount / 100)).toFixed(2);
  }

  const promocodeAppied = (event) => {
    event.preventDefault();
    const userData = { method: "GET" };

    let result = PostWithTokenNoCache(ApiName.checkPromocode + promocode, userData)
      .then(function (response) {
        const promocodeData = JSON.parse(JSON.parse(response.data.data));
        if (
          response.data.status == 200 &&
          promocodeData.is_active === "inactive" ||
          response.data.message == "Coupon Not Found"
        ) {
          setError("Invalid code");
          setPromocodeInvalid(false);
          setBtnVisible(false);
          
          // Track failed promo code in Mixpanel
          try {
            Mixpanel.track('Promo Code Failed', {
              promo_code: promocode,
              package_id: dollarData.id,
              package_name: dollarData.package_name,
              error: 'Invalid code or inactive'
            });
          } catch (error) {
            console.warn('Error tracking promo code failure:', error);
          }
        } else {
          const promocodeData = JSON.parse(JSON.parse(response.data.data));
          if (promocodeData.is_active == "active") {
            var price = dollarData.price;
            var discount = promocodeData.percentage;
            var calculated = calculateDiscount(price, discount); 
            localStorage.setItem('discounted_price', calculated);
            localStorage.setItem('applied_promocode', promocode);
            setAfterDiscount(discount);
            // setCalculatedPrice(calculated);

            setError(true);
            setBtnVisible(false);
            setPromocode("");
            setPromocodeMsg("Code successfully applied");
            setPromocodeInvalid(true);
            setInputDisable(true);
            let keyCoupon = 2017; //WELCOMETEST
            let couponArr = [];
            let valueCoupon = null;
            if ("id" in promocodeData) keyCoupon = promocodeData.id;
            if ("coupon_id" in promocodeData)
              valueCoupon = promocodeData.coupon_id;
            // console.log(keyCoupon,valueCoupon)
            if (keyCoupon && valueCoupon) {
              couponArr["keyCoupon"] = keyCoupon;
              couponArr["valueCoupon"] = valueCoupon;
              setCouponId({ ...couponArr });
            }
            
            // Track successful promo code in Mixpanel
            try {
              Mixpanel.track('Promo Code Applied', {
                promo_code: promocode,
                discount_percentage: discount,
                original_price: price,
                discounted_price: calculated,
                package_id: dollarData.id,
                package_name: dollarData.package_name,
                coupon_id: keyCoupon
              });
            } catch (error) {
              console.warn('Error tracking promo code application:', error);
            }
            
            // Track promo code application
            Analytics.trackFeatureUsage('Promo Code Applied', {
              promo_code: promocode,
              discount_percentage: discount,
              original_price: price,
              discounted_price: calculated
            });
          }
        }
      })
      .catch(function (errors) {
        console.error("Error applying promo code:", errors);
        setError("Failed to apply promo code. Please try again later.");
        console.log(errors);
        
        // Track error in Mixpanel
        try {
          Mixpanel.track('Promo Code Error', {
            promo_code: promocode,
            error: errors?.message || 'Unknown error',
            package_id: dollarData.id,
            package_name: dollarData.package_name
          });
        } catch (trackError) {
          console.warn('Error tracking promo code error:', trackError);
        }
        //return errors;
      });
  };

  const onChangeHandler = (event) => {
    let promocode = event.target.value;
    setPromocode(promocode);

    const minLength = 2;
    if (promocode.length == 0) {
      setPromocodeInvalid(true);
    } else if (minLength <= promocode.length) {
      setError(true);
      setBtnVisible(true);
      setPromocodeInvalid(true);
    } else {
      setError(false);
      setBtnVisible(false);
      setPromocodeInvalid(false);
    }
  };
  
  const onSubmitHandler = (event) => {
    event.preventDefault();
    setLoading(true);
    const getDomain = window.location.origin;

    // Get UTM params from localStorage if they exist
    const utmParams = localStorage.getItem('utm_params');
    let utmQueryString = '';

    if (utmParams) {
      try {
        const params = JSON.parse(utmParams);
        utmQueryString = new URLSearchParams(params).toString();
      } catch (error) {
        console.error('Error parsing UTM params:', error);
      }
    }

    const userData = {
      priceId: dollarData.price_id,
      cancelUrl: getDomain + "/payment-failed",
      successUrl: `${getDomain}/payment-successful${utmQueryString ? `?${utmQueryString}` : ''}`, // Will append orderId later
      customerEmail: email,
    };

    const createOrderData = {
      email_id: email,
      package_id: dollarData.id,
    };

    if (couponId) {
      createOrderData.coupon_id = couponId.keyCoupon;
      userData.promoCode = couponId.valueCoupon;
    }
    
    // Track payment initiation in Mixpanel
    try {
      // Gather device and location info
      const deviceInfo = {
        browser: navigator.userAgent,
        device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        language: navigator.language || navigator.userLanguage
      };
      
      // Get UTM parameters as object
      const utmParamsObj = utmParams ? JSON.parse(utmParams) : {};
      
      // Get plan type from package name
      let planType = '';
      if (dollarData.package_name && dollarData.package_name.toLowerCase().includes('glacier')) {
        planType = 'Glacier';
      } else if (dollarData.package_name && dollarData.package_name.toLowerCase().includes('ice floe')) {
        planType = 'Ice Floe';
      } else if (dollarData.package_name && dollarData.package_name.toLowerCase().includes('polar peak')) {
        planType = 'Polar Peak';
      } else if (dollarData.package_name && dollarData.package_name.toLowerCase().includes('frozen fortune')) {
        planType = 'Frozen Fortune';
      } else {
        planType = `Plan ID: ${createOrderData.package_id}`;
      }
      
      // Track in Mixpanel with explicit plan type
      Mixpanel.track('Payment Initiated', {
        package_id: createOrderData.package_id,
        package_name: dollarData.package_name || `Plan ID: ${createOrderData.package_id}`,
        plan_type: planType,
        price: dollarData.price || 0,
        discounted_price: localStorage.getItem('discounted_price') || dollarData.price || 0,
        has_discount: !!couponId,
        discount_percentage: afterDiscount || 0,
        promo_code: localStorage.getItem('applied_promocode') || '',
        user_email: email,
        timestamp: new Date().toISOString(),
        ...deviceInfo,
        ...utmParamsObj
      });
      
    } catch (error) {
      console.warn('Error tracking payment initiated in Mixpanel:', error);
    }
    
    // Track "Paid Sign Up Started" event using Analytics
    try {
      // Gather device and location info
      const deviceInfo = {
        browser: navigator.userAgent,
        device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        language: navigator.language || navigator.userLanguage
      };
      
      // Get UTM parameters as object
      const utmParamsObj = utmParams ? JSON.parse(utmParams) : {};
      
      // Use the trackPaymentStarted method with a unique timestamp to prevent deduplication
      Analytics.trackPaymentStarted({
        checkout_step: 'payment_initiated',
        package_id: createOrderData.package_id,
        package_name: dollarData.package_name || `Plan ID: ${createOrderData.package_id}`,
        price: dollarData.price || 0,
        discounted_price: localStorage.getItem('discounted_price') || dollarData.price || 0,
        has_discount: !!couponId,
        user_email: email,
        promo_code: localStorage.getItem('applied_promocode') || '',
        timestamp: new Date().toISOString(), // Add unique timestamp
        ...deviceInfo,
        ...utmParamsObj
      });
      
    } catch (error) {
      console.warn('Error tracking payment initiated:', error);
    }

    PostWithTokenNoCache(ApiName.createOrder, createOrderData)
      .then((res) => {
        if (res.data.status == 200) {
          const orderData = JSON.parse(res.data.data);
          const orderId = orderData.orderId;

          // Store orderId in localStorage
          localStorage.setItem('orderId', orderId);

          // Append orderId to success URL
          const successUrlWithOrderId = `${userData.successUrl}${utmQueryString ? '&' : '?'}orderId=${orderId}`;
          userData.successUrl = successUrlWithOrderId;

          // Track order creation in Mixpanel
          // Temporarily commented out to reduce event volume
          /* 
          try {
            Mixpanel.track('Order Created', {
              order_id: orderId,
              package_id: createOrderData.package_id,
              package_name: dollarData.package_name || '',
              price: dollarData.price || 0,
              discounted_price: localStorage.getItem('discounted_price') || dollarData.price || 0,
              promo_code: localStorage.getItem('applied_promocode') || ''
            });
          } catch (error) {
            console.warn('Error tracking order creation in Mixpanel:', error);
          }
          */

          PostWithTokenNoCache(ApiName.confirmAndPay, userData)
            .then(function (response) {
              let paymentUrl = JSON.parse(response.data.data);
              
              // Track redirect to payment page in Mixpanel
              try {
                Mixpanel.track('Payment Redirect', {
                  order_id: orderId,
                  package_id: createOrderData.package_id,
                  package_name: dollarData.package_name || ''
                });
              } catch (error) {
                console.warn('Error tracking payment redirect in Mixpanel:', error);
              }
              
              window.location.href = paymentUrl;
            })
            .catch(function (errors) {
              console.error("Error confirming and paying:", errors);
              setError("Payment confirmation failed. Please try again later.");
              setLoading(false);
              
              // Track payment error in Mixpanel
              try {
                Mixpanel.track('Payment Error', {
                  error: errors?.message || 'Unknown error',
                  order_id: orderId,
                  package_id: createOrderData.package_id,
                  package_name: dollarData.package_name || ''
                });
              } catch (trackError) {
                console.warn('Error tracking payment error in Mixpanel:', trackError);
              }
            });
        }
      })
      .catch((err) => {
        console.error("Error creating order:", err);
        setError("Order creation failed. Please try again later.");
        setLoading(false);
        
        // Track order creation error in Mixpanel
        try {
          Mixpanel.track('Order Creation Error', {
            error: err?.message || 'Unknown error',
            package_id: createOrderData.package_id,
            package_name: dollarData.package_name || ''
          });
        } catch (trackError) {
          console.warn('Error tracking order creation error in Mixpanel:', trackError);
        }
      });

    const minLength = 2;
    if (promocode !== null || (undefined && minLength <= promocode.length)) {
      setError(true);
    } else {
      setError(null);
    }
  };

  const capitalizeFirstLetters = (str) => {
    if (!str || typeof str !== 'string') {
      return '';
    }
    return str
      .replace(/Yearly/gi, '') // Remove the word "Yearly" (case-insensitive)
      .trim()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <>
      <GTM gtmId={gtmId} />
      <DWINTracker />
      <div className="container-fluid">
        <div className="row">
          {/* <div className="col-md-1"></div> */}
          <div className="col-md-5 bg-color">
            <div className="offset-md-3 saver-plane1">
              <div className="rs-logo">
                <img src="../images/r-logo.png" width="50" className="" />
              </div>
              <div className="pricing-plane">
                <div className="card-container">
                  <div className="updgrade-plan-header">
                    <h4>{dollarData.package_name ? capitalizeFirstLetters(dollarData.package_name) : ""}</h4>
                  </div>
                  <table class="table table-striped">
                    <thead>
                      <tr className="backrnd-color">
                        <th className="borderless">
                          <div className="d-flex flex-row justify-content-between">
                            <div className="prce"><p>Pricing</p></div>
                          </div>
                        </th>
                        <th className="borderles">
                          <div className="upgrade-doller-2">
                            <span> ${dollarData.price}</span>

                            <span className="plan-per-month">
                              {yearlyPlanIds.includes(dollarData.id) ? '/Year' : '/Month'}
                            </span>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="borderless">
                        <td className="text-pose">Unlimited Contact Views</td>
                        <td className="text-center">
                          {dollarData?.no_of_contact_views === "Unlimited" ? "Yes" : dollarData?.no_of_contact_views}
                        </td>
                      </tr>
                      <tr className="borderless">
                        <td className="text-pose">Email Credits</td>
                        <td className="text-center">
                          Unlimited
                        </td>
                      </tr>
                      <tr className="row-clr">
                        <td className="text-pose">Exports/Month</td>
                        <td className="text-center">
                          {dollarData?.export ? (Number(dollarData.export)).toLocaleString() : ""}
                        </td>
                      </tr>
                      <tr>
                        <td className="text-pose">20+ Data Insights</td>
                        <td className="text-center">Yes</td>
                      </tr>
                      <tr className="row-clr">
                        <td className="text-pose">
                          Verified Emails and Phone Numbers
                        </td>
                        <td className="text-center">Yes</td>
                      </tr>
                      <tr>
                        <td className="text-pose">Access to ReachAPI</td>
                        <td className="text-center">Yes</td>
                      </tr>
                      <tr className="row-clr">
                        <td className="text-pose">Free Monthly Updates</td>
                        <td className="text-center">Yes</td>
                      </tr>
                      <tr className="borderless">
                        <td className="text-pose">Dedicated Account Manager</td>
                        <td className="text-center">
                          {dollarData.package_name === "polar peak plan yearly" || dollarData.package_name === "polar peak" ? "Yes" : "No"}
                        </td>
                      </tr>

                      <tr className="row-clr">
                        <td className="text-pose">24/7 Customer Support</td>
                        <td className="text-center">
                          {dollarData.package_name === "polar peak plan yearly" || dollarData.package_name === "polar peak" ? "Yes" : "No"}
                        </td>
                      </tr>

                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="col-md-11">
              <p className="start-journeyy">Payment Details</p>
            </div>
            <div className="form-box5">
              <div className="fisrt-layer">
                <br />
                <br />
                <div className="d-flex justify-content-between">
                  <div className="total-discount">Total</div>
                  <div className="dark">
                    <p>
                      {dollarData.package_name === "glacier plan yearly" || dollarData.package_name === "ice floe plan yearly"
                        ? `$${(dollarData.price + 120).toFixed(2)}`
                        : dollarData.package_name === "polar peak plan yearly"
                          ? `$${(dollarData.price + 240).toFixed(2)}`
                          : dollarData.price ? `$${dollarData.price.toFixed(2)}` : ""}
                    </p>
                  </div>
                </div>
                <div className="d-flex justify-content-between">
                  <div className="total-discount">
                    Discount
                    <span className="minus-discount"></span>
                  </div>
                  <div className="dark">
                    <p>
                      {afterDiscount
                        ? `- $${(dollarData.price * (afterDiscount / 100)).toFixed(2)}`
                        : dollarData.package_name === "glacier plan yearly" || dollarData.package_name === "ice floe plan yearly"
                          ? "- $120.00"
                          : dollarData.package_name === "polar peak plan yearly"
                            ? "- $240.00"
                            : ""}
                    </p>
                  </div>
                </div>
              </div>
              <hr className="horizontal" />
              <div className="fisrt-layer">
                <div className="d-flex justify-content-between">
                  <div className="dark-wall">Total (Including Tax)</div>
                  <div className="dark-blue">
                    <p>
                      ${afterDiscount
                        ? (parseFloat(dollarData.price) * (1 - afterDiscount / 100)).toFixed(2)
                        : dollarData.price ? parseFloat(dollarData.price).toFixed(2) : ""}
                    </p>
                  </div>
                </div>
              </div>
              <form onSubmit={promocodeAppied}>
                <div className="second-layer">
                  <p>Have a promo code?</p>
                  <div className="d-flex">
                    <div className="col-md-7 mt-1">
                      <div
                        className={
                          !promocodeMsg
                            ? "input-with-icon-text"
                            : "input-with-icon"
                        }
                      >
                        {promocodeInvalid ? (
                          <input
                            type="text"
                            className="form-control input-field"
                            id="exampleInputPass"
                            name="promocode"
                            onChange={onChangeHandler}
                            value={promocode}
                            placeholder={promocodeMsg}
                            disabled={inputDisable ? "disabled" : ""}
                            autoFocus
                          />
                        ) : (
                          <></>
                        )}

                        {promocodeMsg ? (
                          <img
                            src="../images/promocode-success.png"
                            className="icon"
                          />
                        ) : (
                          <></>
                        )}
                      </div>

                      {!promocodeInvalid ? (
                        <input
                          type="text"
                          className="invalid-input-text"
                          name="promocode"
                          onChange={onChangeHandler}
                          value={promocode}
                          placeholder={promocodeMsg}
                          disabled={inputDisable ? "disabled" : ""}
                          autoFocus
                        />
                      ) : (
                        <></>
                      )}

                      <span className="email-error-message">
                        {error && <p>{error}</p>}
                      </span>
                    </div>

                    <div className="col-md-4 Pnlty">
                      {!btnVisible ? (
                        <input
                          type="submit"
                          value="Apply"
                          className="cp-pluss cust-disabled"
                        />
                      ) : (
                        <input type="submit" value="Apply" className="cp-pluss" />
                      )}
                    </div>
                  </div>
                </div>
              </form>
              <div onClick={onSubmitHandler}>
                <div className="adjust">
                  <button className="Confirman" type="submit">
                    {loading ? "Loading..." : "Confirm and Pay"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default ProceedToPay;