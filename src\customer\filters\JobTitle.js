import { useEffect, useState } from "react";
import { PostWithTokenNoCache, postWithToken } from '../common-files/ApiCalls.js';

import { useContext } from 'react';
import AsyncSelect from 'react-select/async';
import "../assests/css/filter/job_title_v2.css";
import { ApiName } from '../common-files/ApiNames.js';
import DashboardContext from '../common-files/ContextDashboard.js';
import UseTabStore from "../common-files/useGlobalState.js";

const JobTitle = () => {

	const {
		contactJobTitle1,
		checkedItems,
		checkedBoxes,
		resetPage,
		openParentItems,
		setSelectedContactJobTitle1,
		setSelectedContactJobTitleLevel1,
		setCurrentPage,
		setCheckedBoxes,
		setCheckedItems,
		setIsFiltered,
		setOpenParentItems
	} = UseTabStore();
	const [inputValue, setInputValue] = useState('');
	const { dataDC, setDataDC } = useContext(DashboardContext);
	const [joblevel, setJobLevel] = useState([]);
	const [jobDepartments, setJobDepartments] = useState([]);
	const [parentIds, setParentIds] = useState(JSON.parse(localStorage.getItem("jobDepartments")));
	const [childIds, setChildIds] = useState(JSON.parse(localStorage.getItem("jobFunction")))
	const [isJobtitleAutoFocus, setIsJobtitleAutoFocus] = useState(false); // Set one of them to autofocus initially
	const [loading, setLoading] = useState(true);
	const [selectedJobTitleValues, setSelectedJobTitleValues] = useState([]);
	useEffect(() => {
		if ("jobLevel" in dataDC) setCheckedBoxes(Array.isArray(dataDC.jobLevel) ? dataDC.jobLevel : null);
		let DeptArr = null;
		const keys = [];
		if ("DeptFunc" in dataDC) {
			for (let key in dataDC.DeptFunc) {
				if (dataDC.DeptFunc.hasOwnProperty(key)) {
					keys.push(key);
				}
			}
			setParentIds(keys);
			const child = [];
			if (dataDC.DeptFunc) {
				for (var key in dataDC.DeptFunc) {
					if (dataDC.DeptFunc.hasOwnProperty(key)) {
						var innerKeys = Object.keys(dataDC.DeptFunc[key]).filter(innerKey => dataDC.DeptFunc[key][innerKey] === true);
						child.push(innerKeys);
					}
				}
				const flatArray = child.flat();
				setChildIds(flatArray);
			}
			let DeptFunc = dataDC.DeptFunc;
			for (const key in DeptFunc) {
				if (Object.hasOwnProperty.call(DeptFunc, key)) {
					const element = DeptFunc[key];
					DeptArr = { ...DeptArr, [key]: true };
					let EleObj = null;
					for (const keyEle in element) {
						if (Object.hasOwnProperty.call(element, keyEle)) {
							const ele = element[keyEle];
							if (ele) {
								EleObj = { ...EleObj, [keyEle]: true };
								DeptFunc[key] = EleObj;
							}
						}
					}
					if (EleObj == null) {
						DeptFunc[key] = {};
					}
				}
			}
		}
	}, [dataDC, resetPage]);

	useEffect(() => {
		handleFilters("jobLevel", checkedBoxes);
	}, [checkedBoxes, resetPage]);

	useEffect(() => {
		let deptFuncSet = [];
		for (const key in checkedItems) {
			if (Object.hasOwnProperty.call(checkedItems, key)) {
				const element = checkedItems[key];
				if (element) {
					deptFuncSet[key] = element;
				}
			}
		}
		handleFilters("DeptFunc", { ...deptFuncSet });
	}, [checkedItems, resetPage]);

	useEffect(() => {
		if (Object.keys(contactJobTitle1).length < 1) {
			setSelectedJobTitleValues([]);
		} else {
			setSelectedJobTitleValues(contactJobTitle1);
		}
	}, [contactJobTitle1])

	useEffect(() => {
		const url = ApiName.predefinedValuesFilter;
		const dataJobLevel = { "url": "api/get-job-title-level" };
		const dataDeptFunc = { "url": "api/get-department-function" };
		const fetchData = async (data) => {
			// You can await here
			let res = await postWithToken(url, data);
			if (res && "status" in res) {
				if (res.data.status == 200) {
					let titlesObj = JSON.parse(JSON.parse(res.data.data));
					if (data.url == "api/get-department-function") {
						let arrCollect = [];
						let mappedCollection = [];
						let prefixListArr = ["C-Suite", "Engineering", "Design", "Education", "Finance", "Human Resources", "Information Technology", "Legal", "Marketing"];
						let departments = titlesObj.map((v) => {
							if (typeof arrCollect[v.department_name] == 'undefined') {
								arrCollect[v.department_name] = [v.function_name];
							} else {
								arrCollect[v.department_name] = [...arrCollect[v.department_name], v.function_name]
							}
							return v.department_name;
						});
						let uniquedepartments = Array.from(new Set(departments));
						uniquedepartments.filter((v) => {
							if (!prefixListArr.includes(v)) {
								prefixListArr.push(v);
							}
						});
						prefixListArr.filter(v => {
							let jsonParseString = JSON.parse(`{ "id": "${v}" ,"name": "${v}", "subitems": [], "checked":"false" }`);
							jsonParseString.subitems = [...arrCollect[v]];
							mappedCollection.push(jsonParseString);
						});
						setJobDepartments(mappedCollection);
					}
					if (data.url == "api/get-job-title-level") {
						let prefixListArr = ["Owner & Founder", "C-Suite", "Partner", "VP", "Head", "Director"];
						let mapped = titlesObj.map((v) => {
							if (!prefixListArr.includes(v.name)) {
								return JSON.parse(`{ "value": "${v.name}" ,"label": "${v.name}" }`);
							}
						});
						const reversedprefixListArray = prefixListArr.slice().reverse();
						reversedprefixListArray.forEach((v) => {
							mapped.unshift(JSON.parse(`{ "value": "${v}" ,"label": "${v}" }`));
						});
						let filteredArray = mapped.filter(value => value !== null && value !== undefined);
						setJobLevel(filteredArray);
					}

				} else {
					setLoading(false);
				}
			} else {
				setLoading(false);
			}
		}
		fetchData(dataJobLevel);
		fetchData(dataDeptFunc);
	}, []);

	const convertToproperCase = async (data) => {
		if (!data) return [];

		return data.map(val => {
			let capitalizeNext = true; // Flag to determine capitalization
			return [...val].map(char => {
				if (/[a-zA-Z]/.test(char)) { // Check if character is alphabetic
					if (capitalizeNext) {
						capitalizeNext = false;
						return char.toUpperCase(); // Capitalize
					}
					return char.toLowerCase(); // Lowercase
				}
				capitalizeNext = /\s/.test(char); // Reset flag on whitespace
				return char; // Keep special characters as is
			}).join('');
		});
	};

	const SearchJobTitle = ({ autoFocus, onFocus, onBlur }) => {

		// Handle value change when an option is selected or deselected
		const handleValueChange = (selectedOptions) => {
			setCurrentPage(1);
			setIsFiltered(true);

			setSelectedJobTitleValues(selectedOptions);
			setSelectedContactJobTitle1(selectedOptions);
		};

		const loadOptions = (inputText, callback) => {
			if (inputText.length < 1) {
				callback([]);
				return;
			}
			if (inputText && inputText.length > 0) {
				const dataPost = {
					contact_job_title_1: inputText // Pass the inputText to your API request
				};
				// Make an API call here to fetch suggestions based on the inputText
				PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
					.then(async (res) => {
						if (res.data.status === 200) {
							const jobtitle = JSON.parse(res.data.data);

							let properCaseJobTitle = await convertToproperCase(jobtitle);

							// Convert the array to a Set to remove duplicates and then convert it back to an array
							const uniqueJobTitle = [...new Set(properCaseJobTitle)];
							const options = uniqueJobTitle.map((option) => ({
								label: option,
								value: option
							}));
							callback(options);
						}
					})
					.catch((error) => {
						console.error('Error fetching data:', error);
						callback([]); // Clear options on error
					});
			}
		};

		const noOptionsMessage = () => {
			return inputValue.length < 2 ? 'No options found' : 'No options found';
		};


		return (
			<div className="mb-2 mt-3 d-flex flex-column">
				<div className="">
					<AsyncSelect
						cacheOptions
						defaultOptions={false}
						loadOptions={loadOptions}
						isMulti
						placeholder="Search by Job Title"
						onChange={handleValueChange} // Handle selected value changes
						value={selectedJobTitleValues} // Pass selected values
						styles={SearchJobtitleStyles}
						autoFocus={autoFocus}
						onFocus={onFocus}
						onBlur={onBlur}
						isClearable
						noOptionsMessage={noOptionsMessage}
					/>
				</div>
				<div className="search-icon-v2">
					<i className="fas fa-search"></i>
				</div>

			</div>
		)
	}

	const SearchJobtitleStyles = {
		control: (provided, state) => ({
			...provided,

			marginLeft: "16px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
			cursor: "text",

			":hover": {
				borderColor: "#000",
			},
			":focus": {
				border: "1px solid #000",
			},
		}),

		placeholder: (baseStyles, state) => ({
			...baseStyles,
			gridArea: "1/1/2/3",
			color: "#A3AEBB",
			marginLeft: "8px",
			fontSize: "14px",
			// width: "max-content"
		}),


		singleValue: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			backgroudColor: "red",
		}),

		multiValue: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			backgroundColor: "white",
			marginTop: "8px",
			border: "1px solid",
			fontSize: "70%",

		}),


		multiValueGeneric: (provided, state) => ({
			...provided,
			paddingLeft: "8px",
			paddingTop: "10px",
			padding: "1px",
			marginTop: "12px",


		}),

		indicatorsContainer: (provided, state) => ({
			...provided,
			visibility: "hidden",
			fontSize: "0",


		}),

		menuList: (provided, state) => ({
			...provided,
			"&:hover:": {
				color: "#55C2C3",
				marginTop: "-6px",
				marginBottom: "14px",
				backgroudColor: "transperent",
				zIndex: "1000",
			},

			"&:hover": {
				backgroundColor: "inherit",
			},
		}),

		menu: (provided, state) => ({
			...provided,

			border: "1px solid #093D54",
			marginLeft: "26px",
			marginRight: "2rem",
			fontSize: "14px",
			marginRight: "0px",
			position: "relative",
			width: "-webkit-fill-available",
			backgroudColor: "#fff",

			":hover": {
				color: "inherit",
				textDecoration: "none",
			},
		}),

		multiValueRemove: (base, state) => ({
			...base,
			backgroundColor: "#fff",
			setColor: "#000",
			borderRadius: "50%",
			borderRadius: "10px",
			paddingLeft: "-60",
			paddingRight: "0",
			innerHeight: "10px",
			border: "1px solid #08CEDC",
			marginTop: "-7px",
			marginBottom: "2px",
			font: "black !important",
			backgroudColor: "#ffffff",
			width: "11px",
			height: "11px",
			"&:hover": {
				backgroundColor: "#55C2C3",
				color: "black",
			},
		}),

		indicatorSeparator: (provided, state) => ({
			...provided,
			backgroundColor: "hsl(0deg 0% 100%)",
		}),

		MenuList: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#fff",
			outline: "none",
		}),

		LoadingIndicator: (provided, state) => ({
			...provided,
			fontSize: 0
		}),

		valueContainer: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#000",
			outline: "none",
			padding: "0px 5px 0 0",
			width: "300px"
		}),
		container: (provided, state) => ({
			...provided,
			padding: "0px 11px 0 0",
		}),
		group: (provided, state) => ({
			...provided,
			width: "262px",
			// paddingLeft: '23px',
			marginLeft: "32px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
		}),

	};

	function handleFilters(name, value) {
		if (name == "jobLevel") {
			setSelectedContactJobTitleLevel1(value);
		}
		if (name == "jobTitle") {
			setSelectedContactJobTitle1(value);
		} else {
			setDataDC({ ...dataDC, [name]: value });
		}

	}

	const handleCheckboxChange1 = (event) => {
		setCurrentPage(1);
		setIsFiltered(true);

		const name = event.target.name;
		const checked = event.target.checked;
		setCheckedItems({
			...checkedItems,
			[name]: checked,
		});
		setOpenParentItems({
			...openParentItems,
			[name]: checked,
		});
	};

	const handleSubCheckboxChange = (event) => {
		setCurrentPage(1);
		setIsFiltered(true);

		const parentName = event.target.dataset.parentname;
		const name = event.target.name;
		setCheckedItems({
			...checkedItems,
			[parentName]: {
				...checkedItems[parentName],
				[name]: event.target.checked,
			},
		});
	};

	const handleCheckboxChange = (event) => {
		setCurrentPage(1);
		setIsFiltered(true);

		const checkboxValue = event.target.value;

		if (checkedBoxes && checkedBoxes.includes(checkboxValue)) {
			setCheckedBoxes(checkedBoxes.filter((checkbox) => checkbox !== checkboxValue));
		} else if (!checkedBoxes) {
			setCheckedBoxes([checkboxValue]);
		} else {
			setCheckedBoxes([...checkedBoxes, checkboxValue]);
		}
	};

	const SearchJJobttiles = () => {
		return (
			<div>
				<SearchJobTitle
					autoFocus={isJobtitleAutoFocus}
					onFocus={() => setIsJobtitleAutoFocus(true)}
					onBlur={() => setIsJobtitleAutoFocus(false)}
				/>
			</div>
		)
	}
	const JobLevelCheckbox = () => {
		return (
			<div className="d-flex flex-column">
				{joblevel && joblevel.map((checkbox, i) => (
					<label key={i} className="job-checkbox-v2">&nbsp;
						<input
							key={checkbox.label}
							type="checkbox"
							value={checkbox.label}
							checked={checkedBoxes ? checkedBoxes.includes(checkbox.label) : null}
							onChange={handleCheckboxChange}
						/>
						{checkbox.label}
					</label>
				))}
			</div>
		)
	}
	const parentCheckboxes = jobDepartments.map((parentItem) => {
		const subCheckboxes = parentItem.subitems.map((subItem) => (

			<div key={"d_" + subItem} className="d-flex flex-column sub_item_padding">
				<label key={"label_" + subItem} className="job-checkbox-v2">&nbsp;
					<input
						key={"in_" + subItem}
						type="checkbox"
						name={subItem}
						checked={childIds && childIds.includes(subItem)}
						onChange={handleSubCheckboxChange}
						data-parentname={parentItem.name}
					/>
					{subItem}
				</label>
			</div>
		));

		return (
			<div key={parentItem.id}>
				<label key={"label_" + parentItem.id} className="job-checkbox-v2">&nbsp;
					<input id="example-five-checkbox"
						key={parentItem.name}
						type="checkbox"
						name={parentItem.name}
						checked={parentIds && parentIds.includes(parentItem.name)}
						onChange={handleCheckboxChange1}
					/>
					{parentItem.name}
					<span id="example-five" htmlFor="example-five-checkbox">+</span>
				</label>
				{openParentItems[parentItem.name] && subCheckboxes}
			</div>
		);
	});

	return (
		<div>
			<SearchJJobttiles
				autoFocus={isJobtitleAutoFocus}
				onFocus={() => setIsJobtitleAutoFocus(true)}
				onBlur={() => setIsJobtitleAutoFocus(false)}
			/>
			<div className="paragraph">
				<button className="Job-Titles-v2" type="button" data-toggle="collapse" data-target="#collapseExample1" aria-expanded="false" aria-controls="collapseExample">
					Job Level <i className="fa fa-caret-down"></i>
				</button>
				<div className="collapse" id="collapseExample1">
					<div className=" card-body-containerr" >
						<JobLevelCheckbox />
					</div>
				</div>
			</div>
			<div id="accordion">
				<div className="border-">
					<div className="card-header-first" id="headingTwo">
						<h5 className="mb-0">
							<button className="btn btn-link-v2 collapsed" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
								Department & Job Function <i className="fa fa-caret-down"></i>
							</button>
						</h5>
					</div>
					<div id="collapseTwo" className="collapse" aria-labelledby="headingTwo" data-parent="#accordion">
						<div className="card-body-1">
							{parentCheckboxes}
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}
export default JobTitle;