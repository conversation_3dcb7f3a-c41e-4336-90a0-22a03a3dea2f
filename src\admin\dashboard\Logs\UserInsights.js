import React, { useEffect, useState, useRef } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import AdminHeader from "../../layouts/AdminHeader.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import {
  PostWithTokenNoCache,
  getProfilePictureUsingUserId,
} from "../../../customer/common-files/ApiCalls.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import loadingGif from "../../../customer/assests/waiting.gif";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import Alert from "../../../customer/common-files/alert.js";
import { useNavigate, useParams } from "react-router-dom";
import BarChart from "../charts/BarChart.js";

// For freemium plan (your original formatCreditValue)
function formatCreditValue(value) {
  if (value === null || value === undefined) return "0";
  if (typeof value === "number") return value.toLocaleString("en-IN");
  if (typeof value === "string" && value.toLowerCase() === "unlimited") return "Unlimited";
  return value;
}

// For non-freemium plans (modified to match your special logic)
function formatViewValue(value, isTotal = true) {
  if (value === null || value === undefined) return "0";

  if (typeof value === "number") {
    return value.toLocaleString("en-IN");
  }

  if (typeof value === "string") {
    // Special case: For total views in non-freemium, "Unlimited" becomes "0"
    if (isTotal && value.toLowerCase() === "unlimited") {
      return "0";
    }
    return value;
  }

  return "0";
}

const UserInsights = () => { 
  let pageSize = 10;
  const navigate = useNavigate();
  let detail = useParams();
  let detials = JSON.parse(detail.id);
  const {
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
    defaultAlert,
    defaultErrorMsg,
  } = UseTabStore();
  const [userCreadits, setUserCreadits] = useState("");
  const [user, setUser] = useState("");
  const [totalStats, setTotalStats] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [chartLoading, setChartLoading] = useState(false);
  const [days, setDays] = useState(1);
  const [userDuration, setUserDuration] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [data, setData] = useState([]);
  const [tab, setTab] = useState("COUNTS");
  const [userPlans, setUserPlans] = useState(detials.userPlans);
  const [formattedDate, setFormattedDate] = useState("");
  const [dataValues, setDataValues] = useState([]);
  const [labels, setLabels] = useState([]);
  const [profilePicture, setProfilePicture] = useState(null);
  const targetDivRef = useRef(null);
  const [wishListNames, setWishListNames] = useState([]);

  useEffect(() => {
    const getSignleUserDetails = async () => {
      try {
        const params = {
          user_id: parseInt(detials.id),
        };
        const res = await PostWithTokenNoCache(ApiName.fetchSingleUser, params);
        if (res?.status === 200) {
          let data = JSON.parse(res?.data?.data);
          setUser(data);
          creditsUpdate(data.email);
        } else {
          setButtonType("error");
          setDefaultErrorMsg("Sorry, Could not fetch user details");
          setDefaultAlert(true);
        }
      } catch (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors.response.data.message);
        setDefaultAlert(true);
      }
    };
    const creditsUpdate = async (email) => {
      try {
        const params = { user_email: email };
        const res = await PostWithTokenNoCache(ApiName.activeCreadits, params);
        if (res && "status" in res) {
          if (res?.data?.status == 200 && "data" in res?.data) {
            setUserCreadits(JSON.parse(res?.data?.data));
          }
        }
      } catch (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
      }
    };
    const getProfilePic = async () => {
      try {
        const imageUrlTemp = await getProfilePictureUsingUserId(
          ApiName.getProfilePicture,
          parseInt(detials.id)
        );
        if (imageUrlTemp) {
          setProfilePicture(imageUrlTemp);
        }
      } catch (errors) {
        //   setButtonType("error");
        //   setDefaultErrorMsg(errors?.response?.data?.message);
        //   setDefaultAlert(true);
      }
    };

    getSignleUserDetails();
    getProfilePic();
  }, []);

  useEffect(() => {
    getuserFilterUsageHistoryStats();
    getActivityHours();
  }, [days]);

  useEffect(() => {
    getHistory();
  }, [tab, currentPage]);

  useEffect(() => {
    const currentDate = new Date();
    let date = format_Date(currentDate);
    setFormattedDate(date);
  }, []);

  const getHistory = async () => {
    setIsLoading(true);
    let params;
    if (tab == "COUNTS") {
      params = JSON.stringify({
        page: currentPage,
        pageSize: pageSize,
        searchParams: {
          action_type: tab,
          user_id: parseInt(detials.id),
        },
      });
    } else if (tab == "DOWNLOAD") {
      params = JSON.stringify({
        page: currentPage,
        pageSize: pageSize,
        searchParams: {
          action_type: tab,
          user_id: parseInt(detials.id),
          onlyWishList: true,
        },
      });
    } else if (tab == "VIEW") {
      params = JSON.stringify({
        page: currentPage,
        pageSize: pageSize,
        searchParams: {
          action_type: tab,
          user_id: parseInt(detials.id),
          onlyViewRecord: true,
        },
        sortBy: "desc",
      });
    } else if (tab == "ERRORNOTIFICATION") {
      params = JSON.stringify({
        page: currentPage,
        pageSize: pageSize,
        searchParams: {
          response_status: ["500", "400", "401", "403"],
          user_id: parseInt(detials.id),
        },
      });
    } else if (tab == "SINGLEDOWNLOAD") {
      params = JSON.stringify({
        page: currentPage,
        pageSize: pageSize,
        searchParams: {
          action_type: "DOWNLOAD",
          onlyViewRecord: true,
          user_id: parseInt(detials.id),
        },
      });
    } else if (tab == "USERQUERY") {
      params = JSON.stringify({
        page: currentPage,
        pageSize: pageSize,
        sortBy: "desc",
        searchParams: {
          email: user.email,
        },
      });
    }
    if (tab != "USERQUERY") {
      try {
        const res = await PostWithTokenNoCache(
          ApiName.fetchUserHistory,
          params
        );
        if (res && "status" in res) {
          if (res?.data?.status == 200 && "data" in res.data) {
            let data = res.data.data.user_filter_usage_history;

            let record = data.items;
            if (tab == "DOWNLOAD") {
              const ids = [];
              for (let i = 0; i < record.length; i++) {
                if (record[i].wishListId) {
                  ids.push(record[i].wishListId);
                }
              }
              try {
                const params = JSON.stringify({
                  ids: ids,
                });
                let res = await PostWithTokenNoCache(
                  ApiName.fetchListNames,
                  params
                );
                if (res && "status" in res) {
                  if (res?.data?.status == 200 && "data" in res.data) {
                    let data = JSON.parse(res.data.data);
                    setWishListNames(data);
                    for (let i = 0; i < record.length; i++) {
                      if (record[i].wishListId) {
                        for (let j = 0; j < data.length; j++) {
                          if (data[j].id == record[i].wishListId) {
                            record[i].list_name = data[j].name;
                          }
                        }
                      }
                    }
                  }
                  setIsLoading(false);
                }
                setIsLoading(false);
              } catch (errors) {
                setIsLoading(false);
              }
            }
            if (tab == "SINGLEDOWNLOAD" || tab == "VIEW") {
              let new_contacts = [];
              for (let i = 0; i < record.length; i++) {
                if (record[i].viewRecordId) {
                  new_contacts.push(record[i].viewRecordId);
                }
              }
              if (new_contacts != []) {
                const params = JSON.stringify({
                  ids: new_contacts,
                  searchBy: "CONTACT",
                });
                try {
                  const res = await PostWithTokenNoCache(
                    ApiName.passingRecordIds,
                    params
                  );
                  const responseStatus = JSON.parse(res.data.status);
                  if (responseStatus === 200) {
                    const contact_records = JSON.parse(res.data.data);
                    let sorted_array = [];
                    for (let i = 0; i < new_contacts.length; i++) {
                      for (let j = 0; j < contact_records.length; j++) {
                        if (new_contacts[i] == contact_records[j].id) {
                          sorted_array.push(contact_records[j]);
                        }
                      }
                    }
                    record = sorted_array;
                  }
                } catch (errors) { }
              }
            }
            setData(record);
            setTotalPages(data.totalPages);
            setTotalItems(data.totalItems);
            setIsLoading(false);
          }
        }
      } catch (errors) {
        setIsLoading(false);
        //   setButtonType("error");
        //   setDefaultErrorMsg(errors?.response?.data?.message);
        //   setDefaultAlert(true);
      }
    } else {
      try {
        const res = await PostWithTokenNoCache(
          ApiName.contactPagination,
          params
        );
        if (res && "status" in res) {
          if (res?.data?.status == 200 && "data" in res.data) {
            const record = JSON.parse(res?.data?.data);
            setData(record.items);
            setTotalPages(record.totalPages);
            setTotalItems(record.totalItems);
            setIsLoading(false);
          }
        }
      } catch (errors) {
        setData([]);
        setIsLoading(false);
      }
    }
  };
  const getuserFilterUsageHistoryStats = async () => {
    setChartLoading(true);
    // You can await here
    const params = JSON.stringify({
      user_id: parseInt(detials.id),
      days: days,
    });

    try {
      const res = await PostWithTokenNoCache(
        ApiName.userFilterUsageHistoryStats,
        params
      );
      if (res && "status" in res) {
        if (res?.data?.status == 200 && "data" in res?.data) {
          setChartLoading(false);
          setTotalStats(res.data.data.total_stats);
        }
      }
    } catch (errors) {
      setChartLoading(false);
      // setButtonType("error");
      // setDefaultErrorMsg(errors?.response?.data?.message);
      // setDefaultAlert(true);
    }
  };
  const getActivityHours = async () => {
    setChartLoading(true);
    const params = JSON.stringify({
      user_id: parseInt(detials.id),
      days: days,
    });

    try {
      const res = await PostWithTokenNoCache(ApiName.totalHours, params);
      if (res && "status" in res) {
        if (res?.data?.status == 200 && "data" in res.data) {
          const dayViseTimes = res?.data?.data?.login_logout_times_day_wise;

          let label = [];
          let datavalue = [];
          let j = 1;
          Object.keys(dayViseTimes).forEach(function (key) {
            const arrayval = dayViseTimes[key];
            let totalminutes = 0;
            let starttime;
            if (days != 1) {
              for (let i = 0; i < arrayval.length; i++) {
                if (i == 0) {
                  starttime = addTime(arrayval[i].login_time, 5, 30);
                }
                let startDate;
                if (!arrayval[i]?.login_time) {
                  if (arrayval[i]?.logout_time) {
                    const logoutDate = new Date(
                      addTime(arrayval[i].logout_time, 5, 30)
                    );
                    startDate = new Date(
                      logoutDate.getFullYear(),
                      logoutDate.getMonth(),
                      logoutDate.getDate()
                    );
                  } else {
                    startDate = new Date();
                  }
                } else {
                  startDate = new Date(addTime(arrayval[i].login_time, 5, 30));
                }
                let endDate;
                if (!arrayval[i]?.logout_time) {
                  const currentDate = new Date();
                  const loginDateOnly = new Date(
                    startDate.getFullYear(),
                    startDate.getMonth(),
                    startDate.getDate()
                  );
                  if (
                    currentDate.toDateString() !== loginDateOnly.toDateString()
                  ) {
                    endDate = new Date(
                      loginDateOnly.getTime() + 24 * 60 * 60 * 1000 - 1
                    );
                  } else {
                    endDate = new Date();
                  }
                } else {
                  endDate = new Date(addTime(arrayval[i].logout_time, 5, 30));
                }
                const diffInMs = endDate - startDate;
                const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
                if (diffInMinutes) totalminutes = totalminutes + diffInMinutes;
              }

              if (days == 1) {
                if (starttime) {
                  let explode = starttime.split(" ");
                  let explode1 = explode[1].split(":");
                  label.push(explode1[0] + " " + explode1[1]);
                  datavalue.push(totalminutes);
                }
              } else {
                label.push("Day " + j++);
                datavalue.push(totalminutes);
              }
            } else {
              for (let i = 0; i < arrayval.length; i++) {
                if (i == 0) {
                  starttime = addTime(arrayval[i].login_time, 5, 30);
                }
                let startDate;
                if (!arrayval[i]?.login_time) {
                  if (arrayval[i]?.logout_time) {
                    const logoutDate = new Date(
                      addTime(arrayval[i].logout_time, 5, 30)
                    );
                    startDate = new Date(
                      logoutDate.getFullYear(),
                      logoutDate.getMonth(),
                      logoutDate.getDate()
                    );
                  } else {
                    startDate = new Date();
                  }
                } else {
                  startDate = new Date(addTime(arrayval[i].login_time, 5, 30));
                }
                let endDate;
                if (!arrayval[i]?.logout_time) {
                  const currentDate = new Date();
                  const loginDateOnly = new Date(
                    startDate.getFullYear(),
                    startDate.getMonth(),
                    startDate.getDate()
                  );
                  if (
                    currentDate.toDateString() !== loginDateOnly.toDateString()
                  ) {
                    endDate = new Date(
                      loginDateOnly.getTime() + 24 * 60 * 60 * 1000 - 1
                    );
                  } else {
                    endDate = new Date();
                  }
                } else {
                  endDate = new Date(addTime(arrayval[i].logout_time, 5, 30));
                }
                const diffInMs = endDate - startDate;
                const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
                label.push(startDate.getHours() + ":" + startDate.getMinutes());
                datavalue.push(diffInMinutes);
              }
            }
          });
          setDataValues(datavalue);
          setLabels(label);
          setUserDuration(res?.data?.data.user_duration_hours);
          setChartLoading(false);
        }
      }
    } catch (errors) {
      if (
        errors?.response?.data?.status == 400 &&
        errors?.response?.data?.message == "Record not found."
      ) {
        let label = [];
        let datavalue = [];
        for (let i = 1; i <= days; i++) {
          label.push("Day " + i++);
          datavalue.push(0);
        }
        setDataValues(datavalue);
        setLabels(label);
        setChartLoading(false);
      }
      // console.log('errors',errors?.response?.data?.message);
      // console.log('errors',errors?.response?.data?.status);
      // setButtonType("error");
      // setDefaultErrorMsg(errors?.response?.data?.message);
      // setDefaultAlert(true);
    }
  };
  const addTime = (timestamp, hoursToAdd, minutesToAdd) => {
    // Parse the input timestamp
    const [dateStr, timeStr] = timestamp.split(" ");
    const [year, month, day] = dateStr.split("/").map(Number);
    const [hours, minutes, seconds] = timeStr.split(":").map(Number);

    // Create a Date object with the given time
    const date = new Date(year, month - 1, day, hours, minutes, seconds);

    // Add the specified hours and minutes
    date.setHours(date.getHours() + hoursToAdd);
    date.setMinutes(date.getMinutes() + minutesToAdd);

    // Format the date as a string
    const formattedDate =
      [
        date.getFullYear(),
        ("0" + (date.getMonth() + 1)).slice(-2),
        ("0" + date.getDate()).slice(-2),
      ].join("/") +
      " " +
      [
        ("0" + date.getHours()).slice(-2),
        ("0" + date.getMinutes()).slice(-2),
        ("0" + date.getSeconds()).slice(-2),
      ].join(":");

    return formattedDate;
  };
  const getCreditValues = (value) => {
    let point;
    switch (value) {
      case "total-download-used":
        if (
          userCreadits.additional_credit_assigned == "Unlimited" ||
          userCreadits.total_assigned_credit == "Unlimited" ||
          userCreadits.total_balance_credit == "Unlimited"
        ) {
          if (userCreadits.total_balance_credit != "Unlimited") {
            point = userCreadits.total_balance_credit
              ? parseInt(userCreadits.total_balance_credit)
              : 0;
          } else {
            point = "Unlimited";
          }
        } else {
          point =
            (userCreadits.additional_credit_assigned
              ? parseInt(userCreadits.additional_credit_assigned)
              : 0) +
            (userCreadits.total_assigned_credit
              ? parseInt(userCreadits.total_assigned_credit)
              : 0) -
            (userCreadits.total_balance_credit
              ? parseInt(userCreadits.total_balance_credit)
              : 0);
        }
        break;
      case "total-download-credit":
        if (
          userCreadits.additional_credit_assigned == "Unlimited" ||
          userCreadits.total_assigned_credit == "Unlimited"
        ) {
          point = "Unlimited";
        } else {
          point =
            (userCreadits.additional_credit_assigned
              ? parseInt(userCreadits.additional_credit_assigned)
              : 0) +
            (userCreadits.total_assigned_credit
              ? parseInt(userCreadits.total_assigned_credit)
              : 0);
        }
        break;
      case "remaining-download":
        if (
          userCreadits.additional_credit_assigned == "Unlimited" ||
          userCreadits.total_assigned_credit == "Unlimited" ||
          userCreadits.total_balance_credit == "Unlimited"
        ) {
          point = "Unlimited";
        } else {
          point = userCreadits.total_balance_credit
            ? parseInt(userCreadits.total_balance_credit)
            : 0;
        }
        break;
      case "total-viewd":
        if (
          userCreadits.additional_contact_view_assigned == "Unlimited" ||
          userCreadits.total_assigned_contact_view == "Unlimited" ||
          userCreadits.total_balance_credit == "Unlimited"
        ) {
          if (userCreadits.total_balance_contact_view != "Unlimited") {
            point = userCreadits.total_balance_contact_view
              ? parseInt(userCreadits.total_balance_contact_view)
              : 0;
          } else {
            point = "Unlimited";
          }
        } else {
          point =
            (userCreadits.additional_contact_view_assigned
              ? parseInt(userCreadits.additional_contact_view_assigned)
              : 0) +
            (userCreadits.total_assigned_contact_view
              ? parseInt(userCreadits.total_assigned_contact_view)
              : 0) -
            (userCreadits.total_balance_contact_view
              ? parseInt(userCreadits.total_balance_contact_view)
              : 0);
        }
        break;
      case "total-view-credit":
        if (
          userCreadits.additional_contact_view_assigned == "Unlimited" ||
          userCreadits.total_assigned_contact_view == "Unlimited"
        ) {
          point = "Unlimited";
        } else {
          point =
            (userCreadits.additional_contact_view_assigned
              ? parseInt(userCreadits.additional_contact_view_assigned)
              : 0) +
            (userCreadits.total_assigned_contact_view
              ? parseInt(userCreadits.total_assigned_contact_view)
              : 0);
        }
        break;
      case "remaining-view":
        if (
          userCreadits.additional_contact_view_assigned == "Unlimited" ||
          userCreadits.total_assigned_contact_view == "Unlimited" ||
          userCreadits.total_balance_credit == "Unlimited"
        ) {
          point = "Unlimited";
        } else {
          point = userCreadits.total_balance_contact_view
            ? parseInt(userCreadits.total_balance_contact_view)
            : 0;
        }
        break;
    }
    return point;
  };
  const searchKeyValue = (key) => {
    switch (key) {
      case "contact_name":
        return "Contact Name";
      case "contact_first_name":
        return "First Name";
      case "contact_middle_name":
        return "Middle Name";
      case "contact_last_name":
        return "Last Name";
      case "contact_job_title_1":
        return "Job Title";
      case "contact_job_title_level_1":
        return "Job Title Level";
      case "contact_job_dept_name_1":
        return "Job Department Name";
      case "contact_job_function_name_1":
        return "Job Function Name";
      case "contact_email_1":
        return "email";
      case "contact_phone_1":
        return "Phone Number";
      case "company_company_name":
        return "Company Name";
      case "company_website":
        return "Website";
      case "company_address_street":
        return "Address";
      case "company_address_city":
        return "City";
      case "company_address_state":
        return "State";
      case "company_address_zipcode":
        return "Zipcode";
      case "company_address_country":
        return "Country";
      case "company_employee_size":
        return "Company Employee Size";
      case "company_annual_revenue_amount":
        return "Company Annual Revenue Amount";
      case "sic_code":
        return "Siccode";
      case "company_industry_categories_list":
        return "Industry Category";
      case "contact_social_linkedin":
        return "Contact Social Linkedin";
      case "npi_number":
        return "NPI Number";
      default:
        return key;
    }
  };
  const filterApplied = (string) => {
    if (string == undefined || string == null || string == "") return "--";
    let searchstring = "";
    let array = JSON.parse(string);
    let array1 = array?.searchPattern;
    if (array1 !== undefined) {
      Object.keys(array1).forEach((key) => {
        const value = array1[key];
        if (Object.keys(value).length !== 0 && key !== "searchBy") {
          if (searchstring === "") {
            searchstring = searchKeyValue(key);
          } else {
            searchstring += "|" + searchKeyValue(key);
          }
        }
      });
      return searchstring;
    } else {
      return "--";
    }
  };
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const strTime = hours + ":" + minutes + ampm;

    return `${day}/${month}/${year} - ${strTime}`;
  };
  const loadContacts = (id, recordid) => {
    const item = data.filter((item) => item.id == recordid).map((item) => item);
    let params = JSON.stringify({
      userid: parseInt(detials.id),
      userPlans: userPlans,
      id: id,
    });
    if (tab == "COUNTS" || tab == "VIEW") {
      navigate("/admin/user-view-log/" + params, { state: { item } });
    } else if (tab == "SINGLEDOWNLOAD" || tab == "DOWNLOAD") {
      navigate("/admin/user-download-log/" + params, { state: { item } });
    }
  };

  const format_Date = (date) => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };
  // const getErrorDetails = (count) => {
  //   let params = JSON.stringify({
  //     userid: parseInt(detials.id),
  //     userPlans: userPlans,
  //     days:days
  //   });
  //   navigate("/admin/error-notification/" + params);
  // };
  const setTabs = (value) => {
    setCurrentPage(1);
    setTab(value);
  };

  const getResponseMessage = (responseStatus) => {
    switch (responseStatus) {
      case "200":
        return "Success";
        break;
      case "400":
        return "Invalid token";
        break;
      case "500":
        return "Internal Server Error";
        break;
      case "401":
        return "Unauthorized";
        break;
      case "403":
        return "Bad Request";
        break;
    }
  };

  return (
    <>
      <div className={S.app}>
        <AdminHeader />
        <div className={S.main_container}>
          <div className="d-flex flex-row">
            <div style={{ width: "300px" }}>
              <LeftSideNav />
            </div>
            <div className={S.scrollablediv}>
              <div className="p-2">
                <div className="d-flex flex-row  justify-content-between">
                  <div>
                    <p className={S.UserInsightsparagraph}>User Insights</p>
                    <div className={S.underlinisights}></div>
                  </div>
                  <div>
                    <div className={S.insightsdateborder}>
                      <div className="d-flex flex-row">
                        <div>
                          <p className={S.insightsdatename}>Date</p>
                        </div>
                        <div className="d-flex">
                          <p className={S.insightsdates}>{formattedDate} -</p>
                          <p className={S.insightsdates}>{formattedDate}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={S.userinsightsblock}>
                  <div className={S.insightscoloreddiv}>
                    <div className="row">
                      <div className="col-md-1 mt-5 ml-3">
                        <img
                          src={
                            profilePicture
                              ? profilePicture
                              : "../../images/avetar.png"
                          }
                          width="120"
                          className={S.insightspengimage}
                        />
                      </div>
                      <div className="col-md-2 pr-0 mt-5 ml-3">
                        <p className={S.insightsjoanne}>
                          {user.firstName != "null" ? user.firstName : ""}{" "}
                          {user.lastName != "null" ? user.lastName : ""}
                        </p>
                        <p className={S.insightsOfficer}>
                          {user?.jobTitle && user.jobTitle != "null"
                            ? user.jobTitle
                            : ""}
                        </p>
                        <p className={S.insightstabubble}>
                          <a href="#">
                            {user.email != "null" ? user.email : ""}
                          </a>
                        </p>
                      </div>

                      <div className="col-md-8 pr-0 mt-3 mb-3">
                        <div className={S.insightsblockofff}>
                          <div className="row">
                            <div className="col-md-4 pr-0">
                              <div className={S.insightstotaldownloadcredits}>
                                <p className={S.insightsTotalDownloadCredits}>
                                  Total Download Credits
                                </p>
                                <p className={S.insightsdownloadedcredits}>
                                  {getCreditValues("total-download-credit")}
                                </p>
                              </div>
                            </div>
                            <div className="col-md-4 p-0">
                              <div className={S.insightstotaldownloadcredits}>
                                <p className={S.insightsTotalDownloadCredits}>
                                  Download Credits Used
                                </p>
                                <p className={S.insightsdownloadedcredits}>
                                  {getCreditValues("total-download-used")}
                                </p>
                              </div>
                            </div>
                            <div className="col-md-4 pl-0">
                              <div
                                className={S.insightstotaldownloadcreditssss}
                              >
                                <p className={S.insightsTotalDownloadCredits}>
                                  Remaining Download Credits
                                </p>
                                <p className={S.insightsdownloadedcredits}>
                                  {getCreditValues("remaining-download")}
                                </p>
                              </div>
                            </div>
                            <div className="w-100"></div>
                            <div className="col-md-4 pr-0">
                              <div className={S.insightstotaldownloadcreditss}>
                                <p className={S.insightsTotalDownloadCredits}>
                                  Total Views 
                                </p>
                                <p className={S.insightsdownloadedcredits}>
                                  {userCreadits?.user_plan_name === "freemium"
                                    ? formatCreditValue(userCreadits?.total_assigned_email_credits)
                                    : formatViewValue(getCreditValues("total-viewd"), true)}
                                </p>
                              </div>
                            </div>
                            <div className="col-md-4 p-0">
                              <div className={S.insightstotaldownloadcreditss}>
                                <p className={S.insightsTotalDownloadCredits}>
                                  Views Used
                                </p>
                                <p className={S.insightsdownloadedcredits}>
                                  {getCreditValues("total-viewd")}
                                </p>
                              </div>
                            </div>
                            <div className="col-md-4 pl-0">
                              <div className={S.insightstotaldownloadcreditsss}>
                                <p className={S.insightsTotalDownloadCredits}>
                                  Remaining Views
                                </p>
                                <p className={S.insightsdownloadedcredits}>
                                  {userCreadits?.user_plan_name === "freemium"
                                  ? formatCreditValue(userCreadits?.total_balance_email_credits)
                                  : formatViewValue(getCreditValues("remaining-view"), false)}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Personal Information */}
                  <div>
                    <div className="row">
                      <div className="col-md-1"></div>
                      <div className="col-md-10 ml-4">
                        <p className={S.UserInsightsPersonalInformation}>
                          Personal Information
                        </p>
                        <div
                          className={S.PersonalInformationunderlinisights}
                        ></div>
                      </div>
                    </div>

                    <div className="">
                      <div className="row">
                        <div className="col-md-1"></div>
                        <div className="col-md-2">
                          <p className={S.insightsfirstname}>First Name</p>
                          <p className={S.insightsname}>
                            {user.firstName != "null" && user?.firstName
                              ? user.firstName
                              : "*******"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Last Name</p>
                          <p className={S.insightsname}>
                            {user.lastName != "null" && user?.lastName
                              ? user.lastName
                              : "*******"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>
                            Business Email Address
                          </p>
                          <p className={S.insightsname}>
                            {user.email != "null" && user?.email
                              ? user.email
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Phone</p>
                          <p className={S.insightsname}>
                            {user.phone != "null" && user?.phone
                              ? user.phone
                              : "-"}
                          </p>
                        </div>
                      </div>

                      <div className="row mt-3">
                        <div className="col-md-1"></div>
                        <div className="col-md-2">
                          <p className={S.insightsfirstname}>Package Type</p>
                          <p className={S.insightsname}>{userPlans}</p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Status</p>
                          <p className={S.insightsname}>
                            {user.status != "null" && user?.status
                              ? user.status
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Job Title</p>
                          <p className={S.insightsname}>
                            {user.jobTitle != "null" && user?.jobTitle
                              ? user.jobTitle
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Location</p>
                          <p className={S.insightsname}>Location</p>
                        </div>
                      </div>

                      <div className="row mt-3">
                        <div className="col-md-1"></div>
                        <div className="col-md-2">
                          <p className={S.insightsfirstname}>LinkedIn ID</p>
                          <p className={S.insightsname}>
                            {user.linkedIn != "null" && user?.linkedIn
                              ? user.linkedIn
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Facebook ID</p>
                          <p className={S.insightsname}>
                            {user.facebook != "null" && user?.facebook
                              ? user.facebook
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Twitter ID</p>
                          <p className={S.insightsname}>
                            {user.twitter != "null" && user?.twitter
                              ? user.twitter
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Instagram ID</p>
                          <p className={S.insightsname}>
                            {user.instagram != "null" && user?.instagram
                              ? user.instagram
                              : "-"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Company Information */}

                  <div className="mb-5">
                    <div className="row mt-5">
                      <div className="col-md-1"></div>
                      <div className="col-md-10 ml-4">
                        <p className={S.UserInsightsPersonalInformation}>
                          Company Information
                        </p>
                        <div
                          className={S.PersonalInformationunderlinisights}
                        ></div>
                      </div>
                    </div>

                    <div className="">
                      <div className="row">
                        <div className="col-md-1"></div>
                        <div className="col-md-2">
                          <p className={S.insightsfirstname}>Company Name</p>
                          <p className={S.insightsname}>
                            {user?.companyName && user.companyName != "null"
                              ? user.companyName
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Website Address</p>
                          <p className={S.insightsname}>
                            {user?.websiteAddress &&
                              user.websiteAddress != "null"
                              ? user.websiteAddress
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Industry</p>
                          <p className={S.insightsname}>
                            {user?.industry && user.industry != "null"
                              ? user.industry
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Employee Size</p>
                          <p className={S.insightsname}>
                            {user?.employeeSize && user.employeeSize != "null"
                              ? user.employeeSize
                              : "-"}
                          </p>
                        </div>
                      </div>

                      <div className="row mt-3">
                        <div className="col-md-1"></div>
                        <div className="col-md-2">
                          <p className={S.insightsfirstname}>Revenue </p>
                          <p className={S.insightsname}>
                            {user?.revenue && user.revenue != "null"
                              ? user.revenue
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>Company Address</p>
                          <p className={S.insightsname}>
                            {user?.companyAddress &&
                              user.companyAddress != "null"
                              ? user.companyAddress
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>City</p>
                          <p className={S.insightsname}>
                            {user?.city && user.city != "null"
                              ? user.city
                              : "-"}
                          </p>
                        </div>
                        <div className="col-md-3">
                          <p className={S.insightsfirstname}>ZIP Code</p>
                          <p className={S.insightsname}>
                            {user?.zipCode && user.zipCode != "null"
                              ? user.zipCode
                              : "-"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={S.userinsightsblock}>
                  <div className="d-flex flex-row">
                    <div>
                      <p className={S.insightsuserviewlog}>User Activity Log</p>
                    </div>
                  </div>

                  <ul
                    className="nav nav-pills mb-3 ml-3"
                    id="pills-tab"
                    role="tablist"
                  >
                    <li className="userinsightbuttons">
                      <a
                        className={`nav-link`}
                        id="1day"
                        data-toggle="pill"
                        href="#pills-home"
                        role="tab"
                        aria-controls="pills-home"
                        aria-selected="false"
                        style={{
                          color: "#000",
                          fontSize: "14px",
                          border: days === 1 ? "1px solid #55C2C3" : "",
                          backgroundColor: days === 1 ? "#E8F7F7" : "",
                        }}
                        onClick={() => setDays(1)}
                      >
                        1 - Day Activity
                      </a>
                    </li>
                    <li className="nav-item">
                      <a
                        className={`nav-link`}
                        id="7day"
                        data-toggle="pill"
                        href="#pills-profile"
                        role="tab"
                        aria-controls="pills-profile"
                        aria-selected="false"
                        style={{
                          color: "#000",
                          fontSize: "14px",
                          border: days === 7 ? "1px solid #55C2C3" : "",
                          backgroundColor: days === 7 ? "#E8F7F7" : "",
                        }}
                        onClick={() => setDays(7)}
                      >
                        7 - Day Activity
                      </a>
                    </li>
                    <li className="nav-item">
                      <a
                        className={`nav-link`}
                        id="14day"
                        data-toggle="pill"
                        href="#pills-contact"
                        role="tab"
                        aria-controls="pills-contact"
                        aria-selected="false"
                        style={{
                          color: "#000",
                          fontSize: "14px",
                          border: days === 14 ? "1px solid #55C2C3" : "",
                          backgroundColor: days === 14 ? "#E8F7F7" : "",
                        }}
                        onClick={() => setDays(14)}
                      >
                        14 - Day Activity
                      </a>
                    </li>

                    <li className="nav-item">
                      <a
                        className={`nav-link`}
                        id="28day"
                        data-toggle="pill"
                        href="#Day-Activity"
                        role="tab"
                        aria-controls="Day-Activity"
                        aria-selected="false"
                        style={{
                          color: "#000",
                          fontSize: "14px",
                          border: days === 28 ? "1px solid #55C2C3" : "",
                          backgroundColor: days === 28 ? "#E8F7F7" : "",
                        }}
                        onClick={() => setDays(28)}
                      >
                        28 - Day Activity
                      </a>
                    </li>
                  </ul>
                  <div>
                    {!chartLoading && !isLoading ? (
                      <BarChart dataValues={dataValues} labels={labels} />
                    ) : (
                      <div
                        className="mx-auto mt-5"
                        style={{ display: isLoading, textAlign: "center" }}
                      >
                        <img
                          src={loadingGif}
                          alt="Loading"
                          className="loader"
                          width="400"
                        />
                      </div>
                    )}
                  </div>
                  <div
                    className="tab-content"
                    id="pills-tabContent"
                    style={{ margin: "0 20px 0 20px", borderRadius: "10px" }}
                  >
                    <div
                      className="tab-pane fade show active"
                      style={{ color: "#000" }}
                      id="pills-home"
                      role="tabpanel"
                      aria-labelledby="pills-home-tab"
                    >
                      <div className="d-flex flex-row mb-3">
                        <div
                          className="col-sm"
                          style={{ border: "0.1px solid #C9C9C9" }}
                        >
                          <p className={S.insightsUserDuration}>
                            User Duration
                          </p>
                          <p
                            style={{ cursor: "default" }}
                            className={S.insightshours}
                          >
                            {userDuration}hrs
                          </p>
                        </div>
                        <div
                          className="col-sm"
                          style={{ border: "0.1px solid #C9C9C9" }}
                        >
                          <p className={S.insightsUserDuration}>
                            No. of Filters
                          </p>
                          <p
                            style={{ cursor: "default" }}
                            className={S.insightshours}
                          >
                            {totalStats.filterCount
                              ? totalStats.filterCount
                              : 0}
                          </p>
                        </div>
                        <div
                          className="col-sm"
                          style={{ border: "0.1px solid #C9C9C9" }}
                        >
                          <p className={S.insightsUserDuration}>No. of views</p>
                          <p
                            className={S.insightshours}
                            style={{ cursor: "default" }}
                          >
                            {totalStats.viewCount ? totalStats.viewCount : 0}
                          </p>
                        </div>
                        <div
                          className="col-sm"
                          style={{ border: "0.1px solid #C9C9C9" }}
                        >
                          <p className={S.insightsUserDuration}>
                            No. of Downloads
                          </p>
                          <p
                            className={S.insightshours}
                            style={{ cursor: "default" }}
                          >
                            {totalStats.downloadCount
                              ? totalStats.downloadCount
                              : 0}
                          </p>
                        </div>
                        <div
                          className="col-sm"
                          style={{ border: "0.1px solid #C9C9C9" }}
                        >
                          <p className={S.insightsUserDuration}>
                            No. of Error Notifications
                          </p>
                          <p
                            className={S.insightshours}
                            style={{ cursor: "default" }}
                          >
                            {totalStats.errorCount ? totalStats.errorCount : 0}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={S.userinsightsblock}>
                  <div className="d-flex flex-row">
                    <div>
                      <p
                        className={S.insightsuserviewlog}
                        onClick={() => setTabs("COUNTS")}
                        style={{
                          color: tab == "COUNTS" ? "#093D54" : "#C9C9C9",
                          fontWeight: tab == "COUNTS" ? "600" : "100",
                        }}
                      >
                        Filters View Log
                      </p>
                    </div>

                    <div>
                      <p
                        className={S.insightsuserdownloadlog}
                        onClick={() => setTabs("VIEW")}
                        style={{
                          color: tab == "VIEW" ? "#093D54" : "#C9C9C9",
                          fontWeight: tab == "VIEW" ? "600" : "100",
                        }}
                      >
                        User View Log
                      </p>
                    </div>
                    <div>
                      <p
                        className={S.insightsuserdownloadlog}
                        onClick={() => setTabs("DOWNLOAD")}
                        style={{
                          color: tab == "DOWNLOAD" ? "#093D54" : "#C9C9C9",
                          fontWeight: tab == "DOWNLOAD" ? "600" : "100",
                        }}
                      >
                        User Download Log
                      </p>
                    </div>
                    <div>
                      <p
                        className={S.insightsuserdownloadlog}
                        onClick={() => setTabs("SINGLEDOWNLOAD")}
                        style={{
                          color:
                            tab == "SINGLEDOWNLOAD" ? "#093D54" : "#C9C9C9",
                          fontWeight: tab == "SINGLEDOWNLOAD" ? "600" : "100",
                        }}
                      >
                        User Single Download Log
                      </p>
                    </div>
                    <div>
                      <p
                        className={S.insightsuserdownloadlog}
                        onClick={() => setTabs("ERRORNOTIFICATION")}
                        style={{
                          color:
                            tab == "ERRORNOTIFICATION" ? "#093D54" : "#C9C9C9",
                          fontWeight:
                            tab == "ERRORNOTIFICATION" ? "600" : "100",
                        }}
                      >
                        Error Notifications
                      </p>
                    </div>
                    <div>
                      <p
                        className={S.insightsuserdownloadlog}
                        onClick={() => setTabs("USERQUERY")}
                        style={{
                          color: tab == "USERQUERY" ? "#093D54" : "#C9C9C9",
                          fontWeight: tab == "USERQUERY" ? "600" : "100",
                        }}
                      >
                        Contact Us History
                      </p>
                    </div>
                  </div>

                  <div
                    className={S.savedlistbackgroundcolors}
                    ref={targetDivRef}
                  >
                    {tab == "COUNTS" &&
                      (!isLoading ? (
                        <table className="your-refrel-lists">
                          <tbody>
                            <tr className="table-headers-saved-list mt-2">
                              <th style={{ padding: "0px 0px 0 18px" }}>
                                Timestamp
                              </th>
                              <th>Filter Type</th>
                              <th>Filter Applied</th>
                              <th>Contacts Displayed</th>
                              <th>Status</th>
                            </tr>
                            {data.map((item, i) => (
                              <tr className="table-data-small">
                                <td className={S.datagoesheresavedlist}>
                                  {formatDate(item.requestTime)}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {item.filterType}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {filterApplied(item.filterApplied)}
                                </td>
                                <td
                                  style={{
                                    fontSize: "13px",
                                    padding: "0px 0 0 3rem",
                                  }}
                                  className=""
                                >
                                  <img
                                    style={{ cursor: "pointer" }}
                                    src="../../images/eyee.png"
                                    onClick={() =>
                                      loadContacts(item.viewRecordId, item.id)
                                    }
                                  />
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {item.responseStatus == "200"
                                    ? "success"
                                    : "Error"}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      ) : (
                        <div
                          className="mx-auto mt-5"
                          style={{ display: isLoading, textAlign: "center" }}
                        >
                          <img
                            src={loadingGif}
                            alt="Loading"
                            className="loader"
                            width="400"
                          />
                        </div>
                      ))}
                    {tab == "VIEW" &&
                      (!isLoading ? (
                        <table className="your-refrel-lists">
                          <tbody>
                            <tr className="table-headers-saved-list mt-2">
                              <th style={{ padding: "0px 0px 0 18px" }}>
                                Contact Name
                              </th>
                              <th>Job Title</th>
                              <th>Email/Phone</th>
                              <th>Company Name</th>
                              <th>Location</th>
                            </tr>
                            {data.map((item, i) => (
                              <tr className="table-data-small">
                                <td className={S.datagoesheresavedlist}>
                                  {item.contact_name}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {item.contact_job_title_1}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  <div className="d-flex flex-row">
                                    <div className="mr-2">
                                      <img src="../../images/mail-unread.png" />
                                      Email
                                    </div>
                                    <div>
                                      <img src="../../images/phone-alt.png" />
                                      Phone
                                    </div>
                                  </div>
                                </td>
                                <td
                                  style={{
                                    fontSize: "13px",
                                    padding: "0px 0px 0px 7px",
                                  }}
                                  className=""
                                >
                                  {item.company_company_name}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {item.company_address_city},{" "}
                                  {item.company_address_state}
                                  <br></br>
                                  {item.company_address_country}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      ) : (
                        <div
                          className="mx-auto mt-5"
                          style={{ display: isLoading, textAlign: "center" }}
                        >
                          <img
                            src={loadingGif}
                            alt="Loading"
                            className="loader"
                            width="400"
                          />
                        </div>
                      ))}

                    {tab === "DOWNLOAD" &&
                      (!isLoading ? (
                        <table className="your-refrel-lists">
                          <tbody>
                            <tr className="table-headers-saved-list mt-2">
                              <th style={{ padding: "0px 0px 0 18px" }}>Timestamp</th>
                              <th>Downloads</th>
                              <th>Saved List</th>
                              <th>View</th>
                              <th>Status</th>
                            </tr>
                            {data.map((item, i) => {
                              const currentData = wishListNames.filter(
                                (wishList) =>
                                  wishList.id === item.wishListId && wishList.status === "DELETED"
                              );

                              console.log(currentData);

                              return (
                                <tr className="table-data-small" key={i}>
                                  <td className={S.datagoesheresavedlist}>
                                    {formatDate(item.requestTime)}
                                  </td>
                                  <td style={{ fontSize: "13px" }} className="">
                                    {item.filterCount}
                                  </td>
                                  <td style={{ fontSize: "13px" }} className="">
                                    {item.list_name}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "13px",
                                      padding: "0px 0px 0px 7px",
                                    }}
                                    className=""
                                  >
                                    <img
                                      style={{
                                        cursor: currentData.length > 0 && currentData[0].id === item.wishListId && currentData[0].status === "DELETED" ? "not-allowed" : "pointer",
                                        pointerEvents: currentData.length > 0 && currentData[0].id === item.wishListId && currentData[0].status === "DELETED" ? "none" : "auto",
                                      }} eye-gray
                                      src={currentData.length > 0 && currentData[0].id === item.wishListId && currentData[0].status === "DELETED" ? "../../images/eye-gray.png" : "../../images/eyee.png"}
                                      onClick={() => {
                                        loadContacts(item.wishListId, item.id);
                                      }}
                                      alt="View"
                                    />
                                  </td>
                                  <td style={{ fontSize: "13px" }} className="">
                                    {item.responseStatus === "200" ? "Success" : "Error"}
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      ) : (
                        <div
                          className="mx-auto mt-5"
                          style={{ display: isLoading, textAlign: "center" }}
                        >
                          <img
                            src={loadingGif}
                            alt="Loading"
                            className="loader"
                            width="400"
                          />
                        </div>
                      ))}

                    {tab == "SINGLEDOWNLOAD" &&
                      (!isLoading ? (
                        <table className="your-refrel-lists">
                          <tbody>
                            <tr className="table-headers-saved-list mt-2">
                              <th style={{ padding: "0px 0px 0 18px" }}>
                                Contact Name
                              </th>
                              <th>Job Title</th>
                              <th>Email/Phone</th>
                              <th>Company Name</th>
                              <th>Location</th>
                            </tr>
                            {data.map((item, i) => (
                              <tr className="table-data-small">
                                <td className={S.datagoesheresavedlist}>
                                  {item.contact_name}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {item.contact_job_title_1}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  <div className="d-flex flex-row">
                                    <div className="mr-2">
                                      <img src="../../images/mail-unread.png" />
                                      Email
                                    </div>
                                    <div>
                                      <img src="../../images/phone-alt.png" />
                                      Phone
                                    </div>
                                  </div>
                                </td>
                                <td
                                  style={{
                                    fontSize: "13px",
                                    padding: "0px 0px 0px 7px",
                                  }}
                                  className=""
                                >
                                  {item.company_company_name}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {item.company_address_city},{" "}
                                  {item.company_address_state}
                                  <br></br>
                                  {item.company_address_country}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      ) : (
                        <div
                          className="mx-auto mt-5"
                          style={{ display: isLoading, textAlign: "center" }}
                        >
                          <img
                            src={loadingGif}
                            alt="Loading"
                            className="loader"
                            width="400"
                          />
                        </div>
                      ))}

                    {tab == "ERRORNOTIFICATION" &&
                      (!isLoading ? (
                        <table className="your-refrel-lists">
                          <tbody>
                            <tr className="table-headers-saved-list mt-2">
                              <th style={{ padding: "0px 0px 0 18px" }}>
                                Timestamp
                              </th>
                              <th>Filter Type</th>
                              <th>Filter Applied</th>
                              <th>Status</th>
                            </tr>
                            {data.map((item, i) => (
                              <tr className="table-data-small">
                                <td className={S.datagoesheresavedlist}>
                                  {formatDate(item.requestTime)}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {item.filterType}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {filterApplied(item.filterApplied)}
                                </td>
                                <td className="" style={{ fontSize: "13px" }}>
                                  {getResponseMessage(item.responseStatus)}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      ) : (
                        <div
                          className="mx-auto mt-5"
                          style={{ display: isLoading, textAlign: "center" }}
                        >
                          <img
                            src={loadingGif}
                            alt="Loading"
                            className="loader"
                            width="400"
                          />
                        </div>
                      ))}
                    {tab == "USERQUERY" &&
                      (!isLoading ? (
                        <table className="your-refrel-lists">
                          <tbody>
                            <tr className="table-headers-saved-list mt-2">
                              <th style={{ padding: "0px 0px 0 18px" }}>
                                Timestamp
                              </th>
                              <th>Requirement</th>
                            </tr>
                            {data.map((item, i) => (
                              <tr className="table-data-small">
                                <td className={S.datagoesheresavedlist}>
                                  {formatDate(item.createdAt)}
                                </td>
                                <td style={{ fontSize: "13px" }} className="">
                                  {item.requirement}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      ) : (
                        <div
                          className="mx-auto mt-5"
                          style={{ display: isLoading, textAlign: "center" }}
                        >
                          <img
                            src={loadingGif}
                            alt="Loading"
                            className="loader"
                            width="400"
                          />
                        </div>
                      ))}

                    <div className="d-flex flex-row justify-content-between mt-3">
                      <div>
                        <p className={S.insightsSearchResultFound}>
                          Search Result Found
                          <span className={S.insightscontacts}>
                            {" "}
                            {totalItems} Records
                          </span>
                        </p>
                      </div>
                      <div>
                        <Pagination
                          className="pagination-bar"
                          currentPage={currentPage}
                          totalCount={totalPages}
                          pageSize={1}
                          onPageChange={(page) => {
                            setCurrentPage(page);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </>
  );
};

export default UserInsights;
