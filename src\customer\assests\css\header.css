/* Header css */

.<PERSON><PERSON> {
  background-color: #e8f7f7;
}

.logo-position {
  margin: 38px 0 0 0;
}

nav.navbar.navbar-expand-lg.navbar-light {
  background-color: #e8f7f7;
  height: 52px;
  padding: 0 25px 0 25px;
}

.Vl-2 {
  border-left: 2px solid #d5eeee;
  height: 50px;
  margin-left: 7px;
  padding: 0 0px 0px 15px;
}

a.navbar-brand {
  padding: 4px 0 0 11px;
}

/* .had-space {
    padding: 0 14px 0 17px;
} */

button.btn.btn- {
  background-color: #093d54;
  color: #fff;
  outline: none;
  border: 0;
  padding: 6px 25px 6px 25px;
  border-radius: 11px;
  vertical-align: middle;
  font-size: 14px;
  margin: 8px;
}

button.user-alt {
  background-color: white;
  border: none;
  color: #000000;
  padding: 5px 6px 5px 18px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 11px;
  outline: none;
  margin-top: 7px;
}



i.fas.fa-user {
  color: #55c2c3;
  border-radius: 20px;
  padding: 6px 6px 6px 6px;
  box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
  margin-left: 14px;
}

/* .dropdown-menu.dropdown-menu-right.show {
  width: 200px;
  border: 0;
 
  box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
  border-radius: 10px;
} */

.user-image img {
  border-radius: 35px;
  margin: 1px 5px 4px 1px;
}


.ken-hogan-3 p {
  margin: 0;
  padding: 10px 0 10px 4px;
  font-size: 11px;
  color: #000;
  font-weight: 700;
}

.accounting img {
  padding: 10px 0px 6px 12px;
  color: rgb(224, 224, 224);
}

.acc-1 p {
  margin: 0;
  color: #093d54;
  padding: 0 0 0 9px;
  font-weight: 500;
}

.signout {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 11px;
}

.signout button {
  background-color: #fff;
  font-size: 14px;
  color: #093d54;
  font-weight: 600;
  outline: none;
  border: 0;
  box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
  padding: 4px 21px 4px 21px;
  border-radius: 13px;
  cursor: pointer;
}

.box.arrow-top:after {
  content: " ";
  position: absolute;
  right: 9px;
  top: -11px;
  border-top: none;
  border-right: 8px solid #********;
  border-left: 9px solid transparent;
  border-bottom: 11px solid white;
}

.box {
  height: auto;
  background-color: #fff;
  color: #fff;
  position: relative;
  width: 180px;
}

.box.arrow-top {
  margin-top: -6px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 200px;
}

.user-image {
  padding: 10px 3px 3px 3px;
}

.link-space {
  border: none;
  border-left: 1px solid hsla(200, 10%, 50%, 100);
  height: 100vh;
  width: 1px;
}

.acc-1 a {
  text-decoration: none;
}

.vll {
  border-left: 2px solid #d5eeee;
  height: 50px;
  margin-left: 13px;
  padding: 0 0px 0px 15px;
}

.upgradable {
  padding: 7px 0 0 0;
}

span.stay_space {
  padding: 0px 25px 4px 3px;
  font-weight: 600;
}

button.user-alt img {
  box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
  border-radius: 70px;
}

.prompt-messhage {
  width: 230px;
  background-color: #F1F9F9;
  margin: 6px 17px 0 2px;
  border-radius: 8px;
  padding: 7px 7px 7px 13px;
  z-index: 1000;
  position: absolute;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}

.prompt-messhage2 {
  width: 250px;
  background-color: #F1F9F9;
  margin: 6px 17px 0 2px;
  border-radius: 8px;
  padding: 7px 7px 7px 13px;
  z-index: 1000;
  position: absolute;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}

.cross img {
  cursor: pointer;
}

p.suggestions {
  font-size: 14px;
  color: #093D54;
  font-weight: 600;
}

p.suggestions2 {
  font-size: 14px;
  color: #093D54;
  font-weight: 600;
  padding: 0 0px 0 7px;
  margin: 0 0 5px 0;
}

p.suggestions4 {
  font-size: 14px;
  color: #093D54;
  font-weight: 600;
  padding: 0 0px 0 0px;
  margin: 0 0 5px 0;
}

p.suggestions3 {
  font-size: 12px;
  color: #093D54;
  font-weight: 600;
  padding: 0 0px 0 0px;
  margin: 0 0 5px 0;
}

.textarea::placeholder {
  font-size: 12px;
  color: #C5C5C5;
}

.textarea {
  border: 1px solid #55C2C3;
  border-radius: 5px;
  outline: none;
}

button.prompt-button {
  background-color: #093D54;
  color: #fff;
  border: 0;
  font-size: 12px;
  padding: 2px 18px 2px 18px;
  margin: 3px 0 5px 0;
  border-radius: 5px;
  outline: none;
  cursor: pointer;
}

.sticky {
  position: sticky;
  top: 0;
  z-index: 1000;

}

.Ken-Hogan h3 {
  font-size: 16px;
  font-weight: 600;
  margin-left: 11px;
  top: 54px;
}