import { useState } from 'react';
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom';
import Admin from '../../admin/dashboard/AdminDashboard';
import Contact from '../../admin/dashboard/Contact/Contact.js';
import CreatePackageDetails from "../../admin/dashboard/CreatePackegeDetails.js";
import CreateUserDomainBlock from '../../admin/dashboard/DomainBlock/createUserDomainBlock.js';
import UpdateDomainBlock from '../../admin/dashboard/DomainBlock/updateUserDomainBlock.js';
import UsersDomainBlockList from '../../admin/dashboard/DomainBlock/usersDomainBlockList.js';
import CreateDomainRestriction from '../../admin/dashboard/DomainRestriction/createDomainRestriction.js';
import DomainRestrictionList from '../../admin/dashboard/DomainRestriction/domainRestrictionList.js';
import UpdateDomainRestriction from '../../admin/dashboard/DomainRestriction/updateDomainRestriction.js';
import InappPrompt from "../../admin/dashboard/InAppPrompt/AppPrompt.js";
import SendUsersNotifications from '../../admin/dashboard/InAppPrompt/sendUsersNotifications.js';
import ErrorNotification from '../../admin/dashboard/Logs/ErrorNotification.js';
import Logs from '../../admin/dashboard/Logs/Logs.js';
import UserDownloadLogs from '../../admin/dashboard/Logs/UserDownloadLogs.js';
import UserInsights from '../../admin/dashboard/Logs/UserInsights.js';
import UserPaymentHistory from '../../admin/dashboard/Logs/UserPaymentHistory.js';
import UserViewLogs from '../../admin/dashboard/Logs/UserViewLogs.js';
import CreatePromocode from '../../admin/dashboard/Promocode/CreatePromocode.js';
import Promocode from '../../admin/dashboard/Promocode/Promocode.js';
import PromoCodeHistory from '../../admin/dashboard/Promocode/PromoCodeHistory.js';
import AddCredit from '../../admin/dashboard/UserCredits/AddCredit.js';
import UsageDetails from '../../admin/dashboard/UserCredits/UsageDetails.js';
import AllPlans from "../../admin/dashboard/UserPlans/AllPlans.js";
import CreateNewPlan from "../../admin/dashboard/UserPlans/CreateNewPlan.js";
import EditPlan from '../../admin/dashboard/UserPlans/edit_plan.js';
import AllInactiveUsers from '../../admin/dashboard/Users/<USER>';
import AppSumo from '../../admin/dashboard/Users/<USER>';
import CreateUserByAdmin from '../../admin/dashboard/Users/<USER>';
import DeviceHistory from '../../admin/dashboard/Users/<USER>';
import FreetrailUsers from '../../admin/dashboard/Users/<USER>';
import IncompleteRegistration from '../../admin/dashboard/Users/<USER>';
import PaidUsers from '../../admin/dashboard/Users/<USER>';
import ResetPassword from '../../admin/dashboard/Users/<USER>';
import UnverifiedAccounts from '../../admin/dashboard/Users/<USER>';
import UserAction from '../../admin/dashboard/Users/<USER>';
import CompanySideNav from '../../customer/company-filters-new/CompanySidenav.js';
import Dashboard from '../../customer/filters/Dashboard.js';
import LeftNavbar from '../../customer/filters/LeftNavbar.js';
import { default as AccountPage, default as ProfilePage } from '../../customer/layouts/Account_details.js';
import ReachMax from '../../customer/layouts/ReachMax.js';
import PaymentFail from '../../customer/pricing/PaymentFailed.js';
import PaymentSuccess from '../../customer/pricing/PaymentSuccessfull.js';
import UpgradePlan from '../../customer/pricing/UpgradePlan.js';
import VerifyFreetrialEmail from '../../customer/pricing/VerifyYourEmail.js';
import CreateNewPassword from '../../customer/register/CreateNewPassword.js';
import ForgotPassword from '../../customer/register/Forgot.js';
import FreeTrialPayment from '../../customer/register/FreeTrialSignUp.js';
import PasswordChangedSuccessfully from '../../customer/register/PasswordCreatedSuccessfully.js';
import SignIn from '../../customer/register/SignIn.js';
import SuperSaverSignUp from '../../customer/register/SuperSaverSignUp.js';
import VerifyEmail from '../../customer/register/VerifyEmail.js';
import DownForSchedule from '../../server-error/DownForSchedule.js';
import PageNotFound from '../../server-error/PageNotFound.js';
import DashboardContext from '../common-files/ContextDashboard.js';
import LinkedinCallback from '../common-files/LinkedinCallback.js';
import JobTitle from '../filters/JobTitle.js';
import ReachMaxList from '../layouts/ReachMaxList.js';
import CompanyListOne from '../list/CompanyListOne.js';
import ContactListOne from '../list/ContactListOne.js';
import SavedList from '../list/SavedList.js';
import AppSumoPaymentDetails from '../pricing/AppSumoPaymentDetails.js';
import ProceedToPay from '../pricing/ProceedToPay.js';
import AppSumoSignUp from '../register/AppSumoSignup.js';
import AwsSignUp from '../register/AwsSignUp.js';
import SignUp from '../register/SignUp.js';
import PageFive from '../tour-guide/PageFive.js';
import PageFour from '../tour-guide/PageFour.js';
import PageOne from '../tour-guide/PageOne.js';
import PageThree from '../tour-guide/PageThree.js';
import PageTwo from '../tour-guide/PageTwo.js';
import { UserProvider } from './ContextData.js';
import TargetAudiencePage from './TargetAudiencePage.js';
import Enrich from '../enrichment/Enrich.js';
import DataEnrichmentMain from '../enrichment/DataEnrichmentMain.js';
import LeftNavBar from '../../customer/filters/LeftNavbar.js';
import Home from '../layouts/Home.js';
import Search from '../layouts/Search.js';
import Settings from '../settings/Settings.js';

const CreateRouter = () => {
  const [dataDC, setDataDC] = useState({});
  const handleParse = (user) => {
    setDataDC({ ...dataDC, "token": user.token });
  }
  let user = JSON.parse(localStorage.getItem('user'));

  return (
    <UserProvider>
      <DashboardContext.Provider value={{ dataDC, setDataDC }}>
        {() => handleParse(JSON.parse(localStorage.getItem('user')))}
        <BrowserRouter>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<SignIn />} />
            <Route path="/app-sumo-sign-up" element={<AppSumoSignUp />} />
            <Route path="/super-saver-sign-up" element={<SuperSaverSignUp />} />
            <Route path="/aws-sign-up" element={<AwsSignUp />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/verify-email" element={<VerifyEmail />} />
            <Route path="/create-new-password" element={<CreateNewPassword />} />
            <Route path="/password-changed-successfully" element={<PasswordChangedSuccessfully />} />
            <Route path="/proceed-to-pay" element={<ProceedToPay />} />
            <Route path="/upgrade-plan" element={<UpgradePlan />} />
            <Route path="/app-sumo-payment-details" element={<AppSumoPaymentDetails />} />
            <Route path="/payment-successful" element={<PaymentSuccess />} />
            <Route path="/payment-failed" element={<PaymentFail />} />
            <Route path="/sign-up/ice-breaker-plan" element={<FreeTrialPayment />} />
            <Route path="/free-trial-verify-email" element={<VerifyFreetrialEmail />} />
            <Route path="/jobtitle" element={<JobTitle />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/account" element={<AccountPage />} />
            <Route path="/profile-update" element={<ProfilePage />} />
            <Route path="/down-schedule" element={<DownForSchedule />} />
            <Route path="/linkedin" element={<LinkedinCallback />} />
            <Route path="/tour-guide-filter/:id" element={<PageOne />} />
            <Route path="/tour-guide-sort-by/:id" element={<PageTwo />} />
            <Route path="/tour-guide-contacts/:id" element={<PageThree />} />
            <Route path="/tour-guide-wishlist/:id" element={<PageFour />} />
            <Route path="/tour-guide-saved-list/:id" element={<PageFive />} />
            <Route path="refer-earn" element={<ReachMax />} />
            <Route path="/sign-up/:packageName" element={<SignUp />} />
            <Route path="/target-audience/:referralCode" element={<TargetAudiencePage />} />
            <Route path='/download-data' element={<SignIn />} />
            <Route path="/company-list/:id" element={<CompanyListOne />} />
            <Route path="/contact-list/:id" element={<ContactListOne />} />

            {/* Protected routes with LeftNavBar layout */}
            <Route element={<LeftNavBar />}>
              <Route path="/home" element={<Home />} />
              <Route path="/dashboard" element={<Home />} />
              <Route path="/company-filters" element={<CompanySideNav />} />
              <Route path="/search" element={<Search />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/saved-list" element={<SavedList />} />
              <Route path="/enrich" element={<DataEnrichmentMain />} />
              <Route path="/data-enrich" element={<Enrich />} />
              {user && "token" in user && <Route path="reach-max-list" element={<ReachMaxList />} />}
            </Route>

            

            {/* Admin routes */}
            {user && user.role === "admin" && (
              <>
                <Route path="/admin" element={<Admin />} />
                <Route path="/admin/free-trial-users" element={<FreetrailUsers />} />
                <Route path="/admin/paid-users" element={<PaidUsers />} />
                <Route path="/admin/usage-details" element={<UsageDetails />} />
                <Route path="/admin/add-credit" element={<AddCredit />} />
                <Route path="/admin/promocode" element={<Promocode />} />
                <Route path="/admin/create-promocode" element={<CreatePromocode />} />
                <Route path="/admin/create-package-details" element={<CreatePackageDetails />} />
                <Route path="/admin/inapp-prompt" element={<InappPrompt />} />
                <Route path="/admin/all-plans" element={<AllPlans />} />
                <Route path="/admin/create-new-plan" element={<CreateNewPlan />} />
                <Route path="/admin/send-user-notifications" element={<SendUsersNotifications />} />
                <Route path="/admin/all-inactive-users" element={<AllInactiveUsers />} />
                <Route path="/admin/app-sumo" element={<AppSumo />} />
                <Route path="/admin/edit-plan/:id" element={<EditPlan />} />
                <Route path="/admin/create-user" element={<CreateUserByAdmin />} />
                <Route path="/admin/users-domain-block-list" element={<UsersDomainBlockList />} />
                <Route path="/admin/update-user-block/:id" element={<UpdateDomainBlock />} />
                <Route path="/admin/block-users-domain" element={<CreateUserDomainBlock />} />
                <Route path="/admin/domain-restriction-list" element={<DomainRestrictionList />} />
                <Route path="/admin/create-domain-restriction" element={<CreateDomainRestriction />} />
                <Route path="/admin/update-domain-restriction/:id" element={<UpdateDomainRestriction />} />
                <Route path="/admin/reset-password/:id" element={<ResetPassword />} />
                <Route path="/admin/user-action/:id" element={<UserAction />} />
                <Route path="/admin/Logs" element={<Logs />} />
                <Route path="/admin/promocode-history" element={<PromoCodeHistory />} />
                <Route path="/admin/device-history/:id" element={<DeviceHistory />} />
                <Route path="/admin/user-view-log/:id" element={<UserViewLogs />} />
                <Route path="/admin/user-download-log/:id" element={<UserDownloadLogs />} />
                <Route path="/admin/user-insights/:id" element={<UserInsights />} />
                <Route path="/admin/payment-history/:id" element={<UserPaymentHistory />} />
                <Route path="/admin/error-notification/:id" element={<ErrorNotification />} />
                <Route path="/admin/contact-us-history" element={<Contact />} />
                <Route path="/admin/incomplete-registration" element={<IncompleteRegistration />} />
                <Route path="/admin/unverified-accounts" element={<UnverifiedAccounts />} />
              </>
            )}

            {/* Redirects */}
            <Route path="/" element={<Navigate to={user ? "/home" : "/"} />} />
            <Route path="/LeftNavbar" element={<Navigate to="/home" />} />

            {/* Fallback route */}
            <Route path="*" element={<PageNotFound />} />
          </Routes>
        </BrowserRouter>
      </DashboardContext.Provider>
    </UserProvider>
  );
};

export default CreateRouter;