// store.js
import { create } from 'zustand';

const useStore = create((set) => ({
  jobtitle: '',
  cachedCountryRes: '',
  cachedStateRes: '',
  cachedCityRes: '',
  cachedZipcodeRes: '',
  userData: '',
  activeCredits: '',
  updateJobtitle: (jobtitle) => set(() => ({ jobtitle: jobtitle })),
  updateCachedCountryRes: (cachedCountryRes) => set(() => ({ cachedCountryRes: cachedCountryRes })),
  updateCachedStateRes: (cachedStateRes) => set(() => ({ cachedStateRes: cachedStateRes })),
  updateCachedCityRes: (cachedCityRes) => set(() => ({ cachedCityRes: cachedCityRes })),
  updateCachedZipcodeRes: (cachedZipcodeRes) => set(() => ({ cachedZipcodeRes: cachedZipcodeRes })),
  updateUserData: (userData) => set(() => ({ userData: userData })),
  updateActiveCredits: (activeCredits) => set(() => ({ activeCredits: activeCredits })),
  reset: () => set({ userData: '', activeCredits: '' }),
}));

export default useStore;
