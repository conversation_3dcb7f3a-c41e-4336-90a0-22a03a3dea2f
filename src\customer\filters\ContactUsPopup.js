import React, { useState, useRef } from "react";
import { PostWithTokenNoCache, axiosPost } from "../common-files/ApiCalls.js";
import { ApiName } from "../common-files/ApiNames.js";
import { getSessionItem } from "../common-files/LocalStorage.js";
import Thank_you from "../../customer/assests/popups/Thank_you.js";
const ContactUsPopup = () => {
  const buttonRef = useRef(null);
  const thankYouModalBtn = useRef(null);
  let firstName, lastName, email, phone;
  let user = getSessionItem("user");
  if (user != null || undefined) {
    user = JSON.parse(user);
    // console.log(user);
    if ("firstName" in user) firstName = user.firstName;
    if ("lastName" in user) lastName = user.lastName;
    if ("email" in user) email = user.email;
    if ("phone" in user) phone = user.phone && user.phone !== "null" ? user.phone : "";
  }
  async function submitForm(data) {
    // You can await here
    const url = ApiName.contactUs;
    const res = await PostWithTokenNoCache(url, data);
    // console.log(url, data);
    // console.log(res);

    if (res && "status" in res) {
      if (res.data.status == 200) {
        if (buttonRef.current) {
          buttonRef.current.click();
          thankYouModalBtn.current.click();
        }
        // let result = axiosPost(ApiName.reachoutUS, data)
        //   .then(function (response) {
        //     if (buttonRef.current) {
        //       buttonRef.current.click();
        //       thankYouModalBtn.current.click();
        //     }
        //   }).catch(function (errors) {
        //     console.log(errors);
        //   })


      }
    }
  }
  const [formState, setFormState] = useState({
    first_name: firstName,
    last_name: lastName,
    email: email,
    phone: phone,
    requirement: "",
  });

  function handleInputChange(event) {
    setFormState({
      ...formState,
      [event.target.name]: event.target.value,
    });
  }
  function handleSubmit(event) {
    event.preventDefault();
    submitForm(formState);
  }
  return (
    <div>
      {
        <div
          className="modal fade"
          id="contactUsModalCenter"
          tabIndex="-1"
          role="dialog"
          aria-labelledby="contactUsModalCenterTitle"
          aria-hidden="true"
        >
          <div
            className="modal-dialog modal-lg"
            role="document"
            style={{ width: "600px", position:"absolute", left:"0", right:"0", top:"6%" }}
          >
            <div className="modal-content modal-size">
              <div className="">
                <div className="row">
                  <div className="col-sm-4"></div>
                  <div className="col-sm-4">
                    <h5 className="modal-title contactus text-center">Contact Us</h5>

                  </div>
                  <div className="col-sm-4">
                    <button
                      ref={buttonRef}
                      type="button"
                      className="close"
                      data-dismiss="modal"
                      aria-label="Close"
                    >
                      <span aria-hidden="true">&times;&nbsp;</span>
                    </button>
                  </div>
                </div>

              </div>

              <div className="modal-body">
                <form onSubmit={handleSubmit} className="contact-form">
                  <div className="row">
                    <div className="form-group col-md-6">
                      <label htmlFor="Firstname" className="label-names">
                        First Name
                      </label>
                      <input
                        type="text"
                        className="form-control formcolor"
                        id="FirstName"
                        name="first_name"
                        value={firstName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="form-group col-md-6">
                      <label htmlFor="Lastname" className="label-names">
                        Last Name
                      </label>
                      <input
                        type="text"
                        className="form-control formcolor "
                        id="Lastname"
                        name="last_name"
                        value={lastName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-6 mb-3">
                      {/* <div> */}
                      <label htmlFor="email" className="label-names">
                        Business Email Address
                      </label>
                      <input
                        type="email"
                        className="form-control"
                        id="InputEmail1"
                        name="email"
                        value={email}
                        onChange={handleInputChange}
                        aria-describedby="emailHelp"
                      />
                      {/* </div> */}
                      <div className="spacer"></div>
                      <div>
                        <label htmlFor="phoneNumber" className="label-names">
                          Phone Number
                        </label>
                        <input
                          type="text"
                          className="form-control formcolor"
                          id="phoneNumber"
                          name="phone"
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-6">
                      {/* <div> */}
                      <div className="form-group ">
                        <label htmlFor="requirement" className="label-names">
                          Tell Us Your Requirement
                        </label>
                        <textarea
                          className="form-control"
                          id="requirement"
                          rows="4"
                          name="requirement"
                          onChange={handleInputChange}
                          required
                        ></textarea>
                      </div>
                      {/* </div> */}
                    </div>
                  </div>
                  <div className="text-center">
                    <button
                      type="submit"
                      id="btnSubmit"
                      className="contact-popup"
                      style={{ outline: "none" }}
                    >
                      Submit
                    </button>
                  </div>
                </form>
              </div>
              <footer className="reach-usat">
                <p className="Reach-us"> Reach us at </p>
                <div className="d-flex flex-row justify-content-center">
                  <div className="mr-2">
                    <p className="connect-us">
                      <img src="./images/email.png" />
                      <a href="mailto:<EMAIL>">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                  <div>
                    <p className="connect-us">
                      <img src="./images/call.png" />
                      <a href="tel:+****************">+****************</a>
                    </p>
                  </div>
                </div>
              </footer>
            </div>
          </div>
        </div>
      }
      <Thank_you thankYouModalBtn={thankYouModalBtn} />
    </div>
  );
};

export default ContactUsPopup;
