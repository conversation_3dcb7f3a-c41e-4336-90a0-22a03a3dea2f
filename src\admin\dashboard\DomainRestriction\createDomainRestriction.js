import React, { useState, useEffect } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import AdminHeader from "../../layouts/AdminHeader.js";
import LeftSideNav from '../../layouts/LeftSideNav.js';
import {
    postWithToken,
    PostWithTokenNoCache,
} from "../../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { useNavigate, useParams } from "react-router-dom";

const CreateDomainRestriction = () => {

    const navigate = useNavigate();
    const [data, setData] = useState(null);
    const [allowedUserPerDomainMsg, setAllowedUserPerDomainMsg] = useState();
    const [allowedUserMsg, setAllowedUserMsg] = useState();
    const [validDomainMsg, setValidDomainMsg] = useState();

    const handleAllowedUserPerDomainInputChange = (e) => {
        const { id, value } = e.target;
        const pattern = /^\d+$/;
        if (pattern.test(value) && parseInt(value)) {
            setAllowedUserPerDomainMsg("");
        } else {
            setAllowedUserPerDomainMsg("Only Positive Integers");
        }
        setData((prevData) => ({
            ...prevData,
            [id]: value,
        }));
    }

    const handleDomainInputChange = (e) => {

        const { id, value } = e.target;

        if (value.trim()) {
            setValidDomainMsg("")
        } else {
            setValidDomainMsg("Domain could not be a empty")
        }
        setData((prevData) => ({
            ...prevData,
            [id]: value,
        }));
    }

    const handleUserCountInputChange = (e) => {

        const { id, value } = e.target;
        const pattern = /^\d+$/;

        if (pattern.test(value) && parseInt(value)) {
            setAllowedUserMsg("");
        } else {
            setAllowedUserMsg("Only Positive Integers");
        }
        setData((prevData) => ({
            ...prevData,
            [id]: value,
        }));
    };

    const submitHandler = async () => {
        if (!validDomainMsg && !allowedUserMsg && !allowedUserPerDomainMsg) {
            const res = await PostWithTokenNoCache(ApiName.createDomainRestriction, data);
            if (res && "status" in res) {
                if (res.status == 200) {
                    navigate("/admin/domain-restriction-list")
                }
            }
        }
    }

    return (
        <div className={S.app}>
            <AdminHeader />
            <div className={S.main_container}>
                <div className="d-flex flex-row">
                    <div style={{ width: "350px" }}>
                        <LeftSideNav />
                    </div>

                    <div className={S.formlayout}>
                        <form className="mb-3">
                            <h3 className={S.adminsignup}>Create Domain Restriction </h3>

                            <div className="row">
                                <div className="col-md-12">
                                    <div className="form-group">
                                        <label>Domain</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            id="domainName"
                                            onChange={handleDomainInputChange}
                                            required
                                        />
                                        {validDomainMsg ?
                                            (<p className="invalid">{validDomainMsg} </p>)
                                            : ("")}
                                    </div>
                                </div>

                                <div className="col-md-12">
                                    <div className="form-group">
                                        <label>Current Users Count</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            id="userCount"
                                            onChange={handleUserCountInputChange}
                                            required
                                        />
                                        {allowedUserMsg ?
                                            (<p className="invalid">{allowedUserMsg} </p>)
                                            : ("")}
                                    </div>
                                </div>

                                <div className="col-md-12">
                                    <div className="form-group">
                                        <label>Allowed User per Domain</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            id="allowedUserPerDomain"
                                            onChange={handleAllowedUserPerDomainInputChange}
                                            required
                                        />
                                        {allowedUserPerDomainMsg ?
                                            (<p className="invalid">{allowedUserPerDomainMsg} </p>)
                                            : ("")}
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div className="mb-3">
                            <button
                                type="submit"
                                className={`${S.adminsubmit} ${validDomainMsg !== "" || allowedUserMsg !== "" || allowedUserPerDomainMsg !== "" ? 'cust-disabled' : ''}`}
                                onClick={submitHandler}
                            >Submit</button>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    )


}

export default CreateDomainRestriction;