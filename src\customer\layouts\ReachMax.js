import React, { useEffect, useMemo, useRef, useState } from "react";
import "../assests/css/layouts/reachmax.css";
import Header from "./Header";
import UseTabStore from "../common-files/useGlobalState";
import { useNavigate } from "react-router-dom";
import { ApiName } from "../common-files/ApiNames";
import { PostWithTokenNoCache, postWithToken } from "../common-files/ApiCalls";
import loadingGif from '../assests/waiting.gif';
import Alert from "../common-files/alert.js";
import LeftNavbar from '../../customer/filters/LeftNavbar.js';
import Pagination from "../pagination/Pagination.js";
import Target_Audience from "../common-files/Target_Audience.js";
import ReferralError from "./ReferralError.js";

let PageSize = 7;
const ReachMax = () => {

    const [isAuth, setIsAuth] = useState(false);
    const {
        countryLoading,
        stateLoading,
        cityLoading,
        zipcodeLoading,
        selectedTab,
        defaultAlert,
        setDefaultAlert,
        setDefaultErrorMsg,
        defaultErrorMsg,
        setSocialMediaType,
        setCopyContent
    } = UseTabStore();
    const navigate = useNavigate();
    const [currentPage, setCurrentPage] = useState(1);
    const [viewAll, setViewAll] = useState(false);
    const [originalData, setOriginalData] = useState(null);
    const [foundRefList, setFoundRefList] = useState(0);
    const [fname, setFname] = useState(null);
    const [email, setEmail] = useState(null);
    const [show, setShow] = useState(false);
    const [showThis, setShowThis] = useState();
    const [referralCode, setReferralCode] = useState(null);
    const [referredList, setReferredList] = useState(null);
    const [totalReferred, setTotalReferred] = useState(0);
    const [convertedCount, setConvertedCount] = useState(0);
    const [creditEarned, setCreditEarned] = useState(0);
    // const [remainingCredit, setRemainingCredit] = useState(0);
    const [copiedCode, setCopiedCode] = useState(false);
    const [emailInput, setEmailInput] = useState('');
    const [checkValidEmail, setCheckValidEmail] = useState('');
    const [loading, setLoading] = useState(true);
    const [updateReferList, setUpdateReferList] = useState(false);
    const [emailSent, setEmailSent] = useState(false);
    const [totalBalanceContactView, setTotalBalanceContactView] = useState(0);
    const [totalBalanceCredit, setTotalBalanceCredit] = useState(0);
    const [totalAssignedCredit, setTotalAssignedCredit] = useState(0);
    const [redeem, setRedeem] = useState(false);
    const [redeemMessage, setRedeemMessage] = useState('');
    const [emailErr, setEmailErr] = useState('');
    const [referralErr, setReferralErr] = useState(null);
    // Get the current domain name
    const currentDomain = window.location.origin;

    const dropdownRef = useRef(null);
    const yourEmailDescrip = useRef(null);
    const referralAlertRef = useRef(null);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (yourEmailDescrip.current && !yourEmailDescrip.current.contains(event.target)) {
                setShow(false);
            }
        };
        document.addEventListener('click', handleClickOutside);
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    }, [yourEmailDescrip]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowThis(false);
            }
        };
        document.addEventListener('click', handleClickOutside);
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    }, [dropdownRef]);

    useMemo(() => {
        const user = JSON.parse(localStorage.getItem("user"));
        if (!user) {
            window.location.href = '/';
        } else {
            setIsAuth(true);
            if (user) {
                if ("firstName" in user) setFname(user.firstName);
                if ("email" in user) setEmail(user.email);
            }
        }
    }, [])

    useEffect(() => {
        // Add event listeners when the component mounts
        document.addEventListener('keydown', handleEscKeyPress);

        // Remove event listeners when the component unmounts
        return () => {
            document.removeEventListener('keydown', handleEscKeyPress);
        };
    }, []);

    const handleEscKeyPress = (e) => {
        // Check if the pressed key is ESC
        if (e.key === 'Escape') {
            setShowThis(false);
            setShow(false);
        }
    };

    // go back to prevoius page
    const goBack = () => {
        if (selectedTab == "contact") {
            navigate("/dashboard");
        } else {
            navigate("/company-filters");
        }
    }

    // fetch rerefarral list and callback when pagination changed
    useEffect(() => {
        getRefList();
    }, [currentPage])

    // fetch referral code and available credits
    useEffect(() => {
        getRefCode();
    }, [])

    // fetch referral code
    const getRefCode = async () => {
        try {
            let params = JSON.stringify({
                "method": "POST"
            });
            const res = await postWithToken(ApiName.getReferralCode, params);
            if (res && "status" in res) {
                if (res.status == 200) {
                    let data = res.data.data ? JSON.parse(res.data.data) : null;
                    setReferralCode(JSON.parse(data));
                    getCreditDetails(JSON.parse(data));
                }
            } else {
                const res = await postWithToken(ApiName.createReferralCode, params);
                if (res && "status" in res) {
                    if (res.status == 200) {
                        let data = res.data.data ? JSON.parse(res.data.data) : null;
                        setReferralCode(data);
                        getCreditDetails(data);
                    }
                }
            }
        } catch (error) {
            setDefaultErrorMsg("Sorry.! " + error.response.data.message + ". Please try again");
            setLoading(false);
            setDefaultAlert(true);
        }
    };

    function formatDate(inputDate) {
        const options = { year: 'numeric', month: 'short', day: 'numeric' };
        const formattedDate = new Date(inputDate).toLocaleDateString(undefined, options);
        return formattedDate;
    }

    // fetch referral list
    const getRefList = async () => {
        try {
            let params = JSON.stringify({
                "method": "POST"
            });
            const res = await PostWithTokenNoCache(ApiName.getReferredList, params);
            if (res && "status" in res) {
                if (res.status == 200) {
                    let converted_count = 0;
                    let data = res.data.data ? JSON.parse(res.data.data) : null;

                    setOriginalData(data);
                    const firstPageIndex = (currentPage - 1) * PageSize;
                    const lastPageIndex = firstPageIndex + PageSize;

                    let paginationData = data.slice(firstPageIndex, lastPageIndex);
                    let creditPoint = 0;
                    setFoundRefList(data.length);
                    setReferredList(paginationData);
                    data.map((v, i) => {
                        setTotalReferred(i + 1);
                        if (v.credits_earned > 1 && v.subscription_status === 'active') {
                            converted_count = converted_count + 1;
                            setConvertedCount(converted_count);
                            creditPoint = parseInt(v.credits_earned) + creditPoint;
                        }
                    })
                    // setCreditEarned(creditPoint);
                    setUpdateReferList(false);
                    setLoading(false);
                }
            }
        } catch (error) {
            setDefaultErrorMsg("Sorry.! " + error.response.data.message + ". Please try again");
            setLoading(false);
            setDefaultAlert(true);
        }
    };

    // fetch available credits
    const getCreditDetails = async (code) => {
        try {
            let params = JSON.stringify({
                "referral_code": code
            });
            const res = await PostWithTokenNoCache(ApiName.findReferralPoints, params);
            if (res && "status" in res) {

                if (res.status == 200) {
                    let data = res.data.data ? JSON.parse(res.data.data) : null;
                    setCreditEarned(JSON.parse(data.credits_earned));
                    let creditsEarned = JSON.parse(data.credits_earned);
                    // setRemainingCredit(JSON.parse(data.remaining_credits));
                    if (JSON.parse(data.remaining_credits) > 0) {

                        const params = { "user_email": email };
                        const res = await PostWithTokenNoCache(ApiName.activeCreadits, params);

                        if (res?.status === 200) {
                            const dataObj = JSON.parse(res.data.data);
                            const {
                                additional_credit_assigned,
                                total_balance_contact_view,
                                total_balance_credit,
                            } = dataObj;

                            total_balance_contact_view && setTotalBalanceContactView(total_balance_contact_view);
                            total_balance_credit && setTotalBalanceCredit(total_balance_credit);
                            additional_credit_assigned && setTotalAssignedCredit(additional_credit_assigned);
                            redeemNow(code);
                        }
                    }
                    setLoading(false);
                }
            }
        } catch (error) {
            setDefaultErrorMsg("Sorry.! " + error.response.data.message + ". Please try again");
            setLoading(false);
            setDefaultAlert(true);
        }
    };

    const copyReferralCode = () => {
        if (textAreaRef.current) {
            // Select the text in the input element
            textAreaRef.current.select();
            document.execCommand('copy');
        }
        setCopiedCode(true);
        setTimeout(() => {
            setCopiedCode(false);
        }, 3000)
    }

    const handleEmailInputChange = (e) => {
        // setCheckValidEmail(true);
        setEmailInput(e.target.value);
        let emailInput = e.target.value;
        // Split the emailInput by comma and trim each email
        const emails = emailInput.split(',').map(email => email.trim());

        // Validate emails (you can replace this with your own validation logic)
        const isValidEmails = emails.every(email => isValidEmail(email));

        if (!isValidEmails) {
            setEmailErr("Please enter valid business email address(es)");
            setCheckValidEmail(false);
        }
        const isValidBusinessEmails = emails.every(email => isValidBusinessEmail(email));
        if (!isValidBusinessEmails) {
            setEmailErr("Please enter valid business email address(es)");
            setCheckValidEmail(false);
        }
        if (isValidEmails && isValidBusinessEmails) {
            setCheckValidEmail(true);
        }
    };

    // Create a ref to access the button
    const buttonRef = useRef(null);
    const textAreaRef = useRef(null);

    // Function to handle image click
    const handleImageClick = (socialMediaType) => {
        setSocialMediaType(socialMediaType)
        setCopyContent(false);
        // Check if the button ref exists and trigger the click event
        if (buttonRef.current) {
            buttonRef.current.click();
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Split the emailInput by comma and trim each email
        const emails = emailInput.split(',').map(email => email.trim());

        // Validate emails (you can replace this with your own validation logic)
        const isValidEmails = emails.every(email => isValidEmail(email));
        const isValidBusinessEmails = emails.every(email => isValidBusinessEmail(email));

        if (!isValidEmails || !isValidBusinessEmails) {
            setEmailErr("Please enter valid business email address(es)");
            setCheckValidEmail(false);
            return;
        }

        setUpdateReferList(true);
        setCheckValidEmail(true);

        let hasError = false;
        setLoading(true);
        const sendEmail = async (emails) => {
            const params = {
                referral_code: referralCode,
                recipient_email_reference: emails,
                recipient_name_reference: "",
                email_send_status: "1",
                subscription_status: "",
                subscription_package: "",
                credits_earned: "",
                redeem_status: "",
                super_saver_url: `${currentDomain}/sign-up/glacier?referralcode=${referralCode}`,
                freemium_url: `${currentDomain}/sign-up/ice-breaker-plan?referralcode=${referralCode}`,
                username: fname,
            };

            try {
                const res = await PostWithTokenNoCache(ApiName.createReferral, params);
                if (res && res.status === 200) {
                    setLoading(false);
                    setReferredList(null);
                    getRefList();
                    setEmailSent(true);
                    setTimeout(() => {
                        setEmailSent(false);
                    }, 5000);
                    setEmailInput('');
                }
            } catch (error) {
                setUpdateReferList(false);
                hasError = true; // Set the error flag
                if (error.response && error.response.status === 400) {
                    let msg = error.response?.data?.data;
                    setReferralErr(JSON.parse(msg));
                } else {
                    console.log(error.response?.data?.data);
                }
                setLoading(false);
            }
        };

        // Send emails in parallel and handle results
        sendEmail(emails)

        // if (!hasError) {
        //     setLoading(false);
        //     setReferredList(null);
        //     getRefList();
        //     setEmailSent(true);
        //     setTimeout(() => {
        //         setEmailSent(false);
        //     }, 5000);
        //     setEmailInput('');
        // }
    };

    const isValidEmail = (email) => {
        // Replace this with your email validation logic
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const isValidBusinessEmail = (email) => {
        // Check if the email is valid
        const regex = new RegExp(
            "^.+@((?!hotmail)(?!gmail)(?!ymail)(?!googlemail)(?!live)(?!gmx)(?!yahoo)(?!outlook)(?!msn)(?!icloud)(?!facebook)(?!aol)(?!zoho)(?!mail)(?!yandex)(?!hushmail)(?!lycox)(?!lycosmail)(?!inbox)(?!myway)(?!aim)(?!fastmail)(?!goowy)(?!juno)(?!shortmail)(?!atmail)(?!protonmail)(?!postmaster)(?!abuse)(?!admin)(?!all)(?!everyone)(?!ftp)(?!googlegroups.com)(?!investorrelations)(?!jobs)(?!marketing)(?!media)(?!noc)(?!prime)(?!privacy)(?!remove)(?!request)(?!root)(?!sales)(?!security)(?!spam)(?!subscribe)(?!usenet)(?!users)(?!uucp)(?!webmaster)(?!www)(?!info)(?!enquiries)(?!mail)(?!office)(?!head)(?!headteacher)(?!reception)(?!enquiry)(?!post)(?!email)(?!accounts)(?!london)(?!general)(?!enquires)(?!design)(?!support)(?!mailbox)(?!law)(?!service)(?!reservations)(?!information)(?!schooladmin)(?!secretary)(?!enq)(?!advice)(?!studio)(?!bristol)(?!headoffice)(?!bookings)(?!help)(?!main.department).)+..+$"
        );
        let result = regex.test(email.toLowerCase());

        if (result) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            let checkDomain = emailRegex.test(email);

            if (!checkDomain) {
                setEmailErr("Please enter valid business email address(es)");
                setCheckValidEmail(false);
                return false;
            } else {
                setCheckValidEmail(false);
                setEmailErr("");
                return true;
            }
        } else {
            setCheckValidEmail(false);
            setEmailErr("Please enter valid business email address(es)");
            return false;
        }
    };

    const redeemNow = async (code) => {
        try {
            const params = {
                referral_code: code,
            };
            const res = await PostWithTokenNoCache(ApiName.redeemReferralPoints, params);
            if (res && 'status' in res && res.status === 200) {
            } else {
                // Handle error response
                console.log('Error:', res.error); // Access the error message
            }
        } catch (error) {
            // Handle any other errors
            console.log('Error:', error);
        }

    }
    return (
        <>
            {isAuth ? (
                <div>
                    <Header />
                    <div style={{ display: "none" }}><LeftNavbar></LeftNavbar> </div>
                    {!loading && !defaultAlert ? (
                        <>
                            {/* Button with data-toggle and data-target attributes */}
                            <button
                                type="button"
                                className="btn btn-info btn-md"
                                data-toggle="modal"
                                data-target="#targetAudience"
                                ref={buttonRef}
                                style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
                                data-backdrop="true"
                            ></button>
                            <div className="refer-spacing">
                                <div className="d-flex flex-row justify-content-between mb-3">
                                    <div>
                                        <p className="ReachMax"><img src="./images/arrow-left.png" className="goBack" onClick={goBack} />Refer & Earn</p>
                                    </div>
                                    <div>
                                    </div>
                                </div>

                                {/* new */}

                                <div className="row">
                                    <div className="col-md-6 p-0">
                                        <div className="row">
                                            <div className="col-md-9 m-auto">
                                                <p className="copyandsharethelink">Copy & Share Link</p>

                                                <div className="input-group mt-2 " style={{ padding: "0px 0 4px 0", margin: "auto", width: "414px" }}>
                                                    <input className="form-control apis2" ref={textAreaRef} value={`${currentDomain}/sign-up/ice-breaker-plan?referralcode=${referralCode}`} />
                                                    <span className="input-group-append bg-transparent border-left-0" onClick={copyReferralCode}>
                                                        <span className="input-group-text bg-white copy-button">
                                                            <span className="grey-back">
                                                                <img src="./images/copy-icon.png" />&nbsp;&nbsp;&nbsp;&nbsp;Copy
                                                            </span>
                                                        </span>
                                                    </span>


                                                </div>


                                                {copiedCode ? (
                                                    <div className="">
                                                        <span className="successmessage2"><img src="./images/promocode-success.png" width="28" />Copied Successfully</span>

                                                    </div>

                                                )
                                                    : null}



                                            </div>
                                        </div>
                                        <div className="share-your-link-share">

                                            <p>Share Your Link on Social Media</p>
                                            {/* <hr className="horizontal3" /> */}
                                        </div>

                                        <div className="d-flex flex-row justify-content-center">
                                            <div className="checksocialmedia">
                                                <img
                                                    src="./images/Rs_facebook.png"
                                                    width="40px"
                                                    onClick={() => handleImageClick("facebook")}
                                                    style={{ cursor: 'pointer' }} />
                                                <p className="postvia">Post via Facebook</p>
                                            </div>
                                            <div className="checksocialmediaa">
                                                <img
                                                    src="./images/Rs_xstream.png"
                                                    width="40px"
                                                    onClick={() => handleImageClick("twitter")}
                                                    style={{ cursor: 'pointer',margin:"0px 5px 0 5px" }} />
                                                <p className="postvia"> Post via X</p>
                                            </div>
                                            <div className="checksocialmedia">
                                                <img
                                                    src="./images/Rs_linkedin.png"
                                                    width="40px"
                                                    onClick={() => handleImageClick("linkedIn")}
                                                    style={{ cursor: 'pointer' }} />
                                                <p className="postvia">Post via Linkedin</p>
                                            </div>
                                            <div className="checksocialmedia">
                                                <img
                                                    src="./images/Rs_pinterest.png"
                                                    width="40px"
                                                    onClick={() => handleImageClick("pintrest")}
                                                    style={{ cursor: 'pointer' }} />
                                                <p className="postvia">Post via Pinterest</p>
                                            </div>
                                        </div>

                                        <div className="share-your-link-2">
                                            <p>Refer to Your Friends through Email</p>

                                        </div>

                                        <div className="fisrt-layer-1">
                                            <div className="">

                                                <form onSubmit={handleSubmit}>
                                                    <div className="row">
                                                        <div className="col-md-9 mt-2">
                                                            <div className="form-group mt-2" style={{ marginBottom: "6px" }}>
                                                                <input
                                                                    type="text"
                                                                    className={!checkValidEmail && checkValidEmail !== '' ? "form-control apis" : "form-control apis2"}
                                                                    placeholder="Enter business email(s)"
                                                                    value={emailInput}
                                                                    onChange={handleEmailInputChange}
                                                                    style={{ color: "#000" }}
                                                                />
                                                            </div>
                                                            {!checkValidEmail && checkValidEmail !== '' ? (<p className="invalid">{emailErr}</p>) : (<></>)}

                                                        </div>
                                                        <div className="col-md-2 mt-3 p-0">
                                                            <button className={!checkValidEmail && checkValidEmail !== '' ? "send-to-account cust-disabled" : "send-to-account"} type="submit" data-toggle="modal" data-target=".bd-example-modal-sm">Send</button>
                                                        </div>
                                                    </div>
                                                </form>
                                                <div className="row">
                                                    <div className="col-sm-6">

                                                        {emailSent ? (
                                                            <div className="refralemail">
                                                                <p><img src="./images/promocode-success.png" width="26" className="mt-1" />Referral email has been sent successfully.</p>
                                                            </div>
                                                        ) : (
                                                            <p className="Separate">Separate multiple emails with a comma.</p>
                                                        )}
                                                    </div>
                                                    <div className="col-sm-6" ref={yourEmailDescrip}>
                                                        <p className="include" onClick={() => setShow(!show)}>
                                                            What will we include in your email?
                                                        </p>
                                                    </div>
                                                </div>

                                                {show && <div className="included">
                                                    <p className="included-data">We’ll include this message in your email body:</p>
                                                    <p className="letter">
                                                        Subject: Have you heard about ReachStream yet?</p>
                                                    <p className="letter">
                                                        Hi, </p>
                                                    <p className="letter">Are you looking for new business leads or rolling out a new campaign?</p>
                                                    <p className="letter2">Create a list of 5000 new leads in only 3 steps. With ReachStream, you get:</p>
                                                    <ul className="insightsunits">
                                                        <li className="bowl">
                                                            <p className="insights">Emails, phone numbers, and 20+ contact & company insights</p>
                                                        </li>
                                                        <li className="bowl">
                                                            <p className="insights">Opt-in contact information</p>

                                                        </li>
                                                        <li className="bowl">
                                                            <p className="insights">Unlimited views & free contact updates</p>
                                                        </li>
                                                        <li className="bowl">
                                                            <p className="insights">Advanced API access on all plans</p>
                                                        </li>
                                                    </ul>
                                                    <p className="letter">Sign up with this link – <span style={{ color: "#55C2C3", textDecoration: "none" }}>{`${currentDomain}/sign-up/glacier?referralcode=${referralCode}`}</span></p>
                                                    <p className="letter">Or just try it for free – <span style={{ color: "#55C2C3", textDecoration: "none" }}>{`${currentDomain}/sign-up/ice-breaker-plan?referralcode=${referralCode}`}</span> (No credit card details needed)</p>

                                                    <p className="letter">
                                                        Best regards, <br />{fname}
                                                    </p>
                                                </div>}

                                            </div>
                                        </div>

                                        <div className="second-layer-2">
                                            <div className="share-your-link">
                                                <p>How Refer & Earn Works</p>

                                            </div>
                                            <div className="para">
                                                <p>Earn 2000 download credits for every successful Glacier
                                                    referral and 200 download credits for every successful Icebreaker referral.</p>

                                            </div>

                                            <div className="learnmore" ref={dropdownRef}>
                                                <p onClick={() => setShowThis(!showThis)}>Learn more</p>
                                            </div>

                                            {showThis && <div className="included2 ">
                                                <p className="included-data">How to Refer & Earn Credits</p>

                                                <ul className="participate">
                                                    <li className="bowl">To participate, you must be a ReachStream Icebreaker or Glacier user.</li>
                                                    <li className="bowl">Simply copy and share your referral link on social media platforms or via email.</li>
                                                    <li className="bowl">For every successful Icebreaker referral, you get 200 free download credits.</li>
                                                    <li className="bowl">For every successful Glacier referral, you get 2000 free download credits.</li>
                                                    <li className="bowl">Earned credits have unlimited lifetime (does not expire if you fail to renew your plan).</li>
                                                    <li className="bowl">Track your total referrals, referrals converted, and corresponding credits earned in Your Earnings.</li>
                                                </ul>
                                            </div>}
                                        </div>
                                    </div>
                                    <div className="col-md-6 p-0">
                                        <div className="thired-layer-3">
                                            <div className="d-flex flex-row">
                                                <div className="mr-4">
                                                    <div className="share-your-link">
                                                        <p>Your Earnings</p>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div className="nofcredss">
                                                        <p className="credsno">{creditEarned}</p>
                                                        <p className="credits">Credits</p>
                                                    </div>
                                                </div>
                                                {redeem ? (
                                                    <p className="Updnginprs">
                                                        {redeemMessage}
                                                    </p>
                                                ) : (
                                                    <></>
                                                )}
                                            </div>
                                        </div>
                                        {!updateReferList ? (
                                            <div className="fourth-layer-4">
                                                <div className="d-flex flex-row justify-content-between">
                                                    <div >
                                                        <div className="YourReferrals">
                                                            <p>Your Referrals</p>
                                                        </div>
                                                        <Target_Audience
                                                            data={[
                                                                `${currentDomain}/sign-up/glacier?referralcode=${referralCode}`,
                                                                `${currentDomain}/sign-up/ice-breaker-plan?referralcode=${referralCode}`,
                                                                `${currentDomain}/target-audience/referralcode=${referralCode}`,
                                                            ]}
                                                        />

                                                    </div>
                                                    <div >
                                                        <div className="d-flex flex-row mr-4 mt-3">
                                                            <div className="Referred mr-2">
                                                                <p>Total Referred <span className="accesible">{totalReferred}</span></p>
                                                            </div>
                                                            <div className="Referred">
                                                                <p>Total Converted <span className="accesible">{convertedCount}</span></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <table className="your-refrel-list">
                                                    <tbody>
                                                        <tr className="table-headers mt-2">
                                                            <th>Name</th>
                                                            <th>Email</th>
                                                            <th style={{ textAlign: "center" }}>Subscription</th>
                                                            <th style={{ textAlign: "center" }}>Date</th>
                                                            <th style={{ textAlign: "center" }}>Credits Earned</th>
                                                        </tr>
                                                        {referredList && referredList.map((data, i) => (
                                                            <tr className="table-data-small" key={i}>
                                                                <td className="data-goes-here">{data.recipient_name_reference && data.recipient_name_reference !== "null" ? data.recipient_name_reference : null}</td>
                                                                <td className="data-goes-here">{data.recipient_email_reference}</td>
                                                                <td style={{ textAlign: "center", fontSize: "13px" }} className="">{data.subscription_package && data.subscription_package !== "null" ? data.subscription_package : null}</td>
                                                                <td style={{ textAlign: "center", fontSize: "13px" }} className="">{formatDate(data.createdAt)}</td>
                                                                <td style={{ textAlign: "center", fontSize: "13px" }} className="">{data.credits_earned && data.credits_earned !== "null" && data.subscription_status == "active" ? data.credits_earned : 0}</td>
                                                            </tr>
                                                        ))}


                                                    </tbody>
                                                </table>
                                                <div className="d-flex flex-row justify-content-end mb-4">
                                                    <div>
                                                        <Pagination
                                                            className="pagination-bar"
                                                            currentPage={currentPage}
                                                            totalCount={
                                                                foundRefList / PageSize
                                                            }
                                                            pageSize={!viewAll ? 1 : originalData.length}
                                                            onPageChange={(page) => {
                                                                setCurrentPage(page);
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        ) : (
                                            <>
                                                <div className="d-flex flex-row justify-content-center mt-5" style={{ display: loading }}>
                                                    <img src={loadingGif} alt="Loading" className="loader" width="400" />
                                                </div>
                                            </>
                                        )}

                                    </div>
                                </div>


                            </div>
                        </>
                    ) : !loading && defaultAlert ? (
                        <Alert data={defaultErrorMsg} />
                    ) : !countryLoading && !stateLoading && !cityLoading && !zipcodeLoading ? (
                        <>
                            <div className="d-flex flex-row justify-content-center mt-5" style={{ display: loading }}>
                                <img src={loadingGif} alt="Loading" className="loader" width="400" />
                            </div>
                        </>
                    ) : (<>
                        <div className="d-flex flex-row justify-content-center mt-5" style={{ display: loading }}>
                            <img src={loadingGif} alt="Loading" className="loader" width="400" />
                        </div>
                    </>)
                    }
                    {referralErr ? (
                        <ReferralError data={referralErr} setReferralErr={setReferralErr} />
                    ) : (<></>)}
                </div>

                
            ) : <></>}
        </>
    )
}

export default ReachMax;