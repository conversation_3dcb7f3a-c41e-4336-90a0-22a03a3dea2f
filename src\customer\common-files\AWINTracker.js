import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const AWINTracker = ({ orderData }) => {
  const location = useLocation();

  // Cookie helper function
  function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
  }

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const hasAwinSource = searchParams.get('utm_source') === 'awin';

    const awinCookie = getCookie('_aw_sn_116059');
    const awinJCookie = getCookie('_aw_j_116059');

    const hasAwinCookies = !!(awinCookie || awinJCookie);

    // Only load if on payment-successful page and either awin source or awin cookies exist
    if (location.pathname.includes('/payment-successful') && (hasAwinSource || hasAwinCookies)) {
      loadAWINScripts(orderData);
    }

    return () => {
      const script = document.querySelector('script[src="https://www.awin1.com/awtracking.js"]');
      if (script && script.parentNode) {
        script.parentNode.removeChild(script);
      }

      const img = document.querySelector('img[src*="awin1.com/sread.img"]');
      if (img && img.parentNode) {
        img.parentNode.removeChild(img);
      }

      if (window.AWIN) {
        delete window.AWIN;
      }
    };
  }, [location.search, location.pathname, orderData]);

  const loadAWINScripts = (data) => {
    if (document.querySelector('#awin-header-script') || 
        document.querySelector('#awin-conversion-pixel')) {
      return;
    }

    const headerScript = document.createElement('script');
    headerScript.src = 'https://www.awin1.com/awtracking.js';
    headerScript.type = 'text/javascript';
    headerScript.id = 'awin-header-script';
    document.head.appendChild(headerScript);

    const img = document.createElement('img');
    img.src = `https://www.awin1.com/sread.img?tt=ns&tv=2&merchant=116059&amount=${data?.order_subtotal || ''}&cr=${data?.currency_code || ''}&ref=${data?.order_ref || ''}&parts=${data?.commission_group || ''}:${data?.sale_amount || ''}&vc=${data?.voucher_code || ''}&ch=aw&customeracquisition=${data?.customer_acquisition || ''}`;
    img.border = "0";
    img.width = "0";
    img.height = "0";
    img.style.display = "none";
    img.id = 'awin-conversion-pixel';

    const conversionScript = document.createElement('script');
    conversionScript.type = 'text/javascript';
    conversionScript.id = 'awin-conversion-script';
    conversionScript.innerHTML = `
      //<![CDATA[ /*** Do not change ***/
      var AWIN = AWIN || {};
      AWIN.Tracking = AWIN.Tracking || {};
      AWIN.Tracking.Sale = {};
      /*** Set your transaction parameters ***/
      AWIN.Tracking.Sale.amount = "${data?.order_subtotal || ''}";
      AWIN.Tracking.Sale.orderRef = "${data?.order_ref || ''}";
      AWIN.Tracking.Sale.parts = "${data?.commission_group || ''}:${data?.sale_amount || ''}";
      AWIN.Tracking.Sale.voucher = "${data?.voucher_code || ''}";
      AWIN.Tracking.Sale.currency = "${data?.currency_code || ''}";
      AWIN.Tracking.Sale.channel = "aw";
      AWIN.Tracking.Sale.customerAcquisition = "${data?.customer_acquisition || ''}";
      //]]>
    `;

    document.body.appendChild(img);
    document.body.appendChild(conversionScript);
  };

  return null;
};

export default AWINTracker;
