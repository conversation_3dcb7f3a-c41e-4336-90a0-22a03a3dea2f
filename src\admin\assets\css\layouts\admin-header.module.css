.app {
  display: flex;
  flex-direction: column;
}


.header {
  padding: 0px 0 11px 26px;
  background-color: #e8f7f7;
  /* display: flex; */
  justify-content: space-between;
  align-items: center;
}

img.logseye {
  cursor: pointer;
}



.header_image {
  max-width: 100%;
  height: auto;
}

/* .main_container {
  display: flex;
  flex: 1;
} */

.left_navbar {
  /* width: 24%; */
  padding: 0px 1px 0 1px;
  height: 100vh;
  background-color: #f9f9f9;
}

button.activate {
  margin: 0 8px 0 0px;
  padding: 0 22px 0 22px;
  border: 1px solid green;
  background-color: #a1ffa1;
  border-radius: 5px;
  font-size: 12px;
}

.left_navbar button {
  width: 100%;
  border: 0;
  height: 44px;
  text-align: inherit;
  padding: 10px 0 5px 21px;
  background-color: #f9f9f9;
  cursor: pointer;
  outline: none;
  border-radius: 3px;
  color: #093d54;
  font-family: "Lato", Regular;
  /* border: 1px solid; */
  font-weight: 600;
  border-bottom: 1px solid rgb(226, 226, 226);

}

.content {
  flex: 1;
  padding: 0px;
}

.right_sidebar {
  width: 100%;
  padding: 0px 0 0 7px;
}

.admin_name_right {
  text-align: right;

}

.user_info {
  display: flex;
  align-items: center;
}

.user_image {
  margin-right: 10px;
}


button.user-alt p {
  font-size: 14px;
  padding: 3px 0 0 5rem;
  margin: 0;
}

button.sign-out-buttonn {
  background-color: #fff;
  font-size: 14px;
  color: #093d54;
  font-weight: 600;
  outline: none;
  border: 0;
  box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
  padding: 4px 21px 4px 21px;
  border-radius: 13px;
  cursor: pointer;
}

button.user-altt {
  background-color: white;
  border: none;
  color: #000000;
  padding: 0px 6px 3px 18px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 11px;
  outline: none;
  margin-top: 0px;
  margin-bottom: 6px;
}


.paginationContailner {
  bottom: 72px;
  height: 70px;
  left: 87%;
  padding: 0;
  position: relative;
  right: 0;
  width: 200px;
  top: 0px
}

.paginationContainer button {
  margin-left: 5px;
}



/* button.sign_out_2 {
  background-color: aqua;
  border: 0;
  font-size: 14px;
  border-radius: 11px;
  color: #fff;
} */


p.user_name {
  text-align: end;
  padding: 0px 5px 0px 1rem;
  background: #fff;
  margin: 0;
  border-radius: 5px;
  margin-right: 10px;
  margin-top: 18px;

}


button.next {
  background-color: #4CAF50;
  color: #fff;
  margin: 0 0 0 5px;
  border: 0;
  padding: 0 15px 0 15px;
  font-size: 15px;
  border-radius: 5px;
  cursor: pointer;
}

button.previous {
  border: 0.1 solid grey;
  font-size: 14px;
  box-shadow: 0 0 3px 1px #999999;
  border: 0;
  border-radius: 3px;
  cursor: pointer;
}


button.sign_out_2 {
  margin-top: 5px;
  border: 0;
  background-color: #093d54;
  padding: 1px 12px 2px 13px;
  font-size: 14px;
  margin: 18px 10px 0 6px;
  color: #fff;
  border-radius: 8px;
  cursor: pointer;
  outline: none;
}


/* create-promocode-form */

.formlayout {
  width: 500px;
  background-color: #fff;
  margin: auto;
  display: block;
  justify-content: inherit;
  padding: 20px 30px 30px 30px;
  border: 1px solid #dddddd;
  border-radius: 5px;
  margin-bottom: 35px;
  box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
}

.formlayout2 {
  width: 450px;
  background-color: #fff;
  margin: auto;
  display: block;
  justify-content: inherit;
  padding: 20px 30px 30px 30px;
  border: 1px solid #dddddd;
  border-radius: 5px;
  margin-bottom: 35px;
  box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
}


.headname {
  text-align: center;
  padding: 0 0 12px 0;
  color: #093d54;
}


button.clickandsave {
  background-color: #093d54;
  color: #fff;
  border: 0;
  padding: 5px 20px 5px 20px;
  border-radius: 6px;
}

.activedrop {
  width: max-content;
}

button.swichtuser {
  border: 1px solid #093D54;
  padding: 0 15px 0 15px;
  border-radius: 4px;
  font-size: 14px;
  background-color: #F5F5F5;
  color: #000000;
  cursor: pointer;
}

button.btndanger {
  padding: 0 15px 0 15px;
  margin: 0 7px 0 0px;
  font-size: 12px;
  background-color: #F2C3B5;
  border: 1px solid #DE350C;
  border-radius: 3px;
  cursor: pointer;
}

/* 04-08-2023 */

p.pleaseenter {
  color: red;
  text-align: center;
}

p.successfullyy {
  color: #4CAF50;
  text-align: center;
}


.modalheaderswitch {
  text-align: center;
  padding: 0px 0 0 0;
}

h5#exampleModalLabel {
  font-size: 33px;
  padding: 2rem 0 0 0;
}

.error_message {
  color: red;
}

.modaldialogswitch {
  width: 650px;
  margin: auto;
  margin-top: 2rem;
}


button.paiduser {
  border: 0;
  padding: 7px 22px 7px 22px;
  color: #fff;
  background-color: #0D2F6F;
  border-radius: 4px;
  cursor: pointer;
}


.paidmodaldialog {
  width: 400px;
  margin: auto;
  margin-top: 2rem;
}

.paidmodalfooter {
  padding: 1rem 0px 2rem 0;
}

button.Yes {
  padding: 5px 20px 5px 20px;
  margin: 0 5px 0 0px;
  background-color: #00A0AC !important;
  border: 0;
  color: #fff;
  cursor: pointer;
}


button.No {
  background-color: #ffffff;
  color: #000;
  padding: 4px 20px 4px 20px;
  /* box-shadow: rgb(0 0 0 / 16%) 0px 1px 4px, rgb(255 111 111) 0px 0px 0px 3px; */
  cursor: pointer;
  border: 1px solid red;
}


.paidmodaltitle {
  font-size: 27px;
  color: #0D2F6F;
  padding: 15px 0 0 0;
}


.switcheader {
  font-size: 30px;
  color: #0D2F6F;
  padding: 2rem 0 0 0px;
}


h5.modaltitleswitch {
  font-size: 30px;
  margin-top: 2rem;
  text-align: center;
  color: #0D2F6F;
}

button.paiduser {
  background-color: #0D2F6F;
  border: 0;
  color: #fff;
  padding: 5px 15px 5px 15px;
  border-radius: 5px;
}


.tableheaderspace {
  padding: 12px 0px 12px 9px;
  font-size: 12px;
}

.tableheaderspacer {
  padding: 12px 0px 12px 9px;
  font-size: 13px;
  text-align: left;
}

.tableheaderspaces {
  padding: 12px 0px 12px 9px;
  font-size: 13px;
  text-align: left;
}

.tabledataspaceing {
  padding: 10px 0px 10px 9px;
  text-align: center;
  font-size: 14px;
}


.tabledataspacing {
  padding: 12px 0px 12px 9px;
  text-align: left;
  font-size: 14px;
  text-align: center;
}


button.Allocate {
  border: 1px solid #55C2C3;
  padding: 0 10px 0 10px;
  border-radius: 5px;
  font-size: 12px;
  color: #55C2C3;
  background-color: #fff;
  outline: none;
  cursor: pointer;
}

.tabledataspaceingg {
  padding: 8px 0px 8px 9px;
  text-align: left;
  font-size: 14px;
}

.table_responsive {
  display: block;
  width: 100%;
  overflow-x: clip;
  margin-bottom: 3rem;
}


h5.totla_users {
  font-size: 14px;
  margin: 12px 6px 0 0;
  color: #7E8C9C;
}

p.datalegnth {
  font-size: 14px;
  color: #000;
  margin: 10px 0 0 0;
}

input.searchbyemail {
  border: 1px solid #7E8C9C;
  border-radius: 3px;
  outline: none;
  float: right;
  margin: 10px 0px 3px 0px;
}

input.searchbyemail::placeholder {
  font-size: 14px;
  padding: 0 0 0 5px;
  margin: 0;
  color: #7E8C9C;
}

.tablelight {
  box-shadow: inset 0px 0px 9px #afafaf38, 0px 0px 0px 0 #0c20350d;
}


input.enteroptionnumber {
  width: 80px;
  padding: 4px 0 2px 0px;
  margin: 0 4px 0 4px;
  border: 1px solid #c7c7c7;
  border-radius: 5px;
}

.enteroptionnumber::-ms-value {
  font-size: 14px;
  color: red;
  text-align: center;
}

.tablelight {
  box-shadow: inset 0px 0px 9px #afafaf38, 0px 0px 0px 0 #0c20350d;
  background-color: #fff;
  border-radius: 5px;
}

.tablelight,
.tablelight>td,
.tablelight>th {
  background-color: transparent;
  /* box-shadow: inset 0px 0px 9px #afafaf38, 0px 0px 0px 0 #0c20350d; */
}

th.packagename {
  font-size: 14px;
  padding: 12px 8px 12px 8px;
}

td.centeralign {
  text-align: center;
  font-size: 14px;
  margin: 0;
  padding: 0;
}

select.dropyes {
  outline: none;
  border: 1px solid #c7c7c7;
  padding: 4px 6px 4px 6px;
  border-radius: 5px;
}

/* Prompt css */

.inapppromt h5 {
  padding: 20px 0 0 20px;
  color: #000;
  font-weight: 600;
}

.grayback {
  background-color: #0000000D;
  margin: 0 20px 0 20px;
  border-radius: 4px;
}

button.promptbutton {
  outline: none;
  border: 1px solid #7E8C9C;
  padding: 3px 15px 3px 15px;
  font-size: 14px;
  border-radius: 6px;

}

a#dropdownMenuLinkkk {
  background-color: #fff;
  border: 1px solid grey;
  padding: 5px 15px 5px 15px;
  font-size: 14px;
  border-radius: 6px;
  text-decoration: none;
  color: #7E8C9C;
  cursor: pointer;
}

a#dropdownMenuLinkkkAfter {
  background-color: #fff;
  border: 1px solid #093D54;
  padding: 5px 15px 5px 15px;
  font-size: 14px;
  border-radius: 6px;
  text-decoration: none;
  color: #093D54;
  cursor: pointer;
}

button.createnew {
  margin: 22px 0 0 15px;
  background-color: #fff;
  border: 1px solid #7E8C9C;
  font-size: 14px;
  padding: 3px 8px 3px 8px;
  border-radius: 5px;
  color: #7E8C9C;
  cursor: pointer;
  outline: none;
}

button.createnewClicked {
  margin: 22px 0 0 15px;
  background-color: #fff;
  border: 1px solid #093D54;
  font-size: 14px;
  padding: 3px 8px 3px 8px;
  border-radius: 5px;
  color: #093D54;
  cursor: pointer;
  outline: none;
}

button.sendtemplate {
  margin: 7px 0px 0 2px;
  border: 0;
  background-color: #093D54;
  color: #fff;
  font-size: 14px;
  padding: 5px 20px 5px 20px;
  border-radius: 6px;
  cursor: pointer;
  outline: none;
}

button.sendtemplate2 {
  margin: 7px 0px 0 2px;
  border: 1px solid #C9C9C9;
  background-color: #fff;
  color: #7E8C9C;
  font-size: 14px;
  padding: 5px 20px 5px 20px;
  border-radius: 6px;
  cursor: pointer;
  outline: none;
}

.dropbtn {
  background-color: #F1FFFF;
  color: #000;
  padding: 8px 0 8px 14px;
  font-size: 16px;
  border: 1px solid #D3EFF0;
  width: 235px;
  border-radius: 5px;
  text-align: left;
  font-size: 12px;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdowncontent {
  display: none;
  position: absolute;
  background-color: #fff;
  min-width: 235px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  transition: height 0.3s ease-in;
  border-radius: 5px;
  margin-top: 3px;
  font-size: 14px;
}

.dropdowncontent a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;

}


.dropdown:hover .dropdowncontent {
  display: block;

}

input.enterusername {
  padding: 5px 0px 4px 10px;
  border-radius: 4px;
  width: -webkit-fill-available;
  outline: none;
  border: 1px solid #D3EFF0;
  background-color: #F1FFFF;

}

input.enterusername::placeholder {
  font-size: 12px;
  color: #666666;
}


button.savechanges {
  background-color: #093D54;
  color: #fff;
  font-size: 14px;
  border: 0;
  border-radius: 10px;
  padding: 5px 30px 5px 30px;
  cursor: pointer;
}

button.savechanges2 {
  background-color: #093D54;
  color: #fff;
  font-size: 14px;
  border: 0;
  border-radius: 10px;
  padding: 5px 30px 5px 30px;
  cursor: pointer;
  text-align: center;
  margin: 0 0 1rem 0px;
}

button.savechanges2Disable {
  background-color: #093D54;
  color: #fff;
  font-size: 14px;
  border: 0;
  border-radius: 10px;
  padding: 5px 30px 5px 30px;
  cursor: pointer;
  text-align: center;
  margin: 0 0 0rem 0px;
  opacity: 0.5;
}

h5.SentPrompt {
  font-size: 26px;
  margin-left: 6rem;
  margin-top: 2rem;
  font-weight: 600;
  color: #093D54;
}

input.savetemplate {
  margin: auto;
  display: block;
  padding: 4px 25px 4px 15px;
  border-radius: 4px;
  outline: none;
  border: 1px solid #D3EFF0;
  background-color: #F1FFFF;
  width: 25rem;
}

input.savetemplate::placeholder {
  color: #666666;
  font-size: 14px;
}

label.promptlabel {
  font-size: 14px;
}

/* createcustomeplan */

.createcustomerplan h4 {
  color: #093d54;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  padding: 18px 0 0 0px;
}

span.underline {
  border-bottom: 2px solid #093d54;
  color: #093d54;
  display: inline-table;
  position: absolute;
  width: 40px;
  margin: 7px 0 0 0px;
}

button.generatethekey {
  background-color: #093D54;
  color: #fff;
  border: 0;
  padding: 7px 25px 7px 25px;
  border-radius: 10px;
}

.createcenter {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 30px;
}

.createcenter button {
  background-color: #093D54;
  color: #fff;
  padding: 6px 2rem 6px 2rem;
  font-size: 16px;
  border: 0;
  border-radius: 9px;
}

button.cnpln {
  border-radius: 8px;
  font-size: 16px;
  padding: 5px 1rem 5px 1rem;
  border: 0;
  margin: 5px 0 0 0;
  color: #fff;
  background-color: #093D54;
  cursor: pointer;
  outline: none;
}

.selectoption {
  outline: none;
  width: 100px;
  background-color: rgb(232, 247, 247);
  border: 0;
  cursor: pointer;

}

.selectoptions {

  outline: none;
  border: 0;
  background-color: aliceblue;

}

.select-selectoption::after {
  position: absolute;
  content: "";
  top: 14px;
  right: 10px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-color: #fff transparent transparent transparent;
}


label.customfilelabel::after {
  background-color: aliceblue;
}

button.createpromocode {
  border: 0;
  font-size: 14px;
  padding: 6px 20px 6px 20px;
  border-radius: 5px;
  color: #fff;
  background-color: #093D54;
  cursor: pointer;
  margin: 5px 0px 0 0px;
  outline: none;
}




.pure-button-active {
  toggle-trigger: switch;
  background-color: black;
  color: white;
}

html:toggle(switch) .pure-button-active {
  background-color: white;
  color: black;
}



#arrow-top::after {
  content: " ";
  position: absolute;
  right: 30px;
  top: -15px;
  border-top: none;
  border-right: 15px solid transparent;
  border-left: 15px solid transparent;
  border-bottom: 15px solid black;
}


.successmessage {
  margin: auto;
  border: 1px solid #6DE1A4;
  background-color: #EEFFF6;
  padding: 2px 12px 4px 12px;
  border-radius: 6px;
}


.successmessage2 {
  margin: 7px 0 0 11px;
  border: 1px solid #6DE1A4;
  background-color: #EEFFF6;
  padding: 2px 12px 4px 12px;
  border-radius: 6px px;
}

p.que {
  font-size: 12px;
  margin: 0;
  padding: 4px 0 0 4px;
  font-weight: 600;
}


textarea#rstextAreaExample1 {
  font-size: 14px;
  color: #093D54;
  font-weight: 600;
}

button.savethis {
  margin: 6px 0px 0 11px;
  border: 1px solid #C9C9C9;
  background-color: #fff;
  color: #7E8C9C;
  font-size: 14px;
  padding: 5px 20px 5px 20px;
  border-radius: 6px;
  cursor: pointer;
}



/* SearchByDuration */

.inappprompt h1 {
  padding: 20px 20px 0 20px;
  font-size: 18px;
  font-weight: 700;
  color: #093D54;
}


span.underline2 {
  border-bottom: 2px solid #093d54;
  color: #093d54;
  display: inline-table;
  position: absolute;
  width: 40px;
  margin: 0px 0 0 20px;
}


button.all {
  padding: ma;
  margin: 0 0 0 15px;
  padding: 3px 12px 3px 12px;
  border: 1px solid #093D54;
  border-radius: 5px;
  background: #fff;
  cursor: pointer;
}

button.all:focus {
  background-color: #093D54;
  color: #fff;
  border: 1px solid #093D54;
  outline: none;

}

.toggleall {
  margin: 28px 0 0 4px;
}

.searchbackground {
  padding: 20px 2rem;
  background-color: #F9F9F9;
  margin: 1rem 1rem;
  border-radius: 8px;
}

h2.durationby {
  font-size: 18px;
  font-weight: 600;
  color: #093D54;
  margin: 3px;
}

.selectdate {
  border: 1px solid #7E8C9C;
  padding: 4px 12px 3px 13px;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
}

label.tothelabel {
  font-size: 14px;
  margin: 0 8px 0 0px;
}


.inputcontainer2 {
  position: absolute;
}


input.inputsurface::placeholder {
  padding: 0 4rem 0 6px;
  font-size: 12px;
  color: #7E8C9C;
}

.inputsurface {
  width: 300px;
  padding: 5px 0 5px 22px;
  outline: none;
  border-radius: 7px;
  border: 1px solid #7E8C9C;
  font-size: 12px;
}

.inputimage img {
  position: absolute;
  margin: 10px 0 0 8px;
}

table.admintable {
  margin: 0 0 6px 0;
  position: sticky;
  top: 0;
  z-index: 100;
  height: 111px;


}

thead.adminheader {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #ebf6ff;
  /* background-color: #ebf6ff; */
}

th.tabledataheader {
  padding: 14px 0px 14px 20px;
  font-size: 14px;
}

td.admintabledata {
  padding: 14px 0px 14px 20px;
  font-size: 12px;
}

.admintablerow {
  background-color: #fff;
  box-shadow: inset 0 0 9px #afafaf38, 0 0 0 0 #0c20350d;
}

button.sendit {
  padding: 4px 12px 4px 12px;
  border: 0;
  margin: 0 18px 0 0px;
  background-color: #093D54;
  color: #fff;
  border-radius: 5px;
  cursor: pointer;
}

.successmessages {
  border: 1px solid #6DE1A4;
  width: fit-content;
  margin: auto;
  background-color: #EEFFF6;
  border-radius: 8px;
}

p.que {
  font-size: 14px;
  margin: 0;
  padding: 4px 6px 5px 4px;
}

img.procodeimg {
  margin: 3px 3px 0 5px;
}

input.PlanName::placeholder {
  font-size: 14px;
  color: #C9C9C9;
}

input.prices::placeholder {
  font-size: 14px;
  color: #C9C9C9;
}

.Billing {
  color: #55C2C3;
}

input.nofcreds::placeholder {
  font-size: 14px;
  color: #C9C9C9;
  padding: 0 10px 10px 0;
}

p.templateError {
  padding: 3px 0 0 34px;
  font-size: 14px;
  color: red;
}


img.align_right {
  /* position: absolute; */
  margin: 0 14px 0 0px;
}

p.prop {
  margin: 7px;
  padding: 11px;
  display: contents;
}

.admininpts::placeholder {
  font-weight: 600;
}

.adminfixTableHead {
  height: 380px;
  overflow-y: auto;
  z-index: 1;

}

.setopacity {
  opacity: 0.5;
}

.UnlimitedCredits {
  font-size: 14px;
  padding: 0 0 0 10px;
  font-weight: 600;
}


.formlayout {
  background: #fff;
  border: 6px solid #fff;
  border-radius: 19px;
  box-shadow: 0 2px 8px 0 rgba(99, 99, 99, .2);
  padding: 0 25px;
  width: 600px;
  margin: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

h3.adminsignup {
  text-align: center;
  padding: 1rem 0 1rem 0;
  font-weight: 600;
  color: #093d54;
}

button.adminsubmit {
  display: flex;
  margin: auto;
  padding: 5px 14px 5px 14px;
  border: 0;
  border-radius: 7px;
  color: #fff;
  background-color: #093d54;
  outline: none;
  cursor: pointer;
}

h5.FreemiumUsers {
  padding: 10px 0 0 15px;
  color: #093d54;
  font-weight: 600;
  margin: 0px 0 -4px 0;
}

h5.FreemiumUsers2 {
  padding: 10px 0 0 0px;
  color: #093d54;
  font-weight: 600;
  margin: 0px 0 -4px 0;
}


button.resetbutton {
  padding: 5px 15px 5px 15px;
  margin: 0 5px 0 4px;
  font-size: 14px;
  outline: none;
  border: 0;
  background-color: #ececec;
  border-radius: 5px;
  cursor: pointer;
}

th.actionheader {
  padding: 10px 0px 10px 0px;
}

form.adminform {
  width: 600px;
  padding: 10px 22px 0 22px;
  margin: 2rem 0 0 0;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  border-radius: 16px;
}

button.adminbackbutton {
  margin: 20px 0 0 0;
  border: 0;
  font-size: 14px;
  padding: 6px 15px 6px 15px;
  border-radius: 6px;
  color: #fff;
  background-color: #093D54;
  cursor: pointer;
}

.savedlistbackgroundcolor {
  background-color: #F5F5F5;
  padding: 4px 8px 4px 6px;
}

.savedlistbackgroundcolors {
  background-color: #F5F5F5;
  padding: 4px 8px 4px 6px;
  margin: 16px 0 0 0;
}

table.yourrefrellists {
  border-collapse: separate;
  border-spacing: 0 7px;
  width: 100%;
  padding: 0 0px 0 0px;
}

tr.tabledatasmall {
  box-shadow: inset 0px 0px 2px 2px #afafaf38, 0px 0px 0px 0 #0c20350d;
  margin-top: 11px;
  margin-bottom: 30px;
  border-radius: 4px;
  background-color: #fff;
}


td.datagoesheresavedlist {
  padding: 10px 0 10px 0px;
  font-size: 13px;
}

p.durationtext {
  padding: 10px 0 10px 24px;
  margin: 0;
  color: #093D54;
  font-weight: 600;
}

input.searchitem {
  margin: 12px 0 0 3px;
  width: 18rem;
  border: 1px solid #7E8C9C;
  border-radius: 5px;
  padding: 3px 0 3px 35px;
  font-size: 14px;
  color: #7E8C9C;
  outline: none;
}

input.searchitem::placeholder {
  padding: 0 0 0 0px;
  font-size: 14px;
  color: #7E8C9C;
}


p.PromptName {
  margin: 25px 0 0 20px;
  color: #093D54;
  font-weight: 600;
}

p.Support {
  margin: 30px 25px 0 2rem;
  color: #7E8C9C;
  font-weight: 600;
  font-size: 12px;
}

p.Supported {
  margin: 30px 0px 0 30px;
  color: #7E8C9C;
  font-weight: 600;
  font-size: 12px;
}


p.PromptNames {
  margin: 25px 0 0 20px;
  color: #093D54;
  font-weight: 600;
}


p.inputtextedit {
  color: #6DE1A4;
  font-size: 12px;
  padding: 6px 0 0 30px;
  font-weight: 600;
  margin: 0;
}

span.typehreand {
  color: #093D54;
}



th.poputh {
  padding: 10px 0 10px 15px;
  font-size: 14px;
}

td.poputr {
  font-size: 14px;
  padding: 0 0 0 14px;
}

th.poputr {
  font-size: 12px;
  padding: 8px 0 8px 25px;
}

.senditems p {
  padding: 20px 0 0 20px;
  margin: 0;
  color: #093D54;
  font-weight: 600;
}

.senditems {
  background-color: #F9F9F9;
  border-bottom: 1px solid #E1EDF5;
  margin: 0 0 30px 0px;
}


.contenttable {
  border-collapse: collapse;
  margin: 25px 0;
  font-size: 0.9em;
  min-width: 400px;
  border-radius: 5px 5px 0 0;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

.contenttable thead tr {
  background-color: #009879;
  color: #ffffff;
  text-align: left;
  font-weight: bold;
}

.contenttable th,
.contenttable td {
  padding: 12px 15px;
}

.contenttable tbody tr {
  border-bottom: 1px solid #dddddd;
}

.contenttable tbody tr:nth-of-type(even) {
  background-color: #f3f3f3;
}

.contenttable tbody tr:last-of-type {
  border-bottom: 2px solid #009879;
}

.contenttable tbody tr.active-row {
  font-weight: bold;
  color: #009879;
}


/* User Insights */

p.UserInsightsparagraph {
  font-size: 18px;
  color: #093d54;
  font-weight: 600;
  margin: 0px 0 8px 0;
}

.underlinisights {
  border-bottom: 2px solid #093d54;
  width: 36px;
}

p.insightsdatename {
  padding: 0px 2rem 0px 0px;
  margin: 0;
  color: #45484B;
}


p.insightsdates {
  font-size: 13px;
  padding: 4px 0px 0 3px;
  color: #A5A5A5;
  margin: 0;
}

.scrollablediv::-webkit-scrollbar {
  display: none;
}

.scrollablediv {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */

}

.scrollablediv {
  height: 100vh;
  overflow-y: scroll;
  /* Add the ability to scroll */
  margin: 0px 10px 0 10px;
  width: -webkit-fill-available;
}

.insightsdateborder {
  border: 1px solid #70707047;
  border-radius: 12px;
  padding: 6px 15px 6px 15px;
}

.userinsightsblock {
  border: 1px solid #00000029;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  margin: 2rem 0 0 0;
  border-radius: 10px;
}

.insightscoloreddiv {
  background-color: #F9F9F9;
  border-radius: 10px;
}

p.insightsjoanne {
  margin: 16px 0 0 25px;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}

p.insightsOfficer {
  margin: 4px 0 0 25px;
  font-size: 16px;
  color: #000000;
}

p.insightstabubble a {
  color: #55C2C3;
  font-size: 12px;
  font-weight: 600;
  margin: 4px 0 0 25px;
  text-decoration: none;
  cursor: auto;
}

img.insightspengimage {
  border-radius: 102px;
  box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
}

.insightsblockofff {
  margin: 0rm 0 0 0;
  box-shadow: inset 0px 0px 4px 1px #00000029;
  background-color: #fff;
  border-radius: 8px;
}

.insightstotaldownloadcredits {
  border: 1px solid #00000029;
  border-top: 0;
  border-left: 0;
  height: 105px;
}

.insightstotaldownloadcreditss {
  border: 1px solid #00000029;
  border-top: 0;
    border-left: 0;
    border-bottom: 0;
    height: 105px;
}

.insightstotaldownloadcreditsss{
  border: 1px solid #00000029;
  border-top: 0;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
  height: 105px;
}

.insightstotaldownloadcreditssss{
  border: 1px solid #00000029;
  border-top: 0;
  border-left: 0;
  border-right: 0;
  height: 105px;
}

p.insightsTotalDownloadCredits {
  margin: 0;
  text-align: center;
  padding: 17px 0 1px 0;
  color: #093D54;
  font-weight: 600;

}

p.insightsdownloadedcredits {
  margin: 11px 0 10px 0;
  text-align: center;
  font-size: 26px;
  color: #55C2C3;
  font-weight: 600;
}

p.UserInsightsPersonalInformation {
  font-size: 18px;
  font-weight: 600;
  color: #093D54;
  margin: 20px 0 7px 11px;
}

.PersonalInformationunderlinisights {
  border-bottom: 2px solid #093d54;
  width: 36px;
  margin: 0 0 0 12px;
}

p.insightsfirstname {
  margin: 20px 0 0 33px;
  color: #000000;
  font-size: 14px;
  font-weight: 600;
}

p.insightsname {
  margin: 4px 0 0 33px;
  color: #55C2C3;
  font-size: 14px;
  font-weight: 600;
}

p.insightsuserviewlog {
  padding: 15px 0 0 20px;
  font-size: 18px;
  font-weight: 600;
  color: #093D54;
  cursor: pointer;
}


p.insightsuserdownloadlog {
  color: #C9C9C9;
  padding: 0 0 0 3rem;
  margin: 16px 0 0 0;
  font-size: 18px;
  cursor: pointer;
}

.savedlistbackgroundcolors {
  background-color: #fff;
  padding: 4px 8px 4px 8px;
  margin: 0 20px 0 20px;
}

td.datagoesheresavedlist {
  padding: 10px 0 10px 20px;
  font-size: 13px;
}

p.insightsSearchResultFound {
  font-size: 14px;
  color: #C9C9C9;
}

span.insightscontacts {
  color: #000000;
  font-weight: 600;
}

p.insightsUserDuration {
  text-align: center;
  color: #093D54;
  font-weight: 600;
  padding: 14px 0 0 0;
  margin: 0;
}

p.insightshours {
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  color: #55C2C3;
  margin: 5px 0 10px 0;
  cursor: pointer;
}

.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
  color: #fff;
  background-color: #0a1016;
}


/* DetailsOfUser */

button.DetailsOfUserback {
  background-color: #093D54;
  color: #fff;
  font-size: 18px;
  border: 0;
  outline: none;
  padding: 3px 10px 3px 10px;
  border-radius: 4px;
  cursor: pointer;
}

p.Detailsouserdate {
  font-size: 16px;
  color: #093D54;
  font-weight: 600;
}

span.detailsofusercoontactlist {
  color: #000;
  font-weight: 600;
}

.detailsofuserisrtlayer {
  background-color: #F9F9F9;
  padding: 10px 20px 10px 20px;
  border-radius: 10px;
}

.detailsofusersecondlayer {
  border-radius: 10px;
  padding: 1px 0 0px 0;
  background-color: #fff;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}

p.detailsofuserfilter {
  font-size: 16px;
  color: #093D54;
  font-weight: 600;
  margin: 0 0 10px 20px;
}

p.detailsofusercommunity {
  font-size: 11px;
  margin: 0 0 5px 5px;
}

.detalsofuserscrollabe {
  height: 230px;
  overflow-y: auto;
}

p.ErrorNotificationdate {
  margin: 20px 0 0 0;
  font-size: 16px;
  color: #093D54;
  font-weight: 600;
}

.userinsightbuttons {
  color: rgb(0, 0, 0);
  font-size: 14px;
  border: 1px solid rgb(85, 194, 195);
  background-color: rgb(232, 247, 247);
  padding: 8px 15px 8px 15px;
  border-radius: 4px;
}