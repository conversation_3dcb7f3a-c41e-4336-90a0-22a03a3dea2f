import React, { useEffect, useState } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import AdminHeader from "../../layouts/AdminHeader.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import loadingGif from "../../../customer/assests/waiting.gif";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import Alert from "../../../customer/common-files/alert.js";

const Contact = () => {
  const [data, setData] = useState([]);
  const itemsPerPage = 10; // You can adjust the number of items per page here
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [isReadMore, setIsReadMore] = useState(null);
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  useEffect(() => {
    getContactHistory();
  }, [currentPage, searchQuery]);

  const getContactHistory = () => {
    let params = {
      page: searchQuery ? 1 : currentPage,
      pageSize: searchQuery ? 100000 : itemsPerPage,
      sortBy: "desc",
    };
    if (searchQuery) {
      params.searchParams = {
        email: searchQuery,
      };
    }
    searchQuery ? setLoading(false) : setLoading(true);
    let alllogs = PostWithTokenNoCache(
      ApiName.contactPagination,
      JSON.stringify(params)
    )
      .then(async function (res) {
        if (res?.status === 200) {
          const body = JSON.parse(res?.data?.data);
          setData(body.items);
          setTotalPages(body.totalPages);
          setLoading(false);
          setDefaultAlert(false);
        }
      })
      .catch(function (errors) {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
      });
  };

  const toggleReadMore = (id) => {
    if (isReadMore === id) {
      setIsReadMore(null);
    } else {
      setIsReadMore(id);
    }
  };
  const formatDate = (dateTime) => {
    const date = new Date(dateTime);
    const formattedDate = `${date.getFullYear()}/${(date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${date.getDate().toString().padStart(2, "0")}`;
    return formattedDate;
  };

  return (
    <div className={S.app}>
      <AdminHeader />
      <div className="d-flex flex-row">
        <div className="" style={{ width: "350px" }}>
          <LeftSideNav />
        </div>
        {!loading ? (
          <div style={{ width: "100%", padding: "0 10px 0 5px" }}>
            <div className="adminpanelsearchbyduration">
              <div className={S.savedlistbackgroundcolors}>
                <div className="d-flex flex-row justify-content-between">
                  <div>
                    <p className={S.durationtext}>Contact-Us History</p>
                  </div>
                  <div className="searchduriconsclass">
                    <span
                      className="fa fa-search"
                      style={{
                        position: "absolute",
                        color: "#7E8C9C",
                        margin: "21px 0 0 15px",
                        fontSize: "12px",
                      }}
                    ></span>
                    <input
                      placeholder="Search by Email"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className={S.searchitem}
                    />
                  </div>
                </div>
                <table
                  className="yourrefrellists ml-3"
                  style={{ width: "98%" }}
                >
                  <tbody>
                    <tr
                      className="table-headers-saved-list mt-2 "
                      style={{ backgroundColor: "#ebf6ff", height: "40px" }}
                    >
                      <th className={S.tableheaderspace}>Sl No</th>
                      <th className={S.tableheaderspace}>Name</th>
                      <th className={S.tableheaderspace}>Email</th>
                      <th className={S.tableheaderspace}>Phone</th>
                      <th className={S.tableheaderspace}>Requirement</th>
                      <th className={S.tableheaderspace}>Created At</th>
                    </tr>
                    {data.map((item, i) => (
                      <tr
                        key={i}
                        className="tabledatasmall"
                        style={{
                          backgroundColor: "white",
                          boxShadow: "0px 4px 4px 0px #e2e2e2",
                          height: "40px",
                        }}
                      >
                        <td className={S.tabledataspaceing}>
                          {searchQuery ? i + 1 : (currentPage - 1) * 10 + i + 1}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {item?.first_name}
                        </td>
                        <td className={S.tabledataspaceing}>{item?.email}</td>
                        <td className={S.tabledataspaceing}>{item?.phone}</td>
                        <td className={S.tabledataspaceing} style={{textAlign:"left"}}>
                          {item?.requirement.length < 27 ? (
                            item?.requirement
                          ) : (
                            <>
                              {isReadMore == i
                                ? item?.requirement
                                : item?.requirement.slice(0, 25) + "..."}
                              <button
                                onClick={() => toggleReadMore(i)}
                                style={{
                                  color: "#55c2c3",
                                  border: "none",
                                  background: "none",
                                  cursor: "pointer",
                                  outline: "none",
                                }}
                              >
                                {isReadMore == i ? "Show less" : "Show more"}
                              </button>
                            </>
                          )}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {formatDate(item?.createdAt)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {
                  <Pagination
                    className="pagination-bar"
                    currentPage={currentPage}
                    totalCount={totalPages}
                    pageSize={1}
                    onPageChange={(page) => {
                      setCurrentPage(page);
                    }}
                  />
                }
              </div>
            </div>
          </div>
        ) : (
          <div className="mx-auto mt-5" style={{ display: loading }}>
            <img
              src={loadingGif}
              alt="Loading"
              className="loader"
              width="400"
            />
          </div>
        )}
      </div>
      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};

export default Contact;
