@import url('https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

body {
    font-family: 'Lato', 'medium';
}


p.Slist {
    margin: 0;
    padding: 0 0 0 10px;
    color: #093D54;
    font-weight: 600;
}

.back-image img {
    cursor: pointer;
}

button.createnewList {
    background-color: #093D54;
    color: #fff;
    border: 0;
    outline: none;
    font-size: 14px;
    padding: 5px 25px 5px 25px;
    border-radius: 8px;
    margin: 10px 0 0 0;
    cursor: pointer;
}


table.your-refrel-lists {
    border-collapse: separate;
    border-spacing: 0 7px;
    width: 100%;
    padding: 0 0px 0 0px;
}



button.savedlistDownloadbutton {
    background-color: #093D54;
    border: 0;
    font-size: 12px;
    color: #fff;
    padding: 2px 12px 2px 12px;
    border-radius: 2px;
    cursor: pointer;
    margin: 0 0px 0 0px;
    outline: none;
}

.savedlistverticalline {
    border-left: 1px solid #e2e2e2;
    padding: 0 0px 0 0px;
}

.table-headers-saved-list th {
    background-color: #EBF6FF;
    margin-top: 12px !important;
    padding: 10px 0 10px 0;
    font-size: 14px;
    /* max-width: 200px; */
}

td.data-goes-here-saved-list {
    padding: 10px 0 10px 0px;
    font-size: 13px;
}

.savedlistbackgroundcolor {
    background-color: #F5F5F5;
    padding: 4px 8px 4px 6px;
    border-radius: 5px;
}


/* modal  */

button.closeup {
    border: 0;
    background-color: #fff;
    color: #55C2C3;
    font-size: 28px;
    padding: 0px 0px 0px 0px;
    margin: 0px 11px -20px 9px;
    cursor: pointer;
    outline: none;
}

p.VerificationInprogress {
    text-align: center;
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #093D54;
    font-weight: 600;
}

p.addressshortly {
    text-align: center;
    font-size: 14px;
    padding: 0 28px 0 30px;
    color: #000000;
    font-weight: 600;
}

span.noted {
    font-size: 14px;
    color: #55C2C3;
}

p.dontworry {
    text-align: center;
    font-size: 14px;
    color: #093D54;
}

p.receiveyourlist {
    font-size: 14px;
    text-align: center;
    color: #000;
    font-weight: 600;
}

p.noted {
    text-align: center;
    font-size: 12px;
    color: #093D54;
}

p.messages {
    border: 1px solid #6DE1A4;
    margin: auto;
    font-size: 14px;
    padding: 5px 10px 5px 10px;
    border-radius: 13px;
    background-color: #EEFFF6;
    text-align: center;
    width: fit-content;
}

button.close-verification {
    margin: 0px;
    padding: 0px;
}

.tabs-container {
    width: 100%;
    padding: 10px 0 0 0;
    box-sizing: border-box;
}

.tabs {
    /* margin-bottom: 43px; */
    background-color: #E8F7F7;
    width: 200px;
    margin: 0px auto 5px auto;
    text-align: center;
    padding: 5px 0 5px 0px;
    border-radius: 34px;
    box-shadow: inset 0px 3px 4px #00000029;
}


.tab-button {
    padding: 0px 13px;
    /* margin-right: 10px; */
    background-color: transparent;
    border: 0;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
    margin: 0 9px 0 9px;
    outline: none;
    font-weight: 600;
}

.tab-button.active {
    background-color: #FFFFFF;
    color: #093D54;
    border-radius: 18px;
    border: 0;
    box-shadow: 0px 1px 4px #00000029;
    outline: none;
    padding: 2px 14px;
}



.tab-content {
    position: relative;
}

.tab-container {
    display: none;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.tab-container.active {
    display: block;
    opacity: 1;
}

.tab-button:focus,
.tab-button:active {
    outline: none;
    /* Ensure no outline is visible when focused or active */
}

.saved-list-content {
    /* padding: 20px; */
    /* background-color: #f9f9f9; */
    /* border: 1px solid #ddd; */
    border-radius: 8px;
    color: #000;
    width: 100%;
}