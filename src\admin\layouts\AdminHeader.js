import React from 'react';
import S from '../assets/css/layouts/admin-header.module.css';
import { useNavigate } from 'react-router-dom';
import MetaTitle from '../../customer/common-files/MetaTitle';
const AdminHeader = () => {
  const navigate = useNavigate();
  const signoutHandler = async () => {

    return new Promise((resolve) => {
      sessionStorage.clear();
      localStorage.clear();
      resolve();
      navigate('/');
    });

  }
  return (
    <>
      <MetaTitle />
      <header className={S.header}>
        <div className="d-flex flex-row justify-content-between">
          <div>
            <a className={S.header_image}>
              <img
                src="../../images/black-logo.png"
                className="img-fluid mt-3"
                width="150"
              />
            </a>
          </div>
          <div>
          <div className="d-flex flex-row">
            <div >
            <p className={S.user_name}>
              Admin
              <img src="../../images/upload--.png" width="20" />
            </p>
            </div>
            <div >
            <button
              type="submit"
              className={S.sign_out_2}
              onClick={signoutHandler}
            >
              Sign out
            </button>
            </div>
          </div>
           
           
          </div>
        </div>
      </header>
    </>
  );
}
export default AdminHeader;