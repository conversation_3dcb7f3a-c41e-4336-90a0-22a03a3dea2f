import React, { useState, createContext, useEffect } from "react";
import '../../customer/assests/css/forgot.css';
import { Link } from 'react-router-dom';
import VerifyEmail from './VerifyEmail.js';
import { useNavigate } from 'react-router-dom';
import { ApiName } from '../../customer/common-files/ApiNames.js';
import { axiosPost } from '../../customer/common-files/ApiCalls.js';
import Footer from "../layouts/Footer";
import { Helmet } from "react-helmet-async";
import axios from "axios";
import DWINTracker from "../common-files/DWINTracker.js";

export const DataContext = createContext();
const Forgot = () => {

    const [emailErr, setEmailErr] = useState();
    const [email, setEmail] = useState('');
    const [validEmail, setValidEmail] = useState(false);
    const [changeColor, setChangeColor] = useState(true);
    const [submitted, setSubmitted] = useState(false);
    const [blockedDomain, setBlockedDomain] = useState([]);

    const history = useNavigate();

    useEffect(() => {
        const params = {
            method: "POST",
        };
        let result = axios
            .post(ApiName.activeDomainBlockList, params)
            .then(function (response) {
                // const token = JSON.parse(response.data.data);
                if (response.data.status == 200) {
                    let data = JSON.parse(response.data.data);
                    // Extract the "domain" property from each object and store in an array
                    const domainArray = data.map((item) => item.domain);
                    setBlockedDomain(domainArray);
                }
            });
    }, []);

    const onChangeEmail = (event) => {

        let email = event.target.value;
        setEmail(email.toLowerCase());

        // Construct dynamic RegExp for disallowed domains
        const disallowedDomainsRegExp = new RegExp(
            `^.+@((?!${blockedDomain.join("|")}).)+$`
        );
        let result = disallowedDomainsRegExp.test(email);

        if (result) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            let checkDomain = emailRegex.test(email);

            if (!checkDomain) {
                setEmailErr('Please enter a valid business email');
                setValidEmail(false);
                setChangeColor(false);
            } else {
                setValidEmail(true);
                setChangeColor(true);
                setEmailErr('');
            }
        } else {
            setEmailErr('Please enter a valid business email');
            setValidEmail(false);
            setChangeColor(false);
        }
    }

    const onSubmitHandler = (event) => {
        event.preventDefault();

        //console.log(email);
        //alert(ApiName.signIn);
        const data = {
            "email": email,
        }
        setSubmitted(true);
        let result = axiosPost(ApiName.forgotPassword, data)
            .then(function (response) {

                if (response.data.status == 200) {
                    history('/verify-email', {
                        state: {
                            email: email,
                        }
                    });
                }
            }).catch(function (errors) {
                setSubmitted(false);
                // console.log('error',errors);
                if (errors.response.data.status == 400) {
                    setEmailErr(errors.response.data.message);
                    setValidEmail(false);
                    setChangeColor(false);
                } else {
                    setValidEmail(true);
                    setEmailErr('');
                    setChangeColor(true);
                }
            });


    }
    return (
        <>
            <Helmet>
                <title>Forgot Password | ReachStream</title>
                <meta name="description" content="Reset your password in two quicks steps to sign back into your ReachStream account." />
            </Helmet>
            <DWINTracker />
            <div className="container-fluid">
                <div className="row">
                    <div className="col-md-6 bg-color">
                        <div className="offset-md-3 forgot-password">
                            <img src="../images/r-logo.png" width="50" />
                            <br /><br /><br /><br />
                            <img src="../images/group-50970.png" className="img-fluid" />

                        </div>
                        {/* <div className="passowrd-banner-1">
                        <img src="../images/group-50970.png" className="img-fluid" />
                    </div>        */}
                        <br /><br /><br /><br /><br />
                    </div>

                    <div className="col-md-6">
                        {/* <div className="row"> */}
                        {/* <div className="col-md-7"></div> */}
                        <div className="row">
                            <div className="col-sm-11">
                                <div className="Right-sign4">
                                    <Link to="/">Sign In</Link>
                                </div>
                            </div>
                        </div>


                        {/* </div> */}
                        <div className="form-box7">
                            <form onSubmit={onSubmitHandler}>
                                <h2>Forgot Password?</h2>
                                <p className="password-semi-header">Reset your password in 2 quick steps.</p>
                                <div className="make-space3"></div>
                                <span className="email-address"><p>Enter Your Email Address</p></span>

                                {/* <input type="text" className="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" name="email" onChange={onChangeEmail} />*/}
                                {changeColor ? (<input type="text" className="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" onChange={onChangeEmail} value={email} name="email" autoFocus />) : <></>}

                                {!changeColor ? (<input type="text" className="invalid-input-text" aria-describedby="emailHelp" onChange={onChangeEmail} value={email} name="email" autoFocus />) : <></>}
                                {!validEmail ? (
                                    <span className="email-error-message">{emailErr && <p>{emailErr}</p>}</span>
                                ) : (
                                    <></>
                                )}

                                <div className="make-space4"></div>

                                <span>
                                    {!validEmail || submitted ? (
                                        <input
                                            type="submit"
                                            value={!submitted ? "Send" : "Processing"}
                                            className="cp-pluss cust-disabled"
                                        />
                                    ) : (
                                        <input type="submit" value="Send" className="cp-pluss" />
                                    )}
                                </span>
                                <div className="bottom-space"></div>
                            </form>
                        </div>
                    </div>
                    <div className="col-md-1"></div>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default Forgot;