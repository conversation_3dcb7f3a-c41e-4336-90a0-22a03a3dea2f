import React, { useState, useEffect, useRef } from "react";
import { PostWithTokenNoCache } from "../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import { CSVLink } from "react-csv";
import "../assests/css/filter/job_title.css";
import UseTabStore from "../common-files/useGlobalState.js";
import Alert from "../common-files/alert.js";

const ContactModal = ({ isLowViewCredits }) => {
  const csvLinkRef = useRef();
  const modalRef = useRef();
  const contactModalRef = useRef();
  const [downloadedRows, setDownloadedRows] = useState([]);

  const {
    selectedContact,
    defaultAlert,
    viewModal,
    defaultErrorMsg,
    sampleDownloadedIds,
    setSampleDownloadedIds,
    setDefaultError,
    setViewModal,
    selectedTab,
    setDefaultErrorMsg,
    setDefaultAlert,
    setSelectedContact,
    setContactModelId,
    setButtonType
  } = UseTabStore();


  const headers = [

    { label: "Company Name", key: "company_company_name" },
    { label: "Website", key: "company_website" },
    { label: "Company Phone Number", key: "company_phone_1" },
    { label: "Company Employee Size", key: "company_employee_size" },
    { label: "Company Annual Revenue Amount", key: "company_annual_revenue_amount" },
    { label: "Company Industries", key: "company_industries" },
    { label: "Address", key: "company_address_street" },
    { label: "City", key: "company_address_city" },
    { label: "State", key: "company_address_state" },
    { label: "Zipcode", key: "company_address_zipcode" },
    { label: "Country", key: "company_address_country" },
    { label: "Siccode", key: "company_sic_code" },
  ];

  const [showDownloadLink, setShowDownloadLink] = useState(false);

  useEffect(() => {
    // if (isLowViewCredits && viewModal) {
    //   setDefaultErrorMsg("You have exceeded your view credits")
    //   setDefaultAlert(true);
    // }
    contactModalRef.current.click();
  }, [])

  useEffect(() => {
    // Add event listeners when the component mounts
    document.addEventListener('mousedown', handleOutsideClick);
    document.addEventListener('keydown', handleEscKeyPress);

    // Remove event listeners when the component unmounts
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
      document.removeEventListener('keydown', handleEscKeyPress);
    };
  }, []);

  const handleOutsideClick = (e) => {
    // Check if the click is outside the modal
    if (!e.target.closest('.modal')) {
      closeModal();
    }
  };

  const handleEscKeyPress = (e) => {
    // Check if the pressed key is ESC
    if (e.key === 'Escape') {
      closeModal();
    }
  };

  const closeModal = () => {
    setButtonType("");
    setSelectedContact(null);
    setViewModal(false);
    setContactModelId(null);
    modalRef.current.click();
  };

  const csvDownloadHandler = async () => {
    const addActionKey = {
      id: selectedContact.id,
      action: "download",
      searchBy: selectedTab,
    }
    const urlSingleFetch = ApiName.fetchSingleCompanyDetails;
    try {
      const res = await PostWithTokenNoCache(urlSingleFetch, addActionKey);
      const responseStatus = JSON.parse(res.data.status);

      if (responseStatus === 200) {
        setDownloadedRows(JSON.parse(res.data.data));
        setShowDownloadLink(true);
        // refreshGrayOut();
      } else {
        setDefaultErrorMsg("You have exceeded your download credits")
        setDefaultAlert(true);
        setButtonType("upgrade");
        setShowDownloadLink(false);
        modalRef.current.click();
      }
    } catch (error) {
      if (error.response.data.status == 402 && error.response.data.message == "Insufficient credit balance") {
        setDefaultErrorMsg("You have insufficient download credits")
        setDefaultAlert(true);
        setButtonType("upgrade");
        setShowDownloadLink(false);
        modalRef.current.click();
      } else {
        setDefaultErrorMsg("You have insufficient download credits")
        setDefaultAlert(true);
        setButtonType("upgrade");
        setShowDownloadLink(false);
        modalRef.current.click();
      }
    }
  }

  useEffect(() => {
    if (showDownloadLink) {

      csvLinkRef.current.link.click();
    } else {
      const hideDownloadButton = document.getElementById("hideDownload");
      if (hideDownloadButton) {
        hideDownloadButton.style.display = "none";
      }
    }
  }, [downloadedRows]);

  const convertToProperCase = (val) => {
    if (val) {
      // Trim the input to remove any leading or trailing whitespace
      val = val.trim();

      // Match words, keeping special characters in place
      const words = val.match(/\w+|\W+/g);

      // Capitalize the first letter of each alphanumeric word, preserving special characters
      const capitalizedWords = words.map(word =>
        /\w/.test(word) ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : word
      );

      // Join all parts back into a single string
      return capitalizedWords.join('');
    } else {
      return val;
    }
  };


  // const refreshGrayOut = async () => {
  //   try {
  //     await PostWithTokenNoCache(ApiName.downloadedIds, {})
  //       .then(function (response) {
  //         if (response.data.status === 200) {
  //           const dataObj = JSON.parse(response.data.data);
  //           const dataArray = Object.values(dataObj);
  //           setSampleDownloadedIds(dataArray);
  //         }
  //       }).catch(function () {
  //         setTimeout(() => {
  //           setDefaultError('');
  //         })
  //         setDefaultError('Failed to load counts, Try again.!')
  //       });
  //   } catch (error) {
  //     console.error(error);
  //   }
  // };

  return (
    <>
      {viewModal && !defaultAlert ? (
        <>
          <button
            type="button"
            className="btn btn-info btn-md"
            data-toggle="modal"
            data-target="#exampleModalCenter"
            ref={contactModalRef}
            style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
            data-backdrop="true"
          ></button>
          <div
            className="modal fade show"
            id="exampleModalCenter"
            tabIndex="-1"
            role="dialog"
            aria-labelledby="exampleModalCenterTitle"
            aria-hidden="true"
          >
            <div className="modal-dialog modal-lg justify-content-center modal-dialog-centered " role="document">
              <div
                className="modal-content"
                style={{
                  backgroundImage: "url('../images/new_popup_background.png')",
                  // backgroundPosition: "inherit",
                  border: "0",
                  backgroundSize: "cover",
                  width: "750px",
                  backgroundRepeat: "no-repeat",
                  // width: "-webkit-fill-available",
                  height: "500px"
                }}
              >
                <div className="modal-header1">
                  {/* {isLowViewCredits ? <span className="ml-3 text-danger">You have exceeded your view credits.</span> : ""} */}
                  <button
                    ref={modalRef}
                    type="button"
                    className="close"
                    data-dismiss="modal"
                    aria-label="Close"
                    onClick={closeModal}
                    style={{ padding: "4px 10px 0 0px" }}
                  >
                    <span aria-hidden="true">
                      <img src="../images/cancel.png" />
                    </span>
                  </button>
                </div>


                <div className="modal-body1">
                  {/* <div className="back-color">
                </div> */}


                  <div className="bottom-section">
                    <div className="d-flex flex-row justify-content-between mt-4">
                      <div className="d-flex flex-column">
                        <div className="detailing">
                          {/* <div className="d-flex flex-row">
                            <div>
                              <span><img src="../images/building.png" className="mr-2 bimage" /></span>
                            </div>
                            <div>
                              <h4> Company Details</h4>
                              <hr className="hr-detail" />
                            </div>
                          </div> */}

                        </div>
                      </div>
                      <div className="d-flex flex-column mr-4">
                        {selectedContact ? (<button className="companydownload" onClick={csvDownloadHandler} disabled={isLowViewCredits || selectedContact ? sampleDownloadedIds.includes(selectedContact.id) : null}>Download</button>) : (
                          <button className="companydownload">
                            Download
                          </button>
                        )}
                      </div>
                    </div>
                    <div id="hideDownload">
                      <CSVLink
                        data={downloadedRows}
                        headers={headers}
                        ref={csvLinkRef}
                        filename="Company-export.csv"
                      />
                    </div>

                    <div className="">
                      <div className="row mb-3">
                        <div className="col-6">
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Company Name</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_company_name)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Address</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_address_street)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">City</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_address_city)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">State</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_address_state)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Zipcode</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? selectedContact.company_address_zipcode
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Country</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_address_country)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                        </div>
                        {/* <div className="vl"></div> */}
                        <div className="col-6">
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Company Industries</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_industries)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Website</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href={
                                  selectedContact
                                    ? selectedContact.company_website.startsWith("http")
                                      ? selectedContact.company_website
                                      : `https://${selectedContact.company_website}`
                                    : "#"
                                }
                                  target={selectedContact?.company_website ? "_blank" : ""}
                                  style={{ cursor: selectedContact?.company_website ? "pointer" : "" }}>
                                  {selectedContact
                                    ? selectedContact.company_website
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Company size</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? selectedContact.company_employee_size
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Revenue</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? selectedContact.company_annual_revenue_amount
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>

                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">SIC Code</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact ? selectedContact.company_sic_code : ""}
                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Company Phone</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact ? selectedContact.company_phone_1 : ""}
                                </a>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <Alert data={defaultErrorMsg} />
        </>
      )}
    </>
  );
};

export default ContactModal;

export const updateUserCreadits = async () => {
  //alert('updateUserCreadits');

  /* const requestBody = {
     total_balance_credit: totalBalance,
     total_balance_contact_view: totalViewCredit,
   };*/

  /*const response = AxiosPostBearer(ApiName.updateCreadits, requestBody, token)
    .then(function (response) {
      if (response.status == 200) {
        // alert('susccess');
      }
    })
    .catch(function (errors) {
      // console.log(errors);
    });*/
};
export const selectedRecordsIds = (selectedIds, token) => {
  //alert('selectedRecordsIds');

  const requestBody = {
    ids: selectedIds,
  };

};
