import React, { useEffect, useRef, useState } from "react";
import AdminHeader from "../../layouts/AdminHeader.js";
import S from "../../assets/css/layouts/admin-header.module.css";
import { useNavigate } from "react-router-dom";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import {
  postWithToken,
  PostWithTokenNoCache,
} from "../../../customer/common-files/ApiCalls.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import {
  axiosPost,
  PostReqWithTokenNoCache,
  AxiosPostBearer,
} from "../../../customer/common-files/ApiCalls.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import loadingGif from "../../../customer/assests/waiting.gif";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import Alert from "../../../customer/common-files/alert.js";

const FreetrailUsers = (props) => {
  const close_invoice_popup = useRef();
  const {
    foundCounts,
    pageNumber,
    dataPagi,
    loadingCount,
    paginationDataCount,
  } = props;
  const [foundContacts, setFoundContacts] = useState(foundCounts);
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [adminDetails, setAdminDetails] = useState(
    JSON.parse(localStorage.getItem("user"))
  );
  const [message, setMessage] = useState(false);
  const [updateActivate, setUpdateActivate] = useState(false);
  const itemsPerPage = 10; // You can adjust the number of items per page here
  const [currentPage, setCurrentPage] = useState(1);
  const [startFrom, setStartFrom] = useState(1);

  const [formErrors, setFormErrors] = useState({});
  const [allFeilads, setAllFields] = useState(false);
  const [switchUserData, setSwitchuserData] = useState({
    customer_email: "",
    amount_paid: "",
    customer_id: "",
    invoice_id: "",
    customer_name: "",
    status: "",
    paid: "",
    comments: "",
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  const priceData = {
    access_database: "",
    custom_filter: "",
    data_fields: "",
    export: "",
    no_of_contact_views: "",
    no_of_users: "",
    price: "",
    verified_email_phone: "",
  };
  const [pricePlan, setPricePlan] = useState(priceData);
  const [selectedPlan, setSelectedPlan] = useState(0);
  const [promocodeInvalid, setPromocodeInvalid] = useState(true);
  const [promocode, setPromocode] = useState();
  const [inputDisable, setInputDisable] = useState(false);
  const [btnVisible, setBtnVisible] = useState(false);
  const [promocodeMsg, setPromocodeMsg] = useState(false);
  const [error, setError] = useState(false);
  const [validPrmocode, setValidPrmocode] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const getPricePlansDetails = async () => {
    try {
      let params = JSON.stringify({
        method: "GET",
      });
      const res = await PostWithTokenNoCache(ApiName.getPackegeDetails, params);
      if (res && "status" in res) {
        if (res.status == 200) {
          let jsonArray = res.data.data ? JSON.parse(res.data.data) : [];
          setPricePlan(jsonArray);
          setLoading(false);
        }
      }
    } catch (error) {
      // Set pricePlan to empty array on error to prevent filter issues
      setPricePlan([]);
      setLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
    }
  };
  useEffect(() => {
    postData();
  }, [currentPage, searchQuery]);

  const postData = async () => {
    try {
      searchQuery ? setLoading(false) : setLoading(true);
      const params = JSON.stringify({
        method: "POST",
        page: searchQuery ? 1 : currentPage,
        pageSize: searchQuery ? 100000 : itemsPerPage,
        searchParams: {
          userRole: "customer",
          planName: "freemium",
          planStatus: "active",
          email: searchQuery || "",
        },
        sortBy: "DESC",
      });

      const res = await PostWithTokenNoCache(
        ApiName.getAllUserPlansPagination,
        JSON.parse(params)
      );

      if (res?.status === 200) {
        let data = JSON.parse(res.data.data);
        const all_users = data["all_user"];
        setTotalCount(data["all_user"]["totalItems"]);
        setTotalPages(data["all_user"]["totalPages"]);
        setData(data["all_user"]["items"]);
        getPricePlansDetails();
      }
    } catch (errors) {
      // Set data to empty array on error
      setData([]);
      setLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(errors?.response?.data?.message || "Failed to fetch user data");
      setDefaultAlert(true);
    }
  };

  const onChangeHandler = (e) => {
    const { name, value } = e.target;

    // Validate the field based on its name
    let error = "";
    switch (name) {
      case "amount_paid":
        if (!value) {
          error = "Amount is required.";
        } else if (isNaN(value) || parseFloat(value) <= 0) {
          error = "Invalid amount.";
        }
        break;
      case "customer_id":
        if (!value) {
          error = "Customer id is required.";
        }
        break;
      case "invoice_id":
        if (!value) {
          error = "invoice id is required.";
        }
        break;
      case "price_id":
        if (!value) {
          error = "price id is required.";
        }
        break;
      case "comments":
        if (!value) {
          error = "comment is required.";
        }
        break;
      default:
        break;
    }

    // Update the formErrors state
    setFormErrors({
      ...formErrors,
      [name]: error,
    });

    // Update the switchUserData state
    setSwitchuserData({
      ...switchUserData,
      [name]: value,
    });
  };

  const applyCoupon = async (user_id) => {
    if (promocode) {
      const params = {
        coupon_id: promocode,
        user_id,
        user_plan_name: selectedPlan && selectedPlan[0]?.package_name,
      };
      try {
        let response = await PostWithTokenNoCache(
          ApiName.oneTimeCoupon,
          params
        );
        if (response && "status" in response) {
          if (
            response.data.status == 200 &&
            response.data.message == "Coupon Not Found"
          ) {
            setError("Invalid code");
            setPromocodeInvalid(false);
            setBtnVisible(false);
            setPromocodeMsg("Coupon Not Found");
            setLoading(false);
          } else if (
            response.data.status == 200 &&
            response.data.message == "Valid Coupon"
          ) {
            return true;
          } else {
            setError(response.response.data.message);
            setPromocodeMsg(response.response.data.message);
            setPromocodeInvalid(false);
            setBtnVisible(false);
            setLoading(false);
          }
        }
      } catch (errors) {
        setError(errors.response.data.message);
        setPromocodeMsg(errors.response.data.message);
        setPromocodeInvalid(false);
        setBtnVisible(false);
        setLoading(false);
      }
    }
  };

  // switch to app sumo plan
  const onSubmitHandlerAppSumo = async (e) => {
    e.preventDefault();
    if (
      switchUserData.amount_paid &&
      switchUserData.customer_id &&
      switchUserData.invoice_id
    ) {
      const dataPost = {
        email_id: switchUserData.customer_email,
        package_id: selectedPlan && selectedPlan[0]?.id,
      };
      let createOrderRes = await createOrder(dataPost);
      if (createOrderRes) {
        await createUserPlan(createOrderRes.userId, createOrderRes.orderId);
        let applyCouponRes = await applyCoupon(createOrderRes.userId);
        if (applyCouponRes) {
          let sendUpgradeMailRes = await sendUpgradeMail();
          if (sendUpgradeMailRes) {
            closeButton();
            setSelectedPlan(0);
            close_invoice_popup.current.click();
            setLoading(false);
            const updatedRows = data.filter(
              (row) => row.email !== switchUserData.customer_email
            );
            setData(updatedRows);
            setAllFields(false);
          }
        }
      }
    } else {
      setAllFields(true);
    }
  };

  // switch super saver or custom plan
  const onSubmitHandler = async (e) => {
    e.preventDefault();
    if (
      switchUserData.amount_paid &&
      switchUserData.customer_id &&
      switchUserData.invoice_id
    ) {
      const dataPost = {
        email_id: switchUserData.customer_email,
        package_id: selectedPlan && selectedPlan[0]?.id,
      };
      let createOrderRes = await createOrder(dataPost);
      if (createOrderRes) {
        let createUserPlanRes = await createManualPayment();
        if (createUserPlanRes) {
          let sendUpgradeMailRes = await sendUpgradeMail();
          if (sendUpgradeMailRes) {
            closeButton();
            setSelectedPlan(0);
            close_invoice_popup.current.click();
            setLoading(false);
            const updatedRows = data.filter(
              (row) => row.email !== switchUserData.customer_email
            );
            setData(updatedRows);
            setAllFields(false);
          }
        }
      }
    } else {
      setAllFields(true);
    }
  };

  const onSubmitCoupon = async (event) => {
    event.preventDefault();
    if (promocode) {
      let res = await applyCoupon(switchUserData.customer_id);
      if (res) {
        close_invoice_popup.current.click();
        closeButton();
        setSelectedPlan(0);
        setLoading(false);
        const updatedRows = data.filter(
          (row) => row.email !== switchUserData.customer_email
        );
        setData(updatedRows);
        setAllFields(false);
      }
    }
  };

  //create user plan
  const createUserPlan = async (userId, orderId) => {
    const dataPostUserPlan = {
      user_id: userId,
      user_plan_name: selectedPlan && selectedPlan[0]?.package_name,
      order_id: orderId,
      status: "active",
      total_assigned_credit: selectedPlan && selectedPlan[0]?.export,
      total_balance_credit: selectedPlan && selectedPlan[0]?.export,
      total_assigned_contact_view:
        selectedPlan && selectedPlan[0]?.no_of_contact_views,
      total_balance_contact_view:
        selectedPlan && selectedPlan[0]?.no_of_contact_views,
      comments: switchUserData.comments,
    };

    let res = await PostWithTokenNoCache(ApiName.createUserPlan, {
      ...dataPostUserPlan,
    });

    if (res && "status" in res) {
      if (res.data.status == 200) {
        return true;
      } else {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg("Sorry.! could not create the order");
        setDefaultAlert(true);
      }
    }
  };

  const resetPassword = (id) => {
    navigate("/admin/reset-password/" + id);
  };
  const deviceLogin = (id) => {
    navigate("/admin/user-action/" + id);
  };
  const deviceHistory = (id) => {
    navigate("/admin/device-history/" + id);
  };
  const userInsight = (id) => {
    const userPlans = data
      .filter((item) => item.userId == id)
      .map((item) => item.userPlanName);
    navigate(
      "/admin/user-insights/" +
      JSON.stringify({ id: id, userPlans: userPlans[0] })
    );
  };
  const paymentHistory = (id) => {
    navigate("/admin/payment-history/" + id);
  };

  const switchStatus = (number, email) => {
    // Here, if number === 1, then deactivate the user else if 2, activate
    const newStatus = number === 1 ? "deactivate" : "active";

    // Display a confirmation dialog before proceeding with the action
    const confirmed = window.confirm(
      `Are you sure you want to ${newStatus} this user?`
    );

    if (!confirmed) {
      // User canceled the delete operation
      return;
    }

    var myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    myHeaders.append("Authorization", `Bearer ${adminDetails.token}`);

    var raw = JSON.stringify({
      email: email,
      status: newStatus,
    });

    var requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
    };

    fetch(ApiName.activeOrDeactive, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result.status === 200) {
          // Update the user status in the current data
          const newData = data.map((item) => {
            if (item.email === email) {
              return {
                ...item,
                userStatus: newStatus == "deactivate" ? null : newStatus,
              };
            }
            return item;
          });
          setData(newData); // Update the state with the modified data
        }
      })
      .catch((errors) => {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
      });
  };

  const closeButton = async () => {
    setAllFields(false);
    setSwitchuserData({
      comments: "",
      amount_paid: "",
      customer_id: "",
      invoice_id: "",
      customer_name: "",
      status: "",
      paid: "",
      price_id: "",
    });
  };

  //upgrade mail
  const sendUpgradeMail = async () => {
    let params = {
      username: switchUserData.customer_name,
      email: switchUserData.customer_email,
      path: "upgrade-to-preemium",
    };
    let res = await PostWithTokenNoCache(ApiName.upgradeEmail, { ...params });
    if (res && "status" in res) {
      if (res.data.status == 200) {
        return true;
      } else {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg("Sorry.! could not create user plan");
        setDefaultAlert(true);
      }
    }
  };

  //super saver user plan creation
  const createManualPayment = async () => {
    let detail_param = switchUserData;
    detail_param.amount_paid = detail_param.amount_paid * 100;
    let params = {
      ...detail_param,
    };
    let res = await PostWithTokenNoCache(ApiName.switchFreemiumToPreemium, {
      ...params,
    });
    if (res && "status" in res) {
      if (res.data.status == 200) {
        return true;
      } else {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg("Sorry.! could not create user plan");
        setDefaultAlert(true);
      }
    }
  };

  //create order
  async function createOrder(data) {
    try {
      const res = await PostWithTokenNoCache(ApiName.createOrder, { ...data });
      if (res && "status" in res) {
        if (res.data.status == 200) {
          //freemium user create plan
          let orderObj = JSON.parse(res.data.data);
          let orderId = orderObj?.orderId;
          let userId = orderObj?.userId;
          return { orderId: orderId, userId: userId };
        } else {
          setLoading(false);
          setButtonType("error");
          setDefaultErrorMsg("Sorry.! could not create the order");
          setDefaultAlert(true);
        }
      }
    } catch (errors) {
      setLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(errors.response.data.message);
      setDefaultAlert(true);
    }
  }

  const switchToSuperSaver = (item) => {
    setSelectedPlan(0);
    setMessage(false);
    setSwitchuserData({
      customer_id: item.userId,
      customer_email: item.email,
      customer_name: item.firstName,
      status: "paid",
      paid: true,
    });
  };

  // Add loginAsCustomer function
  const loginAsCustomer = (userId, email) => {
    try {
      // Clear localStorage but set the loginAsCustomer flag first
      localStorage.setItem('loginAsCustomer', 'true');
      localStorage.removeItem('user'); // Remove admin user data
      sessionStorage.clear();
      
      // Use navigate instead of window.location.href
      navigate(`/?user_id=${userId}`);
    } catch (error) {
      console.error("Error during login as customer setup:", error);
      alert("There was an error logging in as customer. Please try again.");
    }
  };

  const DropdownActions = ({
    item,
    switchStatus,
    resetPassword,
    deviceLogin,
    deviceHistory,
    userInsight,
  }) => (
    <div className="dropdown">
      <button
        className="btn btn-secondary dropdown-toggle"
        type="button"
        id={`dropdownActions_${item.userId}`}
        data-toggle="dropdown"
        aria-haspopup="true"
        aria-expanded="false"
      >
        Actions
      </button>
      <div
        className="dropdown-menu"
        aria-labelledby={`dropdownActions_${item.userId}`}
      >
        {item.userStatus ? (
          <>
            <button
              className={S.btndanger + " dropdown-item"}
              onClick={() => switchStatus(1, item.email)}
            >
              Inactive
            </button>
          </>
        ) : (
          <>
            <button
              className={S.btndanger + " dropdown-item"}
              onClick={() => switchStatus(2, item.email)}
            >
              Active
            </button>
          </>
        )}
        <button
          className={S.swichtuser + " dropdown-item"}
          data-toggle="modal"
          data-target="#exampleModal"
          onClick={() => switchToSuperSaver(item)}
        >
          Switch User Plan
        </button>
        <button
          className={S.swichtuser + " dropdown-item"}
          onClick={() => resetPassword(item.email)}
        >
          Reset Password
        </button>
        <button
          className={S.swichtuser + " dropdown-item"}
          onClick={() => deviceLogin(item.userId)}
        >
          Device login
        </button>
        <button
          className={S.swichtuser + " dropdown-item"}
          onClick={() => deviceHistory(item.userId)}
        >
          Login/Logout History
        </button>
        <button
          className={S.swichtuser + " dropdown-item"}
          onClick={() => userInsight(item.userId)}
        >
          User Insight
        </button>
        <button
          className={S.swichtuser + " dropdown-item"}
          onClick={() => paymentHistory(item.userId)}
        >
          Payment History
        </button>
        <button
          className={S.swichtuser + " dropdown-item"}
          onClick={() => loginAsCustomer(item.userId, item.email)}
        >
          Login As Customer
        </button>
      </div>
    </div>
  );

  const ActionButtons = ({
    item,
    switchStatus,
    resetPassword,
    deviceLogin,
    deviceHistory,
    userInsight,
    paymentHistory,
  }) => (
    <>
      <DropdownActions
        item={item}
        switchStatus={switchStatus}
        resetPassword={resetPassword}
        deviceLogin={deviceLogin}
        deviceHistory={deviceHistory}
        userInsight={userInsight}
        paymentHistory={paymentHistory}
      />
    </>
  );

  const handleSelectChange = (event) => {
    setValidPrmocode(false);
    setError(false);
    setBtnVisible(false);
    setPromocode("");
    setPromocodeMsg("");
    setPromocodeInvalid(false);
    setInputDisable(false);

    const packageID = event.target.value;
    // Ensure pricePlan is an array before filtering
    const planArray = Array.isArray(pricePlan) ? pricePlan : [];
    let selectPlan = planArray.filter((value) => value?.id == packageID);
    setSelectedPlan(selectPlan);

    // Update the switchUserData state
    setSwitchuserData({
      ...switchUserData,
      ["amount_paid"]: "",
    });

    setFormErrors({
      ...formErrors,
      ["amount_paid"]: "",
    });
  };

  const onChangePromocodeHandler = (event) => {
    let promocode = event.target.value;
    setPromocode(promocode);

    const minLength = 2;
    if (promocode.length == 0) {
      setPromocodeInvalid(true);
    } else if (minLength <= promocode.length) {
      setError(true);
      setBtnVisible(true);
      setPromocodeInvalid(true);
    } else {
      setError(false);
      setBtnVisible(false);
      setPromocodeInvalid(false);
    }
  };

  const formatteDate = (datetime) => {
    const date = new Date(datetime);
    return `${date.getFullYear()}-${(date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
  };

  return (
    <div className={S.app}>
      <AdminHeader />

      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "300px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div style={{ width: "100%", padding: "0 10px 0 0" }}>
              <h5 className={S.FreemiumUsers}>Freemium Users</h5>
              <div className={S.content}></div>
              <aside className={S.right_sidebar}>
                <div className="d-flex flex-row justify-content-between pl-2">
                  <div>
                    <div className="d-flex flex-row">
                      <div>
                        <h5 className={S.totla_users}>Total users:</h5>
                      </div>
                      <div>
                        <p className={S.datalegnth}>{totalCount}</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="admin-search">
                      <input
                        type="text"
                        placeholder="Search by Email"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className={S.searchbyemail}
                      />
                    </div>
                  </div>
                </div>

                <table className="admin-table text-center ">
                  <thead>
                    <tr className="table-active">
                      <th className={S.tableheaderspace}>Sl No</th>
                      <th className={S.tableheaderspace}>Registered On</th>
                      <th className={S.tableheaderspace}>First Name</th>
                      <th className={S.tableheaderspace}>Last Name</th>
                      <th className={S.tableheaderspaces}>Email</th>
                      <th className={S.tableheaderspace}>Phone</th>
                      <th className={S.tableheaderspace}>Package Type</th>
                      <th className={S.tableheaderspace}>Account Status</th>
                      <th className={S.tableheaderspace}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((item, i) => (
                      <tr key={i + 1} className={S.tablelight}>
                        <td className={S.tabledataspaceing}>
                          {(currentPage - 1) * 10 + i + 1}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {formatteDate(item.userCreatedAt)}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {item.firstName}
                        </td>
                        <td className={S.tabledataspaceing}>{item.lastName}</td>
                        <td className={S.tabledataspaceingg}>{item.email}</td>
                        <td className={S.tabledataspaceing}>{item.phone}</td>

                        <td className={S.tabledataspaceing}>
                          {item.userPlanName}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {item.userStatus ? item.userStatus : "Inactive"}
                        </td>
                        <td className={S.tabledataspaceing}>
                          <ActionButtons
                            item={item}
                            switchStatus={switchStatus}
                            resetPassword={resetPassword}
                            deviceLogin={deviceLogin}
                            deviceHistory={deviceHistory}
                            userInsight={userInsight}
                            paymentHistory={paymentHistory}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </aside>
            </div>
          ) : (
            <div className="mx-auto mt-5" style={{ display: loading }}>
              <img
                src={loadingGif}
                alt="Loading"
                className="loader"
                width="400"
              />
            </div>
          )}
        </div>
      </div>

      {!searchQuery && (
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalPages}
          pageSize={1}
          onPageChange={(page) => {
            setCurrentPage(page);
          }}
        />
      )}
      {!loading ? (
        <div
          className="modal fade"
          id="exampleModal"
          tabIndex="-1"
          aria-labelledby="exampleModalLabel"
          aria-hidden="true"
        >
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modalheaderswitch">
                <button
                  className={S.swichtuser}
                  data-toggle="modal"
                  data-target="#exampleModal"
                  ref={close_invoice_popup}
                  style={{ display: "none" }}
                >
                  Switch Plan
                </button>

                <button
                  type="button"
                  className="close"
                  data-dismiss="modal"
                  aria-label="Close"
                  onClick={() => {
                    closeButton();
                  }}
                >
                  <span aria-hidden="true">&times;</span>
                </button>

                <h5 className={S.modaltitleswitch}>Switch User Plan</h5>
              </div>

              {message == true ? (
                <p className={S.successfullyy}>Successfully Added</p>
              ) : (
                ""
              )}
              {allFeilads == true ? (
                <p className={S.pleaseenter}>Please Enter All Fields</p>
              ) : (
                ""
              )}
              <div className="modal-body">
                <div className="row">
                  <div className="col-md-6">
                    <label>Select plan</label>
                    <select
                      className="browser-default custom-select"
                      style={{ margin: "0px 0 0 0" }}
                      onChange={handleSelectChange}
                      value={selectedPlan ? selectedPlan[0]?.id : ""}
                    >
                      <option value="">Select Plan</option>
                      {Array.isArray(pricePlan) && pricePlan
                          .filter((item) => item && item.package_name !== "freemium")
                          .map((item, index) => (
                            <option key={index} value={item.id}>
                              {item.package_name}
                            </option>
                          ))}
                    </select>
                  </div>

                  <div className=" col-md-6 ">
                    <div className="form-group">
                      <label className="label_for_form">Customer Email</label>
                      <input
                        type="email"
                        className="form-control"
                        aria-describedby="customerHelp"
                        name="customer_email"
                        onChange={onChangeHandler}
                        value={switchUserData.customer_email}
                        readOnly={true}
                      />
                    </div>
                  </div>

                  <div className=" col-md-6 ">
                    <label className="label_for_form">Actual amount</label>
                    <div className="form-group">
                      <input
                        type="text"
                        className="form-control"
                        aria-describedby="AmountHelp"
                        name="actual_amount"
                        value={
                          selectedPlan && selectedPlan[0]?.price
                            ? selectedPlan[0]?.price
                            : ""
                        }
                        autoComplete="off"
                        readOnly={true}
                      />
                    </div>
                  </div>

                  <div className=" col-md-6 ">
                    <label className="label_for_form">Amount Paid</label>
                    <div className="form-group">
                      <input
                        type="text"
                        className="form-control"
                        aria-describedby="AmountHelp"
                        name="amount_paid"
                        onChange={onChangeHandler}
                        value={switchUserData.amount_paid}
                        autoComplete="off"
                      />

                      {/* here */}
                      {formErrors.amount_paid && (
                        <div className={S.error_message}>
                          {formErrors.amount_paid}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="col-md-6">
                    <label className="label_for_form">Customer Id</label>
                    <div className="form-group">
                      <input
                        type="text"
                        className="form-control"
                        aria-describedby="idHelp"
                        name="customer_id"
                        onChange={onChangeHandler}
                        value={switchUserData.customer_id}
                        autoComplete="off"
                        readOnly={true}
                      />
                      {formErrors.customer_id && (
                        <div className={S.error_message}>
                          {formErrors.customer_id}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="col-md-6">
                    <label className="label_for_form">Invoice no</label>
                    <div className="form-group">
                      <input
                        type="text"
                        className="form-control"
                        aria-describedby="invoiceHelp"
                        name="invoice_id"
                        onChange={onChangeHandler}
                        value={switchUserData.invoice_id}
                        autoComplete="off"
                      />
                      {formErrors.invoice_id && (
                        <div className={S.error_message}>
                          {formErrors.invoice_id}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="col-md-6">
                    <label className="label_for_form">Customer Name</label>
                    <div className="form-group">
                      <input
                        type="text"
                        className="form-control"
                        aria-describedby="custoHelp"
                        name="customer_name"
                        onChange={onChangeHandler}
                        value={switchUserData.customer_name}
                      />
                    </div>
                  </div>

                  <div className="col-md-6">
                    <label className="label_for_form">Status</label>
                    <div className="form-group">
                      <input
                        type="text"
                        className="form-control"
                        aria-describedby="statusHelp"
                        name="customer_name"
                        onChange={onChangeHandler}
                        value={switchUserData.status}
                      />
                    </div>
                  </div>

                  <div className="col-md-6 mb-3">
                    <label className="label_for_form">Price Id</label>
                    <div className="form-group">
                      <input
                        type="text"
                        className="form-control"
                        aria-describedby="priceHelp"
                        name="price_id"
                        onChange={onChangeHandler}
                        value={switchUserData.price_id}
                        autoComplete="off"
                      />
                      {formErrors.price_id && (
                        <div className={S.error_message}>
                          {formErrors.price_id}
                        </div>
                      )}
                    </div>
                    <input
                      type="hidden"
                      className="form-control"
                      aria-describedby="paidHelp"
                      name="paid"
                      value="true"
                    />
                    {/* </div> */}
                  </div>
                  <div className="col-md-6 mb-3">
                    <label>Comment</label>
                    <textarea
                      className="form-control"
                      onChange={onChangeHandler}
                      value={switchUserData.comments}
                      name="comments"
                    ></textarea>
                  </div>
                  <div
                    className="col-md-6 mb-3"
                    style={{
                      display:
                        selectedPlan &&
                          selectedPlan[0]?.package_name === "APP_SUMO_59"
                          ? "block"
                          : "none",
                    }}
                  >
                    <div className="form-group">
                      <label>Coupon</label>
                      <div
                        className={
                          !promocodeInvalid
                            ? "input-with-icon-text"
                            : "input-with-icon"
                        }
                      >
                        {!promocodeInvalid ? (
                          <input
                            type="text"
                            className="invalid-input-text"
                            name="promocode"
                            onChange={onChangePromocodeHandler}
                            value={promocode}
                            disabled={inputDisable ? "disabled" : ""}
                            autoFocus
                          />
                        ) : (
                          <></>
                        )}
                      </div>

                      {promocodeInvalid ? (
                        <input
                          type="text"
                          className="form-control input-field"
                          name="promocode"
                          onChange={onChangePromocodeHandler}
                          value={promocode}
                          disabled={inputDisable ? "disabled" : ""}
                          autoFocus
                        />
                      ) : (
                        <></>
                      )}

                      <span className="email-error-message">
                        {error && <p>{error}</p>}
                      </span>
                    </div>
                  </div>

                  <div
                    className="col-md-6"
                    style={{
                      display:
                        selectedPlan &&
                          selectedPlan[0]?.package_name === "APP_SUMO_59"
                          ? "block"
                          : "none",
                    }}
                  >
                    <div className="form-group">
                      <label></label>
                    </div>
                  </div>
                </div>
                <div className="col-md-6 mb-3">
                  {!promocode &&
                    selectedPlan &&
                    selectedPlan[0]?.package_name !== "APP_SUMO_59" ? (
                    <button
                      type="submit"
                      className={
                        selectedPlan &&
                          selectedPlan[0]?.id &&
                          switchUserData.amount_paid &&
                          switchUserData.amount_paid > 0 &&
                          switchUserData.customer_id &&
                          switchUserData.invoice_id &&
                          switchUserData.price_id &&
                          !message &&
                          switchUserData.comments
                          ? `${S.adminsubmit}`
                          : `${S.adminsubmit} cust-disabled`
                      }
                      onClick={onSubmitHandler}
                    >
                      Submit
                    </button>
                  ) : promocode && promocodeMsg === "" ? (
                    <button
                      type="button"
                      className={
                        selectedPlan &&
                          selectedPlan[0]?.id &&
                          switchUserData.amount_paid &&
                          switchUserData.amount_paid > 0 &&
                          switchUserData.customer_id &&
                          switchUserData.invoice_id &&
                          switchUserData.price_id &&
                          !message &&
                          promocode &&
                          switchUserData.comments
                          ? `${S.adminsubmit}`
                          : `${S.adminsubmit} cust-disabled`
                      }
                      onClick={onSubmitHandlerAppSumo}
                    >
                      Submit
                    </button>
                  ) : promocodeMsg !== "" ? (
                    <button
                      type="button"
                      className={
                        selectedPlan &&
                          selectedPlan[0]?.id &&
                          switchUserData.amount_paid &&
                          switchUserData.customer_id &&
                          switchUserData.invoice_id &&
                          switchUserData.price_id &&
                          !message &&
                          promocode &&
                          switchUserData.comments
                          ? `${S.adminsubmit}`
                          : `${S.adminsubmit} cust-disabled`
                      }
                      onClick={
                        selectedPlan &&
                        selectedPlan[0]?.package_name === "APP_SUMO_59" &&
                        onSubmitCoupon
                      }
                    >
                      Submit
                    </button>
                  ) : (
                    <></>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <></>
      )}
      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};
export default FreetrailUsers;
