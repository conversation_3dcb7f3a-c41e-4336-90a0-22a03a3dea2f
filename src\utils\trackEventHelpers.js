import Analytics from './analyticsTracking';

/**
 * Creates a trackEvent helper function for a component
 * This helper implements deduplication, consistent properties, and session tracking
 * 
 * @param {Object} userData - User data from the component (can be null)
 * @param {Object} additionalContext - Any additional context to include in all events
 * @returns {Function} A trackEvent function to use in the component
 */
export const createTrackEvent = (userData = {}, additionalContext = {}) => {
  // Create a session ID if one doesn't exist
  if (!sessionStorage.getItem('session_id')) {
    sessionStorage.setItem('session_id', `session_${Date.now()}`);
  }
  
  // Return a trackEvent function configured for this component
  return (eventName, eventProps = {}) => {
    // Create a unique key based on event name and timestamp
    const eventKey = `${eventName}_${Date.now()}`;
    
    // Check if this exact event was tracked in the last second (prevents duplicates)
    if (sessionStorage.getItem(eventKey)) {
      return; // Duplicate event - don't track
    }
    
    // Set a temporary flag to prevent duplicate events (expires after 1 second)
    sessionStorage.setItem(eventKey, "true");
    setTimeout(() => {
      sessionStorage.removeItem(eventKey);
    }, 1000);
    
    // Use the centralized tracking method
    Analytics.trackEventWithDeduplication(
      eventName, 
      { ...additionalContext, ...eventProps }, 
      userData
    );
  };
};

/**
 * Starts tracking the duration of an interaction
 * 
 * @param {string} interactionName - Name of the interaction (used as key in sessionStorage)
 * @returns {void}
 */
export const startInteractionTimer = (interactionName) => {
  sessionStorage.setItem(`${interactionName}_start_time`, new Date().toISOString());
};

/**
 * Gets the duration of an interaction in seconds
 * 
 * @param {string} interactionName - Name of the interaction (used as key in sessionStorage)
 * @returns {number} Duration in seconds or 0 if no start time was recorded
 */
export const getInteractionDuration = (interactionName) => {
  const startTimeStr = sessionStorage.getItem(`${interactionName}_start_time`);
  if (!startTimeStr) return 0;
  
  const startTime = new Date(startTimeStr);
  const endTime = new Date();
  
  return Math.round((endTime - startTime) / 1000);
};

/**
 * Gets user data from localStorage
 * 
 * @returns {Object} User data or empty object if not found
 */
export const getUserData = () => {
  try {
    return JSON.parse(localStorage.getItem('user')) || {};
  } catch (error) {
    console.warn('Error getting user data:', error);
    return {};
  }
};

/**
 * Gets the user's membership type
 * 
 * @returns {string} Membership type or 'unknown' if not found
 */
export const getUserMembership = () => {
  return localStorage.getItem('userMembership') || 'unknown';
};

/**
 * Example usage in a component:
 * 
 * import { createTrackEvent, startInteractionTimer, getInteractionDuration } from '../../utils/trackEventHelpers';
 * 
 * function MyComponent() {
 *   const userData = JSON.parse(localStorage.getItem('user')) || {};
 *   const trackEvent = createTrackEvent(userData, { component: 'MyComponent' });
 *   
 *   const handleOpen = () => {
 *     startInteractionTimer('myForm');
 *     trackEvent('Form Opened', { source: 'button' });
 *   };
 *   
 *   const handleClose = () => {
 *     trackEvent('Form Closed', { 
 *       duration_seconds: getInteractionDuration('myForm') 
 *     });
 *   };
 * }
 */ 