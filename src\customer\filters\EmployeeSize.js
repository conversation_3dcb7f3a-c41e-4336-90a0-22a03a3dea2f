import React, { useState, useEffect, useContext } from 'react';
import { postWithToken } from '../common-files/ApiCalls.js';
import DashboardContext from '../common-files/ContextDashboard.js';
import { ApiName } from '../common-files/ApiNames';
import UseTabStore from '../common-files/useGlobalState.js';

const Employee = () => {
	// State to store selected values 
	const { dataDC, setDataDC } = useContext(DashboardContext);

	const {
		employeeSizeCheckedBoxes,
		resetPage,
		companyEmployeeSize,
		setSelectedCompanyEmployeeSize,
		setCurrentPage,
		setEmployeeSizeCheckedBoxes,
		setIsFiltered
	} = UseTabStore();
	const [employeSizeData, setEmployeSizeData] = useState([]);

	function handleFilters(value) {

		let updatedCompanyEmployeeSize = {};

		// Update the company names based on selected options
		value.forEach((item, index) => {
			updatedCompanyEmployeeSize[index] = item;
		});
		setDataDC({ ...dataDC, ["company_employee_size"]: value });
		setSelectedCompanyEmployeeSize(updatedCompanyEmployeeSize);
	}

	useEffect(() => {
		//check for selected data and make checkbox true
		const arrCompanyEmployeeSize = Object.values(companyEmployeeSize);
		if (arrCompanyEmployeeSize.length > 0) setEmployeeSizeCheckedBoxes(arrCompanyEmployeeSize);
	}, []);

	useEffect(() => {
		if ("company_employee_size" in dataDC) setEmployeeSizeCheckedBoxes(dataDC.company_employee_size);
	}, [dataDC, resetPage]);
	useEffect(() => {
		//set global state and update filter
		handleFilters(employeeSizeCheckedBoxes);
	}, [employeeSizeCheckedBoxes]);

	useEffect(() => {
		const url = ApiName.predefinedValuesFilter;
		const dataEmpSize = { "url": "api/get-emp-size" };
		const fetchData = async (data) => {
			// You can await here
			const res = await postWithToken(url, data);
			if (res && "status" in res) {
				if (res.data.status == 200) {
					let titlesObj = JSON.parse(JSON.parse(res.data.data));
					if (data.url == "api/get-emp-size") {
						titlesObj.sort((a, b) => a.id - b.id);
						let mapped = titlesObj.map(v => JSON.parse(`{ "value": "${v.emp_size}" ,"label": "${v.emp_size}" }`));
						setEmployeSizeData(mapped);
					}
				}
			}
		}
		fetchData(dataEmpSize);
	}, []);

	const handleCheckboxChange = (event) => {
		const checkboxValue = event.target.value;
		setCurrentPage(1);
		setIsFiltered(true);

		if (employeeSizeCheckedBoxes.includes(checkboxValue)) {
			setEmployeeSizeCheckedBoxes(employeeSizeCheckedBoxes.filter((checkbox) => checkbox !== checkboxValue));
		} else {
			setEmployeeSizeCheckedBoxes([...employeeSizeCheckedBoxes, checkboxValue]);
		}
	};
	const SearchEmployeSize = () => {
		return (
			<div className="d-flex flex-column">
				{employeSizeData && employeSizeData.map((checkbox, i) => (
					<label key={i} className="job-checkbox">&nbsp;
						<input
							key={"in_" + checkbox}
							type="checkbox"
							value={checkbox.label}
							checked={employeeSizeCheckedBoxes.includes(checkbox.label)}
							onChange={handleCheckboxChange}
						/>
						{checkbox.label}
					</label>
				))}
			</div>
		)
	}
	return (
		<div>
			<div className="paragraph">
				<button
					className="Job-Titles-1"
					type="button"
					data-toggle="collapse"
					data-target="#collapseExample1Emp"
					aria-expanded="false"
					aria-controls="collapseExample"
				>
					Select Employee Size<i className="fa fa-caret-down"></i>

				</button>
				<div className="collapse" id="collapseExample1Emp">
					<div className=" card-body-containerr" >
						<SearchEmployeSize />
					</div>
				</div>
			</div>
		</div>
	)
}

export default Employee;