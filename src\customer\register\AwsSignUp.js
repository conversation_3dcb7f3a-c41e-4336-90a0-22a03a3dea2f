import React, { useState, useEffect, useContext } from "react";
import "../../customer/assests/css/freetrialpayment.css";
import {
  Link,
  useNavigate,
  createSearchParams,
  useParams,
} from "react-router-dom";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";
import "../../customer/assests/css/password-eye.css";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import {
  axiosPost,
  AxiosPostBearer,
  PostWithTokenNoCache,
} from "../../customer/common-files/ApiCalls.js";
import Footer from "../layouts/Footer";
import { Helmet } from "react-helmet-async";
import DashboardContext from "../common-files/ContextDashboard.js";
import axios from "axios";
import {
  useGoogleLoginLogic,
  useLinkedInLoginLogic,
  useMicrosoftLoginLogic,
} from "../common-files/SocialMediaAuth.js";
import UseTabStore from "../common-files/useGlobalState.js";
import ReCAPTCHA from "react-google-recaptcha";
import GTM from '../common-files/GTM.js';

const AwsSignUp = () => {
  const gtmId = process.env.REACT_APP_GTM_ID;
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const [minLength, setMinLength] = useState(3);
  const [fname, setFname] = useState("");
  const [fnameErr, setFnameErr] = useState("");
  const [validFname, setValidFname] = useState(false);
  const [lname, setLname] = useState("");
  const [lnameErr, setLnameErr] = useState("");
  const [validLname, setValidLname] = useState(false);
  const [email, setEmail] = useState("");
  const [emailErr, setEmailErr] = useState("");
  const [validEmail, setValidEmail] = useState(false);
  const [phone, setPhone] = useState(null);
  const [phoneErr, setPhoneErr] = useState("");
  const [validPhone, setValidPhone] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  const [verificationCodeErr, setVerificationCodeErr] = useState("");
  const [validVerificationCode, setValidVerificationCode] = useState(false);
  const [verificationCodeMsg, setVerificationCodeMsg] = useState("");
  const [password, setPassword] = useState("");
  const [passwordErr, setPasswordErr] = useState("");
  const [validPassword, setValidPassword] = useState(false);
  const [cpassword, setCpassword] = useState("");
  const [cpasswordErr, setCpasswordErr] = useState("");
  const [validConfirmPassword, setValidConfirmPassword] = useState(false);
  const [validLength, setValidLength] = useState(false);
  const [validCase, setValidCase] = useState(false);
  const [validWord, setValidWord] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [validCheckBox, setValidCheckBox] = useState(false);
  const [fNameInvalidText, setFNameInvalidText] = useState(true);
  const [lNameInvalidText, setLNameInvalidText] = useState(true);
  const [emailInvalidText, setEmailInvalidText] = useState(true);
  const [phoneInvalidText, setPhoneInvalidText] = useState(true);
  const [verificationCodeInvalidText, setVerificationCodeInvalidText] =
    useState(true);
  const [passwordInvalidText, setPasswordInvalidText] = useState(true);
  const [confirmpasswordInvalidText, setConfirmPasswordInvalidText] =
    useState(true);
  const [loading, setLoading] = useState(false);
  const [loadingVEmail, setLoadingVEmail] = useState(false);
  const [loadingVCode, setLoadingVCode] = useState(false);
  const [isVerifiedEmail, setIsVerifiedEmail] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [sendVerificationCode, setSendVerificationCode] = useState(false);
  const [verifyEmail, setVerifyEmail] = useState(false);
  const { dataDC, setDataDC } = useContext(DashboardContext);
  const priceData = {
    access_database: "",
    custom_filter: "",
    data_fields: "",
    export: "",
    no_of_contact_views: "",
    no_of_users: "",
    price: "",
    verified_email_phone: "",
  };
  const [dollarData, setDollorData] = useState(priceData);
  const [seconds, setSeconds] = useState(600); // 10 minutes in seconds
  const [isActive, setIsActive] = useState(false);
  const [blockedDomain, setBlockedDomain] = useState([]);
  const [referralCode, setReferralCode] = useState("");
  const { setMarketplaceToken, marketplaceToken } = UseTabStore();
  const [captchValue, setCaptchValue] = useState("");
  const [companyName, setCompanyName] = useState("");

  const [passwordValidation, setPasswordValidation] = useState({
    uppercase: false,
    lowercase: false,
    number: false,
    specialChar: false,
  });
  // Get the value of the 'referralcode' parameter
  useEffect(() => {
    const url = window.location.href;
    function getUrlParameter(name) {
      name = name.replace(/[\[\]]/g, "\\$&");
      const regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)");
      const results = regex.exec(url);
      if (!results) return null;
      if (!results[2]) return "";
      return decodeURIComponent(results[2]);
    }

    // Usage
    const id = getUrlParameter("id");
    const referralCode = getUrlParameter("referralcode");
    setReferralCode(referralCode);
    if (id) {
      setMarketplaceToken(id);
    }
  }, []);

  useEffect(() => {
    let interval;

    if (isActive && seconds > 0) {
      interval = setInterval(() => {
        setSeconds((prevSeconds) => prevSeconds - 1);
      }, 1000);
    } else if (seconds === 0) {
      setIsActive(false);
      // You can perform an action when the timer reaches 0 here
    }

    return () => clearInterval(interval);
  }, [isActive, seconds]);

  useEffect(() => {
    const params = {
      method: "POST",
    };
    let result = axios
      .post(ApiName.activeDomainBlockList, params)
      .then(function (response) {
        // const token = JSON.parse(response.data.data);
        if (response.data.status == 200) {
          let data = JSON.parse(response.data.data);
          // Extract the "domain" property from each object and store in an array
          const domainArray = data.map((item) => item.domain);
          setBlockedDomain(domainArray);
        }
      });
  }, []);

  // Format the seconds into minutes and seconds
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  const handleCheckboxChange = (event) => {
    setIsChecked(event.target.checked);
    if (isChecked) {
      setValidCheckBox(false);
    } else {
      setValidCheckBox(true);
    }
  };

  const onChangeFname = (event) => {
    const specialChars = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~*********]/;
    let fname = event.target.value;
    setFname(fname);

    if (fname.length < 3) {
      setValidFname(false);
      setFnameErr("Enter minimum 3 characters");
      setFNameInvalidText(false);
    } else if (specialChars.test(fname)) {
      setValidFname(false);
      setFNameInvalidText(false);
      setFnameErr("Enter a valid first name, avoid using 123@$!");
    } else {
      setValidFname(true);
      setFnameErr("");
      setFNameInvalidText(true);
    }
  };
  const onChangeCompanyName = (event) => {
    let cname = event.target.value;
    setCompanyName(cname);
  };
  const onChangeLname = (event) => {
    let lname = event.target.value;
    setLname(lname);
    const specialChars = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~*********]/;
    if (lname.length < 1) {
      setValidLname(false);
      setLnameErr("Enter minimum 1 character");
      setLNameInvalidText(false);
    } else if (specialChars.test(lname)) {
      setValidLname(false);
      setLNameInvalidText(false);
      setLnameErr("Enter a valid last name, avoid using 123@$!");
    } else {
      setValidLname(true);
      setLnameErr("");
      setLNameInvalidText(true);
    }
  };
  const onChangeEmail = (event) => {
    let email = event.target.value;

    setEmail(email.toLowerCase());

    // Construct dynamic RegExp for disallowed domains
    const disallowedDomainsRegExp = new RegExp(
      `^.+@((?!${blockedDomain.join("|")}).)+$`
    );
    let result = disallowedDomainsRegExp.test(email);

    if (result) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      let checkDomain = emailRegex.test(email);

      if (!checkDomain) {
        setEmailErr("Please enter a valid business email address");
        setValidEmail(false);
        setEmailInvalidText(false);
      } else {
        setValidEmail(true);
        setEmailErr("");
        setEmailInvalidText(true);
      }
    } else {
      setEmailErr("Please enter a valid business email address");
      setValidEmail(false);
      setEmailInvalidText(false);
    }
  };
  function validatePhoneNumber(phoneNumber) {
    // Regular expression for a phone number with + sign and country code
    const phoneRegex = /^\+\d{1,}$/;

    // Test the phone number against the regular expression
    return phoneRegex.test(phoneNumber);
  }
  const onChangePhone = (event) => {
    let phone = event.target.value;
    // Remove non-numeric characters (except '+')
    phone = phone.replace(/[^0-9+]/g, "");
    setPhone(phone);
    const isValid = validatePhoneNumber(phone);
    if (isValid) {
      setValidPhone(true);
      setPhoneErr("");
      setPhoneInvalidText(true);
    } else {
      setValidPhone(false);
      setPhoneInvalidText(false);
      setPhoneErr("Enter a valid phone number with a country code");
    }
  };
  const onChangePassword = (event) => {
    let password = event.target.value.trim();
    setPassword(password);

    const minLength = 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSymbol = /[\W_]/.test(password);
    const isDictionaryWord =
      /password|123456|qwerty|letmein|monkey|football/.test(password);

    if (password.length < minLength) {
      setValidLength(false);
      setPasswordInvalidText(false);
    } else {
      setValidLength(true);
      setPasswordInvalidText(true);
    }
    setPasswordValidation({
      uppercase: hasUppercase,
      lowercase: hasLowercase,
      number: hasNumber,
      specialChar: hasSymbol,
    });

    if (!hasUppercase || !hasLowercase || !hasNumber || !hasSymbol) {
      setValidCase(false);
      setPasswordInvalidText(false);
    } else {
      setValidCase(true);
      setPasswordInvalidText(true);
    }

    if (isDictionaryWord) {
      setValidWord(false);
      setPasswordInvalidText(false);
    } else {
      setValidWord(true);
      setPasswordInvalidText(true);
    }

    if (validLength && validCase && validWord) {
      setValidPassword(true);
      setPasswordInvalidText(true);
    }
    if (cpassword !== password) {
      setValidConfirmPassword(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidConfirmPassword(true);
      setConfirmPasswordInvalidText(true);
    }
  };
  const onChangeConfirmPassword = (event) => {
    let cPassword = event.target.value;
    setCpassword(cPassword);

    const minLength = 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSymbol = /[\W_]/.test(password);
    const isDictionaryWord =
      /password|123456|qwerty|letmein|monkey|football/.test(password);

    if (password.length < minLength) {
      setValidLength(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidLength(true);
      setConfirmPasswordInvalidText(true);
    }

    if (
      password.length < minLength ||
      !hasUppercase ||
      !hasLowercase ||
      !hasNumber ||
      !hasSymbol
    ) {
      setValidCase(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidCase(true);
      setConfirmPasswordInvalidText(true);
    }

    if (isDictionaryWord) {
      setValidWord(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidWord(true);
      setConfirmPasswordInvalidText(true);
    }
    if (validLength && validCase && validWord) {
      setValidPassword(true);
      setConfirmPasswordInvalidText(true);
    }

    if (cPassword !== password) {
      setValidConfirmPassword(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidConfirmPassword(true);
      setConfirmPasswordInvalidText(true);
    }
  };

  const onChangeVerificationCode = (event) => {
    let verificationCode = event.target.value;
    setLoadingVEmail(true);
    setVerificationCode(verificationCode);
    setValidVerificationCode(true);
    setVerificationCodeErr("");
    setVerificationCodeInvalidText(true);
    setLoadingVCode(true);
  };

  const onSubmitVerificationCode = (event) => {
    event.preventDefault();
    if (
      validFname &&
      validLname &&
      validEmail
      // validPhone
    ) {
      setVerificationCodeMsg("");
      const allData = {
        firstName: fname.trim(),
        lastName: lname.trim(),
        email: email.trim(),
        userRole: "customer",
        phoneNumber: phone ? phone.trim() : null,
        packageId: dollarData.id,
      };
      setLoadingVCode(true);
      let result = axiosPost(ApiName.tempRegister, allData)
        .then(function (response) {
          if (response.data.status == 200) {
            setIsActive(true);
            setVerificationCodeMsg(
              "Check your inbox or spam folder for a verififcation code."
            );
            setSendVerificationCode(true);
            setLoadingVCode(false);
            setLoadingVEmail(true);
          } else if (response.data.status == 400) {
            setValidEmail(false);
            setEmailErr(response.data.message);
            setLoadingVCode(false);
            setLoadingVEmail(false);
          }
        })
        .catch(function (errors) {
          setLoadingVCode(false);
          if (errors.response.data.status == 400) {
            setValidEmail(false);
            setEmailErr(errors.response.data.message);
            setLoadingVCode(false);
            setLoadingVEmail(false);
          }
        });
    }
  };
  const onSubmitVerifyEmailAddress = (event) => {
    event.preventDefault();
    if (validEmail && verificationCode) {
      const allData = {
        code: verificationCode,
        email: email.trim(),
      };
      setLoadingVEmail(false);
      let result = axiosPost(ApiName.activeTempRegister, allData)
        .then(function (response) {
          if (response.data.status == 200) {
            setIsVerifiedEmail(true);
            setIsActive(false);
          } else if (response.data.status == 400) {
            setLoadingVEmail(true);
            setValidVerificationCode(false);
            setVerificationCodeErr(response.data.message);
          }
        })
        .catch(function (errors) {
          if (errors.response.data.status == 400) {
            setLoadingVEmail(true);
            setValidVerificationCode(false);
            setVerificationCodeErr(errors.response.data.message);
          }
        });
    } else {
      setLoadingVEmail(true);
      setValidVerificationCode(false);
      setVerificationCodeErr("Please enter verification code");
    }
  };
  function handleFilters(name, value) {
    setDataDC({ ...dataDC, [name]: value });
  }

  const onSubmitHandler = (event) => {
    setLoading(true);
    let packageId = null;
    let total_assigned_credit = null;
    let total_assigned_contact_view = null;
    if ("id" in dollarData) packageId = dollarData.id;
    if ("export" in dollarData) total_assigned_credit = dollarData.export;
    if ("no_of_contact_views" in dollarData)
      total_assigned_contact_view = dollarData.no_of_contact_views;
    event.preventDefault();
    if (
      validFname &&
      validLname &&
      validEmail &&
      // validPhone &&
      validPassword &&
      validPassword &&
      validConfirmPassword &&
      captchValue
    ) {
      const allData = {
        firstName: fname.trim(),
        lastName: lname.trim(),
        email: email.trim(),
        password: password.trim(),
        confirmPassword: cpassword.trim(),
        userRole: "customer",
        phoneNumber: phone ? phone.trim() : null,
        packageId: packageId,
        agreement: "true",
        marketplaceToken,
        recaptchaToken: captchValue,
        companyName,
      };
      setSubmitted(true);
      let result = PostWithTokenNoCache(ApiName.superSignUp, allData)
        .then(async function (response) {
          if (response.data.status == 200) {
            const token = JSON.parse(response.data.data);
            AxiosPostBearer(ApiName.deviceLogin, {}, token)
              .then(async (res) => {
                if (
                  email &&
                  fname &&
                  token &&
                  response?.data?.role &&
                  packageId &&
                  total_assigned_credit &&
                  total_assigned_contact_view
                ) {
                  processRegistration(
                    email,
                    fname,
                    token,
                    response?.data?.role
                  );
                } else {
                  setValidEmail(true);
                  setEmailErr("");
                }
              })
              .catch(function (errors) {
                setValidEmail(true);
                setEmailErr("");
              });
          }
        })
        .catch(function (errors) {
          setSubmitted(false);
          setError(errors);
        });
    }
  };

  useEffect(() => {
    const postData = async () => {
      try {
        const response = await fetch(ApiName.superSaverDollar, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ method: "GET" }), // Replace with your payload
        });
        const data = await response.json();
        // console.log(data);
        if (data.status == 200) {
          const jsonData = JSON.parse(data.data);
          let filterPlan = jsonData.filter((value) =>
            value?.package_name == "as1e2nk5ar2o0w9kdz3wsnwez" ? value : null
          );
          if (filterPlan[0] !== undefined || null) {
            setDollorData((prevData) => ({
              ...prevData,
              ...filterPlan[0],
            }));
          }
        }
      } catch (error) { }
    };
    postData();
  }, []);

  const { login, google_access_Token } = useGoogleLoginLogic();
  const { Microsoftlogout, microsoftAuth, MicrosoftAccessToken } =
    useMicrosoftLoginLogic();
  const { linkedInAuth, linkedinResponse } = useLinkedInLoginLogic();
  const handleGoogleLogin = () => {
    login();
  };
  useEffect(() => {
    if (google_access_Token) {
      socialLogic(google_access_Token, "google");
    }
  }, [google_access_Token]);

  const microSoftAuth = () => {
    microsoftAuth(); // Call the microsoft login function
  };

  useEffect(() => {
    if (MicrosoftAccessToken) {
      socialLogic(MicrosoftAccessToken, "microsoft");
    }
  }, [MicrosoftAccessToken]);

  const linkedInLogin = () => {
    linkedInAuth.linkedInLogin();
  };

  useEffect(() => {
    if (localStorage.getItem("linkedin-code")) {
      const linkedincode = localStorage.getItem("linkedin-code");
      localStorage.removeItem("linkedin-code");
      socialLogic(linkedincode, "linkedin");
    }
  }, [linkedinResponse]);

  const socialLogic = (access_token, type) => {
    // call the backend
    setLoading(true);
    let packageId = null;
    let total_assigned_credit = null;
    let total_assigned_contact_view = null;
    if ("id" in dollarData) packageId = dollarData.id;
    if ("export" in dollarData) total_assigned_credit = dollarData.export;
    if ("no_of_contact_views" in dollarData)
      total_assigned_contact_view = dollarData.no_of_contact_views;

    const allData = {
      userRole: "customer",
      packageId: packageId,
      provider_name: type,
      provider_token: access_token,
      marketplaceToken,
    };

    let result = axiosPost(ApiName.socialmediaregister, allData)
      .then(async function (res) {
        if (res?.status === 200) {
          const token = JSON.parse(res?.data?.data);
          AxiosPostBearer(ApiName.deviceLogin, {}, token).then(async (resp) => {
            const requestBody = {
              method: "GET",
            };
            const response = AxiosPostBearer(
              ApiName.userData,
              requestBody,
              token
            )
              .then(async (response) => {
                if (response?.status === 200) {
                  const userdata = JSON.parse(response?.data?.data);
                  if (
                    userdata?.email &&
                    userdata?.firstName &&
                    token &&
                    response?.role &&
                    packageId &&
                    total_assigned_credit &&
                    total_assigned_contact_view
                  ) {
                    localStorage.setItem("signin-type", type);
                    processRegistration(
                      userdata?.email,
                      userdata?.firstName,
                      token,
                      response?.role
                    );
                  } else {
                    setValidEmail(true);
                    setEmailErr("");
                  }
                }
              })
              .catch(function (errors) { });
          });
        }
      })
      .catch(function (errors) {
        setError(errors);
      });
  };

  const processRegistration = (email, firstName, token, role) => {
    setEmail(email);
    setFname(firstName);
    localStorage.setItem("cust-username", firstName);
    localStorage.setItem("cust-email", email);
    handleFilters("loggedUserEmail", email);
    handleFilters("token", token);
    localStorage.setItem(
      "user",
      JSON.stringify({ email: email, token: token, role: role })
    );
    createAppTour(email);
    navigate("/dashboard", {
      state: {
        email: email,
        token: token,
      },
    });
  };

  const createAppTour = async (email) => {
    const date = Math.floor(Date.now() / 1000);
    const params = JSON.stringify({
      name: "Welcome-Tour-" + email + date,
      actionType: "OPEN",
      repeatTimeDefault: 1,
      currentRepeatedTime: 1,
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.appTourCreate, params);
    } catch (errors) { }
  };
  const setError = (errors) => {
    if (
      errors?.response?.data?.status === 400 &&
      errors?.response?.data?.message === "Email is already registered"
    ) {
      setEmailErr(errors.response.data.message);
      setValidEmail(false);
    } else if (
      errors?.response?.data?.status === 400 &&
      errors?.response?.data?.message ===
      "The registration limit for this domain has been exceeded."
    ) {
      setEmailErr(errors.response.data.message);
      setValidEmail(false);
    } else if (
      errors?.response?.data?.status === 400 &&
      errors?.response?.data?.message === "Marketplace Token Is Invalid"
    ) {
      setEmailErr(errors.response.data.message);
      setValidEmail(false);
    } else {
      setValidEmail(true);
      setEmailErr("");
    }
  };

  return (
    <>
      <Helmet>
        <title>Sign Up to AWS Marketplace | ReachStream</title>
        <meta
          name="description"
          content="Sign up to ReachStream's AWS Marketplace plan in a few simple steps to download free contact data today."
        />
      </Helmet>
      <GTM gtmId={gtmId} />
      <div className="container-fluid">
        <div className="row">
          {/* <div className="col-md-1"></div> */}
          <div className="col-md-5 bg-color">
            <div className="offset-md-3 float-center saver-plane">
              {/* <img src="../images/r-logo.png" width="50" className="img-fluid" /> */}
              {/*<h3>Super Saver Plan</h3>*/}
              <div className="saver-plane-sign-up">
                <img src="../images/r-logo.png" width="50" className="" />
                <h3>Super Saver Plan</h3>
              </div>
              {/* <div className="super-saver"> 
                                {referralCode ? (
                                    <Link to={`/super-saver-sign-up?referralcode=${referralCode}`}>
                                        <button className="switch">Switch to Super Saver Plan</button>
                                    </Link>
                                ) : (
                                    <Link to="/super-saver-sign-up">
                                        <button className="switch">Switch to Super Saver Plan</button>
                                    </Link>
                                )}
                            </div> */}
              <div className="pricing-plane">
                <table className="table table-striped">
                  {/* <thead>
                    <tr className="backrnd-color">
                      <th className="borderless">Pricing</th>
                      <th className="borderles">
                        <span className="doller">
                          {dollarData && dollarData.price !== null
                            ? `$${dollarData.price}`
                            : null}{" "}
                          <span className="month">/ Month</span>
                        </span>
                      </th>
                    </tr>
                  </thead> */}

                  <thead>
                    <tr className="free-trial-table-header">
                      <th>
                        <p className="borderless">Pricing</p>
                      </th>
                      <th>
                        <span className="doller">
                          {dollarData && dollarData.price !== null
                            ? `$${dollarData.price}`
                            : null}{" "}
                          <span className="month">/ Month</span>
                        </span>
                      </th>
                    </tr>

                  </thead>
                  <tbody>
                    <tr className="borderless">
                      <td className="text-pose">Number of Contact Views</td>
                      <td className="text-center">
                        {dollarData.no_of_contact_views}
                      </td>
                    </tr>
                    <tr className="row-clr">
                      <td className="text-pose">Export to CSV</td>
                      <td className="text-center">{dollarData.export}</td>
                    </tr>
                    <tr>
                      <td className="text-pose">Number of Users</td>
                      <td className="text-center">{dollarData.no_of_users}</td>
                    </tr>
                    <tr className="row-clr">
                      <td className="text-pose">
                        Verified Emails and Phone Numbers
                      </td>
                      <td className="text-center">
                        {dollarData.verified_email_phone}
                      </td>
                    </tr>
                    <tr>
                      <td className="text-pose">Access to Global Database</td>
                      <td className="text-center">
                        {dollarData.access_database}
                      </td>
                    </tr>
                    <tr className="row-clr">
                      <td className="text-pose">Custom Filters</td>
                      <td className="text-center">
                        {dollarData.custom_filter}
                      </td>
                    </tr>

                    <tr className="borderless">
                      <td className="text-pose">Data Fields</td>
                      <td className="text-center">{dollarData.data_fields}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div className="col-md-6 pr-5">
            <div className="zeta">
              <h3 className="start-journey">Start Your Journey With Us!</h3>
            </div>
            <div className="form-box">
              <form>
                <h3>Sign Up</h3>
                <p className="semi-header10">
                  Already have an account? <Link to="/"> Sign In</Link>
                </p>

                <div className="continue">
                  <div className="d-flex flex-row justify-content-around">
                    <div className="with">
                      <p>Continue with :</p>
                    </div>
                    <div className="social" onClick={handleGoogleLogin}>
                      <img
                        src="../images/googgle.png"
                        className="img-fluid"
                        width="40"
                      />
                    </div>
                    <div className="social" onClick={microSoftAuth}>
                      <img
                        src="../images/microsoft.png"
                        className="img-fluid"
                        width="40"
                      />
                    </div>
                    <div className="social" ocClick={linkedInLogin}>
                      <img
                        src="../images/linkkkedin.png"
                        className="img-fluid"
                        width="40"
                      />
                    </div>
                  </div>
                </div>
                {/* const [socialerrormes, setsocialerrormes] = useState('');
            const [socialerror, setsocialerror] = useState(false); */}

                <h6 className="horizontal-line">
                  <span className="horback">Or</span>
                </h6>

                <div className="row">
                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label htmlFor="usr" className="n-password">
                      First Name<span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    {fNameInvalidText ? (
                      <input
                        type="text"
                        className="form-control"
                        id={fname.length >= 3 ? "signupinput" : ""}
                        name="firstName"
                        onChange={onChangeFname}
                        value={fname}
                        autoFocus
                      />
                    ) : (
                      <input
                        type="text"

                        className="invalid-input-text"
                        name="firstName"
                        onChange={onChangeFname}
                        value={fname}
                        autoFocus
                      />
                    )}
                    {!validFname ? (
                      <span className="signup-errors">
                        {fnameErr && <p>{fnameErr}</p>}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label htmlFor="usr" className="n-password">
                      Last Name<span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    {lNameInvalidText ? (
                      <input
                        type="text"
                        className="form-control"
                        id={lname.length >= 1 ? "signupinput" : ""}
                        name="firstName"
                        onChange={onChangeLname}
                        value={lname}
                      />
                    ) : (
                      <input
                        type="text"

                        className="invalid-input-text"
                        name="firstName"
                        onChange={onChangeLname}
                        value={lname}
                        autoFocus
                      />
                    )}
                    {!validLname ? (
                      <span className="signup-errors">
                        {lnameErr && <p>{lnameErr}</p>}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label htmlFor="usr" className="n-password">
                      Company Name
                    </label>
                    <input
                      type="text"
                      id={companyName.length >= 1 ? "signupinput" : ""}
                      onChange={onChangeCompanyName}
                      className="form-control"
                    />
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label className="n-password">Phone Number</label>
                    {phoneInvalidText ? (
                      <input
                        type="text"
                        className="form-control"
                        id={validPhone ? "signupinput" : ""}
                        name="phoneNumber"
                        onChange={onChangePhone}
                        value={phone}
                      />
                    ) : (
                      <input

                        type="text"
                        className="invalid-input-text"
                        name="phoneNumber"
                        onChange={onChangePhone}
                        value={phone}
                        autoFocus
                      />
                    )}
                    {!validPhone ? (
                      <span className="signup-errors">
                        {phoneErr && <p>{phoneErr}</p>}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label className="n-password">
                      Business Email<span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    {emailInvalidText ? (
                      <input
                        type="text"
                        className="form-control"
                        id={validEmail ? "signupinput" : ""}
                        name="firstName"
                        onChange={onChangeEmail}
                        value={email}
                      />
                    ) : (
                      <input

                        type="text"
                        className="invalid-input-text"
                        name="firstName"
                        onChange={onChangeEmail}
                        value={email}
                        autoFocus
                      />
                    )}

                    {!validEmail ? (
                      <span className="signup-errors">
                        {emailErr && <p>{emailErr}</p>}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <div className="spacing"></div>
                    <button
                      type="button"
                      onClick={onSubmitVerificationCode}
                      className={`sendverificationcode btn-block ${loadingVCode ||
                        loadingVEmail ||
                        !validEmail ||
                        !validFname ||
                        !validLname
                        ? "cust-disabled"
                        : ""
                        }`}
                    >
                      Send Verification Code
                    </button>
                    {isActive ? (
                      <span className="signup-message">
                        <p>
                          Verify in : {String(minutes).padStart(2, "0")}:
                          {String(remainingSeconds).padStart(2, "0")}
                        </p>
                      </span>
                    ) : null}
                    <span className="signup-message">
                      {verificationCodeMsg && !isVerifiedEmail && (
                        <p>
                          {verificationCodeMsg}
                          <span
                            className="signup-message mouse-pointer"
                            onClick={onSubmitVerificationCode}
                          >
                            {verificationCodeMsg && !isVerifiedEmail && (
                              <span className="resend">Resend</span>
                            )}
                          </span>
                        </p>
                      )}
                    </span>
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>

                    <div class="form-group">
                      <label for="exampleInputEmail1">
                        Verification Code
                        <span style={{ color: "#DE350B" }}>*</span>
                      </label>

                      {verificationCodeInvalidText ? (
                        <input
                          type="text"
                          class="form-control"
                          disabled={!sendVerificationCode}
                          id={validVerificationCode ? "signupinput" : ""}
                          name="verificationCode"
                          onChange={onChangeVerificationCode}
                          value={verificationCode}
                          readOnly={isVerifiedEmail}
                        />
                      ) : (
                        <input

                          type="text"
                          className="invalid-input-text"
                          name="verificationCode"
                          onChange={onChangeVerificationCode}
                          value={verificationCode}
                          autoFocus
                          readOnly={isVerifiedEmail}
                        />
                      )}
                      {!validVerificationCode ? (
                        <span className="signup-errors">
                          {verificationCodeErr && <p>{verificationCodeErr}</p>}
                        </span>
                      ) : (
                        <></>
                      )}
                    </div>
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <div className="spacing"></div>
                    {!isVerifiedEmail ? (
                      <button
                        type="button"
                        onClick={onSubmitVerifyEmailAddress}
                        className={`verifyemailaddress btn-block ${!loadingVEmail ? "cust-disabled" : ""
                          }`}
                      >
                        &nbsp; &nbsp;Verify Email Address &nbsp;
                      </button>
                    ) : (
                      <div className="input-with-check-icon">
                        <input
                          className=" input-field-check"
                          name="promocode"
                          placeholder="Verification successful"
                          readOnly
                        />
                        <img
                          src="../images/promocode-success.png"
                          className="check-icon"
                        />
                      </div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label className="n-password">
                      Password<span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    <div className="password-input">
                      {passwordInvalidText ? (
                        <input
                          type={showPassword ? "text" : "password"}
                          className="form-control"
                          id={password ? "signupinput" : ""}
                          name="password" //{showPassword ? "text" : "password"}
                          onChange={onChangePassword}
                          value={password}
                          disabled={!isVerifiedEmail}
                        />
                      ) : (
                        <input

                          type={showPassword ? "text" : "password"}
                          className="invalid-input-text"
                          name="password"
                          onChange={onChangePassword}
                          value={password}
                          autoFocus
                          disabled={!isVerifiedEmail}
                        />
                      )}
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        <FontAwesomeIcon
                          icon={showPassword ? faEye : faEyeSlash}
                          style={{ pointerEvents: "none" }}
                        />
                      </button>
                    </div>
                    {password ? (
                      <span className="signup-message">
                        {!validLength ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;Password
                            must be 8 characters
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;Password must
                            be 8 characters
                          </p>
                        )}
                        {!passwordValidation.uppercase ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one uppercase (A-Z)
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            uppercase (A-Z)
                          </p>
                        )}
                        {!passwordValidation.lowercase ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one lowercase (a-z)
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            lowercase (a-z)
                          </p>
                        )}
                        {!passwordValidation.number ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one number
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            number
                          </p>
                        )}
                        {!passwordValidation.specialChar ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one Special character (@, #, &, $, etc.)
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            Special character (@, #, &, $, etc.)
                          </p>
                        )}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label className="n-password">
                      Confirm Password
                      <span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    <div className="password-input">
                      {confirmpasswordInvalidText ? (
                        <input
                          type={showConfirmPassword ? "text" : "password"}
                          className="form-control"
                          id={validConfirmPassword ? "signupinput" : ""}
                          name="confirmPassword"
                          onChange={onChangeConfirmPassword}
                          value={cpassword}
                          disabled={!isVerifiedEmail}
                        />
                      ) : (
                        <input
                          type={showConfirmPassword ? "text" : "password"}
                          className="invalid-input-text"
                          name="confirmPassword"
                          onChange={onChangeConfirmPassword}
                          value={cpassword}
                        />
                      )}
                      <button
                        type="button"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                      >
                        <FontAwesomeIcon
                          icon={showConfirmPassword ? faEye : faEyeSlash}
                          style={{ pointerEvents: "none" }}
                        />
                      </button>
                    </div>
                    {validCase &&
                      validLength &&
                      validWord &&
                      !validConfirmPassword ? (
                      <span className="signup-errors">
                        <p>Passwords do not match</p>
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>
                </div>

                <div className="custom-control custom-checkbox  mt-2">
                  <input
                    type="checkbox"
                    className="custom-control-input"
                    id="customCheck2"
                    defaultChecked={isChecked}
                    checked={isChecked}
                    onChange={handleCheckboxChange}
                  />
                  <label
                    className="custom-control-label"
                    htmlFor="customCheck2"
                  >
                    I agree to ReachStream's{" "}
                    <a
                      href="https://www.reachstream.com/terms-conditions/"
                      className="Term"
                      target="_blank"
                    >
                      Terms
                    </a>{" "}
                    and{" "}
                    <a
                      href="https://www.reachstream.com/privacy-center/"
                      className="Term"
                      target="_blank"
                    >
                      Privacy Policy.
                    </a>
                  </label>

                  {validFname &&
                    validLname &&
                    // validPhone &&
                    validPassword &&
                    validEmail &&
                    validConfirmPassword &&
                    !validCheckBox ? (
                    <span className="signup-errors">
                      <p></p>
                    </span>
                  ) : (
                    <></>
                  )}
                </div>
                <div style={{ margin: "20px 0 0 0" }}>
                  <ReCAPTCHA
                    sitekey={process.env.REACT_APP_GOOGLE_RECAPTCHA_SITE_KEY}
                    onChange={(token) => setCaptchValue(token)}
                    onExpired={() => setCaptchValue("")}
                  />
                </div>
                <div className="make-space"></div>
                <span>
                  <img src="https://ct.capterra.com/capterra_tracker.gif?vid=2141371&vkey=1024d0cab7760c225323da44f90c5d0c" />
                  {!validFname ||
                    !validLname ||
                    // !validPhone ||
                    !validPassword ||
                    !validEmail ||
                    !validConfirmPassword ||
                    !validCheckBox ||
                    !captchValue ||
                    submitted ? (
                    <input
                      type="button"
                      onClick={onSubmitHandler}
                      value={!submitted ? "Create Account" : "Processing"}
                      className="cp-pluss1 cust-disabled"
                    />
                  ) : (
                    <input
                      type="button"
                      onClick={onSubmitHandler}
                      value="Create Account"
                      className="cp-pluss"
                    />
                  )}
                </span>
              </form>
            </div>
          </div>
          <div className="col-md-1"></div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default AwsSignUp;
