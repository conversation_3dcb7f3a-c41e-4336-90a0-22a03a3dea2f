@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap');

/* .form-box3 h3 {
    text-align: center;
    padding: 0 0 9px 0px;
    font-weight: 600;
    margin-top: 10%;
    color: #093D54;
} */

p.semi-header8 {
    text-align: center;
    color: #000;
    font-size: 14px;
    font-weight: 500;
    padding: 18px 0 0 0;
}

/* span.email-address p {
    margin: 0px 0 10px 0px;
} */

span.password p {
    margin: 0 0 10px 0;
}

p.semi-header {
    text-align: center;
    font-size: 14px;
    margin-bottom: 23px;
}

p.semi-header a {
    text-decoration: none;
	color: #55C2C3;
    line-height: 27px;
}


p.semi-header a:hover {
    color: #55C2C3;
}

p.password-label {
    padding: 8px 0 0 0;
}

p.password-label a{
	text-decoration: none;
}

p.password-label a:hover{
	color: #007bff;
}



.password-change-banner img {
    width: 500px;
    margin: 0 auto;
    display: block;
    padding-top: 6%;
    padding-bottom: 25%;
    margin-top: 6%;
}


.saver-plane2 {
    margin-top: 15%;
}

.bg-color {
    background-color: #E8F7F7;
    height: auto;
}





p.text-end a {
    padding: 0 53px 0 3px;
}
height-100 {
    height: 100vh
}




input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0
}

.resend-code.offset-md-2 span {
    font-size: 12px;
    color: #C2C8CE;
    padding: 0 0 0 16px;
}

p.text-end a {
    text-decoration: none;
    color: #55C2C3;
}

.didnt a {
    text-decoration: none;
    color: #55C2C3;
}


span.err {
    font-size: 12px;
    color: #7E8C9C;
}

span.error {
    font-size: 12px;
    color: #DE350B;
}

.card-body {
    max-width: 28rem;
    border-radius: 32px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
    border: 0;
    margin: auto;
    height: 30rem;
    margin-top: 10%;
    padding: 0;
}

.card-body h3 {
    text-align: center;
    margin-top: 15px;
    color: #093D54;
    font-weight: 600;
    line-height: 40px;
}

.saver-plane-password-change {
    margin-top: 10%;
    margin-bottom: 9%;
}

.password-changed-successfully-banner {
    margin-bottom: 23%;
}

span.Shift a {
    text-decoration: none;
}

span.Shift button {
    /*background-color: #093D54;
    color: #fff;
    outline: none;
    padding: 10px 40px 10px 40px;
    border-radius: 19px;
    cursor: pointer;
    border: 1px solid #093D54;
    font-size: 19px;*/
    background-color: #093D54;
    color: #fff;
    outline: none;
    padding: 10px 40px 10px 40px;
    border-radius: 15px;
    cursor: pointer;
    border: 1px solid #093D54;
    font-size: 19px;
    margin: 0 auto;
    display: block;

}


