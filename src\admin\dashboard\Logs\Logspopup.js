import S from '../../assets/css/layouts/admin-header.module.css';
const Popup = ({ data, item, onClose }) => {
    const formatDate = (dateTime) => {
        const date = new Date(dateTime);
        const formattedDate = `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')} - ${date.getHours() % 12 || 12}:${date.getMinutes().toString().padStart(2, '0')} ${(date.getHours() >= 12) ? 'pm' : 'am'}`;
        return formattedDate;
    };
    return (
        <>
            {/* <button type="button" class="btn btn-primary" data-toggle="modal" data-target=".bd-example-modal-lg">Large modal</button> */}
            <div class="modal  bd-example-modal-lg" style={{ display: 'block', important: 'true', backgroundColor: '#0000007d' }} >
                <div class="modal-dialog modal-lg">
                    <div class="modal-content" style={{ backgroundImage: "url('./images/popup_banner_2.png')", border: "0", backgroundSize: "cover", backgroundRepeat: "no-repeat" }}>
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel"></h5>
                            <button onClick={onClose} style={{ padding: "6px 14px 6px 0" }} type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div className="d-flex flex-row justify-content-between bg-light">
                            <div className="d-flex flex-row">
                                <div>
                                    <p className={S.PromptName}>Prompt Name</p>
                                    <p className={S.PromptName}>Prompt Message</p>
                                </div>
                                <div>
                                    <p className={S.Support}>{data?.records?.popupName}</p>
                                    <p className={S.Supported}>{data?.records?.content}</p>
                                    <p className={S.inputtextedit}>
                                        &#60;input type="text=<span className={S.typehreand}>Type here</span>"&#62;
                                    </p>
                                    <p className={S.inputtextedit}>
                                        &#60;input type="button=<span className={S.typehreand}>Submit</span>"&#62;
                                    </p>
                                </div>
                            </div>
                            <div className="d-flex flex-row">
                                <div>
                                    <p className={S.PromptName}>Data and Time</p>
                                    <p className={S.PromptNames}>Send</p>
                                </div>
                                <div>
                                    <p className={S.Support}>{formatDate(data?.dateTime)}</p>
                                    <p className={S.Support}>{data?.userCount} Times</p>
                                </div>
                                <div>
                                </div>
                                <div>
                                </div>
                            </div>
                        </div>
                        <div className="d-flex flex-row justify-content-between bg-light">
                            <div className="d-flex flex-row">
                                <div>
                                </div>
                                <div>
                                </div>
                            </div>
                        </div>
                        <div className={S.senditems}>
                            <p>Send Items</p>
                            <div className="ml-3 mr-3 mb-4">
                                <div className="stickyy" style={{ height: "168px", overflowY: "auto", padding: "0 10px 0 4px" }}>
                                    <table>
                                        <thead style={{ position: "sticky", top: "0", zIndex: "1" }}>
                                            <tr className="table-headers-saved-list mt-2" style={{ backgroundColor: "#ebf6ff", height: "40px" }}>
                                                <th style={{ textAlign: "center" }}>Sl No</th>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Phone</th>
                                                <th >Package Type</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {item.map((row, i) => (
                                                <tr className="tabledatasmall" style={{ borderRadius: "10px" }} >
                                                    <td style={{ borderLeft: "thin solid #55C2C3", borderTop: "thin solid #55C2C3", borderBottom: "thin solid #55C2C3", textAlign: "center", padding: "6px 0 5px 0px", fontSize: "14px" }}>{i + 1}</td>
                                                    <td style={{ borderTop: "thin solid #55C2C3", borderBottom: "thin solid #55C2C3", fontSize: "14px" }}>{row?.firstName + ' ' + row?.lastName}</td>
                                                    <td style={{ borderTop: "thin solid #55C2C3", borderBottom: "thin solid #55C2C3", fontSize: "14px" }}>{row?.email}</td>
                                                    <td style={{ borderTop: "thin solid #55C2C3", borderBottom: "thin solid #55C2C3", fontSize: "14px" }}>{row?.phone}</td>
                                                    <td style={{ borderTop: "thin solid #55C2C3", borderBottom: "thin solid #55C2C3", borderRight: "thin solid #55C2C3", fontSize: "14px" }}>{row?.userPlanName}</td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
export default Popup;