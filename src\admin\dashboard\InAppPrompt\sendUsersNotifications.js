import React, { useEffect, useState, useRef } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import { useNavigate, useLocation } from "react-router-dom";
import AdminHeader from "../../layouts/AdminHeader.js";
import {
  postWithToken,
  PostWithTokenNoCache,
} from "../../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import loadingGif from "../../../customer/assests/waiting.gif";

const SendUsersNotifications = () => {
  const [data, setData] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");

  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const location = useLocation();
  const [selectUsers, setSelectUsers] = useState([]);
  const [successMsg, setSuccessMsg] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [packageTypeVal, setPackageTypeVal] = useState("");
  const [excludePackageType, setExcludePackageType] = useState("");
  const containerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [packageType, setPackageType] = useState("all");
  const [checkbox, setCheckbox] = useState(false);
  const [packageChanged, setIsPackageChanged] = useState(false);
  useEffect(() => {
    setCurrentPage(1);
    setTotalPages(1);
    getUserData(1);
    setIsLoading(true);
  }, [searchQuery]);

  useEffect(() => {
    if (packageChanged) {
      setFromDate("");
      setToDate("");
      // setCurrentPage(1);
      setTotalPages(1);
      getUserData(1);
      setIsLoading(true);
    }
  }, [packageTypeVal, packageChanged]);

  useEffect(() => {
    if (fromDate && toDate) {
      if (new Date(fromDate) < new Date(toDate)) {
        setCurrentPage(1);
        setTotalPages(1);
        getUserData(1);
        setIsLoading(true);
      } else {
        setFromDate("");
      }
    }
  }, [toDate, fromDate]);

  const getUserData = async (id) => {
    console.log("id", id);
    console.log("packageTypeVal", packageTypeVal);
    console.log("length", data.length);
    try {
      let condition;
      if (id == 1) {
        condition = true;
      } else {
        condition = totalPages >= currentPage;
      }
      if (condition) {
        let currentPageValue;
        if (id == 2) {
          currentPageValue = currentPage;
        } else if (id == 1) {
          currentPageValue = 1;
        }

        if (searchQuery || (fromDate && toDate)) {
          currentPageValue = 1;
        }
        const params = JSON.stringify({
          page: searchQuery ? 1 : currentPageValue,
          pageSize: searchQuery ? 100000 : 20,
          searchParams: {
            userRole: "customer",
            planStatus: "active",
            planName: packageTypeVal,
            excludedPlans: excludePackageType,
            email: searchQuery,
            from: fromDate,
            to: toDate,
          },
          sortBy: "DESC",
        });

        const res = await PostWithTokenNoCache(
          ApiName.getAllUserPlansPagination,
          params
        );
        if (res && "status" in res) {
          if (res.status == 200) {
            let record = JSON.parse(res.data.data);
            let records = record.all_user.items;
            const updatedrecord = records.map((user) => ({
              ...user,
              isSelected: selectUsers.includes(user.userId),
              //   isSelected: selectUsers.includes(user.userId),
            }));
            setCheckbox(false);
            const allUserIdsSelected = records.every((user) =>
              selectUsers.includes(user.userId)
            );
            setCheckbox(allUserIdsSelected);
            let newData = [...updatedrecord];
            setTotalPages(record.all_user.totalPages);
            if (id == 2) {
              setData((prevData) => [...prevData, ...newData]);
              //   setData(newData);
              setCurrentPage(currentPage + 1);
            } else if (id == 1) { 
              setIsPackageChanged(false);             
              setData(newData);
              setCurrentPage(2);
            }
            setIsPageLoading(false);
            setIsLoading(false);
          }
          
        }
      } else {
        setIsPageLoading(false);
      }
    } catch (error) {
      // Handle any errors
    }
  };
  const changepackage = (packageDetail) => {
    let packageTypes;
    let excludePackageTypes;
    switch (packageDetail) {
      case "all":
        packageTypes = "";
        excludePackageTypes = "";
        break;
      case "freemium":
        packageTypes = "freemium";
        excludePackageTypes = "";
        break;
      case "super-saver":
        packageTypes = "super_saver";
        excludePackageTypes = "";
        break;
      case "app-sumo":
        packageTypes = "APP_SUMO_59";
        excludePackageTypes = "";
        break;
      case "custom":
        packageTypes = "";
        excludePackageTypes = ["freemium", "super_saver", "APP_SUMO_59", null];
        break;
    }
    setData([]);
    setPackageType(packageDetail);
    setExcludePackageType(excludePackageTypes);
    setPackageTypeVal(packageTypes);
    setIsPackageChanged(true);
  };

  const selectUsersOnChange = (userId) => {
    let updatedData;
    if (selectUsers.includes(userId)) {
      setSelectUsers(selectUsers.filter((id) => id !== userId));
      updatedData = data.map((user) => ({
        ...user,
        isSelected: user.userId == userId ? false : user.isSelected,
      }));
    } else {
      setSelectUsers([...selectUsers, userId]);
      updatedData = data.map((user) => ({
        ...user,
        isSelected: user.userId == userId ? true : user.isSelected,
      }));
    }

    setData(updatedData);
  };

  const selectAllUser = (isChecked) => {
    setCheckbox(isChecked);
    let updatedData;
    if (isChecked) {
      setSelectUsers((prevSelectUsers) => {
        const updatedSelectUsers = [...prevSelectUsers];
        data.forEach((element) => {
          if (!updatedSelectUsers.includes(element.userId)) {
            updatedSelectUsers.push(element.userId);
          }
        });
        return updatedSelectUsers;
      });
      updatedData = data.map((user) => ({
        ...user,
        isSelected: true,
      }));
    } else {
      const usersToRemove = [];
      data.forEach((element) => {
        if (selectUsers.includes(element.userId)) {
          usersToRemove.push(element.userId);
        }
      });
      setSelectUsers((prevSelectUsers) =>
        prevSelectUsers.filter((id) => !usersToRemove.includes(id))
      );
      updatedData = data.map((user) => ({
        ...user,
        isSelected: false,
      }));
    }
    setData([...updatedData]);
  };

  const submitUserNotification = () => {
    const currentDate = new Date();

    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, "0");
    const day = String(currentDate.getDate()).padStart(2, "0");
    const hours = String(currentDate.getHours()).padStart(2, "0");
    const minutes = String(currentDate.getMinutes()).padStart(2, "0");
    const seconds = String(currentDate.getSeconds()).padStart(2, "0");

    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    let notificationUserArray = [];
    for (var i = 0; i < selectUsers.length; i++) {
      const data = {
        userAction: "read-" + formattedDate,
        notificationPopupId: location.state && location.state.templateId,
        userId: selectUsers[i],
        createdAt: formattedDate,
      };
      notificationUserArray.push(data);
    }
    let params = {
      notificationList: notificationUserArray,
    };
    try {
      PostWithTokenNoCache(ApiName.createUserNotification, params)
        .then(function (response) {
          if (response.data.status === 200) {
            const dataObj = JSON.parse(response.data.data);
            setSuccessMsg(true);
            setTimeout(() => {
              setSuccessMsg(false);
            }, 3000);
          }
        })
        .catch(function (errors) {});
    } catch (error) {
      //   console.error(error);
    }
  };

  useEffect(() => {
    if(packageChanged){
      return;
    }
    const handleScroll = () => {
      const container = containerRef.current;
      if (
        container.scrollTop + container.clientHeight ===
          container.scrollHeight &&
        container.scrollLeft + container.clientWidth >= container.scrollWidth &&
        data.length > 0
      ) {
        setIsPageLoading(true);
        getUserData(2);
      }
    };

    const container = containerRef.current;
    container.addEventListener("scroll", handleScroll);
    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [currentPage,packageChanged]);

  useEffect(()=>{
    console.log('called');
  },[currentPage])

  const formatteDate = (datetime) => {
    const date = new Date(datetime);
    return `${date.getFullYear()}-${(date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
  };

  return (
    <div className={S.app}>
      <AdminHeader />
      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          <div style={{ width: "100%", padding: "0 10px 0 0" }}>
            <div className={S.inappprompt}>
              <h1>In-App Prompt</h1>
              <span className={S.underline2}></span>
            </div>

            <div className={S.toggleall}>
              <div className="d-flex flex-row ">
                <div className="mr-2">
                  <button
                    style={{
                      backgroundColor: packageType === "all" ? "#093D54" : "",
                      color: packageType === "all" ? "white" : "",
                    }}
                    className={S.all}
                    onClick={() => changepackage("all")}
                  >
                    All
                  </button>
                </div>
                <div className="mr-2">
                  <button
                    style={{
                      backgroundColor:
                        packageType === "freemium" ? "#093D54" : "",
                      color: packageType === "freemium" ? "white" : "",
                    }}
                    className={S.all}
                    onClick={() => changepackage("freemium")}
                  >
                    Freemium
                  </button>
                </div>
                <div className="mr-2">
                  <button
                    style={{
                      backgroundColor:
                        packageType === "super-saver" ? "#093D54" : "",
                      color: packageType === "super-saver" ? "white" : "",
                    }}
                    className={S.all}
                    onClick={() => changepackage("super-saver")}
                  >
                    Super saver
                  </button>
                </div>
                <div className="mr-2">
                  <button
                    style={{
                      backgroundColor:
                        packageType === "app-sumo" ? "#093D54" : "",
                      color: packageType === "app-sumo" ? "white" : "",
                    }}
                    className={S.all}
                    onClick={() => changepackage("app-sumo")}
                  >
                    App Sumo
                  </button>
                </div>
                <div className="mr-2">
                  <button
                    style={{
                      backgroundColor:
                        packageType === "custom" ? "#093D54" : "",
                      color: packageType === "custom" ? "white" : "",
                    }}
                    className={S.all}
                    onClick={() => changepackage("custom")}
                  >
                    Custom
                  </button>
                </div>
              </div>
            </div>

            <div className={S.searchbackground}>
              <div className="d-flex flex-row justify-content-between">
                <div>
                  <div className="d-flex flex-row">
                    <div className="mr-4">
                      <h2 className={S.durationby}>Search By Duration</h2>
                    </div>
                    <div className="mr-4">
                      <label htmlFor="birthday" className={S.tothelabel}>
                        From
                      </label>
                      <input
                        className={S.selectdate}
                        type="date"
                        id="fromDate"
                        name="fromDate"
                        value={fromDate}
                        onChange={(e) => setFromDate(e.target.value)}
                      />
                    </div>
                    <div className="mr-4">
                      <label htmlFor="birthday" className={S.tothelabel}>
                        To
                      </label>
                      <input
                        className={S.selectdate}
                        type="date"
                        id="toDate"
                        name="toDate"
                        value={toDate}
                        onChange={(e) => setToDate(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
                <div className={S.inputimage}>
                  <img src="../images/search.png" alt="Image" />
                  <input
                    type="link"
                    className={S.inputsurface}
                    placeholder="Search by Email"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div ref={containerRef} className={S.adminfixTableHead}>
                <table className={S.admintable}>
                  <thead className={S.adminheader}>
                    <tr className={S.admintr}>
                      <th
                        className={S.tabledataheader}
                        scope="col"
                        style={{ padding: "7px 0px 9px 0px" }}
                      >
                        <input
                          type="checkbox"
                          className="checkboxstyle"
                          id="checkbox4"
                        />
                        <label className="second" htmlFor="checkbox4">
                          <input
                            className="custom-checkbox"
                            type="checkbox"
                            onChange={(e) => selectAllUser(e.target.checked)}
                            checked={checkbox}
                          />
                        </label>
                      </th>
                      <th
                        className={S.tabledataheader}
                        scope="col"
                        style={{ padding: "7px 0px 9px 0px" }}
                      >
                        Sl No
                      </th>
                      <th
                        className={S.tabledataheader}
                        scope="col"
                        style={{ padding: "7px 0px 9px 0px" }}
                      >
                        Registered On
                      </th>
                      <th
                        className={S.tabledataheader}
                        scope="col"
                        style={{ padding: "7px 0px 9px 0px" }}
                      >
                        Name
                      </th>
                      <th
                        className={S.tabledataheader}
                        scope="col"
                        style={{ padding: "7px 0px 9px 0px" }}
                      >
                        Email
                      </th>
                      <th
                        className={S.tabledataheader}
                        scope="col"
                        style={{ padding: "7px 0px 9px 0px" }}
                      >
                        Phone
                      </th>
                      <th
                        style={{ padding: "7px 0px 9px 0px" }}
                        className={S.tabledataheader}
                        scope="col"
                      >
                        Packege Type
                      </th>
                    </tr>
                  </thead>
                  <tbody className="admintablebody">
                    {!isLoading ? (
                      data.map((item, index) => (
                        <tr
                          className={S.admintablerow}
                          key={item.userId || index}
                        >
                          <td
                            className={S.admintabledata}
                            style={{ padding: "7px 0px 9px 0px" }}
                          >
                            <input
                              type="checkbox"
                              className="checkboxstyle"
                              id={`checkbox${index}`}
                              onChange={() => selectUsersOnChange(item.userId)}
                            />
                            <label
                              className="second"
                              htmlFor={`checkbox${index}`}
                            >
                              <input
                                className="custom-checkbox"
                                type="checkbox"
                                onChange={() =>
                                  selectUsersOnChange(item.userId)
                                }
                                checked={item.isSelected}
                              />
                            </label>
                          </td>
                          <td
                            className={S.admintabledata}
                            style={{ padding: "7px 0px 9px 0px" }}
                          >
                            {index + 1}
                          </td>
                          <td
                            className={S.admintabledata}
                            style={{ padding: "7px 0px 9px 0px" }}
                          >
                            {formatteDate(item.userCreatedAt)}
                          </td>
                          <td
                            className={S.admintabledata}
                            style={{ padding: "7px 0px 9px 0px" }}
                          >
                            {item.firstName}
                          </td>
                          <td
                            className={S.admintabledata}
                            style={{ padding: "7px 0px 9px 0px" }}
                          >
                            {item.email}
                          </td>
                          <td
                            className={S.admintabledata}
                            style={{ padding: "7px 0px 9px 0px" }}
                          >
                            {item.phone === "null" ? "" : item.phone}
                          </td>
                          <td
                            className={S.admintabledata}
                            style={{
                              width: "100px",
                              padding: "7px 0px 9px 0px",
                            }}
                          >
                            {item.userPlanName}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="7" style={{ textAlign: "center" }}>
                          <img
                            src={loadingGif}
                            alt="Loading"
                            className="loader"
                            width="400"
                          />
                        </td>
                      </tr>
                    )}
                    {isPageLoading ? (
                      <tr>
                        <td colSpan="7" style={{ textAlign: "center" }}>
                          Loading...
                        </td>
                      </tr>
                    ) : (
                      <></>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
            <div className="d-flex flex-row justify-content-end">
              <div>
                {successMsg ? (
                  <button
                    type="submit"
                    style={{ outline: "none" }}
                    className={S.savechanges2Disable}
                    disabled
                  >
                    Send
                  </button>
                ) : (
                  <button
                    type="submit"
                    style={{ outline: "none" }}
                    onClick={submitUserNotification}
                    className={S.sendit}
                  >
                    Send
                  </button>
                )}

                {successMsg && (
                  <div
                    className="d-flex flex-row"
                    style={{
                      border: "1px solid #6DE1A4",
                      backgroundColor: "#EEFFF6",
                      margin: "5px 0 0 0",
                      padding: "0 0 0 8px",
                      borderRadius: "5px",
                    }}
                  >
                    <div>
                      <img src="../images/promocode.png" width="15" />
                    </div>
                    <div>
                      <p className={S.que}>Successfully Sent.</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SendUsersNotifications;
