import React, { useEffect, useState } from 'react';
import CompanySideNav from '../../customer/company-filters-new/LeftNavbar.js'
import '../assests/css/layouts/navbar.css';
import Header from '../../customer/layouts/Header.js';
import CompanyTable from './CompanyContactTable';

import loadingGif from '../assests/waiting.gif';
import '../assests/css/Loader.css'; // Import the CSS file with the defined styles
import UseTabStore from '../common-files/useGlobalState.js';

const Navbar = () => {
	const [paginationDataCount, setPaginationDataCount] = useState();
	const { setSelectedTab } = UseTabStore();

	useEffect(() => {
		setSelectedTab("company");
	}, [])
	return (
		<div className='ljljsdkkjsdlk'>
			<Header key={"header"} setPaginationDataCount={(count) => { setPaginationDataCount(count) }} />
			<div className="kjjwqeijeo">
				<div className="d-flex flex-row">
					<div className="">
						<CompanySideNav key={"navbar"} />
					</div>
					<div className="p-2 pr-4 w-100">
						<div className="loader-container" id="table-loader" style={{ display: 'none', margin: "9rem 0 0 0" }}>
							<img src={loadingGif} alt="Loading" className="m-auto d-block" width="300"/>
						</div>
						{paginationDataCount && <CompanyTable key={"table"} paginationDataCount={paginationDataCount} />}
					</div>
				</div>
			</div>
		</div>
	)
}
export default Navbar;