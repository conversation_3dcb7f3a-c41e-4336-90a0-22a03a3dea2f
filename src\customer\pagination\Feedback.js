
import React, { useState, useContext, useEffect, useRef } from "react";
import { ApiName } from "../common-files/ApiNames.js";
import { AxiosPostBearer, PostWithTokenNoCache } from '../common-files/ApiCalls.js';

function Feedback() {
    const [isOpen, setIsOpen] = useState(false);
    const [rating, setRating] = useState(0);
    const [feedbackEmoji, setFeedbackEmoji] = useState(0);
    const [feedbackMessage, setFeedbackMessages] = useState(null);
    const [response, setResponse] = useState(false);
    const [process, setProcess] = useState(false);
    const [error, setError] = useState(false);
    const [feedbackFile, setFeedbackFile] = useState([]);
    const [contactData, setContactData] = useState({
        firstName: null,
        lastName: null,
        email: null,
        token: null,
    });

    useEffect(() => {
        // Add event listeners when the component mounts
        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('keydown', handleEscKeyPress);

        // Remove event listeners when the component unmounts
        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
            document.removeEventListener('keydown', handleEscKeyPress);
        };
    }, []);

    const close = () => {
        setIsOpen(false);
        setFeedbackEmoji(0);
        setRating(0);
        setFeedbackMessages(null);
        setFeedbackFile([]);

    }
    const handleOutsideClick = (e) => {
        // Check if the click is outside the modal
        if (!isOpen && !e.target.closest('.feedback')) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        // Check if the pressed key is ESC
        if (!isOpen && e.key === 'Escape') {
            close();
        }
    };

    useEffect(() => {
        const user = JSON.parse(localStorage.getItem("user"));
        const postData = () => {
            const requestBody = {
                "method": "GET"
            };
            const response = AxiosPostBearer(ApiName.userData, requestBody, user.token)
                .then(function (response) {
                    const jsonData = JSON.parse(response.data.data);
                    const total = { ...jsonData, ...user }; // session-user +jsondata
                    const merge = { ...contactData, ...total };
                    setContactData(merge);
                }).catch(function (errors) {
                });
        }
        postData();
    }, []);

    const toggleContentBox = () => {
        setIsOpen(!isOpen);
    };

    const handlefeedbackemoji = (id) => {
        setFeedbackEmoji(id);
    }

    const handleRating = (id) => {
        setRating(id);
    }

    const handleFeedback = (e) => {
        if (e.target.type === 'file') {
            const files = Array.from(e.target.files); // Convert FileList to array
            setFeedbackFile(prevFiles => [...prevFiles, ...files]);
        } else {
            const { name, value } = e.target;
            setFeedbackMessages(value);
        }
    }

    const removefile = (indexToRemove) => {
        setFeedbackFile(prevFiles => prevFiles.filter((file, index) => index !== indexToRemove));
    }

    const handleFeedbackCancel = () => {
        setFeedbackEmoji(0);
        setRating(0);
        setFeedbackMessages(null);
        setFeedbackFile([]);
        setIsOpen(!isOpen);
    }
    const fileToBase64 = (file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                resolve(reader.result);
            };
            reader.onerror = error => reject(error);
        });
    };

    const handleFeedbackSend = async () => {
        setProcess(true);
        let base64Array = [];
        if (feedbackFile.length > 0) {
            for (const file of feedbackFile) {
                const base64Data = await fileToBase64(file);
                base64Array.push(base64Data);
            }
        }

        const data = {
            "fname": contactData.firstName,
            "lname": contactData.lastName,
            "email": contactData.email,
            "feedback": feedbackEmoji,
            "rating": rating,
            "suggestion": (feedbackMessage ? feedbackMessage : ''),
            "file": base64Array
        }

        await await PostWithTokenNoCache(ApiName.userFeedback, JSON.stringify(data))
            .then(async function (response) {
                if (response != null && "status" in response) {
                    if (response.data.status === 200) {
                        const dataObj = JSON.parse(response.data.data);
                        if (dataObj.flag) {
                            setError(false);
                            setFeedbackEmoji(0);
                            setRating(0);
                            setFeedbackMessages(null);
                            setFeedbackFile([]);
                            setProcess(false);
                            setResponse(true);
                            setTimeout(() => {
                                setResponse(false);
                            }, 10000)
                        }
                    }
                }
            }).catch((error) => {
                setProcess(false);
                setError(true);
                setTimeout(() => {
                    setError(false);
                }, 10000)
            });
    }
    return (
        <>
            <div className="d-flex flex-row-reverse button-position">
                <div className="">
                    {isOpen && (
                        <div className="content feedback">
                            <p className="where">Were you able to find the information you were looking for?</p>

                            <div className="d-flex flex-row ml-2">
                                <div className="mr-1 ml-2">
                                    <p className="angry" onClick={() => handlefeedbackemoji(1)}>
                                        {feedbackEmoji === 1 ? (
                                            <img src="../images/really-negetive-blue.png" width="30" />
                                        ) : (
                                            <img src="../images/really-negetive.png" width="30" />
                                        )}
                                    </p>
                                </div>
                                <div className="mr-1 ml-2">
                                    <p className="angry" onClick={() => handlefeedbackemoji(2)}>
                                        {feedbackEmoji === 2 ? (
                                            <img src="../images/Negative-blue.png" width="30" />
                                        ) : (
                                            <img src="../images/Negative.png" width="30" />
                                        )}
                                    </p>
                                </div>
                                <div className="mr-1 ml-2">
                                    <p className="angry" onClick={() => handlefeedbackemoji(3)}>
                                        {feedbackEmoji === 3 ? (
                                            <img src="../images/Itwasalright-blue.png" width="30" />
                                        ) : (
                                            <img src="../images/Itwasalright.png" width="30" />
                                        )}
                                    </p>
                                </div>
                                <div className="mr-1 ml-2">
                                    <p className="angry" onClick={() => handlefeedbackemoji(4)}>
                                        {feedbackEmoji === 4 ? (
                                            <img src="../images/positive-blue.png" width="30" />
                                        ) : (
                                            <img src="../images/positive.png" width="30" />
                                        )}
                                    </p>
                                </div>
                                <div className="mr-1 ml-2">
                                    <p className="angry" onClick={() => handlefeedbackemoji(5)}>
                                        {feedbackEmoji === 5 ? (
                                            <img src="../images/really-positive-blue.png" width="30" />
                                        ) : (
                                            <img src="../images/really-positive.png" width="30" />
                                        )}
                                    </p>
                                </div>
                            </div>
                            <div className="experience">
                                <p>How would you rate your experience?</p>
                                <div className="d-flex flex-row ml-2">
                                    <div className="ml-2" onClick={() => handleRating(1)}>
                                        {rating >= 1 ? (
                                            <img src="../images/blue-star.png" />
                                        ) : (
                                            <img src="../images/white-star.png" />
                                        )}
                                    </div>
                                    <div className="ml-2" onClick={() => handleRating(2)}>
                                        {rating >= 2 ? (
                                            <img src="../images/blue-star.png" />
                                        ) : (
                                            <img src="../images/white-star.png" />
                                        )}
                                    </div>
                                    <div className="ml-2" onClick={() => handleRating(3)}>
                                        {rating >= 3 ? (
                                            <img src="../images/blue-star.png" />
                                        ) : (
                                            <img src="../images/white-star.png" />
                                        )}
                                    </div>
                                    <div className="ml-2" onClick={() => handleRating(4)}>
                                        {rating >= 4 ? (
                                            <img src="../images/blue-star.png" />
                                        ) : (
                                            <img src="../images/white-star.png" />
                                        )}
                                    </div>
                                    <div className="ml-2" onClick={() => handleRating(5)}>
                                        {rating >= 5 ? (
                                            <img src="../images/blue-star.png" />
                                        ) : (
                                            <img src="../images/white-star.png" />
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className="suggestion mt-3 ">
                                <p >Share a feedback or suggestion</p>
                                <textarea className="feedbacksuggestion" onChange={(e) => handleFeedback(e)} id="w3review" value={feedbackMessage ? feedbackMessage : ''} name="feedbacksuggestion" rows="4" cols="33" placeholder=" Type your feedback...">
                                </textarea>
                            </div>
                            <div className="addascreenshot" style={{ position: "relative" }}>
                                <input multiple style={{ position: "absolute", cursor: "pointer" }} type="file" accept="image/*" onChange={(e) => handleFeedback(e)} name="feedbackscreenshot" className="hidden-file-input" />
                                <p style={{ position: "relative", zIndex: "1", backgroundColor: "white", pointerEvents: 'none', paddingBottom: "2px" }}><img src="../images/addscreenshot.png" />&nbsp; &nbsp;Add a screenshot</p>
                                {feedbackFile.map((file, index) => (
                                    <p style={{ position: "relative", padding: '0px 10px 0px 19px', margin: '2px 18px', backgroundColor: '#e5f6ff', width: 'fit-content', borderRadius: '5px' }}>
                                        <span onClick={() => removefile(index)} style={{ cursor: "pointer", position: 'absolute', top: '3px', left: '2px', backgroundColor: '#c5c3c3', color: 'red', padding: '1px 4px', borderRadius: '50%', fontSize: '7px' }}>X</span>
                                        {file.name}
                                    </p>
                                ))}
                                {/* {response ? (
                                    <div style={{ fontSize: '14px', textAlign: 'center', color: 'white', backgroundColor: '#55c2c3', borderRadius: '6px', margin: '6px' }}>
                                        <i className="fa fa-check" style={{ color: '#55c2c3', backgroundColor: 'white', padding: '1px', borderRadius: '50%' }}></i>&nbsp; &nbsp;
                                        Your Feedback recieved successfully!!
                                    </div>
                                ) : (
                                    <></>
                                )}
                                {process ? (
                                    <div style={{ fontSize: '14px', textAlign: 'center', color: 'white', backgroundColor: '#55c2c3', borderRadius: '6px', margin: '6px' }}>
                                        <i className="fa fa-check" style={{ color: '#55c2c3', backgroundColor: 'white', padding: '1px', borderRadius: '50%' }}></i>&nbsp; &nbsp;
                                        Please wait, submitting in progress...
                                    </div>
                                ) : (
                                    <></>
                                )} */}
                                {error ? (
                                    <div style={{ fontSize: '14px', textAlign: 'center', color: 'white', backgroundColor: '#55c2c3', borderRadius: '6px', margin: '6px', padding: '5px 0 5px 0' }}>
                                        <i className="fa fa-check" style={{ color: '#55c2c3', backgroundColor: 'white', padding: '1px', borderRadius: '50%' }}></i>&nbsp; &nbsp;
                                        Sorry.! something went wrong, please try again.
                                    </div>
                                ) : (
                                    <></>
                                )}
                            </div>


                            <div className="d-flex flex-row justify-content-center mb-4 mt-4">
                                <div className="mr-3">
                                    {feedbackEmoji > 0 && rating > 0 ? (
                                        <button type="submit" className="sendbutton" onClick={handleFeedbackCancel}>Cancel</button>
                                    ) : (
                                        <button type="submit" className="sendbutton" style={{ backgroundColor: "#a7a7a7", cursor: "not-allowed" }}>Cancel</button>
                                    )}
                                </div>
                                <div>
                                    {feedbackEmoji > 0 && rating > 0 ? (
                                        <button type="submit" className="sendbuttonn" onClick={handleFeedbackSend}>Send</button>
                                    ) : (
                                        <button type="submit" className="sendbuttonn" style={{ backgroundColor: "#a7a7a7", cursor: "not-allowed" }}>Send</button>
                                    )}

                                </div>
                            </div>
                            {response ? (
                                <div className="feedbacksentsuceess">
                                    <div className="d-flex flex-row">
                                        <div style={{ backgroundColor: "rgb(238, 255, 246)", border: "1px solid rgb(109, 225, 164)", padding: "5px 8px 5px 6px", borderRadius: "7px", fontSize: "12px", width: "fit-content", margin: "auto", position: "absolute", top: "25rem", left: "11rem" }}>
                                            <span>
                                                <img src="../../images/promocode.png" width="16" style={{ margin: "-2px 8px 0px 0px" }} />
                                            </span>
                                            Sent Successfully
                                        </div>
                                        <div></div>
                                    </div>
                                </div>
                            ) : (
                                <></>
                            )}
                            {/* {error ? (
                                <div className="feedbacksentsuceess">
                                    <div className="d-flex flex-row">
                                        <div style={{ backgroundColor: "rgb(238, 255, 246)", border: "1px solid rgb(109, 225, 164)", padding: "5px 8px 5px 6px", borderRadius: "7px", fontSize: "12px", width: "fit-content", margin: "auto", position: "absolute", top: "25rem", left: "11rem" }}>
                                            <span>
                                                <img src="../../images/promocode.png" width="16" style={{ margin: "-2px 8px 0px 0px" }} />
                                            </span>
                                            Sorry.! Please try again.
                                        </div>
                                        <div></div>
                                    </div>
                                </div>
                            ) : (
                                <></>
                            )} */}
                        </div>
                    )}


                </div>

                <div className="demand">
                    <button className="toggle-button" style={{ textTransform: 'none' }} onClick={toggleContentBox}>
                        {isOpen ? '' : ''} Feedback  <span className="caretwant">
                            {isOpen ? (
                                <i className="fa fa-caret-down" style={{ padding: "5px 7px 11px 10px" }}></i>
                            ) : (
                                <i className="fa fa-caret-up" style={{ float: 'right', padding: '3px 7px 11px 10px', position: 'absolute', right: '0' }}></i>
                            )}

                        </span>
                    </button>


                </div>


            </div>
        </>
    );
}
export default Feedback;