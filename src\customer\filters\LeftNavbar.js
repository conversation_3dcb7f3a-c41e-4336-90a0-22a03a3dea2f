import { useEffect, useMemo, useState } from "react";
import { useNavigate, useLocation, Outlet } from "react-router-dom";
import "../assests/css/filter/leftnavbarv2.css";
import "../assests/css/filter/job_title.css"
import UseTabStore from "../common-files/useGlobalState";

const menuItems = [
  { key: "home", icon: "images/e-home.png", activeIcon: "images/active-home.png", tooltip: "Home", path: "/home" },
  { key: "search", icon: "images/e-search.png", activeIcon: "images/active-search.png", tooltip: "Search", path: "/search" },
  { key: "saved", icon: "images/e-list.png", activeIcon: "images/active-menu.png", tooltip: "Saved List", path: "/saved-list" },
  { key: "enrich", icon: "images/enrich.png", activeIcon: "images/active-enrich.png", tooltip: "Enrichment", path: "/enrich" },
  { key: "grid", icon: "images/e-integration.png", activeIcon: "images/active-integration.png", tooltip: "Grid", path: "/grid" },
  { key: "e-guide", icon: "images/e-guide.png", activeIcon: "images/active-e-guide.png", tooltip: "E-guide", path: "/e-guide" },
  { key: "help", icon: "images/e-supprt.png", activeIcon: "images/active-guide.png", tooltip: "Help", path: "/help" },
  { key: "settings", icon: "images/settings.png", activeIcon: "images/active-setting.png", tooltip: "Settings", path: "/settings" },
];

const LeftNavBar = ({ children }) => {

   useMemo(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    if (!user) {
      window.location.href = "/";
    }
  }, []);

  const {
    setSettingsActiveTab
  } = UseTabStore();

  const navigate = useNavigate();
  const location = useLocation();

  const getActiveComponent = () => {
    const currentPath = location.pathname;
    const item = menuItems.find(item => currentPath.startsWith(item.path));
    return item ? item.key : "home";
  };

  const [activeComponent, setActiveComponent] = useState(getActiveComponent());

  useEffect(() => {
    setActiveComponent(getActiveComponent());
  }, [location.pathname]);

  const topIcons = menuItems.slice(0, 1);
  const middleIcons = menuItems.slice(1, 3);
  const bottomIcons = menuItems.slice(3, 5);
  const footerIcons = menuItems.slice(5);

  const handleIconClick = (path) => {
    if (path == "/settings") {
      setSettingsActiveTab("security");
    }
    navigate(path);
  };

  const renderIcons = (items) =>
    items.map(({ key, icon, activeIcon, tooltip, path }) => (
      <div
        key={key}
        className={`icon-wrapper ${activeComponent === key ? "active" : ""}`}
        onClick={() => handleIconClick(path)}
      >
        <img
          src={activeComponent === key ? activeIcon : icon}
          alt={key}
          className="sidebar-icon"
        />
        {tooltip && <span className="tooltip-text">{tooltip}</span>}
        {activeComponent === key && <div className="active-indicator" />}
      </div>
    ));

  return (
    <div className="d-flex" style={{ height: "100vh", overflow: "hidden" }}>
      {/* Sidebar */}
      <div
        className="new-sidebar d-flex flex-column justify-content-between align-items-center bg-white border-end"
        style={{
          width: "50px",
          height: "100vh",
          position: "fixed",
          left: 0,
          top: 0,
        }}
      >
        <div className="d-flex flex-column align-items-center w-100 pt-3 pb-3">
          <img src="images/rs-favicon.png" className="img-fluid mb-3" alt="logo" width="18" />
          {renderIcons(topIcons)}
          <hr className="sidebar-divider" />
          {renderIcons(middleIcons)}
          <hr className="sidebar-divider" />
          {renderIcons(bottomIcons)}
        </div>

        {/* Bottom footer icons */}
        <div className="d-flex flex-column align-items-center pb-3">
          {renderIcons(footerIcons)}
        </div>
      </div>

      {/* Main content */}
      <div
        className="no-horizontal-scroll-container">
        {children || <Outlet />}
      </div>
    </div>
  );
};

export default LeftNavBar;