import React, { useEffect, useRef, useState } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import { useNavigate } from "react-router-dom";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import {
  PostWithTokenNoCache,
  postWithToken,
} from "../../../customer/common-files/ApiCalls.js";
import "react-datepicker/dist/react-datepicker.css";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import AdminHeader from "../../layouts/AdminHeader.js";
import Alert from "../../../customer/common-files/alert.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import loadingGif from "../../../customer/assests/waiting.gif";

let PageSize = 10;
const AllPlans = (props) => {
  const allocatePopup = useRef();
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [showPopup, setShowPopup] = useState(false);
  const [planUrl, setPlanUrl] = useState("");
  const [checkValidEmail, setCheckValidEmail] = useState("");
  const [emailInput, setEmailInput] = useState("");
  const [serialNumbers, setSerialNumbers] = useState([]);
  const [emailErr, setEmailErr] = useState("");
  const [isAllocated, setIsAllocated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  useEffect(() => {
    const postData = async () => {
      try {
        let params = JSON.stringify({
          page: currentPage,
          pageSize: PageSize,
          sortBy: "DESC",
        });
        setLoading(true);

        const res = await PostWithTokenNoCache(
          ApiName.pricePlanePagination,
          params
        );
        if (res && "status" in res) {
          if (res.status == 200) {
            let jsonArray = res.data.data ? JSON.parse(res.data.data) : [];
            setTotalCount(jsonArray.totalItems);
            setTotalPages(jsonArray.totalPages);
            setData(jsonArray.items);
            setLoading(false);
          }
        }
      } catch (errors) {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
      }
    };
    postData();
  }, [currentPage]);

  useEffect(() => {
    // Calculate the starting serial number for the current page
    const startSerialNumber = (currentPage - 1) * PageSize + 1;

    // Generate an array of serial numbers based on the current page
    const newSerialNumbers = Array.from(
      { length: PageSize },
      (_, index) => startSerialNumber + index
    );

    setSerialNumbers(newSerialNumbers);
  }, [currentPage, PageSize]);

  // Get the current domain name
  const currentDomain = window.location.origin;

  function convertToUrlFriendlyString(inputString) {
    // Convert the string to lowercase
    const lowercaseString = inputString ? inputString.toLowerCase() : "";

    // Replace spaces with hyphens
    const urlFriendlyString = inputString
      ? lowercaseString.replace(/\s+/g, "-")
      : "";

    return urlFriendlyString;
  }

  const createNewPlan = async () => {
    navigate("/admin/create-new-plan");
  };

  const handleDelete = async (id) => {
    try {
      // Display a confirmation dialog before proceeding with the delete operation
      const confirmed = window.confirm(
        "Are you sure you want to delete this item?"
      );
      if (!confirmed) {
        // User canceled the delete operation
        return;
      }
      var raw = { id: id };
      const url = `${ApiName.deletePackegeDetails}`;
      const res = await postWithToken(url, raw);
      if (res && "status" in res) {
        if (res.status == 200) {
          //Remove the deleted item from the data array
          setData((prevData) => prevData.filter((item) => item.id !== id));
        }
      }
    } catch (error) {
      // Handle any errors
    }
  };

  const handleEdit = async (id) => {
    navigate("/admin/edit-plan/" + id);
  };

  const allocatePlan = (url) => {
    setPlanUrl(url);
    setShowPopup(true);
    allocatePopup.current.click();
  };

  const close = () => {
    setShowPopup(false);
  };

  const isValidEmail = (email) => {
    // Replace this with your email validation logic
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const isValidBusinessEmail = (email) => {
    // Check if the email is valid
    const regex = new RegExp(
      "^.+@((?!hotmail)(?!gmail)(?!ymail)(?!googlemail)(?!live)(?!gmx)(?!yahoo)(?!outlook)(?!msn)(?!icloud)(?!facebook)(?!aol)(?!zoho)(?!mail)(?!yandex)(?!hushmail)(?!lycox)(?!lycosmail)(?!inbox)(?!myway)(?!aim)(?!fastmail)(?!goowy)(?!juno)(?!shortmail)(?!atmail)(?!protonmail)(?!postmaster)(?!abuse)(?!admin)(?!all)(?!everyone)(?!ftp)(?!googlegroups.com)(?!investorrelations)(?!jobs)(?!marketing)(?!media)(?!noc)(?!prime)(?!privacy)(?!remove)(?!request)(?!root)(?!sales)(?!security)(?!spam)(?!subscribe)(?!usenet)(?!users)(?!uucp)(?!webmaster)(?!www)(?!info)(?!enquiries)(?!mail)(?!office)(?!head)(?!headteacher)(?!reception)(?!enquiry)(?!post)(?!email)(?!accounts)(?!london)(?!general)(?!enquires)(?!design)(?!support)(?!mailbox)(?!law)(?!service)(?!reservations)(?!information)(?!schooladmin)(?!secretary)(?!enq)(?!advice)(?!studio)(?!bristol)(?!headoffice)(?!bookings)(?!help)(?!main.department).)+..+$"
    );
    let result = regex.test(email.toLowerCase());

    if (result) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      let checkDomain = emailRegex.test(email);

      if (!checkDomain) {
        setEmailErr("Please enter a valid business email address");
        setCheckValidEmail(false);
        return false;
      } else {
        setCheckValidEmail(false);
        setEmailErr("");
        return true;
      }
    } else {
      setCheckValidEmail(false);
      setEmailErr("Please enter a valid business email address");
      return false;
    }
  };

  const allocatePlanToUser = async (e) => {
    e.preventDefault();

    // Split the emailInput by comma and trim each email
    const emails = emailInput.split(",").map((email) => email.trim());

    // Validate emails (you can replace this with your own validation logic)
    const isValidEmails = emails.every((email) => isValidEmail(email));

    if (!isValidEmails) {
      setEmailErr("Separate multiple emails with a comma");
      setCheckValidEmail(false);
      return false;
    }
    const isValidBusinessEmails = emails.every((email) =>
      isValidBusinessEmail(email)
    );
    if (!isValidBusinessEmails) {
      setEmailErr("Please enter a valid business email address");
      setCheckValidEmail(false);
      return false;
    }

    let flag = 1;
    if (isValidEmails && isValidBusinessEmails) {
      setCheckValidEmail(true);
      for (let i = 0; i < emails.length; i++) {
        const params = {
          register_link: planUrl,
          email: emails[i],
        };
        const res = await PostWithTokenNoCache(
          ApiName.notifyCustomPlan,
          params
        );
        if (res && "status" in res && res.status !== 200) {
          flag = 0;
        }
      }
      if (flag) {
        setIsAllocated(true);
        setEmailInput("");
      }
      setTimeout(() => {
        setIsAllocated("");
      }, 5000);
    } else {
      if (!emailErr) setEmailErr("Separate multiple emails with a comma");
      setCheckValidEmail(false);
    }
  };
  const handleEmailInputChange = (e) => {
    setCheckValidEmail(true);
    setEmailInput(e.target.value);
  };
  return (
    <div className={S.app}>
      <AdminHeader />
      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div style={{ width: "100%" }}>
              <aside className={S.right_sidebar}>
                <h5 className={S.FreemiumUsers2}>All Plans</h5>
                <div className="pr-2">
                  <div className="d-flex flex-row justify-content-between">
                    <div>
                      <div className="d-flex flex-row">
                        <div>
                          <h5 className={S.totla_users}>Total plans:</h5>
                        </div>
                        <div>
                          <p className={S.datalegnth}>{totalCount}</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="admin-search">
                        {/* Step 1: Add a search input field */}
                        <button
                          type="button"
                          onClick={createNewPlan}
                          className={S.cnpln}
                        >
                          Create New Plan
                        </button>
                      </div>
                    </div>
                  </div>
                  <table>
                    <thead>
                      <tr className="table-active text-center">
                        <th className={S.packagename}>Sl No</th>
                        <th className={S.packagename}>Plans Name</th>
                        <th className={S.packagename}>Credits</th>
                        <th className={S.packagename}>Profile Views</th>
                        <th className={S.packagename}>Price</th>
                        <th className={S.packagename}>Reg Link</th>
                        <th className={S.packagename}>Action</th>
                        <th className={S.packagename}>Edit</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.map((item, i) => (
                        <tr className={S.tablelight} key={i}>
                          <td className={S.tabledataspacing}>
                            &nbsp;&nbsp;&nbsp;{(currentPage - 1) * 10 + i + 1}
                          </td>
                          <td className={S.tabledataspacing}>
                            {item.package_name}
                          </td>
                          <td className={S.tabledataspacing}>{item.export}</td>
                          <td className={S.tabledataspacing}>
                            {item.no_of_contact_views}
                          </td>
                          <td className={S.tabledataspacing}>{item.price}</td>
                          <td className={S.tabledataspacing}>
                            {item.package_name ? (
                              <a
                                href={`${currentDomain}/sign-up/${convertToUrlFriendlyString(
                                  item.package_name
                                )}`}
                                target="_blank"
                              >
                                {convertToUrlFriendlyString(item.package_name)}
                              </a>
                            ) : null}
                          </td>

                          <td className={S.tabledataspacing}>
                            <button
                              type="button"
                              className={S.Allocate}
                              onClick={() =>
                                allocatePlan(
                                  `${currentDomain}/sign-up/${convertToUrlFriendlyString(
                                    item.package_name
                                  )}`
                                )
                              }
                            >
                              Allocate&nbsp;&nbsp;&nbsp;
                              <i className="fa fa-telegram"></i>
                            </button>
                          </td>
                          <td className={S.tabledataspacing}>
                            <div className="d-flex flex-row justify-content-center">
                              <div className="mr-2">
                                <i
                                  className="fa fa-edit"
                                  onClick={() => handleEdit(item.id)}
                                  style={{
                                    fontSize: "18px",
                                    color: "#55C2C3",
                                    cursor: "pointer",
                                  }}
                                ></i>
                              </div>
                              <div className="ml-2">
                                <i
                                  className="fa fa-trash"
                                  onClick={() => handleDelete(item.id)}
                                  style={{
                                    fontSize: "18px",
                                    color: "red",
                                    cursor: "pointer",
                                  }}
                                ></i>
                              </div>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </aside>
            </div>
          ) : (
            <div className="mx-auto mt-5" style={{ display: loading }}>
              <img
                src={loadingGif}
                alt="Loading"
                className="loader"
                width="400"
              />
            </div>
          )}
        </div>

        <div className={S.content}>{/* Main content */}</div>
      </div>
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={totalPages}
        pageSize={1}
        onPageChange={(page) => {
          setCurrentPage(page);
        }}
      />
      <>
        <button
          type="button"
          className="btn btn-info btn-md"
          data-toggle="modal"
          data-target="#allocatePlan"
          ref={allocatePopup}
          style={{ display: "none" }} // Hide the button, it's just for triggering click programmatically
          data-backdrop="true"
        ></button>
        <div
          className="modal fade bd-example-modal-sm show"
          tabIndex="-1"
          role="dialog"
          aria-labelledby="mySmallModalLabel"
          aria-hidden="true"
          id="allocatePlan"
        >
          <div className="modal-dialog modal-dialog-centered" role="document">
            <div className="modal-content">
              <div className="row">
                <div className="col-md-10 text-center">
                  <h5 className={S.SentPrompt}>Allocate Plan to Customer</h5>
                </div>
                <div className="col-md-2">
                  <button
                    type="button"
                    onClick={close}
                    className="close"
                    data-dismiss="modal"
                    aria-label="Close"
                  >
                    <span aria-hidden="true">&times;&nbsp;</span>
                  </button>
                </div>
              </div>
              <div className="modal-body">
                <div className="form-group align-center">
                  <input
                    type="text"
                    className={S.savetemplate}
                    id="exampleInputText1"
                    aria-describedby="textHelp"
                    placeholder="Enter email address"
                    value={emailInput}
                    onChange={handleEmailInputChange}
                  />
                  {!checkValidEmail && checkValidEmail !== "" ? (
                    <span className="email-error-message text-center">
                      <p>{emailErr}</p>
                    </span>
                  ) : (
                    <></>
                  )}
                </div>
              </div>
              <div className="text-center">
                <button
                  type="button"
                  onClick={allocatePlanToUser}
                  className={
                    checkValidEmail ? S.savechanges2 : `${S.savechanges2}`
                  }
                >
                  Send
                </button>
              </div>
              {isAllocated ? (
                <>
                  <div className={S.successmessages}>
                    <div className="d-flex flex-row">
                      <div>
                        <img
                          className={S.procodeimg}
                          src="../images/promocode.png"
                          width="15"
                        />
                      </div>
                      <div>
                        <p className={S.que}>
                          Custom plan allocated successfully
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="mt-3"></div>
                </>
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </>
      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};
export default AllPlans;
