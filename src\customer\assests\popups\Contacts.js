import React from "react";
import '../../assests/css/filter/popup.css';
const Contacts = () => {
    return (
        <div>
            <button type="button" className="btn btn-primary" data-toggle="modal" data-target="#exampleModalCenter">
                Launch demo modal
            </button>

            <div className="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
                aria-hidden="true">
                <div className="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div className="modal-content">

                        <div className="modal-header text-white">
                            <h5 className="modal-title text-center">Contact Us</h5>
                            <button type="button" className="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>

                        <div className="modal-body">
                            <form action="process-data.php" method="POST" onsubmit="return validateForm()" className="contact-form">

                                <div className="form-row">
                                    <div className="form-group col-md-6">
                                        <label for="Firstname" className="label-names">First Name</label>
                                        <input type="text" className="form-control formcolor" id="FirstName" name="FirstName" required />

                                    </div>
                                    <div className="form-group col-md-6">
                                        <label for="Lastname" className="label-names">Last Name</label>
                                        <input type="text" className="form-control formcolor " id="Lastname" name="Lastname" required />

                                    </div>
                                </div>

                                <div className="row">
                                    <div  className="col-6 mb-3">
                                        <div>
                                            <label for="email" className="label-names">Business Email Address</label>
                                            <input type="email" className="form-control" id="InputEmail1" aria-describedby="emailHelp" />
                                        </div>
                                        <div className="spacer"></div>
                                        <div >
                                            <label for="phoneNumber" className="label-names">Phone Number</label>
                                            <input type="text" className="form-control formcolor" id="phoneNumber" name="phoneNumber"
                                                oninput="ValidateNo()" onkeypress="return isNumber(event)" required />
                                        </div>
                                    </div>
                                    <div  className="col-6">
                                        <div>
                                            <div className="form-group">
                                                <label for="requirement" className="label-names">Tell Us Your Requirement</label>
                                                <textarea className="form-control" id="requirement" rows="4" name="requirement" required></textarea>
                                            </div>
                                        </div>
                                    </div>

                                </div>


                                <div className="text-center"><button type="submit" id="btnSubmit" className="btn btn-primary">Submit</button></div>

                            </form>
                        </div>
                        <footer>
                            <p className="Reach-us"> Reach us at </p>
                            <div className="contact-info">

                                <p><img src="./images/Group 51747.png" /><a href="mailto:<EMAIL>"><EMAIL></a></p>
                                <p><img src="./images/Group 51746.png" /><a href="tel:+1234567890">+****************</a></p>
                            </div>
                        </footer>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Contacts;