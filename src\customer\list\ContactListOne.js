import React, { useEffect, useState, useContext } from "react";
import "../assests/css/filter/contact_list_one.css";
import { Link, useNavigate, useParams } from "react-router-dom";
import UseTabStore from "../common-files/useGlobalState.js";
import Alert from "../common-files/alert.js";
import { ApiName } from "../common-files/ApiNames.js";
import loadingGif from "../assests/waiting.gif";
import Pagination from "../pagination/Pagination.js";
import Header from "../layouts/Header.js";
import ContactModal from "../filters/ContactModal.js";
import {
  postWithToken,
  PostWithTokenNoCache,
} from "../common-files/ApiCalls.js";
import DashboardContext from "../common-files/ContextDashboard.js";
const ContactListOne = () => {

  const pathname = window.location.pathname.split('/').filter(Boolean);

  const { dataDC } = useContext(DashboardContext);

  const [pageSize, setPageSize] = useState(
    dataDC.membership === "trail" ? 10 : dataDC.membership === "prime" ? 25 : 10
  );
  const { id } = useParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItem, setTotalItem] = useState(1);
  const [listItemData, setListItemData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [listDetail, setListDetail] = useState(false);
  const [listCount, setListCount] = useState(0);
  const [selectUsers, setSelectUsers] = useState([]);
  const [wishListItedmIds, setWishListItedmIds] = useState([]);
  const [checkbox, setCheckbox] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [showSuccessMessage, setSuccessMessage] = useState(false);
  const [showContactModal, setShowContactModal] = useState(true);
  const [viewRevealedEmail, setViewRevealedEmail] = useState(null);

  const {
    revealedEmail,
    checkIsRevealedHistory,
    emailRevealedHistory,
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultErrorMsg,
    setDefaultAlert,
    setViewModal,
    setContactModelId,
    selectedContact,
    viewModal,
    contactModelId,
    setSelectedContact,
    setIsLowViewCredits,
    setCheckIsRevealedHistory,
    setEmailRevealedHistory,
    setRevealedEmail
  } = UseTabStore();

  // useEffect(() => {
  //   emailVerifyHistory();
  // }, [])

  useEffect(() => {
    fetchList();
  }, [currentPage]);

  const closeEmail = async (id) => {
    viewRevealedEmail == id ? setViewRevealedEmail("") : setViewRevealedEmail(id);
  }

  const convertToProperCase = (val) => {
    if (val) {
      // Split words based on whitespace or non-alphanumeric characters
      const words = val.split(/\s+|\W+/);
      // Convert the first letter of each word to uppercase
      const capitalizedWords = words.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase());
      // Join the capitalized words back into a string
      return capitalizedWords.join(' ');
    } else {
      return val;
    }
  }

  // const viewMail = async (id) => {
  //   // Get the hidden input field by its id
  //   const hiddenInput = document.getElementById(`hiddenInput-${id}`);

  //   // Check if the element exists
  //   if (hiddenInput) {
  //     const textToCopy = hiddenInput.value;

  //     // Create a temporary textarea element to copy the text to the clipboard
  //     const tempTextArea = document.createElement("textarea");
  //     tempTextArea.value = textToCopy;
  //     document.body.appendChild(tempTextArea);
  //     tempTextArea.select();
  //     document.execCommand("copy");
  //     document.body.removeChild(tempTextArea);

  //   }
  //   viewRevealedEmail == id ? setViewRevealedEmail("") : setViewRevealedEmail(id);
  // }

  // const emailVerifyHistory = async () => {
  //   try {
  //     await PostWithTokenNoCache(ApiName.emailVerifyHistory, {})
  //       .then(function (response) {
  //         if (response.data.status === 200) {
  //           const dataObj = JSON.parse(response.data.data);
  //           setEmailRevealedHistory(dataObj);
  //           setRevealedEmail([]);
  //         }
  //       })
  //       .catch(function (errors) { });
  //   } catch (error) {
  //   }
  // };

  const revealMail = async (id) => {
    const params = {
      dataIds: [id],
      singleDownload: true
    };
    setRevealedEmail([...new Set([...revealedEmail, id])]);
    await PostWithTokenNoCache(ApiName.revealEmail, params)
      .then(function (response) {
        if (response.data.status === 200) {
          setCheckIsRevealedHistory(!checkIsRevealedHistory);
          // emailVerifyHistory(); // get downloaded data
        }
      })
      .catch(function (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors.response.data.message);
        setDefaultAlert(true);
      });
  }

  const fetchList = async () => {
    try {
      const param = JSON.stringify({
        id: id,
      });

      const res = await PostWithTokenNoCache(ApiName.fetchList, param);
      if (res && "status" in res) {
        if (res.status == 200) {
          const records = JSON.parse(res?.data?.data);
          setListCount(records.listCount);
          setListDetail(records);
          getAllListRecords();
        } else {
          setIsLoading(false);
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
        }
      } else {
        setIsLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(res.response.data.message);
        setDefaultAlert(true);
      }
    } catch (error) {
      setIsLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
    }
  };

  const isDisabled = pathname[0] === 'contact-list';

  const disabledStyle = {
    pointerEvents: 'none',
    opacity: 0.5,
    cursor: 'not-allowed',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '0.5rem', // Spacing between text and icon
  };

  const getAllListRecords = async () => {
    const param = JSON.stringify({
      page: currentPage,
      pageSize: pageSize,
      searchPattern: {
        searchBy: "CONTACT",
        wishListId: id,
      },
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.getAllListRecords, param);
      if (res && "status" in res) {
        if (res.status == 200) {
          const record = JSON.parse(res.data.data);
          setWishListItedmIds(record?.searchPattern?.wishListItems);
          setTotalItem(record?.totalCount);

          const records = record.records;
          const updatedrecord = records.map((item) => ({
            ...item,
            isSelected: selectUsers.includes(item.id),
            //   isSelected: selectUsers.includes(user.userId),
          }));
          setCheckbox(false);
          const allUserIdsSelected = records.every((item) =>
            selectUsers.includes(item.id)
          );
          setCheckbox(allUserIdsSelected);
          let newData = [...updatedrecord];
          setListItemData(newData);
          setIsLoading(false);
        } else {
          setTotalItem(0);
          setListItemData([]);
          setIsLoading(false);
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
        }
      } else {
        setTotalItem(0);
        setListItemData([]);
        setIsLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(res.response.data.message);
        setDefaultAlert(true);
      }
    } catch (error) {
      setTotalItem(0);
      setListItemData([]);
      setIsLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
    }
  };
  const deleteItem = async (id) => {
    deleteItemFromList([id]);
  };
  const deleteItemFromList = async (ids) => {
    const primaryKey = wishListItedmIds
      .filter((item) => ids.includes(item.dataId))
      .map((item) => item.id);
    try {
      const param = JSON.stringify({
        ids: primaryKey,
      });
      const res = await PostWithTokenNoCache(
        ApiName.listItemDeleteByIds,
        param
      );
      if (res && "status" in res) {
        if (res.status == 200) {
          const count = listCount - ids.length;
          setListCount(count);
          const param = JSON.stringify({
            id: listDetail.id,
            listName: listDetail.listName,
            wishListSection: listDetail.wishListSection,
            status: "OPEN",
            listCount: count,
          });
          try {
            const res = await PostWithTokenNoCache(ApiName.updateList, param);
            if (res && "status" in res) {
              if (res.status == 200) {
                setSuccessMessage(true);
                getAllListRecords();
              } else {
                setButtonType("error");
                setDefaultErrorMsg(res.response.data.message);
                setDefaultAlert(true);
              }
            } else {
              setButtonType("error");
              setDefaultErrorMsg(res.response.data.message);
              setDefaultAlert(true);
            }
          } catch (error) {
            setButtonType("error");
            setDefaultErrorMsg(error?.response?.data?.message);
            setDefaultAlert(true);
          }
        } else {
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
        }
      } else {
        setButtonType("error");
        setDefaultErrorMsg(res.response.data.message);
        setDefaultAlert(true);
      }
    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
    }
  };
  const deleteSelectedItem = () => {
    if (selectUsers.length > 0) {
      setIsPopupOpen(true);
    }
  };
  const selectAllUser = (isChecked) => {
    setCheckbox(isChecked);
    let updatedData;
    if (isChecked) {
      setSelectUsers((prevSelectUsers) => {
        const updatedSelectUsers = [...prevSelectUsers];
        listItemData.forEach((element) => {
          if (!updatedSelectUsers.includes(element.id)) {
            updatedSelectUsers.push(element.id);
          }
        });
        return updatedSelectUsers;
      });
      updatedData = listItemData.map((item) => ({
        ...item,
        isSelected: true,
      }));
    } else {
      const usersToRemove = [];
      listItemData.forEach((element) => {
        if (selectUsers.includes(element.id)) {
          usersToRemove.push(element.id);
        }
      });
      setSelectUsers((prevSelectUsers) =>
        prevSelectUsers.filter((id) => !usersToRemove.includes(id))
      );
      updatedData = listItemData.map((item) => ({
        ...item,
        isSelected: false,
      }));
    }
    setListItemData([...updatedData]);
  };
  const selectUsersOnChange = (id) => {
    let updatedData;
    if (selectUsers.includes(id)) {
      setSelectUsers(selectUsers.filter((userId) => userId !== id));

      updatedData = listItemData.map((item) => ({
        ...item,
        isSelected: item.id == id ? false : item.isSelected,
      }));
    } else {
      setSelectUsers([...selectUsers, id]);
      updatedData = listItemData.map((item) => ({
        ...item,
        isSelected: item.id == id ? true : item.isSelected,
      }));
    }

    setListItemData(updatedData);
  };
  useEffect(() => {
    setCheckbox(false);
    const allUserIdsSelected = listItemData.every((item) =>
      selectUsers.includes(item.id)
    );
    setCheckbox(allUserIdsSelected);
  }, [selectUsers]);

  const onCloseDelete = () => {
    setIsPopupOpen(false);
  };
  const onDeleteSuccess = async () => {
    setIsPopupOpen(false);
    deleteItemFromList(selectUsers);
  };

  const successMessageStyle = {
    position: "absolute",
    bottom: "20%",
    left: "46%",
    backgroundColor: "#EEFFF6",
    border: "1px solid #6DE1A4",
    padding: "4px 18px",
    borderRadius: "7px",
    fontSize: "14px",
  };
  const check = {
    color: "#6DE1A4",
    backgroundColor: "white",
    borderRadius: "50%",
    padding: "3px",
    fontSize: "10px",
    position: "absolute",
    top: "3px",
    left: "3px",
  };
  const checkblock = {
    backgroundColor: "#6DE1A4",
    padding: "3px 11px",
    marginRight: "5px",
    borderRadius: "4px",
    position: "relative",
  };

  useEffect(() => {
    let timer;
    if (showSuccessMessage) {
      timer = setTimeout(() => {
        setSuccessMessage(false);
      }, 5000);
    }
    return () => clearTimeout(timer);
  }, [showSuccessMessage]);

  const view = (id) => {
    setViewModal(true);
    setContactModelId(id);
  };

  useEffect(() => {
    fetchSingleContact(contactModelId);
  }, [contactModelId]);

  const urlSingleFetch = ApiName.fetchSingleRecord;
  async function fetchSingleContactRecord(
    data,
    total_balance_contact_view,
    total_balance_credit
  ) {
    const addActionKey = {
      ...data,
      action: "view",
      searchBy: "contact",
    };
    const res = await postWithToken(urlSingleFetch, addActionKey);
    if (res && "status" in res) {
      if (res.data.status === 200) {
        let dataObj = JSON.parse(res.data.data);
        if (dataObj[0] === undefined || dataObj[0] === null) return;
        if (showContactModal) {
          setIsLowViewCredits(false);
          setSelectedContact(dataObj[0]);
          setDefaultAlert(false);
          if (total_balance_contact_view != null) {
            let totalBalanceContactView = total_balance_contact_view;
            if ("membership" in dataDC && dataDC.membership === "prime") {
              totalBalanceContactView += 1;
            } else {
              totalBalanceContactView -= 1;
            }
            const requestBody = {
              total_balance_contact_view: totalBalanceContactView,
              total_balance_credit: total_balance_credit,
            };
          }
        }
      }
    }
  }

  const fetchSingleContact = async (dataId) => {
    if (dataId) {
      const data = { id: dataId };
      if ("membership" in dataDC && dataDC.membership === "prime") {
        // fetchSingleContactRecord(data,null,null);
        try {
          const activeCreaditsFetchRes = await PostWithTokenNoCache(
            ApiName.activeCredit,
            {}
          );
          if (activeCreaditsFetchRes && "status" in activeCreaditsFetchRes) {
            if (activeCreaditsFetchRes.data.status === 200) {
              let dataObj = JSON.parse(activeCreaditsFetchRes.data.data);
              let total_balance_contact_view =
                "total_balance_contact_view" in dataObj
                  ? dataObj.total_balance_contact_view
                  : null;
              if (total_balance_contact_view == "Unlimited")
                total_balance_contact_view = 0;
              let total_balance_credit =
                "total_balance_credit" in dataObj
                  ? dataObj.total_balance_credit
                  : null;
              if (
                total_balance_contact_view !== undefined &&
                total_balance_contact_view !== null &&
                Number(total_balance_contact_view) != NaN
              ) {
                fetchSingleContactRecord(
                  data,
                  Number(total_balance_contact_view),
                  Number(total_balance_credit)
                );
              } else {
                setSelectedContact(null);
                setIsLowViewCredits(true);
              }
            }
          } else {
            setSelectedContact(null);
            setIsLowViewCredits(true);
          }
        } catch (error) {
          setButtonType("error");
          setDefaultErrorMsg(error.response.data.message);
          setDefaultAlert(true);
        }
      } else {
        try {
          const activeCreaditsFetchRes = await PostWithTokenNoCache(
            ApiName.activeCredit,
            {}
          );
          if (activeCreaditsFetchRes && "status" in activeCreaditsFetchRes) {
            if (activeCreaditsFetchRes.data.status === 200) {
              let dataObj = JSON.parse(activeCreaditsFetchRes.data.data);
              let total_balance_contact_view =
                "total_balance_contact_view" in dataObj
                  ? dataObj.total_balance_contact_view
                  : null;
              let total_balance_credit =
                "total_balance_credit" in dataObj
                  ? dataObj.total_balance_credit
                  : null;
              if (
                total_balance_contact_view &&
                Number(total_balance_contact_view) != NaN &&
                Number(total_balance_contact_view) > 0
              ) {
                fetchSingleContactRecord(
                  data,
                  Number(total_balance_contact_view),
                  Number(total_balance_credit)
                );
              } else {
                setSelectedContact(null);
                setIsLowViewCredits(true);
              }
            }
          } else {
            setSelectedContact(null);
            setIsLowViewCredits(true);
          }
        } catch (error) {
          setButtonType("error");
          setDefaultErrorMsg(error.response.data.message);
          setDefaultAlert(true);
        }
      }
    }
  };

  return (
    <>
      <div>
        <Header />
        {!isLoading ? (
          <div className="p-4">
            <div className="d-flex flex-row">
              <Link to="/saved-list">
                <img src="../images/arrow-left.png" />
              </Link>
              <div>
                <p className="savedcontactlist">Saved Lists</p>
              </div>
            </div>

            <div className="contactlistfirstlayer">
              <div className="d-flex flex-row">
                <div>
                  <p className="ContactList3">{listDetail.listName}</p>
                </div>
                <div>
                  <p className="contactlistSearchResultFound">
                    Search Result Found{" "}
                    <span className="fivehundradcontact">
                      {totalItem} Contacts
                    </span>
                  </p>
                </div>
                <div className="trasho">
                  {listDetail.status == "IN_PROGRESS" ||
                    listDetail.status == "VERIFIED" ||
                    listDetail.status == "DOWNLOADED" ? (
                    <i
                      className="fa fa-trash-o"
                      style={{
                        fontSize: "20px",
                        padding: "2px 2px 2px 2px",
                        backgroundColor: "#fff",
                        margin: "0 0 0 10px",
                        color: "#C2C8CE",
                        cursor: "auto",
                      }}
                    ></i>
                  ) : selectUsers.length == 0 ? (
                    <i
                      className="fa fa-trash-o"
                      style={{
                        fontSize: "20px",
                        padding: "2px 2px 2px 2px",
                        backgroundColor: "#fff",
                        margin: "0 0 0 10px",
                        color: "#C2C8CE",
                        cursor: "auto",
                      }}
                    ></i>
                  ) : (
                    <img
                      onClick={() => deleteSelectedItem()}
                      src="../images/delete.png"
                      style={{
                        cursor: "pointer",
                        padding: "2px 6px 2px 6px",
                        margin: "0 0 0 10px",
                        backgroundColor: "#fff",
                        padding: "2px 3px 2px 3px",
                      }}
                    />
                  )}
                </div>
              </div>

              <div className="">
                <table style={{ borderSpacing: "0 8px" }}>
                  <thead className="contactlistheader">
                    <th className="contactlistthh">
                      <input
                       style={{ margin: "0 8px 0 15px" }}
                        type="checkbox"
                        checked={checkbox}
                        onChange={(e) => selectAllUser(e.target.checked)}
                      />
                      Contact Name &nbsp;
                      <i
                        className="fa fa-sort"
                        style={{ fontSize: "12px" }}
                      ></i>
                    </th>
                    <th className="contactlistth">
                      Job Title &nbsp;
                      <i
                        className="fa fa-sort"
                        style={{ fontSize: "12px" }}
                      ></i>
                    </th>
                    <th className="contactlistth">Email</th>
                    <th className="contactlistth">
                      Company Name &nbsp;
                      <i
                        className="fa fa-sort"
                        style={{ fontSize: "12px" }}
                      ></i>
                    </th>
                    <th className="contactlistth">Location</th>
                    <th
                      className="contactlistth"
                      style={{ padding: "0 0 0 0px" }}
                    >
                      Actions
                    </th>
                  </thead>
                  <tbody className="contactlistbody">
                    {!isLoading && listItemData.length > 0 ? (
                      listItemData.map((item) => {

                        let isRevealIdExist = emailRevealedHistory.filter(data => {
                          return data.dataId === item.id;
                        });

                        return (
                          <tr
                            style={{
                              boxShadow: "rgba(175, 175, 175, 0.22) 0px 1px 5px",
                              borderRadius: "2px",
                            }}
                          >
                            <td className="" style={{ fontWeight: "600", padding: "10px 0 10px 15px", fontSize: "14px" }}>
                              <input
                                type="checkbox"
                                checked={item.isSelected}
                                onChange={() => selectUsersOnChange(item.id)}
                              />
                              {convertToProperCase(item.contact_name)}
                            </td>
                            <td className="" style={{ fontWeight: "400", fontSize: "14px", padding: "0 0 0 0" }}>
                              {convertToProperCase(item.contact_job_title_1)}
                            </td>
                            <td className="" style={{ fontWeight: "400", fontSize: "14px", padding: "0 0 0 0" }}>

                              <button
                                style={isDisabled ? disabledStyle : {}}
                                type="button"
                                className="RevealEmail"
                              >
                                Reveal Email
                              </button>

                              {/* {
                                isRevealIdExist && isRevealIdExist.length < 1 && revealedEmail.includes(item?.id) ?
                                  (
                                    <button className="Verifyingemail">
                                      <i
                                        className="fa fa-refresh fa-spin"
                                        style={{
                                          padding: "0px 2px 0 5px",
                                          color: "#B7B7B7",
                                        }}
                                      ></i>
                                      Verifying email <span className="percentage"></span>
                                    </button>
                                  ) : isRevealIdExist.length === 0 ||
                                    isRevealIdExist[0].dataId !== item.id ? (
                                    <button
                                      type="button"
                                      className="RevealEmail"
                                      onClick={() => revealMail(item.id)}
                                    >
                                      Reveal Email
                                    </button>
                                  ) : isRevealIdExist[0].dataId === item.id &&
                                    isRevealIdExist[0].verifierStatus === "Invalid" ? (
                                    <button
                                      type="button"
                                      className="dashboardEmailNotfound"
                                    >
                                      Email Not found
                                    </button>
                                  ) : isRevealIdExist[0].dataId === item.id &&
                                    (isRevealIdExist[0].verifierStatus === "Risky" || isRevealIdExist[0].verifierStatus === "catch_all" || isRevealIdExist[0].verifierStatus === "Uncertain") ? (
                                    <>
                                      <input
                                        type="hidden"
                                        id={`hiddenInput-${item.id}`}
                                        value={isRevealIdExist[0].emailId}
                                      />
                                      {viewRevealedEmail === item.id ? (
                                        <div className="verifiedemailleayerone">
                                          <div className="d-flex flex-row justify-content-end">
                                            <div onClick={closeEmail}>
                                              <img className="emailcancelbutton" src="../../images/cancel.png" />
                                            </div>
                                          </div>
                                          <div className="d-flex flex-row">
                                            <div>
                                              <p
                                                className="douglas45gmailcom"
                                                onClick={() => viewMail(item.id)}
                                              >
                                                {isRevealIdExist[0].emailId}&nbsp;

                                              </p>
                                            </div>
                                            <div>
                                              <i className="fas fa-copy" style={{ color: "#55C2C3", fontSize: "10px" }}></i>
                                            </div>
                                          </div>

                                          <small className="Businessemail">
                                            Business email
                                          </small>

                                          <div className="verifiedemailleayerone2">
                                            <div className="d-flex flex-row justify-content-end">
                                              <div onClick={closeEmail}>
                                                <img className="emailcancelbutton" src="../../images/cancel.png" />
                                              </div>
                                            </div>

                                            <p className="VerifiedEmail2">
                                              <i class="fa fa-envelope"></i> Verified Email
                                            </p>
                                            <small className="Thisemailisverifiedandvalid">
                                              This email is verified and valid
                                            </small>
                                          </div>
                                        </div>
                                      ) : (
                                        <div
                                          className="d-flex flex-row"
                                          onClick={() => viewMail(item.id)}
                                        >
                                          <div>
                                            <i className="fa fa-copy"></i>&nbsp;&nbsp;&nbsp;
                                          </div>
                                          <div>
                                            <p className="dashboardemail">
                                              {isRevealIdExist[0].emailId}
                                            </p>
                                          </div>
                                        </div>
                                      )}
                                    </>
                                  ) : isRevealIdExist[0].dataId === item.id &&
                                    isRevealIdExist[0].verifierStatus === "Valid" || isRevealIdExist[0].verifierStatus === "Guaranteed" ? (
                                    <>
                                      <input
                                        type="hidden"
                                        id={`hiddenInput-${item.id}`}
                                        value={isRevealIdExist[0].emailId}
                                      />
                                      {viewRevealedEmail === item.id ? (

                                        <div className="verifiedemailleayerone">
                                          <div className="d-flex flex-row justify-content-end">
                                            <div onClick={closeEmail}>
                                              <img className="emailcancelbutton" src="../../images/cancel.png" />
                                            </div>
                                          </div>
                                          <div className="d-flex flex-row">
                                            <div>
                                              <p
                                                className="douglas45gmailcom"
                                                onClick={() => viewMail(item.id)}
                                              >
                                                {isRevealIdExist[0].emailId}&nbsp;

                                              </p>
                                            </div>
                                            <div>
                                              <i className="fas fa-copy" style={{ color: "#55C2C3", fontSize: "10px" }}></i>
                                            </div>
                                          </div>

                                          <small className="Businessemail">
                                            Business email
                                          </small>

                                          <div className="verifiedemailleayerone2">
                                            <div className="d-flex flex-row justify-content-end">
                                              <div onClick={closeEmail}>
                                                <img className="emailcancelbutton" src="../../images/cancel.png" />
                                              </div>
                                            </div>
                                            <p className="VerifiedEmail2">
                                              <i class="fa fa-envelope"></i> Verified Email
                                            </p>
                                            <small className="Thisemailisverifiedandvalid">
                                              This email is verified and valid
                                            </small>
                                          </div>
                                        </div>
                                      ) : (
                                        <div
                                          className="d-flex flex-row"
                                          onClick={() => viewMail(item.id)}
                                        >
                                          <div>
                                            <i className="fa fa-copy"></i>&nbsp;&nbsp;&nbsp;
                                          </div>
                                          <div>
                                            <p className="dashboardemail">
                                              {isRevealIdExist[0].emailId}
                                            </p>
                                          </div>
                                        </div>
                                      )}
                                    </>
                                  ) : (
                                    <button
                                      type="button"
                                      className="RevealEmail"
                                      onClick={() => revealMail(item.id)}
                                    >
                                      Reveal Email
                                    </button>
                                  )} */}
                            </td>
                            <td className="" style={{ fontWeight: "400", fontSize: "14px", padding: "0 0 0 0" }}>
                              {convertToProperCase(item.company_company_name || "")}
                            </td>
                            <td className="" style={{ fontWeight: "400", fontSize: "14px", padding: "0 0 0 0" }}>
                              {convertToProperCase(item.company_address_city || "")},{" "}
                              {convertToProperCase(item.company_address_state || "")},{" "}
                              {convertToProperCase(item.company_address_country || "")}
                            </td>
                            <td className="" style={{ fontWeight: "400", fontSize: "14px", padding: "0 0 0 0" }}>
                              <div className="d-flex flex-row">
                                <div>
                                  <img
                                    style={{
                                      cursor: "pointer",
                                      padding: "0px 6px 0 0px",
                                    }}
                                    src="../images/eyee.png"
                                    onClick={() => view(item.id)}
                                  />
                                </div>
                                <div className="savedlistverticalline"></div>
                                <div>
                                  {listDetail.status == "IN_PROGRESS" ||
                                    listDetail.status == "VERIFIED" ||
                                    listDetail.status == "DOWNLOADED" ? (
                                    <i
                                      className="fa fa-trash-o"
                                      style={{
                                        cursor: "auto",
                                        fontSize: "20px",
                                        backgroundColor: "#fff",
                                        margin: "0 0 0 10px",
                                        color: "#C2C8CE",
                                        margin: "0 0 0 7px",
                                      }}
                                    ></i>
                                  ) : (
                                    <img
                                      onClick={() => deleteItem(item.id)}
                                      src="../images/delete.png"
                                      style={{
                                        cursor: "pointer",
                                        margin: "0 0 0 0",
                                        padding: "0 0 0 7px",
                                      }}
                                    />
                                  )}
                                </div>
                              </div>
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <></>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ) : (
          <div
            className="mx-auto mt-5"
            style={{ display: isLoading, textAlign: "center" }}
          >
            <img
              src={loadingGif}
              alt="Loading"
              className="loader"
              width="400"
            />
          </div>
        )}
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalItem / pageSize}
          pageSize={1}
          onPageChange={(page) => {
            setCurrentPage(page);
          }}
        />
        {defaultAlert && defaultErrorMsg ? (
          <Alert data={defaultErrorMsg} />
        ) : (
          <></>
        )}
      </div>
      {isPopupOpen && (
        <Popup
          selectedCounts={selectUsers}
          onCloseDelete={onCloseDelete}
          onDeleteSuccess={onDeleteSuccess}
        />
      )}
      {showSuccessMessage && (
        <div style={successMessageStyle}>
          <span style={checkblock}>
            <i className="fa fa-check" style={check}></i>
          </span>
          Deleted Successfully.
        </div>
      )}
      {selectedContact && viewModal ? (
        <ContactModal isLowViewCredits={false} />
      ) : (
        <></>
      )}
    </>
  );
};

export default ContactListOne;

const Popup = ({ selectedCounts, onCloseDelete, onDeleteSuccess }) => {
  return (
    <div
      class="modal"
      style={{
        display: "block",
        important: "true",
        backgroundColor: "#0000007d",
      }}
      id="exampleModalCenter"
      tabindex="-1"
      role="dialog"
      aria-labelledby="exampleModalCenterTitle"
    >
      <div class="modal-dialog modal-dialog-centered" role="document" style={{ width: "500px" }}>
        <div class="modal-content">
          <div
            class="modal-header"
            style={{ borderBottom: "0", padding: "6px 11px 0 0px" }}
          >
            <h5 class="modal-title" id="exampleModalLongTitle"></h5>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              onClick={onCloseDelete}
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" style={{ borderBottom: "0", padding: "0" }}>
            <p className="areyousure">
              Are you sure you want to remove {selectedCounts.length} number{" "}
              <br /> of contacts from your saved list
            </p>

            <div className="d-flex flex-row justify-content-center">
              <div className="mr-5">
                <button
                  type="submit"
                  onClick={onDeleteSuccess}
                  className="yesno"
                >
                  Yes
                </button>
              </div>
              <div>
                <button type="submit" onClick={onCloseDelete} className="no">
                  No
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
