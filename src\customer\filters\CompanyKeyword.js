import React, { useEffect, useState } from 'react';
import AsyncSelect from 'react-select/async';
import { ApiName } from '../common-files/ApiNames.js';
import { PostWithTokenNoCache, postWithToken } from '../common-files/ApiCalls.js';
import UseTabStore from '../common-files/useGlobalState.js';

const CompanyKeyword = () => {
	// State to store selected values
	const {
		setCompanyKeyword,
		companyKeyword,
		setCurrentPage,
		setIsFiltered
	} = UseTabStore();
	const [selectedValues, setSelectedValues] = useState([]);
	const [isCompanyKeywordAutoFocus, setIsCompanyKeywordAutoFocus] = useState(false); // Set one of them to autofocus initially
	const [inputValue, setInputValue] = useState('');

	useEffect(() => {
		if (Object.keys(companyKeyword).length < 1) {
			setSelectedValues([]);
		} else {
			const selectedVal = Object.keys(companyKeyword).map(key => {
				return { label: companyKeyword[key], value: companyKeyword[key] };
			});
			setSelectedValues(selectedVal);
		}
	}, [companyKeyword])

	const convertToProperCase = async (data) => {
		let properCaseData = [];
		if (data) {
			properCaseData = data.map(val => {
				// Trim the input string to remove leading and trailing whitespace
				val = val.trim();

				// Match words while preserving non-alphanumeric characters in between
				const words = val.match(/\b\w+\b/g);
				let result = "";
				let lastIndex = 0;

				words.forEach(word => {
					// Find the position of each word in the original string
					const start = val.indexOf(word, lastIndex);

					// Append special characters or spaces before the current word
					result += val.slice(lastIndex, start);

					// Capitalize the word
					result += word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();

					// Update lastIndex to the end of this word
					lastIndex = start + word.length;
				});

				// Append any remaining special characters or spaces after the last word
				result += val.slice(lastIndex);

				return result;
			});
		}
		return properCaseData;
	};


	const loadOptions = (inputText, callback) => {
		if (inputText.length < 2) {
			callback([]);
			return;
		}
		if (inputText && inputText.length >= 2) {
			const dataPost = {
				company_keywords_list: inputText // Pass the inputText to your API request
			};
			console.log(inputValue, "inputValue");
			// Make an API call here to fetch suggestions based on the inputText
			PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
				.then(async (res) => {
					if (res.data.status === 200) {
						const company = JSON.parse(res.data.data);

						let properCaseCompany = await convertToProperCase(company);

						// Convert the array to a Set to remove duplicates and then convert it back to an array
						const uniqueCompany = [...new Set(properCaseCompany)];

						const options = uniqueCompany.map((option) => ({
							label: option,
							value: option
						}));
						callback(options);
					}
				})
				.catch((error) => {
					console.error('Error fetching data:', error);
					callback([]); // Clear options on error
				});
		}
	};

	const noOptionsMessage = () => {
		return inputValue.length < 2 ? 'No options found' : 'No options found';
	};


	const SearchCompanyKeyword = ({ autoFocus, onFocus, onBlur }) => {

		// Handle value change when an option is selected or deselected
		const handleValueChange = (selectedOptions) => {
			setSelectedValues(selectedOptions);
			setIsFiltered(true);

			let updatedCompanyKeywords = {};

			// Update the company names based on selected options
			selectedOptions.forEach((item, index) => {
				updatedCompanyKeywords[index] = item.value;
			});
			setCompanyKeyword(updatedCompanyKeywords);
		};

		return (
			<div className="mb-2 mt-3 d-flex flex-column">
				<div className="">
					<AsyncSelect
						cacheOptions
						defaultOptions={false}
						loadOptions={loadOptions}
						isMulti
						placeholder="Search by Company Keyword"
						onChange={handleValueChange} // Handle selected value changes
						value={selectedValues} // Pass selected values
						styles={SearchJobtitleStyles}
						autoFocus={autoFocus}
						onFocus={onFocus}
						onBlur={onBlur}
						isClearable
						// onInputChange={(value) => setInputValue(value)}
						noOptionsMessage={noOptionsMessage}
						className={`async-select`}
					/>
				</div>
				<div className="search-icon">
					<i className="fas fa-search"></i>
				</div>

			</div>
		)
	}

	const SearchJobtitleStyles = {
		control: (provided, state) => ({
			...provided,

			marginLeft: "26px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
			cursor: "text",

			":hover": {
				borderColor: "#000",
			},
			":focus": {
				border: "1px solid #000",
			},
		}),

		placeholder: (baseStyles, state) => ({
			...baseStyles,
			gridArea: "1/1/2/3",
			color: "#A3AEBB",
			marginLeft: "6px",
			fontSize: "14px",
			width: "187px"
		}),


		singleValue: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			backgroudColor: "red",
		}),

		multiValue: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			backgroundColor: "white",
			marginTop: "8px",
			border: "1px solid",
			fontSize: "70%",

		}),


		multiValueGeneric: (provided, state) => ({
			...provided,
			paddingLeft: "8",
			paddingTop: "10px",
			padding: "1px",
			marginTop: "12px",


		}),

		IndicatorsContainer: (provided, state) => ({
			...provided,
			visibility: "hidden",
			padding: "0"

		}),

		indicatorsContainer: (provided, state) => ({
			...provided,
			visibility: "hidden",
			padding: "0"
		}),

		loadingIndicator: (provided, state) => ({
			...provided,
			visibility: "hidden",
			width: "0",
			fontSize: "0"
		}),


		menuList: (provided, state) => ({
			...provided,
			"&:hover:": {
				color: "#55C2C3",
				marginTop: "-6px",
				marginBottom: "14px",
				backgroudColor: "transperent",
				zIndex: "1000",
			},

			"&:hover": {
				backgroundColor: "inherit",
			},
		}),

		menu: (provided, state) => ({
			...provided,

			border: "1px solid #093D54",
			marginLeft: "26px",
			marginRight: "2rem",
			fontSize: "14px",
			marginRight: "0px",
			position: "relative",
			width: "-webkit-fill-available",
			backgroudColor: "#fff",

			":hover": {
				color: "inherit",
				textDecoration: "none",
			},
		}),

		multiValueRemove: (base, state) => ({
			...base,
			backgroundColor: "#fff",
			setColor: "#000",
			borderRadius: "50%",
			borderRadius: "10px",
			paddingLeft: "-60",
			paddingRight: "0",
			innerHeight: "10px",
			border: "1px solid #08CEDC",
			marginTop: "-7px",
			marginBottom: "2px",
			font: "black !important",
			backgroudColor: "#ffffff",
			width: "11px",
			height: "11px",
			"&:hover": {
				backgroundColor: "#55C2C3",
				color: "black",
			},
		}),

		indicatorSeparator: (provided, state) => ({
			...provided,
			backgroundColor: "hsl(0deg 0% 100%)",
		}),

		MenuList: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#fff",
			outline: "none",
		}),



		valueContainer: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#000",
			outline: "none",
			padding: "0px 5px 0 0",
			width: "300px"
		}),
		container: (provided, state) => ({
			...provided,
			padding: "0px 11px 0 0",
		}),
		group: (provided, state) => ({
			...provided,
			width: "262px",
			// paddingLeft: '23px',
			marginLeft: "32px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
		}),

	};


	return (
		<div>
			<SearchCompanyKeyword
				autoFocus={isCompanyKeywordAutoFocus}
				onFocus={() => setIsCompanyKeywordAutoFocus(true)}
				onBlur={() => setIsCompanyKeywordAutoFocus(false)}
			/>
		</div>
	)
}
export default CompanyKeyword;