# Customer Journey Tracking Implementation

This document provides a comprehensive guide to the customer journey tracking implementation in the ReachStream application, which enhances the existing Mixpanel integration.

## Table of Contents
1. [Overview](#overview)
2. [Files Created and Modified](#files-created-and-modified)
3. [Customer Journey Phases](#customer-journey-phases)
4. [Integration with Email Marketing](#integration-with-email-marketing)
5. [Implementation Details](#implementation-details)
6. [Usage Guide](#usage-guide)

## Overview

The customer journey tracking system is designed to monitor users throughout their entire lifecycle with the ReachStream application, from initial awareness through signup, activation, engagement, and retention. This implementation allows for:

- Tracking user behavior at each stage of the customer journey
- Identifying bottlenecks in the conversion funnel
- Segmenting users based on their lifecycle stage
- Triggering targeted email campaigns based on user behavior
- Monitoring key metrics like credit usage and search patterns

## Files Created and Modified

### New Files Created:
- `src/utils/customerJourneyTracking.js`: Core utility functions for tracking the customer journey

### Files Modified:
- `src/utils/analyticsTracking.js`: Enhanced with new tracking events for the customer journey
- `src/App.js`: Added lifecycle stage tracking and inactivity monitoring
- `src/customer/register/SignUp.js`: Added form abandonment tracking
- `src/customer/filters/Dashboard.js`: Enhanced user activity and engagement tracking
- `src/customer/pricing/UpgradePlan.js`: Added pricing page view tracking
- `src/customer/common-files/ApiCalls.js`: Added credit usage tracking
- `src/customer/filters/LeftNavbar.js`: Added filter usage tracking
- `MIXPANEL_USER_GUIDE.md`: Updated with customer journey tracking documentation

## Customer Journey Phases

### 1. Reach Phase
Tracking the initial user acquisition and engagement:

- **Signup Form Views**: Tracks when users view the signup form
- **Incomplete Signup**: Detects when users abandon the signup process
- **Signup Completion**: Records successful signups with source attribution
- **UTM Parameter Tracking**: Captures marketing campaign data

### 2. Activation Moments
Tracking key activation events that indicate user engagement:

- **First Login**: Records the first time a user accesses the dashboard
- **First Search**: Tracks the user's first search query
- **First Email Reveal**: Monitors when users first reveal contact information
- **First Download**: Records the user's first contact download

### 3. Engagement Phase
Monitoring ongoing user engagement with the platform:

- **Search Patterns**: Tracks search frequency and filter usage
- **Credit Usage**: Monitors consumption of credits over time
- **Download Activity**: Tracks download volume and frequency
- **Feature Usage**: Records which features users engage with most

### 4. Retention Phase
Tracking indicators of user retention or churn risk:

- **Login Frequency**: Monitors how often users log in
- **Inactivity Periods**: Detects when users haven't logged in for extended periods
- **Pricing Page Views**: Identifies users considering plan changes
- **Credit Limit Approaches**: Detects when users are nearing their credit limits

## Integration with Email Marketing

The tracking system is designed to integrate with email marketing platforms through Mixpanel's webhook feature:

1. **Cohort Creation**: Create user segments in Mixpanel based on journey stage and behavior
2. **Webhook Configuration**: Set up webhooks to trigger when users enter specific cohorts
3. **Email Triggers**: Connect webhooks to email marketing platform to send targeted emails

Example email triggers include:
- Incomplete signup follow-up emails
- Onboarding sequence for new users
- Re-engagement emails for inactive users
- Upgrade offers for users approaching credit limits
- Feature education emails for users with limited filter usage

## Implementation Details

### Lifecycle Stage Tracking

Users are automatically categorized into lifecycle stages based on their behavior:

```javascript
// From customerJourneyTracking.js
export const determineLifecycleStage = (user) => {
  if (!user) return 'Unknown';
  
  // Extract user metrics
  const totalDownloads = user.totalDownloads || 0;
  const lastLoginDate = user.lastLoginDate ? new Date(user.lastLoginDate) : null;
  const creditPercentageUsed = user.creditPercentageUsed || 0;
  const daysInactive = lastLoginDate ? Math.floor((new Date() - lastLoginDate) / (1000 * 60 * 60 * 24)) : 0;
  
  // Determine stage
  if (!user.hasLoggedIn) return 'Incomplete Registration';
  if (daysInactive >= 15) return 'At Risk';
  if (creditPercentageUsed >= 90) return 'Credit Limit Reached';
  if (totalDownloads >= 10) return 'Engaged';
  if (totalDownloads > 0) return 'Active';
  return 'Onboarding';
};
```

### Form Abandonment Tracking

The signup form tracks user progress and detects abandonment:

```javascript
// From SignUp.js
useEffect(() => {
  // Save timestamp when form is first loaded
  const signupStartTime = new Date().getTime();
  sessionStorage.setItem('signup_start_time', signupStartTime);
  
  // Add beforeunload event listener to track abandonment
  const handleBeforeUnload = () => {
    // Get completed fields
    const formData = {
      firstName: fname,
      lastName: lname,
      email: email,
      // ... other fields
      lastFocusedField,
      planType: packageName || '',
      totalFields: 6 // Count of total fields in the form
    };
    
    // Track the abandonment
    trackSignupAbandonment(formData);
  };
  
  window.addEventListener('beforeunload', handleBeforeUnload);
  
  return () => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
  };
}, [fname, lname, email, phone, password, cpassword, lastFocusedField, packageName, referralcode]);
```

### User Activity Tracking

Dashboard visits and user activity are tracked to monitor engagement:

```javascript
// From Dashboard.js
// Track user visit to dashboard with enhanced details
Analytics.trackFeatureUsage('Dashboard View', { 
  user_email: user.email,
  days_since_signup: calculateDaysSinceSignup()
});

// Track user activity and update user properties
trackUserActivity(user);

// Check for user inactivity patterns
const daysInactive = checkUserInactivity(user);
```

### Credit Usage Monitoring

Credit consumption is tracked to identify users approaching limits:

```javascript
// From customerJourneyTracking.js
export const trackCreditUsage = async (userEmail) => {
  try {
    const creditData = await getUserCreditData(userEmail);
    const creditPercentage = (creditData.used / creditData.total) * 100;
    
    Analytics.trackCreditUsage({
      creditsUsed: creditData.used,
      totalCredits: creditData.total,
      percentageUsed: creditPercentage,
      email: userEmail
    });
    
    // Update user lifecycle stage based on credit usage
    if (creditPercentage >= 100) {
      Analytics.updateUserLifecycleStage(userEmail, 'Credit Limit Reached', {
        'Credit Usage': '100%',
        'Credits Remaining': 0
      });
    } else if (creditPercentage >= 90) {
      Analytics.updateUserLifecycleStage(userEmail, 'Credit Limit Near', {
        'Credit Usage': `${creditPercentage.toFixed(1)}%`,
        'Credits Remaining': creditData.total - creditData.used
      });
    }
    
    return creditPercentage;
  } catch (error) {
    console.error('Error tracking credit usage:', error);
    return null;
  }
};
```

### Filter Usage Analysis

Search patterns and filter usage are tracked to identify users who might need assistance:

```javascript
// From customerJourneyTracking.js
export const trackSearchAndFilterUsage = (filterData, resultsCount, user) => {
  if (!user || !user.email) return;
  
  const filterCount = filterData.length;
  
  Analytics.trackFilterUsage({
    filterCount: filterCount,
    filterTypes: filterData.map(f => f.type),
    searchResults: resultsCount,
    hasResults: resultsCount > 0,
    email: user.email
  });
  
  // Track when users use limited filters
  if (filterCount < 3) {
    Analytics.track('Limited Filter Usage', {
      filter_count: filterCount,
      user_email: user.email,
      days_since_signup: calculateDaysSinceSignup()
    });
  }
  
  // Track when searches return no results
  if (resultsCount === 0) {
    Analytics.trackNoSearchResults({
      terms: filterData.map(f => `${f.type}:${f.value}`).join(', '),
      filters: filterData.map(f => f.type)
    });
  }
};
```

## Usage Guide

### Tracking Events

To track customer journey events in your components:

```javascript
import Analytics from '../../utils/analyticsTracking';
import { trackUserActivity } from '../../utils/customerJourneyTracking';

// Track a user activity
trackUserActivity(user);

// Track a milestone event
Analytics.trackEngagementMilestone({
  type: 'download_milestone',
  value: '10+ Contacts',
  email: user.email
});
```

### Updating User Lifecycle Stage

To manually update a user's lifecycle stage:

```javascript
import Analytics from '../../utils/analyticsTracking';

// Update lifecycle stage
Analytics.updateUserLifecycleStage(userEmail, 'Engaged', {
  'Reason': 'Downloaded 10+ contacts',
  'Date': new Date()
});
```

### Monitoring User Properties

To update comprehensive user properties for segmentation:

```javascript
import { updateUserProperties } from '../../utils/customerJourneyTracking';

// Update all user properties
updateUserProperties({
  email: '<EMAIL>',
  planType: 'Premium',
  totalDownloads: 15,
  creditsUsed: 50,
  totalCredits: 100
});
```

### Analyzing Journey Data in Mixpanel

1. **Funnel Analysis**: Create funnels in Mixpanel to track conversion through journey stages
2. **Cohort Analysis**: Create cohorts based on user behavior and lifecycle stage
3. **Retention Analysis**: Use retention reports to identify patterns in user engagement
4. **User Profiles**: View individual user journeys to understand behavior patterns

### Extending the Tracking System

To add new journey tracking capabilities:

1. Add new tracking events to `analyticsTracking.js`
2. Add helper functions to `customerJourneyTracking.js`
3. Implement the tracking in relevant components
4. Update the documentation to reflect new tracking capabilities 