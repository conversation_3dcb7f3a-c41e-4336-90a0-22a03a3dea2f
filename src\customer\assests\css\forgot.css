@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap');



.form-box7 {
    max-width: 450px;
    margin:0px 17px 4px;
    padding: 0 25px 0 25px;
    background: #ffffff;
    border: 6px solid #fff;
	box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
	border-radius: 19px;
    /*margin-top: 3%;*/
}

.form-box7 h2 {
    text-align: center;
    padding: 0 0 9px 0px;
    font-weight: 600;
    margin-top: 15%;
    color: #093D54;
    font-family: 'Lato','Medium';
}


span.email-address p {
    margin: 0px 0 10px 0px;
    color: #000;
    font-family: 'Lato','Medium';
    font-weight: 500;
}

span.password p {
    margin: 0 0 10px 0;
}

p.password-semi-header {
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Lato','Medium';
}

p.semi-header a {
    text-decoration: none;
	color: #007bff;
}


p.semi-header a:hover {
    color: #007bff;
}

p.password-label {
    padding: 8px 0 0 0;
}

p.password-label a{
	text-decoration: none;
}

p.password-label a:hover{
	color: #007bff;
}

/* input.cp-pluss {
    margin: 0 auto;
    display: block;
    padding: 10px 40px 10px 40px;
    outline: 0;
    background-color: #0C243C;
	color: #fff;
    border-radius: 15px;
    border: none;
	font-family: 'Roboto',sans-serif;
	cursor: pointer;
	margin-bottom: 40px;
} */

.passowrd-banner-1 img {
    width: 500px;
    margin: 0 auto;
    display: block;
    padding-top: 10%;
    margin-bottom: 26%;
}

.Right-sign4 a {
    text-decoration: none;
    color: #55C2C3;
    font-family: 'Lato','Medium';
}

.saver-plane3 {
    margin-top: 10%;
    margin-bottom: 0%;
}

.bg-color {
    background-color: #E8F7F7;
    height: auto;
}

.make-space3 {
    margin-top: 10%;
}


.make-space4 {
    margin-top: 8%;
}

.bottom-space {
    margin-top: 24%;
}

input#exampleInputEmail1 {
    border-color: #C9C9C9;
    /* padding:9px 0 9px 11px; */
}

.Right-sign3 {
    margin-top: 14%;
}




.Right-sign4 {
    margin-top: 14%;
    margin-bottom: 13px;
    text-align: end;
    width: 28rem;
}

span.email-error p {
    font-size: 14px;
    padding: 3px 0 0 2px;
    color: #DE350B;
}

@media (max-width: 600px) {
    .Right-sign4 a{
        margin-left:34px;
     }
  }

  @media  only screen and (min-width: 280px) and (max-width: 767px) {
   
.Right-sign4 {
    padding: 0 27px 0px 0px;
}
}

  /* @media  only screen and (min-width: 280px) and (max-width: 767px) {
   
} */

  .invalid-input-text{
  /* border: 2px solid gray;*/
   width: 100%;
   height: 40px;
   outline: none;
   border: 1px solid red;
   border-radius: 5px;
   padding: 0 0px 0px 12px;

   background-color:  #FFF9F9;

}


.forgot-password {
    margin-top: 12%;
}

