@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap");
.switch {
  text-align: center;
  padding: 30px 0 30px 0px;
  margin-top: 2%;
}

.switch a {
  text-decoration: none;
  color: #fff;
  background-color: #093d54;
  padding: 18px 22px 18px 22px;
  border-radius: 20px;
  /* font-size: 14px; */
}

@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;700&family=Roboto&display=swap");

.form-box {
  max-width: 600px;
  margin: 0px 17px 4px;
  padding: 0 25px 0 25px;
  background: #ffffff;
  border: 6px solid #fff;
  /* box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px; */
  border-radius: 19px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}

.form-box h3 {
  text-align: center;
  padding: 0 0 9px 0px;
  font-weight: 600;
  margin-top: 23px;
  color: #093d54;
}

span.email-address p {
  margin: 0px 0 10px 0px;
}

span.password p {
  margin: 0 0 10px 0;
}

p.semi-header2 {
  text-align: center;
  font-size: 14px;
}

p.semi-header2 a {
  text-decoration: none;
  color: #55c2c3;
}

p.semi-header a:hover {
  color: #007bff;
}

p.password-label {
  padding: 8px 0 0 0;
}

p.password-label a {
  text-decoration: none;
}

p.password-label a:hover {
  color: #007bff;
}

input.cp-pluss {
  margin: 0 auto;
  display: block;
  padding: 10px 40px 10px 40px;
  outline: 0;
  background-color: #0c243c;
  color: #fff;
  border-radius: 15px;
  border: none;
  font-family: "Roboto", sans-serif;
  cursor: pointer;
  margin-bottom: 40px;
}

/* .banner-1 img {
    width: 500px;
    margin: 0 auto;
    display: block;
    padding-top: 13%;
} */

.bg-color {
  background-color: #e8f7f7;
  height: auto;
  /* margin-left: 6%; */
}

/* Signup */

.saver-plane {
  margin-top: 10%;
  margin-bottom: 10%;
}

.saver-plane h3 {
  text-align: center;
  margin-top: 15px;
  font-weight: 700;
  color: #0c243c;
  margin-bottom: 18px;
}

th.borderless {
  color: #fff;
}

.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 1rem;
  background-color: #fff;
  border-radius: 20px;
}

th.borderless {
  border: none;
}

td.borderless {
  border: none;
}

.table td,
.table th {
  padding: 15px 1px 14px 20px;
  vertical-align: top;
  border-top: 1px solid #dee2e6;
  font-weight: 600;
}

tr.backrnd-color {
  background-color: #55c2c3;
  border-radius: 24px;
  font-size: 24px;
}

th.borderles span {
  background-color: #fff;
  padding: 7px 23px 7px 23px;
  border-radius: 15px;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
  padding: 20px 30px 20px 16px;
}

table.table.table-striped {
  border-collapse: collapse;
  border-radius: 1em;
  overflow: hidden;
  box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
  max-width: 391px;
}

td.text-pose {
  font-size: 14px;
  padding: 15px;
  padding: 12px 0 12px 20px;
  font-weight: 600;
}

td.text-center {
  font-size: 14px;
  font-weight: 600;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgb(255 255 255 / 5%);
}

tr.row-clr {
  background-color: #6b8eb126;
}

.trusted-by p {
  text-align: center;
  margin-top: 3rem;
  font-size: 18px;
  font-weight: 600;
}

p.start-journey {
  text-align: center;
  font-size: 27px;
  font-weight: 600;
  margin-top: 13%;
}

input#f_name {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#l_name {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#e_mail {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#phone_number {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#p_assword {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#c_password {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

.spacing {
  padding: 16px 0 0 0;
}

.custom-control.custom-checkbox {
  margin-top: 30px;
}

a.Term {
  text-decoration: none;
}

input#customCheck2 {
  border: 1px solid #c9c9c9;
  outline: none;
}

span.pill-button {
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
  border: none;
  background-color: #e2ebe8;
  color: rgb(0, 0, 0);
  padding: 3px 23px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 30px;
  border-radius: 33px;
  width: 39px;
  outline: none;
  margin-left: 4px;
}

.make-space {
  margin-top: 10%;
}

a.Term {
  text-decoration: none;
  color: #55c2c3;
}