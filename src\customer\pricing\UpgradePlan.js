import React, { useState, useContext, useEffect } from "react";
import "../../customer/assests/css/paymentdetails.css";
import { Link, useLocation } from "react-router-dom";
import "../../customer/assests/css/password-eye.css";
import { envConfig } from "../../config";
import {
  PostWithTokenNoCache,
} from "../common-files/ApiCalls.js";
import { ApiName } from "../common-files/ApiNames.js";
import DashboardContext from "../common-files/ContextDashboard.js";
import Footer from "../layouts/Footer";
import DWINTracker from "../common-files/DWINTracker.js";
import Analytics from '../../utils/analyticsTracking';
import { trackPricingPageView } from '../../utils/customerJourneyTracking';
import Mixpanel from '../../utils/mixpanel';

const UpgradePlan = () => {
  const { dataDC, setDataDC } = useContext(DashboardContext);

  const [promocode, setPromocode] = useState();
  const [error, setError] = useState(false);
  const [afterDiscount, setAfterDiscount] = useState(0);
  const location = useLocation();
  const { email, planDetails } = location.state || {};
  const [promocodeInvalid, setPromocodeInvalid] = useState(true);
  const [calculatedPrice, setCalculatedPrice] = useState(planDetails?.price || 0);
  const [btnVisible, setBtnVisible] = useState(false);
  const [promocodeMsg, setPromocodeMsg] = useState(false);
  const [inputDisable, setInputDisable] = useState(false);
  const [couponId, setCouponId] = useState(null);
  const [loading, setLoading] = useState(false);
  const yearlyPlanIds = envConfig.yearlyPlanIds;

  if (!email) {
    window.location.href = "/";
  }

  function calculateDiscount(price, discount) {
    return (price - price * (discount / 100)).toFixed(2);
  }

  const promocodeAppied = (event) => {
    event.preventDefault();
    const userData = { method: "GET" };

    let result = PostWithTokenNoCache(ApiName.checkPromocode + promocode, userData)
      .then(function (response) {
        const promocodeData = JSON.parse(JSON.parse(response.data.data));
        if (
          response.data.status == 200 &&
          promocodeData.is_active === "inactive" ||
          response.data.message == "Coupon Not Found"
        ) {
          setError("Invalid code");
          setPromocodeInvalid(false);
          setBtnVisible(false);
          
          // Track failed promo code in Mixpanel
          try {
            Mixpanel.track('Promo Code Failed', {
              promo_code: promocode,
              package_id: planDetails.id,
              package_name: planDetails.package_name,
              error: 'Invalid code or inactive',
              context: 'upgrade_plan'
            });
          } catch (error) {
            console.warn('Error tracking promo code failure:', error);
          }
        } else {
          const promocodeData = JSON.parse(JSON.parse(response.data.data));
          if (promocodeData.is_active == "active") {
            var price = planDetails.price;
            var discount = promocodeData.percentage;
            var calculated = "$" + calculateDiscount(price, discount);
            localStorage.setItem('discounted_price', calculated);
            localStorage.setItem('applied_promocode', promocode);
            setAfterDiscount(discount);
            setCalculatedPrice(calculated);

            setError(true);
            setBtnVisible(false);
            setPromocode("");
            setPromocodeMsg("Code successfully applied");
            setPromocodeInvalid(true);
            setInputDisable(true);
            let keyCoupon = 2017; //WELCOMETEST
            let couponArr = [];
            let valueCoupon = null;
            if ("id" in promocodeData) keyCoupon = promocodeData.id;
            if ("coupon_id" in promocodeData)
              valueCoupon = promocodeData.coupon_id;
            // console.log(keyCoupon,valueCoupon)
            if (keyCoupon && valueCoupon) {
              couponArr["keyCoupon"] = keyCoupon;
              couponArr["valueCoupon"] = valueCoupon;
              setCouponId({ ...couponArr });
            }
            
            // Track successful promo code in Mixpanel
            try {
              Mixpanel.track('Promo Code Applied', {
                promo_code: promocode,
                discount_percentage: discount,
                original_price: price,
                discounted_price: calculated.replace('$', ''),
                package_id: planDetails.id,
                package_name: planDetails.package_name,
                coupon_id: keyCoupon,
                context: 'upgrade_plan'
              });
            } catch (error) {
              console.warn('Error tracking promo code application:', error);
            }
          }
        }
      })
      .catch(function (errors) {
        //console.log(tokenSent);
        console.log(errors);
        
        // Track error in Mixpanel
        try {
          Mixpanel.track('Promo Code Error', {
            promo_code: promocode,
            error: errors?.message || 'Unknown error',
            package_id: planDetails.id,
            package_name: planDetails.package_name,
            context: 'upgrade_plan'
          });
        } catch (trackError) {
          console.warn('Error tracking promo code error:', trackError);
        }
        //return errors;
      });
  };

  const onChangeHandler = (event) => {
    // promo appley
    //console.log(error);
    let promocode = event.target.value;
    setPromocode(promocode);

    const minLength = 2;
    if (promocode.length == 0) {
      setPromocodeInvalid(true);
    } else if (minLength <= promocode.length) {
      setError(true);
      setBtnVisible(true);
      setPromocodeInvalid(true);
    } else {
      setError(false);
      setBtnVisible(false);
      setPromocodeInvalid(false);
    }
  };
  const onSubmitHandler = (event) => {
    event.preventDefault();
    setLoading(true);
    const getDomain = window.location.origin;
    const userData = {
      priceId: planDetails.price_id,
      cancelUrl: getDomain + "/payment-failed",
      successUrl: getDomain + "/payment-successful",
      customerEmail: email,
    };
    const createOrderData = {
      email_id: email,
      package_id: planDetails.id,
    };
    if (couponId) {
      createOrderData.coupon_id = couponId.keyCoupon;
      userData.promoCode = couponId.valueCoupon;
    }
    
    // Track payment initiation in Mixpanel
    try {
      // Gather device and location info
      const deviceInfo = {
        browser: navigator.userAgent,
        device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        language: navigator.language || navigator.userLanguage
      };
      
      // Get UTM parameters from localStorage
      const utmParams = localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {};
      
      // Get plan type from package name
      let planType = '';
      if (planDetails.package_name && planDetails.package_name.toLowerCase().includes('glacier')) {
        planType = 'Glacier';
      } else if (planDetails.package_name && planDetails.package_name.toLowerCase().includes('ice floe')) {
        planType = 'Ice Floe';
      } else if (planDetails.package_name && planDetails.package_name.toLowerCase().includes('polar peak')) {
        planType = 'Polar Peak';
      } else if (planDetails.package_name && planDetails.package_name.toLowerCase().includes('frozen fortune')) {
        planType = 'Frozen Fortune';
      } else {
        planType = `Plan ID: ${createOrderData.package_id}`;
      }
      
      // Track in Mixpanel directly without deduplication
      Mixpanel.track('Upgrade Payment Initiated', {
        package_id: createOrderData.package_id,
        package_name: planDetails.package_name || `Plan ID: ${createOrderData.package_id}`,
        plan_type: planType,
        price: planDetails.price || 0,
        discounted_price: afterDiscount ? (parseFloat(planDetails.price) * (1 - afterDiscount / 100)).toFixed(2) : planDetails.price,
        has_discount: !!couponId,
        discount_percentage: afterDiscount || 0,
        promo_code: localStorage.getItem('applied_promocode') || '',
        user_email: email,
        timestamp: new Date().toISOString(),
        ...deviceInfo,
        ...utmParams
      });
      
      // Also track in Analytics with a unique timestamp
      Analytics.trackPaymentStarted({
        checkout_step: 'upgrade_initiated',
        package_id: createOrderData.package_id,
        package_name: planDetails.package_name || `Plan ID: ${createOrderData.package_id}`,
        price: planDetails.price || 0,
        discounted_price: afterDiscount ? (parseFloat(planDetails.price) * (1 - afterDiscount / 100)).toFixed(2) : planDetails.price,
        has_discount: !!couponId,
        user_email: email,
        promo_code: localStorage.getItem('applied_promocode') || '',
        timestamp: new Date().toISOString(), // Add unique timestamp
        ...deviceInfo,
        ...utmParams
      });
    } catch (error) {
      console.warn('Error tracking payment initiated in Mixpanel:', error);
    }
    
    PostWithTokenNoCache(ApiName.createOrder, createOrderData)
      .then((res) => {
        if (res.data.status == 200) {
          // Track order creation in Mixpanel
          try {
            const orderData = JSON.parse(res.data.data);
            const orderId = orderData.orderId;
            
            // Store orderId in localStorage
            localStorage.setItem('orderId', orderId);
            
            Mixpanel.track('Upgrade Order Created', {
              order_id: orderId,
              package_id: createOrderData.package_id,
              package_name: planDetails.package_name || '',
              price: planDetails.price || 0,
              discounted_price: afterDiscount ? (parseFloat(planDetails.price) * (1 - afterDiscount / 100)).toFixed(2) : planDetails.price,
              promo_code: localStorage.getItem('applied_promocode') || ''
            });
          } catch (error) {
            console.warn('Error tracking order creation:', error);
          }
          
          PostWithTokenNoCache(ApiName.confirmAndPay, userData)
            .then(function (response) {
              let paymentUrl = JSON.parse(response.data.data);
              
              // Track redirect to payment page in Mixpanel
              try {
                const orderId = localStorage.getItem('orderId');
                Mixpanel.track('Upgrade Payment Redirect', {
                  order_id: orderId || 'unknown',
                  package_id: createOrderData.package_id,
                  package_name: planDetails.package_name || ''
                });
              } catch (error) {
                console.warn('Error tracking payment redirect:', error);
              }
              
              window.location.href = paymentUrl;
            })
            .catch(function (errors) {
              setLoading(false);
              
              // Track payment error in Mixpanel
              try {
                const orderId = localStorage.getItem('orderId');
                Mixpanel.track('Upgrade Payment Error', {
                  error: errors?.message || 'Unknown error',
                  order_id: orderId || 'unknown',
                  package_id: createOrderData.package_id,
                  package_name: planDetails.package_name || ''
                });
              } catch (trackError) {
                console.warn('Error tracking payment error:', trackError);
              }
            });
        }
      })
      .catch((err) => {
        setLoading(false);
        
        // Track order creation error in Mixpanel
        try {
          Mixpanel.track('Upgrade Order Creation Error', {
            error: err?.message || 'Unknown error',
            package_id: createOrderData.package_id,
            package_name: planDetails.package_name || ''
          });
        } catch (trackError) {
          console.warn('Error tracking order creation error:', trackError);
        }
      });

    const minLength = 2;
    if (promocode !== null || (undefined && minLength <= promocode.length)) {
      setError(true);
    } else {
      setError(null);
    }
  };

  const capitalizeFirstLetters = (str) => {
    if (!str || typeof str !== 'string') {
      return '';
    }
    return str
      .replace(/Yearly/gi, '') // Remove the word "Yearly" (case-insensitive)
      .trim()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  useEffect(() => {
    const viewStartTime = new Date();
    const user = JSON.parse(localStorage.getItem('user'));
    const currentPlan = user?.planType || 'Icebreaker'; // Replace with actual plan name from your data
    
    if (user) {
      trackPricingPageView({
        ...user,
        plan: currentPlan
      });
      
      // Track view duration when leaving the page
      return () => {
        const viewEndTime = new Date();
        const timeOnPage = Math.round((viewEndTime - viewStartTime) / 1000); // in seconds
        
        Analytics.trackPricingPageView({
          time_on_page: timeOnPage,
          current_plan: currentPlan,
          email: user.email
        });
      };
    }
  }, []);

  const handlePlanSelect = (planDetails) => {
    // Existing plan selection logic...
    
    // Add this tracking code
    const user = JSON.parse(localStorage.getItem('user'));
    if (user) {
      Analytics.trackPlanViewed({
        plan_name: planDetails.name,
        plan_price: planDetails.price,
        current_plan: user.planType || 'Icebreaker', // Replace with actual plan name from your data
        upgrade_path: `${user.planType || 'Icebreaker'} → ${planDetails.name}`,
        email: user.email
      });
    }
    
    // Continue with your existing logic...
  };

  return (
    <>
      <DWINTracker />
      <div className="container-fluid">
        <div className="row">
          {/* <div className="col-md-1"></div> */}
          <div className="col-md-5 bg-color">
            <div className="offset-md-3 saver-plane1">
              <div className="rs-logo">
                <img src="../images/r-logo.png" width="50" className="" />
              </div>
              <div className="pricing-plane">
                <div className="card-container">
                  <div className="updgrade-plan-header">
                    <h4>{capitalizeFirstLetters(planDetails.package_name)}</h4>
                  </div>
                  <table class="table table-striped">
                    <thead>
                      <tr className="backrnd-color">
                        <th className="borderless">
                          <div className="d-flex flex-row justify-content-between">
                            <div className="prce"><p>Pricing</p></div>
                          </div>
                        </th>
                        <th className="">
                          <div className="upgrade-doller">
                            <span >
                              ${planDetails.price}
                            </span>
                            <span className="month">
                              {yearlyPlanIds.includes(planDetails.id) ? '/Year' : '/Month'}
                            </span>
                          </div>

                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="borderless">
                        <td className="text-pose">Unlimited Contact Views</td>
                        <td className="text-center">
                          {planDetails?.no_of_contact_views === "Unlimited" ? "Yes" : planDetails?.no_of_contact_views}
                        </td>
                      </tr>
                      <tr className="borderless">
                        <td className="text-pose">Email Credits</td>
                        <td className="text-center">
                          Unlimited
                        </td>
                      </tr>
                      <tr className="row-clr">
                        <td className="text-pose">Exports/Month</td>
                        <td className="text-center">
                          {planDetails?.export ? (Number(planDetails.export)).toLocaleString() : ""}
                        </td>
                      </tr>
                      <tr>
                        <td className="text-pose">20+ Data Insights</td>
                        <td className="text-center">Yes</td>
                      </tr>
                      <tr className="row-clr">
                        <td className="text-pose">
                          Verified Emails and Phone Numbers
                        </td>
                        <td className="text-center">Yes</td>
                      </tr>
                      <tr>
                        <td className="text-pose">Access to ReachAPI</td>
                        <td className="text-center">Yes</td>
                      </tr>
                      <tr className="row-clr">
                        <td className="text-pose">Free Monthly Updates</td>
                        <td className="text-center">Yes</td>
                      </tr>
                      <tr className="borderless">
                        <td className="text-pose">Dedicated Account Manager</td>
                        <td className="text-center">
                          {planDetails.package_name === "polar peak plan yearly" || planDetails.package_name === "polar peak" ? "Yes" : "No"}
                        </td>
                      </tr>

                      <tr className="row-clr">
                        <td className="text-pose">24/7 Customer Support</td>
                        <td className="text-center">
                          {planDetails.package_name === "polar peak plan yearly" || planDetails.package_name === "polar peak" ? "Yes" : "No"}
                        </td>
                      </tr>

                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="col-md-11">
              <p className="start-journeyy">Payment Details</p>
            </div>
            <div className="form-box5">
              <div className="fisrt-layer">
                <br />
                <br />
                <div className="d-flex justify-content-between">
                  <div className="total-discount">Total</div>
                  <div className="dark">
                    <p>
                      {planDetails.package_name === "glacier plan yearly" || planDetails.package_name === "ice floe plan yearly"
                        ? `$${(planDetails.price + 120).toFixed(2)}`
                        : planDetails.package_name === "polar peak plan yearly"
                          ? `$${(planDetails.price + 240).toFixed(2)}`
                          : `$${planDetails.price.toFixed(2)}`}
                    </p>
                  </div>
                </div>
                <div className="d-flex justify-content-between">
                  <div className="total-discount">
                    Discount
                    <span className="minus-discount"></span>
                  </div>
                  <div className="dark">
                    <p>
                      {afterDiscount
                        ? `- $${(planDetails.price * (afterDiscount / 100)).toFixed(2)}`
                        : planDetails.package_name === "glacier plan yearly" || planDetails.package_name === "ice floe plan yearly"
                          ? "- $120.00"
                          : planDetails.package_name === "polar peak plan yearly"
                            ? "- $240.00"
                            : null}
                    </p>
                  </div>
                </div>
              </div>
              <hr className="horizontal" />
              <div className="fisrt-layer">
                <div className="d-flex justify-content-between">
                  <div className="dark-wall">Total (Including Tax)</div>
                  <div className="dark-blue">
                    <p>
                      ${afterDiscount
                        ? (parseFloat(planDetails.price) * (1 - afterDiscount / 100)).toFixed(2)
                        : parseFloat(planDetails.price).toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
              <form onSubmit={promocodeAppied}>
                <div className="second-layer">
                  <p>Have a promo code?</p>
                  <div className="d-flex">
                    <div className="col-md-7 mt-1">
                      <div
                        className={
                          !promocodeMsg
                            ? "input-with-icon-text"
                            : "input-with-icon"
                        }
                      >
                        {promocodeInvalid ? (
                          <input
                            type="text"
                            className="form-control input-field"
                            id="exampleInputPass"
                            name="promocode"
                            onChange={onChangeHandler}
                            value={promocode}
                            placeholder={promocodeMsg}
                            disabled={inputDisable ? "disabled" : ""}
                            autoFocus
                          />
                        ) : (
                          <></>
                        )}

                        {promocodeMsg ? (
                          <img
                            src="../images/promocode-success.png"
                            className="icon"
                          />
                        ) : (
                          <></>
                        )}
                      </div>

                      {!promocodeInvalid ? (
                        <input
                          type="text"
                          className="invalid-input-text"
                          name="promocode"
                          onChange={onChangeHandler}
                          value={promocode}
                          placeholder={promocodeMsg}
                          disabled={inputDisable ? "disabled" : ""}
                          autoFocus
                        />
                      ) : (
                        <></>
                      )}

                      <span className="email-error-message">
                        {error && <p>{error}</p>}
                      </span>
                    </div>

                    <div className="col-md-4 Pnlty">
                      {!btnVisible ? (
                        <input
                          type="submit"
                          value="Apply"
                          className="cp-pluss cust-disabled"
                        />
                      ) : (
                        <input type="submit" value="Apply" className="cp-pluss" />
                      )}
                    </div>
                  </div>
                </div>
              </form>
              <div onClick={onSubmitHandler}>
                <div className="adjust">
                  <button className="Confirman" type="submit">
                    {loading ? "Loading..." : "Confirm and Pay"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default UpgradePlan;