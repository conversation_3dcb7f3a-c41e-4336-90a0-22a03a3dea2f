import React, { useEffect, useRef } from "react";
import UseTabStore from "./useGlobalState";
import { useNavigate } from "react-router-dom";

const Alert = (props) => {
    // Get the current domain name
    const currentDomain = window.location.origin;

    const navigate = useNavigate();
    const alertRef = useRef();
    const closeAlertRef = useRef();
    const { setButtonType, buttonType, defaultErrorMsg, defaultAlert, setDefaultAlert, setDefaultErrorMsg, setViewModal, viewModal } = UseTabStore();

    useEffect(() => {
        // Add event listeners when the component mounts
        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('keydown', handleEscKeyPress);

        // Remove event listeners when the component unmounts
        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
            document.removeEventListener('keydown', handleEscKeyPress);
        };
    }, []);

    const handleOutsideClick = (e) => {
        // Check if the click is outside the modal
        if (!e.target.closest('.modal')) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        // Check if the pressed key is ESC
        if (e.key === 'Escape') {
            close();
        }
    };


    const close = () => {
        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");
        setViewModal(false);
    }

    return (
        <>
            <button
                type="button"
                className="btn btn-info btn-md"
                data-toggle="modal"
                data-target="#alertModal"
                ref={alertRef}
                style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
                data-backdrop="true"
            ></button>
            {defaultAlert && defaultErrorMsg && (buttonType == "error" || buttonType == "success") ? (
                <div className="modal fade bd-example-modal-sm show" tabIndex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" id="alertModal" style={{ display: 'block', zIndex: 9999 }}>
                    <div className="modal-dialog modal-sm" style={{ position: 'fixed', top: '50%', left: '56%', transform: 'translate(-50%, -50%)', width: '180px', maxWidth: '90%' }}>
                        <div className="modal-content" style={{ borderRadius: "12px", display: "block", width: "100%", padding: "6px" }}>
                            <div className="d-flex flex-row justify-content-end">
                                <button
                                    ref={closeAlertRef}
                                    type="button"
                                    className="close"
                                    data-dismiss="modal"
                                    aria-label="Close"
                                    onClick={close}
                                    style={{ display: 'none' }}
                                />
                            </div>
                            <div className="d-flex flex-column align-items-center text-center">
                                <div style={{ marginBottom: '8px' }}>
                                    <img src={`${currentDomain}/images/dev-penguin.png`} width="30" style={{ marginBottom: '8px' }} />
                                    <p className={buttonType === "success" ? "anerror-green" : "anerror-red"} style={{ fontSize: '14px', fontWeight: 'bold' }}>
                                        {buttonType === "success" ? "Success" : "Error"}
                                    </p>
                                </div>
                                <div className="lorem" style={{ marginBottom: '10px' }}>
                                    <p style={{ fontSize: '12px' }}>{props.data}</p>
                                </div>
                                <div className="upgrdbutton">
                                    <button type="button" onClick={close} style={{ padding: '4px 12px', fontSize: '12px' }}>Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <></>
            )}

        </>
    )
}

export default Alert;
