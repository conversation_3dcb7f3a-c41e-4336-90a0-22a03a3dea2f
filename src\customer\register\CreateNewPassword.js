import React, { useState, useContext, useEffect } from "react";
import "../../customer/assests/css/Cnewpswrd.css";
import { useNavigate, useLocation } from "react-router-dom";
import { Link } from "react-router-dom";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";
import "../../customer/assests/css/password-eye.css";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import { AxiosPostBearer } from "../../customer/common-files/ApiCalls.js";
import Footer from "../layouts/Footer";
import Analytics from '../../utils/analyticsTracking';
import Mixpanel from '../../utils/mixpanel';

const CreateNewPassword = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const [password, setPassword] = useState("");
  const [cpassword, setCpassword] = useState("");
  const [validLength, setValidLength] = useState(false);
  const [validCase, setValidCase] = useState(false);
  const [validWord, setValidWord] = useState(false);
  const [validConfirmPassword, setValidConfirmPassword] = useState(false);
  const [passwordColor, setPasswordColor] = useState(true);
  const [passwordValidation, setPasswordValidation] = useState({
    uppercase: false,
    lowercase: false,
    number: false,
    specialChar: false,
  });
  const [errorMessage, setErrorMessage] = useState("");

  const location = useLocation();
  
  // Track page view when component mounts
  useEffect(() => {
    try {
      // Gather device and location info
      const deviceInfo = {
        browser: navigator.userAgent,
        device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        language: navigator.language || navigator.userLanguage
      };
      
      Analytics.track('Create New Password Page Viewed', {
        email: location.state?.email || '',
        flow_type: 'password_reset',
        page_path: window.location.pathname,
        referrer: document.referrer || 'direct',
        ...deviceInfo
      });
      
      // Identify user if email is available
      if (location.state?.email) {
        Mixpanel.identify(location.state.email);
      }
    } catch (error) {
      console.warn('Error tracking create password page view:', error);
    }
  }, [location.state?.email]);

  const onChangePassword = (event) => {
    let password = event.target.value.trim();

    setPassword(password);
    setErrorMessage("");
    
    const minLength = 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /(?=.*[0-9]).*$/.test(password);
    const hasSymbol = /^(?=.*[~`!@#$%^&*()--+={}\[\]|\\:;"'<>,.?/_₹]).*$/.test(
      password
    );
    const isDictionaryWord =
      /password|123456|qwerty|letmein|monkey|football/.test(password);

    if (password.length < minLength) {
      setValidLength(false);
    } else {
      setValidLength(true);
    }

    setPasswordValidation({
      uppercase: hasUppercase,
      lowercase: hasLowercase,
      number: hasNumber,
      specialChar: hasSymbol,
    });

    if (
      password.length < minLength ||
      !hasUppercase ||
      !hasLowercase ||
      !hasNumber ||
      !hasSymbol
    ) {
      setValidCase(false);
    } else {
      setValidCase(true);
    }
    if (isDictionaryWord) {
      setValidWord(false);
    } else {
      setValidWord(true);
    }

    if (cpassword !== password) {
      setValidConfirmPassword(false);
      setPasswordColor(false);
    } else {
      setValidConfirmPassword(true);
      setPasswordColor(true);
    }
    
    // Track password strength as user types
    try {
      if (password.length >= 4) {
        const passwordStrength = 
          (validLength ? 25 : 0) + 
          (hasUppercase ? 25 : 0) + 
          (hasLowercase ? 10 : 0) + 
          (hasNumber ? 20 : 0) + 
          (hasSymbol ? 20 : 0);
        
        Analytics.trackFeatureUsage('Password Strength Updated', {
          email: location.state?.email || '',
          strength_score: passwordStrength,
          meets_requirements: validLength && validCase && validWord,
          password_length: password.length
        });
      }
    } catch (error) {
      console.warn('Error tracking password strength:', error);
    }
  };
  
  const onChangeConfirmPassword = (event) => {
    let cPassword = event.target.value;
    setCpassword(cPassword);
    if (cPassword !== password) {
      setValidConfirmPassword(false);
      setPasswordColor(false);
    } else {
      setValidConfirmPassword(true);
      setPasswordColor(true);
      
      // Track when passwords match
      try {
        Analytics.trackFeatureUsage('Passwords Match', {
          email: location.state?.email || '',
          password_length: password.length,
          meets_all_requirements: validLength && validCase && validWord
        });
      } catch (error) {
        console.warn('Error tracking password match:', error);
      }
    }
  };
  
  const onSubmitHandler = (event) => {
    event.preventDefault();
    
    // Track password reset attempt
    try {
      Analytics.track('Password Reset Attempted', {
        email: location.state?.email || '',
        password_meets_requirements: validLength && validCase && validWord && validConfirmPassword
      });
    } catch (error) {
      console.warn('Error tracking password reset attempt:', error);
    }
    
    if (cpassword == password) {
      const verifyEmailData = {
        email: location.state.email,
        newPassword: password,
        confirmPassword: cpassword,
      };
      const token = location.state.token;
      let result = AxiosPostBearer(
        ApiName.createNewPassword,
        verifyEmailData,
        token
      )
        .then(function (response) {
          if (response.data.status == 200) {
            // Track successful password reset
            try {
              Analytics.track('Password Reset Successful', {
                email: location.state?.email || '',
                password_strength: validLength && validCase && validWord ? 'strong' : 'moderate'
              });
              
              // Update user properties
              if (location.state?.email) {
                Mixpanel.people.set({
                  'Last Password Reset': new Date().toISOString(),
                  'Password Reset Complete': true
                });
              }
            } catch (error) {
              console.warn('Error tracking password reset success:', error);
            }
            
            navigate("/password-changed-successfully", {
              state: {
                email: location.state.email,
              },
            });
          }
        })
        .catch(function (error) {
          setPassword("");
          setCpassword("");
          
          // Track failed password reset
          try {
            Analytics.track('Password Reset Failed', {
              email: location.state?.email || '',
              error_message: error.response?.data?.message || 'Unknown error'
            });
          } catch (trackingError) {
            console.warn('Error tracking password reset failure:', trackingError);
          }
          
          if (error.response) {
            // Set the error message from the API response
            setErrorMessage(error.response.data.message);
          } else {
            // Set a generic error message if the response is unavailable
            setErrorMessage("An unexpected error occurred. Please try again.");
          }
        });
    } else {
      setValidConfirmPassword(false);
    }
  };
  return (
    <>
      <div className="app">
        <div className="container-fluid">
          <div className="row">
            <div className="col-md-6 bg-color">
              <div className="offset-md-3 saver-plane3">
                <img src="../images/r-logo.png" width="50" alt="logo" />
                <div className="crate-new-passwrd-banner">
                  <img
                    src="../images/group-50970.png"
                    className="img-fluid"
                    alt="banner"
                  />
                </div>
              </div>
            </div>
            <div className="col-md-6">
              <div className="text-end">
                <p>
                  <Link to="/">Sign In</Link>
                </p>
              </div>
              <div className="form-box4">
                <h3>Create New Password</h3>
                <p className="semi-header3">
                  Your new password must be different from your <br /> previous
                  password. <br />
                </p>
                <form className="crnpwrsd">
                  <div className="form-group">
                    <label className="n-password" label="firstname">
                      New Password
                    </label>

                    <div className="password-input">
                      <input
                        value={password}
                        className="form-control"
                        type={showPassword ? "text" : "password"}
                        name="password"
                        onChange={onChangePassword}
                      ></input>
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        <FontAwesomeIcon
                          icon={showPassword ? faEye : faEyeSlash}
                          style={{ pointerEvents: "none", color: "#55C2C3" }}
                        />
                      </button>
                    </div>
                    {password ? (
                      <span className="err">
                        {!validLength ? (
                          <p style={{ color: "red", marginBottom: "0" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;Password
                            must be 8 characters
                          </p>
                        ) : (
                          <p style={{ color: "green", marginBottom: "0" }}>
                            <i className="fa fa-check"></i>&nbsp;Password must
                            be 8 characters
                          </p>
                        )}
                        {!passwordValidation.uppercase ? (
                          <p style={{ color: "red", marginBottom: "0" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one uppercase (A-Z)
                          </p>
                        ) : (
                          <p style={{ color: "green", marginBottom: "0" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            uppercase (A-Z)
                          </p>
                        )}
                        {!passwordValidation.lowercase ? (
                          <p style={{ color: "red", marginBottom: "0" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one lowercase (a-z)
                          </p>
                        ) : (
                          <p style={{ color: "green", marginBottom: "0" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            lowercase (a-z)
                          </p>
                        )}
                        {!passwordValidation.number ? (
                          <p style={{ color: "red", marginBottom: "0" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one number
                          </p>
                        ) : (
                          <p style={{ color: "green", marginBottom: "0" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            number
                          </p>
                        )}
                        {!passwordValidation.specialChar ? (
                          <p style={{ color: "red", marginBottom: "0" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one Special character (@, #, &, $, etc.)
                          </p>
                        ) : (
                          <p style={{ color: "green", marginBottom: "0" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            Special character (@, #, &, $, etc.)
                          </p>
                        )}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>
                  <div className="Cnpwrd"></div>
                  <div className="form-group">
                    <label className="n-password" label="firstname">
                      Confirm Password
                    </label>
                    <div className="password-input">
                      {passwordColor ? (
                        <input
                          type={showConfirmPassword ? "text" : "password"}
                          className="form-control"
                          id="exampleInputPassword1"
                          name="confirmPassword"
                          onChange={onChangeConfirmPassword}
                          value={cpassword}
                        />
                      ) : (
                        <input
                          type={showConfirmPassword ? "text" : "password"}
                          className="invalid-input-text"
                          name="confirmPassword"
                          onChange={onChangeConfirmPassword}
                          value={cpassword}
                        />
                      )}
                      <button
                        type="button"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                      >
                        <FontAwesomeIcon
                          icon={showConfirmPassword ? faEye : faEyeSlash}
                          style={{ pointerEvents: "none", color: "#55C2C3" }}
                        />
                      </button>
                    </div>
                    {validCase &&
                      validLength &&
                      validWord &&
                      !validConfirmPassword ? (
                      <span className="email-error-message">
                        <p>
                          {" "}
                          'Confirm password' does not match your new password
                        </p>
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>
                  {errorMessage && <span className="email-error-message"><p>{errorMessage}</p></span>}
                  <span className="Shift">
                    <span>
                      {!validCase ||
                        !validLength ||
                        !validWord ||
                        !validConfirmPassword ? (
                        <input
                          type="button"
                          onClick={onSubmitHandler}
                          value="Change Password"
                          className="cp-pluss1 cust-disabled"
                        />
                      ) : (
                        <input
                          type="button"
                          onClick={onSubmitHandler}
                          value="Change Password"
                          className="cp-pluss1"
                        />
                      )}
                    </span>
                  </span>
                </form>
                <br />
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default CreateNewPassword;
