import React, { useEffect, useState } from "react";
import AdminHeader from "../../layouts/AdminHeader.js";
import S from "../../assets/css/layouts/admin-header.module.css";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import Alert from "../../../customer/common-files/alert.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import loadingGif from "../../../customer/assests/waiting.gif";
import { Link, useParams } from "react-router-dom";

const DeviceHistory = () => {
  const { id } = useParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [data, setData] = useState([]);
  const itemsPerPage = 10;
  const [loading, setLoading] = useState(true);
  const [totalPage, setTotalPage] = useState(0);
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  useEffect(() => {
    const postData = async () => {
      try {
        let params = JSON.stringify({
          page: currentPage,
          pageSize: itemsPerPage,
          searchParams: {
            user_id: id,
          },
          sortBy: "createdAt",
        });
        setLoading(true);
        const res = await PostWithTokenNoCache(ApiName.deviceHistory, params);
        // console.log(res.data);
        if (res && "status" in res) {
          if (res.status == 200) {
            let jsonArray = res.data;
            setTotalPage(jsonArray.totalPages);
            setData(jsonArray.items);
            setLoading(false);
          } else {
            setButtonType("error");
            setDefaultErrorMsg(res.response.data.message);
            setDefaultAlert(true);
          }
        } else {
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
        }
        setLoading(false);
      } catch (error) {
        setButtonType("error");
        setDefaultErrorMsg(error?.response?.data?.message);
        setDefaultAlert(true);
        setLoading(false);
      }
    };
    postData();
  }, [currentPage]);

  const dateFormat = (date) => {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = parsedDate.getMonth() + 1;
    const day = parsedDate.getDate();
    const hours = parsedDate.getHours();
    const minutes = parsedDate.getMinutes();
    const ampm = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 === 0 ? 12 : hours % 12;
    return `${year}/${month}/${day} - ${formattedHours}:${
      minutes < 10 ? "0" : ""
    }${minutes} ${ampm}`;
  };

  return (
    <div className={S.app}>
      <AdminHeader></AdminHeader>
      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div style={{ width: "100%", padding: "0 10px 0 0" }}>
              <aside className={S.right_sidebar}>
                <div className={S.table_responsive}>
                  <div className="d-flex flex-row">
                    <div className="d-flex flex-row">
                      <div>
                        <p className="savedcontactlist">Login/Logout History</p>
                      </div>
                    </div>
                  </div>
                  <table>
                    <thead className="table-active text-center">
                      <tr>
                        <th className={S.packagename}>SL No</th>
                        <th className={S.packagename}>Login Time</th>
                        <th className={S.packagename}>Logout Time</th>
                        <th className={S.packagename}>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.map((item, i) => (
                        <tr className={S.tablelight} key={i + 1}>
                          <td className={S.centeralign}>
                            {(currentPage - 1) * 10 + i + 1}
                          </td>
                          <td className={S.centeralign}>{item.login_time}</td>
                          <td className={S.centeralign}>{item.logout_time}</td>
                          <td className="d-flex flex-row justify-content-center text-center">{item.status}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </aside>
            </div>
          ) : (
            <div className="mx-auto mt-5" style={{ display: loading }}>
              <img
                src={loadingGif}
                alt="Loading"
                className="loader"
                width="400"
              />
            </div>
          )}
        </div>
      </div>
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={totalPage}
        pageSize={1}
        onPageChange={(page) => {
          setCurrentPage(page);
        }}
      />

      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};
export default DeviceHistory;