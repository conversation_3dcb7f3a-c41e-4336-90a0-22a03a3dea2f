import React, { useEffect, useRef } from 'react';
import '../../customer/assests/css/verifyemail.css';
import { useLocation, Link, useNavigate/* other hooks */ } from 'react-router-dom';
import { useState } from 'react';
import { ApiName } from '../../customer/common-files/ApiNames.js';
import { axiosPost } from '../../customer/common-files/ApiCalls.js';
import Footer from '../layouts/Footer';
import Analytics from '../../utils/analyticsTracking';
import Mixpanel from '../../utils/mixpanel';

const VerifyEmail = () => {
    const location = useLocation();
    const [emailErr, setEmailErr] = useState();
    const [checkButton, setCheckButton] = useState(false);
    const [otp, setOtp] = useState([]);
    const [otpError, setOtpError] = useState(false);
    const [validEmail, setValidEmail] = useState(false);


    const [first, setFirst] = useState('');
    const [second, setSecond] = useState('');
    const [third, setThird] = useState('');
    const [fourth, setForth] = useState('');
    const [fifth, setFifth] = useState('');
    const [sixth, setSixth] = useState('');

    const firstInputRef = useRef();
    const secondInputRef = useRef();
    const thirdInputRef = useRef();
    const fourthInputRef = useRef();
    const fifthInputRef = useRef();
    const sixthInputRef = useRef();

    const verifyEmailData = {
        'email': location.state.email,
        'otp': 123456,

    }
    const [data, setData] = useState(verifyEmailData);
    const navigate = useNavigate();
    const [seconds, setSeconds] = useState(600); // 10 minutes in seconds
    const [isActive, setIsActive] = useState(true);

    // Track page view when component mounts
    useEffect(() => {
        // Track email verification page view
        try {
            // Gather device and location info
            const deviceInfo = {
                browser: navigator.userAgent,
                device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
                viewport_size: `${window.innerWidth}x${window.innerHeight}`,
                language: navigator.language || navigator.userLanguage
            };
            
            Analytics.track('Email Verification Page Viewed', {
                email: location.state?.email || '',
                verification_type: 'password_reset',
                page_path: window.location.pathname,
                referrer: document.referrer || 'direct',
                ...deviceInfo
            });
            
            // Identify user if email is available
            if (location.state?.email) {
                Mixpanel.identify(location.state.email);
            }
        } catch (error) {
            console.warn('Error tracking email verification page view:', error);
        }
    }, [location.state?.email]);

    useEffect(() => {
        const handlePaste = async (event) => {
            const clipboardData = event.clipboardData || window.clipboardData;
            const pastedText = clipboardData.getData('text');

            // Assuming the pasted text is a 6-digit OTP
            if (/^\d{6}$/.test(pastedText)) {
                const otpDigits = pastedText.split('');

                setFirst(otpDigits[0]);
                setSecond(otpDigits[1]);
                setThird(otpDigits[2]);
                setForth(otpDigits[3]);
                setFifth(otpDigits[4]);
                setSixth(otpDigits[5]);
                
                // Track OTP paste event
                try {
                    Analytics.trackFeatureUsage('OTP Code Pasted', {
                        email: location.state?.email || '',
                        verification_type: 'password_reset',
                        input_method: 'paste'
                    });
                } catch (error) {
                    console.warn('Error tracking OTP paste:', error);
                }
            }
        };

        document.addEventListener('paste', handlePaste);

        return () => {
            document.removeEventListener('paste', handlePaste);
        };
    }, [location.state?.email]);

    useEffect(() => {
        // Focus on the first input when the component mounts
        firstInputRef.current.focus();
    }, []);

    useEffect(() => {
        let interval;

        if (isActive && seconds > 0) {
            interval = setInterval(() => {
                setSeconds((prevSeconds) => prevSeconds - 1);
            }, 1000);
        } else if (seconds === 0) {
            setIsActive(false);
            // Track OTP expiration
            try {
                Analytics.track('OTP Expired', {
                    email: location.state?.email || '',
                    verification_type: 'password_reset'
                });
            } catch (error) {
                console.warn('Error tracking OTP expiration:', error);
            }
        }

        return () => clearInterval(interval);
    }, [isActive, seconds, location.state?.email]);

    // Format the seconds into minutes and seconds
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    const onChageHandler = (event) => {
        const value = event.target.value;
        const name = event.target.name;

        if (name === 'firstNumber') {
            setFirst(value);
            if (value !== '' && /^\d+$/.test(value)) {
                secondInputRef.current.focus();
            }
        }
        if (name === 'secondNumber') {
            setSecond(value);
            if (value !== '' && /^\d+$/.test(value)) {
                thirdInputRef.current.focus();
            }
        }
        if (name === 'thirdNumber') {
            setThird(value);
            if (value !== '' && /^\d+$/.test(value)) {
                fourthInputRef.current.focus();
            }
        }
        if (name === 'fourthNumber') {
            setForth(value);
            if (value !== '' && /^\d+$/.test(value)) {
                fifthInputRef.current.focus();
            }
        }
        if (name === 'fifthNumber') {
            setFifth(value);
            if (value !== '' && /^\d+$/.test(value)) {
                sixthInputRef.current.focus();
            }
        }
        if (name === 'sixthNumber') {
            setSixth(value);
        }
    };

    useEffect(() => {
        const addOtp = first + second + third + fourth + fifth + sixth;
        //alert(addOtp);
        if (addOtp.length === 6) {
            setOtp(addOtp);
            setCheckButton(true);
            
            // Track complete OTP entered
            try {
                Analytics.trackFeatureUsage('OTP Code Entered', {
                    email: location.state?.email || '',
                    verification_type: 'password_reset',
                    input_method: 'manual',
                    time_remaining: seconds
                });
            } catch (error) {
                console.warn('Error tracking OTP entry:', error);
            }
        } else {
            setCheckButton(false);
            // ... handle the condition when OTP length is not 6
        }
    }, [first, second, third, fourth, fifth, sixth, location.state?.email, seconds]);

    // Rest of your code...


    const onSubmitHandler = (event) => {
        event.preventDefault();
        const verifyEmailData = {
            'email': location.state.email,
            'verificationCode': otp,

        }
        //console.log(verifyEmailData);

        // Track verification attempt
        try {
            Analytics.track('Email Verification Attempted', {
                email: location.state?.email || '',
                verification_type: 'password_reset',
                time_remaining: seconds
            });
        } catch (error) {
            console.warn('Error tracking verification attempt:', error);
        }

        let result = axiosPost(ApiName.verifyEmail, verifyEmailData)
            .then(function (response) {
                //console.log(response.data);
                const resData = JSON.parse(response.data.data);
                //console.log(resData);

                if (response.data.status == 200) {
                    // Track successful verification
                    try {
                        Analytics.track('Email Verification Successful', {
                            email: location.state?.email || '',
                            verification_type: 'password_reset',
                            time_taken: 600 - seconds // Time taken to verify in seconds
                        });
                        
                        // Update user properties
                        if (location.state?.email) {
                            Mixpanel.people.set({
                                'Email Verified': true,
                                'Last Verification': new Date().toISOString()
                            });
                        }
                    } catch (error) {
                        console.warn('Error tracking successful verification:', error);
                    }
                    
                    //localStorage.setItem('user', JSON.stringify({token:response.data.data}))
                    //console.log(localStorage.getItem('user'));
                    navigate('/create-new-password', {
                        state: {
                            email: verifyEmailData.email,
                            token: resData,
                        }
                    });

                }
            }).catch(function (errors) {
                // console.log('error',errors);
                if (errors.response.data.status == 400) {
                    setEmailErr(errors.response.data.message);
                    setValidEmail(false);
                    
                    // Track failed verification
                    try {
                        Analytics.track('Email Verification Failed', {
                            email: location.state?.email || '',
                            verification_type: 'password_reset',
                            error_message: errors.response.data.message || 'Unknown error',
                            attempt_count: 1 // You might want to track multiple attempts
                        });
                    } catch (error) {
                        console.warn('Error tracking failed verification:', error);
                    }
                } else {
                    setValidEmail(true);
                    setEmailErr('');
                }
            });
    }


    document.addEventListener("DOMContentLoaded", function (event) {
        function OTPInput() {
            const inputs = document.querySelectorAll('#otp > *[id]');
            for (let i = 0; i < inputs.length; i++) { inputs[i].addEventListener('keydown', function (event) { if (event.key === "Backspace") { inputs[i].value = ''; if (i !== 0) inputs[i - 1].focus(); } else { if (i === inputs.length - 1 && inputs[i].value !== '') { return true; } else if (event.keyCode > 47 && event.keyCode < 58) { inputs[i].value = event.key; if (i !== inputs.length - 1) inputs[i + 1].focus(); event.preventDefault(); } else if (event.keyCode > 64 && event.keyCode < 91) { inputs[i].value = String.fromCharCode(event.keyCode); if (i !== inputs.length - 1) inputs[i + 1].focus(); event.preventDefault(); } } }); }
        } OTPInput();
    });




    return (
        <>
            <div className="container-fluid">
                <div className="row">
                    <div className="col-md-6 bg-color">
                        <div className="offset-md-3 saver-plane4">
                            <img src="../images/r-logo.png" width="50" alt='logo' />
                            <div className="verify-email-banner-1">
                                <img src="../images/group-50970.png" className="img-fluid" alt='banner' />
                            </div>
                        </div>


                    </div>
                    <div className="col-md-5">
                        <div className="row">
                            <div className="col-sm-11">
                                <div className="verify-your-email-">
                                    <p className="text-end1">
                                        <Link to="/">Sign In</Link></p>
                                </div>
                            </div>
                        </div>


                        <div className="form-box3">
                            <h3>Verify Your Email</h3>
                            <p className="semi-header4">Enter the 6-digit verification code sent to <br />
                                {data.email} <Link to="/forgot-password">Change</Link></p>
                            <form onSubmit={onSubmitHandler}>
                                <div className="row">
                                    <div className="m-auto d-block">

                                        <div id="otp" className="inputs d-flex flex-row justify-content-center mt-4">

                                            <input
                                                ref={firstInputRef}
                                                className="m-0 text-center form-control rounded"
                                                type="text"
                                                id="first"
                                                maxlength="1"
                                                name="firstNumber"
                                                autocomplete="off"
                                                value={first}
                                                onChange={onChageHandler} />

                                            <div className="extra"></div>

                                            <input
                                                ref={secondInputRef}
                                                className="m-0 text-center form-control rounded"
                                                type="text"
                                                id="first"
                                                maxlength="1"
                                                name="secondNumber"
                                                autocomplete="off"
                                                value={second}
                                                onChange={onChageHandler} />

                                            <div className="extra"></div>

                                            <input
                                                ref={thirdInputRef}
                                                className="m-0 text-center form-control rounded"
                                                type="text"
                                                id="first"
                                                maxlength="1"
                                                name="thirdNumber"
                                                autocomplete="off"
                                                value={third}
                                                onChange={onChageHandler} />

                                            <div className="extra"></div>

                                            <input
                                                ref={fourthInputRef}
                                                className="m-0 text-center form-control rounded"
                                                type="text"
                                                id="first"
                                                maxlength="1"
                                                name="fourthNumber"
                                                autocomplete="off"
                                                value={fourth}
                                                onChange={onChageHandler} />

                                            <div className="extra"></div>

                                            <input
                                                ref={fifthInputRef}
                                                className="m-0 text-center form-control rounded"
                                                type="text"
                                                id="first"
                                                maxlength="1"
                                                name="fifthNumber"
                                                autocomplete="off"
                                                value={fifth}
                                                onChange={onChageHandler} />

                                            <div className="extra"></div>

                                            <input
                                                ref={sixthInputRef}
                                                className="m-0 text-center form-control rounded"
                                                type="text"
                                                id="first"
                                                maxlength="1"
                                                name="sixthNumber"
                                                autocomplete="off"
                                                value={sixth}
                                                onChange={onChageHandler} /><br />

                                        </div>
                                        <div className="resend-code">
                                            {!validEmail ? (
                                                <span className="email-error-message">{emailErr && <p className="timing">{emailErr}</p>}</span>
                                            ) : (
                                                <></>
                                            )}

                                        </div>

                                        <div className="timing">
                                            <span className="counter">Resend code in {String(minutes).padStart(2, '0')}:{String(remainingSeconds).padStart(2, '0')}</span>
                                        </div>

                                        <span className="Shift">
                                            {!checkButton ? (
                                                <input type="submit" value="Verify" className="cp-pluss cust-disabled" />
                                            ) : (
                                                <input type="submit" value="Verify" className="cp-pluss" />
                                            )}
                                        </span>
                                    </div>
                                </div>

                            </form>
                            <div className={`didnt ${remainingSeconds === 0 ? '' : 'disable-resend-action'}`}>
                                <p>Didn't get a code? <Link to="/forgot-password">Resend</Link></p>
                            </div>

                            <div className="spam-footer">
                                <p>
                                    Still don't see a code? Please check your <br /> spam folder.
                                </p>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
            <Footer />
        </>
    )
}
export default VerifyEmail;