import React from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  PointElement,
  LineController,
  BarController,
} from "chart.js";
import { Chart } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  LineController,
  BarController
);

const BarChart = ({ dataValues, labels }) => {
  const data = {
    labels,
    datasets: [
      {
        type: "bar",
        data: dataValues,
        backgroundColor: "rgba(9, 61, 84, 1.0)",
        borderColor: "rgba(9, 61, 84, 1.0)",
        borderWidth: 1,
        barThickness: "30",
      },
      {
        type: "line",
        data: dataValues,
        borderColor: "rgba(85, 194, 195, 1)",
        borderWidth: 2,
        fill: false,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top",
        display: false,
      },
      title: {
        display: false,
        text: "Title",
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        display: true, // Hide the y-axis
        title: {
          display: true,
          text: "Minutes", // Optional title for the y-axis
        },
        ticks: {
          display: true, // Display y-axis labels
        },
      },
      x: {
        beginAtZero: true,
        grid: {
          display: false, // Hide the x-axis grid lines
        },
      },
    },
  };

  return <Chart type="bar" data={data} options={options} />;
};

export default BarChart;
