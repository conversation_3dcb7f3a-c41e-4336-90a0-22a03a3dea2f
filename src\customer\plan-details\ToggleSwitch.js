const ToggleSwitch = ({ activeTab, toggleTab }) => (
    <div
        onClick={toggleTab}
        style={{
            ...toggleSwitchStyle,
            justifyContent: activeTab === "annual" ? "flex-start" : "flex-end",
        }}
    >
        <div className="roll-tab" style={rollTabStyle}></div>
    </div>
);
const rollTabStyle = {
    width: "20px",
    height: "20px",
    backgroundColor: "#fff",
    borderRadius: "50%",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
    transition: "all 0.3s ease",
    background: "linear-gradient(270deg, rgba(8, 192, 203, 1) 0%, rgba(28, 141, 156, 1) 100%)",
};
const toggleSwitchStyle = {
    position: "relative",
    width: "40px",
    height: "25px",
    backgroundColor: "rgb(255 255 255)",
    borderRadius: "50px",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    padding: "2px",
    margin: "0 10px",
    transition: "background-color 0.3s ease",
    boxShadow: "inset 0px 0px 6px #d7d7d7",
};

export default ToggleSwitch; 