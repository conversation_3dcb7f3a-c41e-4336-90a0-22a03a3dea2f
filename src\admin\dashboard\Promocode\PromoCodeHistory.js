import React, { useEffect, useState } from "react";
import AdminHeader from "../../layouts/AdminHeader.js";
import S from "../../assets/css/layouts/admin-header.module.css";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import Alert from "../../../customer/common-files/alert.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import loadingGif from "../../../customer/assests/waiting.gif";

const PromoCodeHistory = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [data, setData] = useState([]);
  const itemsPerPage = 10;
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [totalPage, setTotalPage] = useState(0);
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  useEffect(() => {
    const postData = async () => {
      try {
        let params;
        if (!searchQuery) {
          params = JSON.stringify({
            page: currentPage,
            pageSize: itemsPerPage,            
            sortBy: "desc",
          });
        } else {
          params = JSON.stringify({
            page: searchQuery ? 1 : currentPage,
            pageSize: searchQuery ? 1000 : itemsPerPage,
            searchParams: {
              promocode: searchQuery,
            },
            sortBy: "desc",
          });
        }
        searchQuery ? setLoading(false) : setLoading(true);
        const res = await PostWithTokenNoCache(
          ApiName.promocodeHistory,
          params
        );
        if (res && "status" in res) {
          if (res.status == 200) {
            let jsonArray = res.data.data ? JSON.parse(res.data.data) : [];
            setTotalPage(jsonArray.promocode_use_history.totalPages);
            setData(
              jsonArray.promocode_use_history.items
                ? jsonArray.promocode_use_history.items
                : []
            );
            setLoading(false);
          } else {
            setButtonType("error");
            setDefaultErrorMsg(res.response.data.message);
            setDefaultAlert(true);
            setLoading(false);
          }
        } else {
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
          setLoading(false);
        }
      } catch (error) {
        setButtonType("error");
        setDefaultErrorMsg(error?.response?.data?.message);
        setDefaultAlert(true);
        setLoading(false);
      }
    };
    postData();
  }, [currentPage, searchQuery]);

  const dateFormat = (date) => {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = parsedDate.getMonth() + 1;
    const day = parsedDate.getDate();
    const hours = parsedDate.getHours();
    const minutes = parsedDate.getMinutes();
    const ampm = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 === 0 ? 12 : hours % 12;
    return `${year}/${month}/${day} - ${formattedHours}:${
      minutes < 10 ? "0" : ""
    }${minutes} ${ampm}`;
  };

  return (
    <div className={S.app}>
      <AdminHeader></AdminHeader>
      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div style={{ width: "100%", padding: "0 10px 0 0" }}>
              <aside className={S.right_sidebar}>
                <div className={S.table_responsive}>
                  <div className="d-flex flex-row justify-content-end">
                    <div
                      className={S.content}
                      style={{ fontSize: "20px", paddingTop: "7px" }}
                    >
                      Promocode History
                    </div>
                    <div className="admin-search">
                      <input
                        type="text"
                        placeholder="Search by promocode"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className={S.searchbyemail}
                      />
                    </div>
                  </div>
                  <table>
                    <thead className="table-active text-center">
                      <tr>
                        <th className={S.packagename}>SL No</th>
                        <th className={S.packagename}>User Id</th>
                        <th className={S.packagename}>Name</th>
                        <th className={S.packagename}>Email</th>
                        <th className={S.packagename}>Promocode</th>
                        <th className={S.packagename}>Package</th>
                        <th className={S.packagename}>Status</th>
                        <th className={S.packagename}>Created At</th>
                        <th></th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.map((item, i) => (
                        <tr className={S.tablelight} key={i + 1}>
                          <td className={S.centeralign}>
                            {(currentPage - 1) * 10 + i + 1}
                          </td>
                          <td className={S.centeralign}>{item.id}</td>
                          <td className={S.centeralign}>
                            {item.firstName} {item.lastName}
                          </td>
                          <td className={S.centeralign}>{item.email}</td>
                          <td className={S.centeralign}>{item.promocode}</td>
                          <td className={S.centeralign}>{item.status}</td>
                          <td className={S.centeralign}>
                            {item.is_active ? "Active" : "In-active"}
                          </td>
                          <td className="d-flex flex-row justify-content-center text-center">
                            {dateFormat(item.createdAt)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </aside>
            </div>
          ) : (
            <div className="mx-auto mt-5" style={{ display: loading }}>
              <img
                src={loadingGif}
                alt="Loading"
                className="loader"
                width="400"
              />
            </div>
          )}
        </div>
      </div>
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={totalPage}
        pageSize={1}
        onPageChange={(page) => {
          setCurrentPage(page);
        }}
      />

      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};
export default PromoCodeHistory;
