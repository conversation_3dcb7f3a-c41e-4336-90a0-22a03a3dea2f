import Analytics from './analyticsTracking';
import Mixpanel from './mixpanel';

/**
 * Helper utilities for tracking user journey milestones, lifecycle stages,
 * and implementing the customer journey requirements.
 */

// Check if storage is available
const isStorageAvailable = (type) => {
  try {
    const storage = window[type];
    const x = '__storage_test__';
    storage.setItem(x, x);
    storage.removeItem(x);
    return true;
  } catch (e) {
    return false;
  }
};

// Memory-based fallback for when localStorage is not available
const memoryStorage = {
  data: {},
  getItem(key) {
    return this.data[key] !== undefined ? this.data[key] : null;
  },
  setItem(key, value) {
    this.data[key] = value;
  },
  removeItem(key) {
    delete this.data[key];
  }
};

// Helper function to safely get data from storage
export const getFromStorage = (key, defaultValue = null) => {
  if (isStorageAvailable('localStorage')) {
    try {
      const value = localStorage.getItem(key);
      return value !== null ? value : defaultValue;
    } catch (e) {
      console.warn('Error accessing localStorage:', e);
      return memoryStorage.getItem(key) || defaultValue;
    }
  }
  return memoryStorage.getItem(key) || defaultValue;
};

// Helper function to safely set data in storage
export const setToStorage = (key, value) => {
  if (isStorageAvailable('localStorage')) {
    try {
      localStorage.setItem(key, value);
    } catch (e) {
      console.warn('Error writing to localStorage:', e);
      memoryStorage.setItem(key, value);
    }
  } else {
    memoryStorage.setItem(key, value);
  }
};

// Helper to calculate days since signup
export const calculateDaysSinceSignup = () => {
  const signupDate = getFromStorage('signup_date');
  if (!signupDate) return 0;
  
  const now = new Date();
  const signup = new Date(signupDate);
  const diffTime = Math.abs(now - signup);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Helper to determine lifecycle stage based on user data
export const determineLifecycleStage = (user) => {
  if (!user) return 'Unknown';
  
  // Extract user metrics
  const totalDownloads = user.totalDownloads || 0;
  const lastLoginDate = user.lastLoginDate ? new Date(user.lastLoginDate) : null;
  const creditPercentageUsed = user.creditPercentageUsed || 0;
  const daysInactive = lastLoginDate ? Math.floor((new Date() - lastLoginDate) / (1000 * 60 * 60 * 24)) : 0;
  
  // Determine stage
  if (!user.hasLoggedIn) return 'Incomplete Registration';
  if (daysInactive >= 15) return 'At Risk';
  if (creditPercentageUsed >= 90) return 'Credit Limit Reached';
  if (totalDownloads >= 10) return 'Engaged';
  if (totalDownloads > 0) return 'Active';
  return 'Onboarding';
};

// Track incomplete signup
export const trackSignupAbandonment = (formData) => {
  const startTime = parseInt(getFromStorage('signup_start_time'));
  if (!startTime || getFromStorage('user')) return;
  
  const currentTime = new Date().getTime();
  const timeSpent = Math.floor((currentTime - startTime) / 1000); // in seconds
  
  // Count completed fields
  const completedFields = [];
  Object.entries(formData).forEach(([key, value]) => {
    if (value && typeof value === 'string' && value.trim() !== '') {
      completedFields.push(key);
    }
  });
  
  try {
    Analytics.trackIncompleteSignup({
      timeSpent: timeSpent,
      fieldsCompleted: completedFields,
      lastField: formData.lastFocusedField || '',
      email: formData.email || '',
      form_completion_percentage: formData.totalFields ? 
        (completedFields.length / formData.totalFields) * 100 : 0,
      utm_source: getFromStorage('utm_params') ? 
        JSON.parse(getFromStorage('utm_params')).utm_source : '',
      plan_type: formData.planType || ''
    });
  } catch (e) {
    console.warn('Error tracking signup abandonment:', e);
  }
};

// Track credit usage and trigger appropriate actions
export const trackCreditUsage = async (userEmail) => {
  try {
    // This should be replaced with actual API calls to get credit data
    // Simulating API response for example
    const creditData = await getUserCreditData(userEmail);
    
    const creditPercentage = (creditData.used / creditData.total) * 100;
    
    Analytics.trackCreditUsage({
      creditsUsed: creditData.used,
      totalCredits: creditData.total,
      percentageUsed: creditPercentage,
      email: userEmail
    });
    
    // Update user lifecycle stage based on credit usage
    if (creditPercentage >= 100) {
      Analytics.updateUserLifecycleStage(userEmail, 'Credit Limit Reached', {
        'Credit Usage': '100%',
        'Credits Remaining': 0
      });
    } else if (creditPercentage >= 90) {
      Analytics.updateUserLifecycleStage(userEmail, 'Credit Limit Near', {
        'Credit Usage': `${creditPercentage.toFixed(1)}%`,
        'Credits Remaining': creditData.total - creditData.used
      });
    }
    
    return creditPercentage;
  } catch (error) {
    console.error('Error tracking credit usage:', error);
    return null;
  }
};

// Mock function - replace with actual implementation
const getUserCreditData = async (email) => {
  // This should be replaced with an actual API call
  return {
    used: 185,
    total: 200
  };
};

// Track user activity and inactivity
export const trackUserActivity = (user) => {
  if (!user || !user.email) return;
  
  // Update last active date
  const lastActive = new Date();
  localStorage.setItem('last_active_date', lastActive.toISOString());
  
  // Update Mixpanel profile
  Analytics.trackUserIdentify(user.email, {
    'Last Active Date': lastActive,
    'Days Since Signup': calculateDaysSinceSignup()
  });
};

export const checkUserInactivity = (user) => {
  if (!user || !user.email) return;
  
  const lastActiveDate = localStorage.getItem('last_active_date');
  if (!lastActiveDate) return;
  
  const now = new Date();
  const lastActive = new Date(lastActiveDate);
  const diffTime = Math.abs(now - lastActive);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  // Track inactivity milestones
  if (diffDays >= 3) {
    Analytics.trackInactivity({
      daysInactive: diffDays,
      lastSeen: lastActiveDate,
      email: user.email
    });
    
    // Update user lifecycle for extended inactivity
    if (diffDays >= 15) {
      Analytics.updateUserLifecycleStage(user.email, 'At Risk', {
        'Days Inactive': diffDays
      });
    }
  }
  
  return diffDays;
};

// Track when a user views the pricing page multiple times
export const trackPricingPageView = (user) => {
  if (!user || !user.email) return;
  
  // Get previous view count from localStorage
  const pricingViewsKey = `pricing_views_${user.email}`;
  let viewCount = parseInt(localStorage.getItem(pricingViewsKey) || '0');
  viewCount += 1;
  
  // Track current view with count
  Analytics.trackPricingPageView({
    viewCount: viewCount,
    currentPlan: user.plan || 'free',
    email: user.email
  });
  
  // Store updated count
  localStorage.setItem(pricingViewsKey, viewCount.toString());
  
  // For Icebreaker plan users who viewed pricing multiple times
  if (user.plan === 'Icebreaker' && viewCount >= 3) {
    Analytics.updateUserLifecycleStage(user.email, 'Considering Upgrade', {
      'Pricing Views': viewCount
    });
  }
};

// Track filter usage and provide insights
export const trackSearchAndFilterUsage = (filterData, resultsCount, user) => {
  if (!user || !user.email) return;
  
  const filterCount = filterData.length;
  
  Analytics.trackFilterUsage({
    filterCount: filterCount,
    filterTypes: filterData.map(f => f.type),
    searchResults: resultsCount,
    hasResults: resultsCount > 0,
    email: user.email
  });
  
  // Track when users use limited filters
  if (filterCount < 3) {
    Analytics.trackFilterUsage({
      filter_count: filterCount,
      user_email: user.email,
      days_since_signup: calculateDaysSinceSignup()
    });
  }
  
  // Track when searches return no results
  if (resultsCount === 0) {
    Analytics.trackNoSearchResults({
      terms: filterData.map(f => `${f.type}:${f.value}`).join(', '),
      filters: filterData.map(f => f.type)
    });
  }
};

// Track referrals
export const trackReferralSignup = (referralCode, newUserEmail) => {
  if (!referralCode || !newUserEmail) return;
  
  // In a real implementation, you'd fetch the referrer from the code
  getReferrerFromCode(referralCode).then(referrer => {
    Analytics.trackReferral({
      referrerEmail: referrer.email,
      referredEmail: newUserEmail,
      referralCode: referralCode,
      status: 'Signed Up'
    });
    
    // Also track for the referrer
    Analytics.trackFeatureUsage('User Referred', {
      referrer: referrer.email,
      referred: newUserEmail,
      referral_code: referralCode
    });
  });
};

// Mock function - replace with actual implementation
const getReferrerFromCode = async (code) => {
  // This should be replaced with an actual API call
  return {
    email: '<EMAIL>',
    name: 'Referrer User'
  };
};

// Track email reveals and downloads
export const trackEmailReveal = (contactInfo, isAddedToList) => {
  Analytics.trackEmailRevealed({
    contactId: contactInfo.id,
    contactType: contactInfo.type || 'standard',
    addedToList: isAddedToList,
    downloaded: false
  });
};

export const trackContactDownload = (contacts, listName) => {
  const user = JSON.parse(localStorage.getItem('user'));
  if (!user) return;
  
  Analytics.trackContactsDownloaded({
    count: contacts.length,
    listName: listName,
    contactTypes: contacts.map(c => c.type),
    isFirstDownload: !localStorage.getItem('has_downloaded')
  });
  
  // Mark that user has downloaded contacts
  localStorage.setItem('has_downloaded', 'true');
  
  // Track milestone for 10+ contacts downloaded
  if (contacts.length >= 10) {
    Analytics.trackEngagementMilestone({
      type: 'download_milestone',
      value: '10+ Contacts',
      email: user.email
    });
  }
};

// Update all user properties for consistent tracking
export const updateUserProperties = (user) => {
  if (!user || !user.email) return;
  
  // Calculate credit usage percentage
  const creditPercentage = user.creditsUsed && user.totalCredits ? 
    (user.creditsUsed / user.totalCredits) * 100 : 0;
  
  // Set comprehensive user properties for segmentation
  Analytics.trackUserIdentify(user.email, {
    'Plan Type': user.planType || 'free',
    'Sign Up Date': user.signupDate ? new Date(user.signupDate) : new Date(),
    'Last Login Date': new Date(),
    'Credits Remaining': user.totalCredits ? user.totalCredits - user.creditsUsed : 0,
    'Total Credits': user.totalCredits || 0,
    'Credit Percentage Used': creditPercentage,
    'Total Downloads': user.totalDownloads || 0,
    'Days Since Signup': calculateDaysSinceSignup(),
    'Lifecycle Stage': determineLifecycleStage(user)
  });
}; 