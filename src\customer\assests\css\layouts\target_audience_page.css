.modal-conetnt {
    border: 1px solid #BFBFBF;
    margin: 15px 10px 0 10px;
    padding: 10px 0px 0 10px;
    border-radius: 5px;
}

p.prospects’ {
    font-size: 12px;
    color: #000;
    padding: 0px 10px 0 10px;
}

a.getleads {
    font-weight: 600;
    text-decoration: none;
    color: #0391EA;
}


a.getleadss {

    text-decoration: underline;
    color: #0391EA;
}

.modal-title-include {
    font-size: 18px;
    color: #093D54;
    font-weight: 600;
    padding: 0px 0px 0 23px;
    margin: 0;
}

hr.you-line {
    margin: 10px 0 0 26px;
    width: 34px;
    padding: 0 0px 0 0;
    border: 1px solid #093D54;
}


/* target 2 */

.primary {
    margin-top: 2rem;
}

.secondary {
    border: 1px solid #BFBFBF;
    width: 550px;
    margin: auto;
    border-radius: 5px;
}

.pagecaption p {
    padding: 0 0 0 23px;
    color: #093D54;
    font-weight: 600;
    margin: 0;
    font-size: 18px;
}

hr.pagehorizontal {
    border: 1px solid #093D54;
    width: 32px;
    margin: 8px 0 0 25px;
}

.reachaudience img {
    margin: auto;
    display: block;
    padding: 20px 0 24px 0px;
}

.thired-leyout {
    margin: auto;
    border: 1px solid #BFBFBF;
    width: 500px;
    border-radius: 5px;
}

p.supereffective {
    font-size: 14px;
    padding: 10px 10px 0 16px;
    color: #000;
    margin: 7px 0 0 0;
}

a.getthe {
    color: #0391EA;
    text-decoration: none;
    font-weight: 600;
}

a.upgradethe {
    color: #0391EA;
    text-decoration: underline;
}

button.sharenow {
    margin: 14px 23px 15px 0px;
    border: 0;
    background-color: #093D54;
    color: #fff;
    padding: 5px 20px 5px 20px;
    border-radius: 10px;
    cursor: pointer;
}
