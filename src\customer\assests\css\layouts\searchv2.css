@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@600&display=swap');


body {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  /* Regular */
}

.tab-btn {
  padding: 10px 20px;
  border: none;
  background-color: transparent;
  cursor: pointer;
  border-radius: 4px;
}

.tab-btn.active {
  background-color: transparent;
  /* color: #fff; */
}

.tab-content {
  background: #f9f9f9;
  padding: 4px;
  /* border: 1px solid #ddd; */
  border-radius: 4px;
}

span.expand-filter {
  background-color: #fff;
  padding: 2px 10px 4px 10px;
  border-radius: 12px;
  margin: 72px 0 0 0;
  border: 1px solid #EAEAEA;
  box-shadow: 0 0 0 2px #f6f6f6;
  position: relative;
  top: 20px;
}

.inactive-img {
  opacity: 0.5;
  filter: grayscale(100%);
  transition: all 0.3s ease;
}

.tab-btn:hover .inactive-img {
  opacity: 0.8;
  filter: grayscale(50%);
}

p.search-img {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.filter-section-v2 {
  /* border: 1px solid #E3E3E5; */
  border-radius: 12px;
}

p.filters-v2 {
  font-size: 14px;
  margin: 10px 0 0 15px;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 8px;
  left: 3px;
  width: 100%;
  height: 2px;
  background-color: #1473E6;
  border-radius: 1px;
}

p.clear-all-v2 {
  margin: 10px 14px 0 0px;
  font-size: 14px;
  color: #146EF6;
}