import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const DWINTracker = () => {
  const location = useLocation();

  useEffect(() => {
    // Only load on pages that are not payment-successful
    // if (!location.pathname.includes('/payment-successful')) {
    //   loadDWINScript();
    // }
    loadDWINScript();

    return () => {
      // Cleanup if needed
      const script = document.querySelector('script[src="https://www.dwin1.com/116059.js"]');
      if (script && script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [location.pathname]);

  const loadDWINScript = () => {
    // Check if script already exists to avoid duplicates
    const existingScript = document.querySelector('script[src="https://www.dwin1.com/116059.js"]');
    if (existingScript) return;

    const script = document.createElement('script');
    script.src = 'https://www.dwin1.com/116059.js';
    script.type = 'text/javascript';
    script.defer = true;
    script.id = 'dwin-tracker';
    
    // Error handling
    script.onerror = () => {
      console.error('DWIN script failed to load');
      // Implement retry logic or analytics here if needed
    };
    
    document.head.appendChild(script);
  };

  return null; // This component doesn't render anything
};

export default DWINTracker;