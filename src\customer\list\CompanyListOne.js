import React, { useEffect, useState, useContext } from "react";
import "../assests/css/filter/contact_list_one.css";
import { Link, useParams } from "react-router-dom";
import UseTabStore from "../common-files/useGlobalState.js";
import Alert from "../common-files/alert.js";
import { ApiName } from "../common-files/ApiNames.js";
import loadingGif from "../assests/waiting.gif";
import Pagination from "../pagination/Pagination.js";
import Header from "../layouts/Header";
import DashboardContext from "../common-files/ContextDashboard.js";
import {
  PostWithTokenNoCache,
  postWithToken,
} from "../common-files/ApiCalls.js";
import ContactModal from "../company-filters-new/CompanyModel.js";
const CompanyListOne = () => {
  const { dataDC } = useContext(DashboardContext);

  const [pageSize, setPageSize] = useState(
    dataDC.membership === "trail" ? 10 : dataDC.membership === "prime" ? 25 : 10
  );

  const { id } = useParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItem, setTotalItem] = useState(1);
  const [listItemData, setListItemData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showContactModal, setShowContactModal] = useState(true);
  const [listDetail, setListDetail] = useState([]);
  const [listCount, setListCount] = useState(0);
  const [selectUsers, setSelectUsers] = useState([]);
  const [checkbox, setCheckbox] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [showSuccessMessage, setSuccessMessage] = useState(false);
  const [wishListItedmIds, setWishListItedmIds] = useState([]);
  const {
    viewModal,
    isLowViewCredits,
    selectedContact,
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultErrorMsg,
    setDefaultAlert,
    contactModelId,
    setIsLowViewCredits,
    setSelectedContact,
    setContactModelId,
    setViewModal
  } = UseTabStore();

  useEffect(() => {
    fetchList();
  }, [currentPage]);

  const getAllListRecords = async () => {
    try {
      const param = JSON.stringify({
        page: currentPage,
        pageSize: pageSize,
        searchPattern: {
          searchBy: "COMPANY",
          wishListId: parseInt(id),
        },
      });
      const res = await PostWithTokenNoCache(ApiName.viewCompanyListItems, param);
      if (res && "status" in res) {
        if (res.status == 200) {
          const record = JSON.parse(res.data.data);
          setTotalItem(record.totalCount);
          const records = record.records;

          const formattedWishListItems = records.map((item) => ({
            id: item.wish_list_item_id,
            wishListId: parseInt(id),
            dataId: item.id,
            createdAt: item.createdAt,
            updateAt: item.updateAt
          }));
          setWishListItedmIds(formattedWishListItems);
          const updatedrecord = records.map((item) => ({
            ...item,
            isSelected: selectUsers.includes(item.wish_list_item_id),
          }));
          setCheckbox(false);
          const allUserIdsSelected = records.every((item) =>
            selectUsers.includes(item.wish_list_item_id)
          );
          setCheckbox(allUserIdsSelected);
          let newData = [...updatedrecord];
          setListItemData(newData);
          setIsLoading(false);
        } else {
          setIsLoading(false);
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
        }
      } else {
        setIsLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(res.response.data.message);
        setDefaultAlert(true);
      }
    } catch (error) {
      setIsLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
    }
  };

  const viewDetails = ((id) => {
    setViewModal(true);
    const data = { "id": id };
    fetchSingleContactRecord(data, 0, 0);
    setDefaultAlert(false);
  })

  const urlSingleFetch = ApiName.fetchSingleCompanyDetails;
  async function fetchSingleContactRecord(data, total_balance_contact_view, total_balance_credit) {
    const addActionKey = {
      ...data,
      action: "view",
      searchBy: "COMPANY",
    }
    const res = await postWithToken(urlSingleFetch, addActionKey);
    if (res && "status" in res) {
      if (res.data.status === 200) {
        let dataObj = JSON.parse(res.data.data);
        if (dataObj[0] === undefined || dataObj[0] === null) return;
        if (showContactModal) {
          setIsLowViewCredits(false);
          setSelectedContact(dataObj[0]);
          if (total_balance_contact_view != null) {
            let totalBalanceContactView = total_balance_contact_view;
            if ("membership" in dataDC && dataDC.membership === "prime") {
              totalBalanceContactView += 1;
            } else {
              totalBalanceContactView -= 1;
            }
            const requestBody = {
              total_balance_contact_view: totalBalanceContactView,
              total_balance_credit: total_balance_credit
            };
          }
        }
      }
    }
  }

  const deleteItem = async (id) => {
    deleteItemFromList([id]);
  };
  const deleteItemFromList = async (ids) => {
    const primaryKey = wishListItedmIds && Array.isArray(wishListItedmIds) && wishListItedmIds.length > 0
      ? wishListItedmIds
        .filter((item) => ids.includes(item.id))
        .map((item) => item.id)
      : ids;

    try {
      const param = JSON.stringify({
        ids: primaryKey,
      });
      const res = await PostWithTokenNoCache(
        ApiName.listItemDeleteByIds,
        param
      );
      if (res && "status" in res) {
        if (res.status == 200) {
          const count = totalItem - ids.length;
          setListCount(count);
          const param = JSON.stringify({
            id: id,
            listName: listDetail.listName,
            wishListSection: listDetail.wishListSection,
            status: "OPEN",
            listCount: count,
          });
          try {
            const res = await PostWithTokenNoCache(ApiName.updateList, param);
            if (res && "status" in res) {
              if (res.status == 200) {
                setSuccessMessage(true);
                getAllListRecords();
              } else {
                setButtonType("error");
                setDefaultErrorMsg(res.response.data.message);
                setDefaultAlert(true);
              }
            } else {
              setButtonType("error");
              setDefaultErrorMsg(res.response.data.message);
              setDefaultAlert(true);
            }
          } catch (error) {
            setButtonType("error");
            setDefaultErrorMsg(error?.response?.data?.message);
            setDefaultAlert(true);
          }
        } else {
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
        }
      } else {
        setButtonType("error");
        setDefaultErrorMsg(res.response.data.message);
        setDefaultAlert(true);
      }
    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
    }
  };

  const fetchList = async () => {
    try {
      const param = JSON.stringify({
        id: id,
      });
      setIsLoading(true);
      const res = await PostWithTokenNoCache(ApiName.fetchList, param);
      if (res && "status" in res) {
        if (res.status == 200) {
          const records = JSON.parse(res?.data?.data);
          setListCount(records.listCount);
          setListDetail(records);
          getAllListRecords();
        } else {
          setIsLoading(false);
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
        }
      } else {
        setIsLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(res.response.data.message);
        setDefaultAlert(true);
      }
    } catch (error) {
      setIsLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
    }
  };

  const deleteSelectedItem = () => {
    // need to show popup
    if (selectUsers.length > 0) setIsPopupOpen(true);
  };
  const selectAllUser = (isChecked) => {
    
    // Update checkbox state immediately
    setCheckbox(isChecked);
  
    // Update selected users
    if (isChecked) {
      // Select all
      const allIds = listItemData.map(item => item.wish_list_item_id);
      setSelectUsers(allIds);
      
      // Update items' selected state
      setListItemData(listItemData.map(item => ({
        ...item,
        isSelected: true
      })));
    } else {
      // Deselect all
      setSelectUsers([]);
      
      // Update items' selected state
      setListItemData(listItemData.map(item => ({
        ...item,
        isSelected: false
      })));
    }
  };
  const selectUsersOnChange = (id) => {
    let updatedData;
    if (selectUsers.includes(id)) {
      setSelectUsers(selectUsers.filter((userId) => userId !== id));

      updatedData = listItemData.map((item) => ({
        ...item,
        isSelected: item.wish_list_item_id == id ? false : item.isSelected,
      }));
    } else {
      setSelectUsers([...selectUsers, id]);
      updatedData = listItemData.map((item) => ({
        ...item,
        isSelected: item.wish_list_item_id == id ? true : item.isSelected,
      }));
    }
    setListItemData(updatedData);
  };

  const onCloseDelete = () => {
    setIsPopupOpen(false);
  };
  const onDeleteSuccess = async () => {
    setIsPopupOpen(false);
    deleteItemFromList(selectUsers);
  };

  const successMessageStyle = {
    position: "absolute",
    bottom: "20%",
    left: "46%",
    backgroundColor: "#EEFFF6",
    border: "1px solid #6DE1A4",
    padding: "4px 18px",
    borderRadius: "7px",
    fontSize: "14px",
  };
  const check = {
    color: "#6DE1A4",
    backgroundColor: "white",
    borderRadius: "50%",
    padding: "3px",
    fontSize: "10px",
    position: "absolute",
    top: "3px",
    left: "3px",
  };
  const checkblock = {
    backgroundColor: "#6DE1A4",
    padding: "3px 11px",
    marginRight: "5px",
    borderRadius: "4px",
    position: "relative",
  };

  useEffect(() => {
    let timer;
    if (showSuccessMessage) {
      timer = setTimeout(() => {
        setSuccessMessage(false);
      }, 5000);
    }
    return () => clearTimeout(timer);
  }, [showSuccessMessage]);

  return (
    <>
      <Header />
      {!isLoading ? (
        <div className="p-4">
          <div className="d-flex flex-row">
            <Link to="/saved-list">
              <img src="../images/arrow-left.png" />
            </Link>
            <div>
              <p className="savedcontactlist">Saved List</p>
            </div>
          </div>

          <div className="contactlistfirstlayer">
            <div className="d-flex flex-row">
              <div>
                <p className="ContactList3">{listDetail.listName}</p>
              </div>
              <div>
                <p className="contactlistSearchResultFound">
                  Search Result Found{" "}
                  <span className="fivehundradcontact">
                    {totalItem} Company
                  </span>
                </p>
              </div>
              <div className="trasho">
                {listDetail.status == "IN_PROGRESS" ||
                  listDetail.status == "VERIFIED" ||
                  listDetail.status == "DOWNLOADED" ? (
                  <i
                    className="fa fa-trash-o"
                    style={{
                      fontSize: "20px",
                      padding: "2px 6px 2px 6px",
                      backgroundColor: "#fff",
                      margin: "0 0 0 10px",
                      color: "#C2C8CE",
                    }}
                  ></i>
                ) : selectUsers.length == 0 ? (
                  <i
                    className="fa fa-trash-o"
                    style={{
                      fontSize: "17px",
                      padding: "0px 3px 0px 3px",
                      backgroundColor: "#fff",
                      margin: "0 0 0 10px",
                      color: "#C2C8CE",
                      cursor: "auto",
                    }}
                  ></i>
                ) : (
                  <img
                    onClick={() => deleteSelectedItem()}
                    src="../images/delete.png"
                    style={{
                      cursor: "pointer",
                      padding: "2px 6px 2px 6px",
                      margin: "0 0 0 10px",
                      backgroundColor: "#fff",
                      padding: "2px 3px 2px 3px",
                    }}
                  />
                )}
              </div>
            </div>

            <div className="">
              <table>
                <thead className="contactlistheader">
                  <th className="contactlistth">
                    <input
                      type="checkbox"
                      checked={checkbox}
                      onChange={(e) => selectAllUser(e.target.checked)}
                    />
                    Company Name
                  </th>
                  <th className="contactlistth">Company URL</th>
                  <th className="contactlistth">Employee Size</th>
                  <th className="contactlistth">Revenue</th>
                  <th className="contactlistth">Industry</th>
                  <th className="contactlistth">Location</th>
                  <th className="contactlistth" style={{ textAlign: "center" }}>
                    Actions
                  </th>
                </thead>
                <tbody className="contactlistbody">
                  {listItemData.map((item) => (
                    <tr>
                      <td className="contactlisttd">
                        <input
                          type="checkbox"
                          checked={item.isSelected}
                          onChange={() => selectUsersOnChange(item.wish_list_item_id)}
                        />
                        {item.company_company_name}
                      </td>
                      <td className="contactlisttd">
                        {item.company_website ? item.company_website : "--"}
                      </td>
                      <td className="contactlisttd">
                        {item.company_employee_size
                          ? item.company_employee_size
                          : 0}
                        +
                      </td>
                      <td className="contactlisttd">
                        {item.company_annual_revenue_amount
                          ? item.company_annual_revenue_amount
                          : "--"}
                      </td>
                      <td className="contactlisttd">
                        {item.company_industries
                          ? item.company_industries
                          : "--"}
                      </td>
                      <td className="contactlisttd">
                        {item.company_address_city}, <br />{" "}
                        {item.company_address_state},
                        {item.company_address_country}
                      </td>
                      <td className="contactlisttd">
                        <div className="d-flex flex-row justify-content-center">
                          <div>
                            <img
                              style={{
                                cursor: "auto",
                                margin: "0 0 0 0",
                                padding: "0 6px 0 0px",
                              }}
                              src="../images/eyee.png"
                              onClick={() => viewDetails(item.id)}
                            />
                          </div>
                          <div className="savedlistverticalline"></div>
                          <div>
                            {listDetail.status == "IN_PROGRESS" ||
                              listDetail.status == "VERIFIED" ||
                              listDetail.status == "DOWNLOADED" || selectUsers.length > 1? (
                              <i
                                className="fa fa-trash-o"
                                style={{
                                  cursor: "auto",
                                  fontSize: "20px",
                                  backgroundColor: "#fff",
                                  color: "#C2C8CE",
                                  margin: " 0px 7px",
                                }}
                              ></i>
                            ) : (
                              <img
                                onClick={() => deleteItem(item.wish_list_item_id)}
                                src="../images/delete.png"
                                style={{
                                  cursor: "pointer",
                                  margin: "0",
                                  padding: "0 0 0 6px",
                                }}
                              />
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      ) : (
        <div
          className="mx-auto mt-5"
          style={{ display: isLoading, textAlign: "center" }}
        >
          <img src={loadingGif} alt="Loading" className="loader" width="400" />
        </div>
      )}
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={totalItem / pageSize}
        pageSize={1}
        onPageChange={(page) => {
          setCurrentPage(page);
        }}
      />
      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
      {isPopupOpen && (
        <Popup
          selectedCounts={selectUsers}
          onCloseDelete={onCloseDelete}
          onDeleteSuccess={onDeleteSuccess}
        />
      )}
      {showSuccessMessage && (
        <div style={successMessageStyle}>
          <span style={checkblock}>
            <i className="fa fa-check" style={check}></i>
          </span>
          Deleted Successfully.
        </div>
      )}
       {selectedContact && viewModal ? <ContactModal isLowViewCredits={isLowViewCredits} /> : (<></>)}
    </>
  );
};

export default CompanyListOne;

const Popup = ({ selectedCounts, onCloseDelete, onDeleteSuccess }) => {
  return (
    <div
      class="modal"
      style={{
        display: "block",
        important: "true",
        backgroundColor: "#0000007d",
      }}
      id="exampleModalCenter"
      tabindex="-1"
      role="dialog"
      aria-labelledby="exampleModalCenterTitle"
    >
      <div class="modal-dialog modal-dialog-centered" role="document" style={{ width: "500px" }}>
        <div class="modal-content">
          <div
            class="modal-header"
            style={{ borderBottom: "0", padding: "6px 11px 0 0px" }}
          >
            <h5 class="modal-title" id="exampleModalLongTitle"></h5>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              onClick={onCloseDelete}
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" style={{ borderBottom: "0", padding: "0" }}>
            <p className="areyousure">
              Are you sure you want to remove {selectedCounts.length} number{" "}
              <br /> of contacts from your saved list
            </p>

            <div className="d-flex flex-row justify-content-center">
              <div className="mr-5">
                <button
                  type="submit"
                  onClick={onDeleteSuccess}
                  className="yesno"
                >
                  Yes
                </button>
              </div>
              <div>
                <button type="submit" onClick={onCloseDelete} className="no">
                  No
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
