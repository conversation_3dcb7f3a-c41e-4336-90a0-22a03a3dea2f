import React, { useState } from 'react';
import { PostWithTokenNoCache } from '../../customer/common-files/ApiCalls';
import { ApiName } from '../../customer/common-files/ApiNames';
import S from '../assets/css/layouts/admin-header.module.css';

import ReactSelect from 'react-select';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useNavigate } from 'react-router-dom';
import AdminHeader from '../layouts/AdminHeader';

const CreatePackegeDetails = () => {

	const navigate = useNavigate();

	const [accessDB, setaccessDB] = useState('');
	const [customFilter, setCustomFilter] = useState('');
	const [dataFields, setdataFields] = useState('');
	const [exportData, setexportData] = useState('');
	const [expiryDate, setExpiryDate] = useState('');
	const [numberOfContactViews, setNumberOfContactViews] = useState('');
	const [numberOfContactUsers, setNumberOfContactUsers] = useState('');
	const [price, setPrice] = useState('');
	const [packegeName, setPackegeName] = useState('');
	const [verifyEmailPhone, setVerifyEmailPhone] = useState('');

	// Calculate the minimum date (e.g., today's date)
	const today = new Date();
	const minDate = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
	const signoutHandler = async () => {

		return new Promise((resolve) => {
			sessionStorage.clear();
			resolve();
			navigate('/');
		});

	}

	const handleSave = async () => {

		// Perform validation checks before saving
		if (!price || !numberOfContactViews || !accessDB || !customFilter || !dataFields || !exportData || !verifyEmailPhone || !packegeName) {
			// If any required field is missing, show an error message or take appropriate action
			alert('Error: All fields are required');
			return;
		}
		try {
			console.log(accessDB, customFilter, dataFields, exportData, numberOfContactViews, numberOfContactUsers, price, packegeName);

			const params = {
				"price": price,
				"no_of_contact_views": numberOfContactViews,
				"export": exportData,
				"no_of_users": numberOfContactUsers,
				"verified_email_phone": verifyEmailPhone,
				"access_database": accessDB,
				"custom_filter": customFilter,
				"data_fields": dataFields,
				"package_name": packegeName


			};
			const res = await PostWithTokenNoCache(ApiName.createPackegeDetails, params);

			if (res && 'status' in res && res.status === 200) {
				navigate('/admin/package-details')
			} else {
				alert('Already exists')
				// Handle error response
				console.log('Error:', res.error); // Access the error message
			}
		} catch (error) {
			// Handle any other errors
			console.log('Error:', error);
		}
	};

	return (
		<div className={S.app}>
			 <AdminHeader />
			<div className={S.main_container}>
				<nav className={S.left_navbar} style={{width: "300px"}}>
					<button onClick={(e) => navigate('/admin/free-trial-users')}><span><img src="../images/freetrial.png"  className="mr-3"/></span>Free trial users</button>
					<button onClick={(e) => navigate('/admin/paid-users')}><span><img src="../images/paidusers.png"  className="mr-3"/></span>Paid Users</button>
					<button onClick={(e) => navigate('/admin/usage-details')}><span><img src="../images/usericon.png" className="mr-3"/></span>Usage Details</button>
					<button onClick={(e) => navigate('/admin/promocode')}><span><img src="../images/promocodeicon.png" className="mr-3"/></span>Promocode</button>
					<button onClick={(e) => navigate('/admin/package-details')}><span><img src="../images/doller.png" className="mr-3"/></span>Package Details</button>
				</nav>
				<div className={S.content}>
					{/* Main content */}
				</div>
				<aside className={S.right_sidebar}>

					<div className={S.formlayout}>
						<div className={S.headname}>
							<h2>Create Package Details!</h2>
						</div>

						<form>
						<div className="row">
							<div className="col-md-6">
							<div>
								<label>Access Database</label>
								<select className="form-control mb-2"
									value={accessDB}
									onChange={(e) => setaccessDB(e.target.value)}
								>
									<option value="">Select</option>
									<option value="Yes">Yes</option>
									<option value="No">No</option>
								</select>
							</div>
							</div>
							<div className="col-md-6">
							<div>
								<label>Custom Filter</label>
								<select className="form-control mb-2"
									value={customFilter}
									onChange={(e) => setCustomFilter(e.target.value)}
								>
									<option value="">Select</option>
									<option value="Yes">Yes</option>
									<option value="No">No</option>
								</select>
							</div>
							</div>
							<div className="col-md-6">
							<div>
								<label>Data Fields</label>
								<input className="form-control mb-2"
									type="number"
									value={dataFields}
									onChange={(e) => setdataFields(e.target.value)}
								/>
							</div>
							</div>
							<div className="col-md-6">
							<div>
								<label>export</label>
								<input className="form-control mb-2"
									type="text"
									value={exportData}
									onChange={(e) => setexportData(e.target.value)}
								/>
							</div>
							</div>
							<div className="col-md-6">
							<div>
								<label>Number of Contact Views</label>
								<input className="form-control mb-2"
									type="text"
									value={numberOfContactViews}
									onChange={(e) => setNumberOfContactViews(e.target.value)}

								/>
							</div>
							</div>
							<div className="col-md-6">
							<div>
								<label>Number of Users</label>
								<input className="form-control mb-2"
									type="text"
									value={numberOfContactUsers}
									onChange={(e) => setNumberOfContactUsers(e.target.value)}

								/>
							</div>
							</div>
							<div className="col-md-6">
							<div>
								<label>Verify Email or Phone</label>
								<select className="form-control mb-2"
									value={verifyEmailPhone}
									onChange={(e) => setVerifyEmailPhone(e.target.value)}
								>
									<option value="">Select</option>
									<option value="yes">Yes</option>
									<option value="no">No</option>
								</select>
							</div>
							</div>

							<div className="col-md-6">
							<div>
								<label>Price</label>
								<input className="form-control mb-2"
									type="text"
									value={price}
									onChange={(e) => setPrice(e.target.value)}

								/>
							</div>
							</div>

							<div className="col-md-6">
							<div>
								<label>Package Name</label>
								<input className="form-control mb-2"
									type="text"
									value={packegeName}
									onChange={(e) => setPackegeName(e.target.value)}

								/>
							</div>
							</div>
						</div>
							

							
							
							
							
							
							


							
							
							<div className="mt-4">
								<button className={S.clickandsave} type="button" onClick={handleSave}>
									Save
								</button>
							</div>
						</form>
					</div>


				</aside>
			</div>
		</div>
	);
};

export default CreatePackegeDetails;