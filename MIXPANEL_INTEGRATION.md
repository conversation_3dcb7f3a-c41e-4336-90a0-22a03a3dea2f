# Mixpanel Integration Documentation

This document provides a comprehensive guide to the Mixpanel integration and code fixes implemented in the ReachStream application.

## Table of Contents
1. [Overview](#overview)
2. [Files Created](#files-created)
3. [Files Modified](#files-modified)
4. [Tracking Features](#tracking-features)
5. [Consent Management](#consent-management)
6. [Error Fixes](#error-fixes)
7. [Tracking Prevention Handling](#tracking-prevention-handling)
8. [Usage Guide](#usage-guide)
8. [Related Documentation](#related-documentation)

## Overview

Mixpanel was integrated into the application to track user behavior, with a focus on privacy compliance and fixing several React-related errors. The integration includes:

- Privacy-first tracking approach with user consent
- Fix for React console errors
- Improved API error handling
- Comprehensive analytics capabilities

## Files Created

### 1. `src/components/CookieConsent.js`

A new component that displays a GDPR-compliant cookie consent banner.

```javascript
import React, { useState, useEffect } from 'react';
import Mixpanel from '../utils/mixpanel';

const CookieConsent = () => {
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice
    const hasConsent = localStorage.getItem('tracking_consent');
    if (!hasConsent) {
      setShowBanner(true);
    }
  }, []);

  const acceptTracking = () => {
    localStorage.setItem('tracking_consent', 'accepted');
    Mixpanel.optIn();
    setShowBanner(false);
  };

  const declineTracking = () => {
    localStorage.setItem('tracking_consent', 'declined');
    Mixpanel.optOut();
    setShowBanner(false);
  };

  // ... render banner UI ...
};
```

**Purpose:**
- Displays a consent banner to users who haven't made a tracking choice
- Provides options to accept or decline tracking
- Stores user preference in localStorage under 'tracking_consent'
- Triggers Mixpanel's opt-in/opt-out functionality based on user choice

### 2. `src/utils/analyticsTracking.js`

A utility file with common tracking functions for the application.

```javascript
import Mixpanel from './mixpanel';

// Common analytics events for the application
const Analytics = {
  // User events
  trackSignUp: (method, userInfo = {}) => {
    Mixpanel.track('User Sign Up', {
      method,
      ...userInfo
    });
  },
  
  // ... other tracking functions ...
  
  // Feature usage events
  trackFeatureUsage: (featureName, properties = {}) => {
    Mixpanel.track(`Feature Used: ${featureName}`, properties);
  },
  
  // ... other event tracking functions ...
};

export default Analytics;
```

**Purpose:**
- Centralizes tracking functions
- Provides a standardized way to track common events
- Makes it easier to maintain tracking logic

## Files Modified

### 1. `src/utils/mixpanel.js`

Modified to add enhanced privacy settings and tracking control.

**Before:**
```javascript
import mixpanel from 'mixpanel-browser';

// Initialize with project token
mixpanel.init('getUserMetrics', {
  debug: process.env.NODE_ENV === 'development',
  track_pageview: true,
  persistence: 'localStorage'
});

// Utility functions for tracking
const Mixpanel = {
  identify: (id) => {
    mixpanel.identify(id);
  },
  // ... other functions ...
  track: (name, props) => {
    mixpanel.track(name, props);
  },
  // ... more functions ...
};

export default Mixpanel;
```

**After:**
```javascript
import mixpanel from 'mixpanel-browser';

// Initialize with project token
mixpanel.init('getUserMetrics', {
  debug: process.env.NODE_ENV === 'development',
  track_pageview: true,
  persistence: 'localStorage',
  respect_dnt: true,
  opt_out_tracking_by_default: false,
  cookie_domain: window.location.hostname,
  secure_cookie: true,
  cross_site_cookie: false
});

// Utility functions for tracking
const Mixpanel = {
  identify: (id) => {
    mixpanel.identify(id);
  },
  // ... other functions ...
  track: (name, props) => {
    if (!mixpanel.has_opted_out_tracking()) {
      mixpanel.track(name, props);
    }
  },
  // ... modified functions with opt-out checks ...
  optIn: () => {
    mixpanel.opt_in_tracking();
    return true;
  },
  optOut: () => {
    mixpanel.opt_out_tracking();
    return true;
  }
};

export default Mixpanel;
```

**Changes Made:**
- Added `respect_dnt: true` to respect browser's Do Not Track setting
- Added `opt_out_tracking_by_default: false` for default opt-in tracking
- Added `cookie_domain: window.location.hostname` to set first-party cookies
- Added `secure_cookie: true` for enhanced security
- Added `cross_site_cookie: false` to avoid cross-site tracking prevention
- Added checks before tracking calls with `if (!mixpanel.has_opted_out_tracking())`
- Added opt-in/opt-out functions for user consent management

### 2. `src/App.js`

Modified to include the cookie consent component and conditional tracking.

**Before:**
```javascript
import React, { useEffect } from 'react';
import Router from './customer/common-files/Router.js';
import Mixpanel from './utils/mixpanel';

function App(){
  useEffect(() => {
    // Initialize Mixpanel tracking when app loads
    Mixpanel.track('App Initialized');
  }, []);

  return(
    <div>
        <Router />
    </div>
  );
}

export default App;
```

**After:**
```javascript
import React, { useEffect } from 'react';
import Router from './customer/common-files/Router.js';
import Mixpanel from './utils/mixpanel';
import CookieConsent from './components/CookieConsent';

function App(){
  useEffect(() => {
    // Only track app initialization if user has consented
    const hasConsent = localStorage.getItem('tracking_consent');
    if (hasConsent === 'accepted') {
      Mixpanel.track('App Initialized');
    }
  }, []);

  return(
    <div>
        <Router />
        <CookieConsent />
    </div>
  );
}

export default App;
```

**Changes Made:**
- Added import for `CookieConsent` component
- Added conditional tracking based on user consent
- Added `CookieConsent` component to the render tree

### 3. `src/customer/common-files/Router.js`

Fixed React child function warning.

**Before:**
```javascript
return (
  <UserProvider>
    <DashboardContext.Provider value={{ dataDC, setDataDC }}>
      {() => handleParse(JSON.parse(localStorage.getItem('user')))}
      <BrowserRouter>
        <AppWithTracker>
          <Routes>
            // ... routes ...
          </Routes>
        </AppWithTracker>
      </BrowserRouter>
    </DashboardContext.Provider>
  </UserProvider>
)
```

**After:**
```javascript
const parseUserData = () => {
  const storedUser = JSON.parse(localStorage.getItem('user'));
  if (storedUser) {
    handleParse(storedUser);
  }
};

// Call the parse function in useEffect
useEffect(() => {
  parseUserData();
}, []);

return (
  <UserProvider>
    <DashboardContext.Provider value={{ dataDC, setDataDC }}>
      <BrowserRouter>
        <AppWithTracker>
          <Routes>
            // ... routes ...
          </Routes>
        </AppWithTracker>
      </BrowserRouter>
    </DashboardContext.Provider>
  </UserProvider>
)
```

**Changes Made:**
- Removed the function call inside JSX (which caused the React child function warning)
- Created a separate function `parseUserData` to handle the parsing
- Added a useEffect hook to call the function when the component mounts

### 4. `src/customer/filters/Dashboard.js`

Fixed invalid DOM property warning.

**Before:**
```javascript
<img
  src="../../images/demo-image.png"
  class="img-fluid"
  alt="Responsive image"
  style={{ display: "none" }}
/>
```

**After:**
```javascript
<img
  src="../../images/demo-image.png"
  className="img-fluid"
  alt="Responsive image"
  style={{ display: "none" }}
/>
```

**Changes Made:**
- Changed `class` to `className` as required by React

### 5. `src/customer/common-files/useGlobalState.js`

Fixed deprecated Zustand storage API warning.

**Before:**
```javascript
const UseTabStore = create(
  persist(
    (set, get) => ({
      // ... state and actions ...
    }),
    {
      name: 'tab-store',
      getStorage: () => localStorage,
      serialize: (state) => lzString.compress(JSON.stringify(state)),
      deserialize: (str) => {
        try {
          return JSON.parse(lzString.decompress(str)) || initialState;
        } catch (error) {
          console.error('Failed to deserialize state:', error);
          return initialState;
        }
      },
      // ... other options ...
    }
  )
);
```

**After:**
```javascript
// Custom storage with compression
const customStorage = createJSONStorage(() => ({
  getItem: (name) => {
    const value = localStorage.getItem(name);
    return value ? lzString.decompress(value) : null;
  },
  setItem: (name, value) => {
    if (value && value.length > 4 * 1024 * 1024) {
      console.warn('Trying to store oversized data, skipping');
      return;
    }
    localStorage.setItem(name, lzString.compress(value));
  },
  removeItem: (name) => localStorage.removeItem(name)
}));

const UseTabStore = create(
  persist(
    (set, get) => ({
      // ... state and actions ...
    }),
    {
      name: 'tab-store',
      storage: customStorage,
      // ... other options ...
    }
  )
);
```

**Changes Made:**
- Imported `createJSONStorage` from Zustand middleware
- Created a custom storage implementation with compression
- Replaced deprecated options with the `storage` option

### 6. `src/customer/common-files/ApiCalls.js`

Improved error handling for profile picture fetching.

**Before:**
```javascript
export const fetchProfilePicture = async (url, data) => {
  let user = getSessionItem("user");
  let token = null;
  user = user ? JSON.parse(user) : null;
  if (user && "token" in user) {
    token = user.token;
  }

  let response = null;
  let myHeaders = new Headers();
  myHeaders.append("Authorization", `Bearer ${token}`);

  let requestOptions = {
    method: "POST",
    headers: myHeaders,
    redirect: "follow",
  };

  let res = await fetch(url, requestOptions)
    .then((response) => {
      if (response.ok) {
        return response.blob();
      } else {
        if (response.status === 401) {
          localStorage.removeItem('user');
          sessionStorage.clear();
        }
        return null;
      }
    })
    .then((data) => {
      if (data) {
        const imageURL = URL.createObjectURL(data);
        return imageURL;
      }
      return null;
    })
    .catch((error) => {
      return null;
    });

  return res;
};
```

**After:**
```javascript
export const fetchProfilePicture = async (url, data) => {
  let user = JSON.parse(localStorage.getItem("user"));
  let token = null;
  
  if (!user || !("token" in user)) {
    // Return null if user is not authenticated
    console.warn("User not authenticated for profile picture fetch");
    return null;
  }
  
  token = user.token;

  let myHeaders = new Headers();
  myHeaders.append("Authorization", `Bearer ${token}`);
  myHeaders.append("Content-Type", "application/json");

  let requestOptions = {
    method: "POST",
    headers: myHeaders,
    redirect: "follow",
  };

  try {
    const response = await fetch(url, requestOptions);
    
    // Check for various error conditions
    if (!response.ok) {
      // If unauthorized, clear user data
      if (response.status === 401) {
        localStorage.removeItem('user');
        sessionStorage.clear();
      }
      
      // For 400 errors, just log it - the profile picture is non-essential
      if (response.status === 400) {
        console.warn("Profile picture fetch returned 400 - user may not have a profile picture");
      }
      
      return null;
    }
    
    // Process successful response
    const blob = await response.blob();
    if (blob) {
      const imageURL = URL.createObjectURL(blob);
      return imageURL;
    }
    
    return null;
  } catch (error) {
    console.warn("Error fetching profile picture:", error);
    return null;
  }
};
```

**Changes Made:**
- Improved user authentication check
- Added proper Content-Type header
- Used try/catch with async/await for better error handling
- Added specific handling for 400 errors
- Added detailed warning logs

## Tracking Features

The integration includes several tracking features:

### 1. Page View Tracking

Added in `src/customer/common-files/Router.js`:

```javascript
const RouteChangeTracker = () => {
  const location = useLocation();
  
  useEffect(() => {
    // Track page view when location changes
    Mixpanel.pageView(location.pathname);
  }, [location]);
  
  return null;
};
```

### 2. User Identification

Added in `src/customer/register/SignIn.js`:

```javascript
Mixpanel.identify(userdata?.email);
Mixpanel.people.set({
  '$email': userdata?.email,
  '$last_login': new Date(),
  'Role': 'User'
});
```

### 3. Event Tracking

Added in various components, for example in `src/customer/register/SignIn.js`:

```javascript
// In onSubmitHandler
Mixpanel.track('Sign In Attempt', { email: email });

// In handleGoogleLogin
Mixpanel.track('Google Login Attempt');

// In microSoftAuth
Mixpanel.track('Microsoft Login Attempt');

// In linkedInLogin
Mixpanel.track('LinkedIn Login Attempt');
```

### 4. Feature Usage Tracking

Added in `src/customer/filters/Dashboard.js`:

```javascript
// Track user visit to dashboard
Analytics.trackFeatureUsage('Dashboard View', { user_email: user.email });

// Track app tour initiation
Analytics.trackFeatureUsage('App Tour Started', { tour_id: record[0].id });

// Track usage data fetch
Analytics.trackFeatureUsage('Usage Data Fetched', { 
  items_count: dataArray.length 
});
```

## Consent Management

The consent management flow works as follows:

1. **First Visit**: User sees the cookie consent banner
2. **User Decision**:
   - If accepted:
     - Sets `tracking_consent=accepted` in localStorage
     - Calls `Mixpanel.optIn()` to enable tracking
   - If declined:
     - Sets `tracking_consent=declined` in localStorage
     - Calls `Mixpanel.optOut()` to disable tracking
3. **Subsequent Visits**:
   - Banner not shown if decision was already made
   - Tracking respects previous decision

4. **Tracking Checks**:
   - All tracking functions check for opt-out status before tracking
   - App initialization tracking only happens if consent is given

## Error Fixes

Several React errors were fixed:

### 1. Function as React Child

**Error**: "Functions are not valid as a React child"
**Fix**: Removed function call from JSX and used useEffect instead

### 2. Invalid DOM Property

**Error**: "Invalid DOM property `class`. Did you mean `className`?"
**Fix**: Changed `class` to `className` in the `img` element

### 3. Deprecated Storage API

**Error**: "`getStorage`, `serialize` and `deserialize` options are deprecated"
**Fix**: Used the newer `storage` option with custom implementation

### 4. API 400 Error

**Error**: "POST https://stg-rs-fe.reachstream.com/api/AAA/v1/user/profile/picture 400 (Bad Request)"
**Fix**: Improved error handling in the `fetchProfilePicture` function

## Tracking Prevention Handling

We've implemented several improvements to address issues with browsers that have tracking prevention features:

1. **Storage Fallbacks**: When localStorage/cookies are inaccessible, we now use a memory-based fallback storage system.

```javascript
// Memory-based fallback storage
const memoryStore = {
  events: [],
  userId: null,
  userProperties: {},
  consentGiven: false
};
```

2. **Robust Error Handling**: All tracking calls are now wrapped in try/catch blocks to prevent application crashes.

```javascript
try {
  mixpanel.track(event_name, properties);
} catch (error) {
  console.warn(`Error tracking event ${event_name}:`, error);
}
```

3. **Tracking Availability Detection**: We now check if tracking is available before attempting to use it.

```javascript
const isTrackingAvailable = () => {
  try {
    const testKey = '__mixpanel_test__';
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);
    return true;
  } catch (e) {
    console.warn('Tracking prevention detected: localStorage not accessible');
    return false;
  }
};
```

4. **Enhanced Cookie Consent**: The CookieConsent component now handles tracking prevention scenarios and informs users about their privacy settings.

5. **Privacy-First Configuration**: Mixpanel is configured to respect user privacy settings.

```javascript
mixpanel.init(MIXPANEL_TOKEN, {
  debug: process.env.NODE_ENV !== 'production',
  persistence: 'localStorage',
  cookie_domain: window.location.hostname,
  secure_cookie: true,
  ip: false,
  ignore_dnt: false
});
```

6. **Fallback Tracking System**: We've implemented a complete fallback tracking mechanism that works even when browser privacy features block cookies and localStorage.

```javascript
// Fallback tracking implementation
const FallbackTracking = {
  // Flag to indicate if this is a fallback
  isFallback: true,
  
  // Track an event
  track: function(eventName, properties = {}) {
    if (!this.consentGiven) return;
    
    try {
      // Add event to memory store
      memoryStore.events.push({
        event: eventName,
        properties: properties,
        timestamp: new Date().toISOString()
      });
      
      // Log event for debugging
      if (process.env.NODE_ENV !== 'production') {
        console.log('[Fallback Tracking]', eventName, properties);
      }
    } catch (error) {
      console.warn('Error in fallback tracking:', error);
    }
  },
  
  // Other tracking methods...
};
```

7. **Seamless Tracking API**: The Analytics utility automatically uses the appropriate tracking method (Mixpanel or fallback) based on browser capabilities.

```javascript
// Example of how the tracking system switches between regular and fallback tracking
track: function(event_name, properties = {}, callback) {
  try {
    if (this.usingFallback) {
      FallbackTracking.track(event_name, properties);
    } else {
      mixpanel.track(event_name, properties, callback);
    }
  } catch (error) {
    // Try fallback if main tracking fails
    try {
      if (!this.usingFallback) {
        FallbackTracking.track(event_name, properties);
      }
    } catch (fallbackError) {
      console.warn('Fallback tracking also failed:', fallbackError);
    }
  }
}
```

8. **Debugging Tools**: Added methods to check if fallback tracking is being used and to retrieve events stored in the fallback system.

```javascript
// Check if using fallback
const usingFallback = Analytics.isUsingFallback();

// Get fallback events for debugging
const fallbackEvents = Analytics.getFallbackEvents();
```

These improvements ensure that our tracking system works reliably across different browsers and respects user privacy settings, while still providing valuable analytics data.

## Usage Guide

### Tracking Events

To track events in your components:

```javascript
import Mixpanel from '../../utils/mixpanel';

// Track a simple event
Mixpanel.track('Button Clicked');

// Track an event with properties
Mixpanel.track('Form Submitted', {
  formType: 'contact',
  fieldsCompleted: 5
});
```

### Using the Analytics Utility

For common tracking patterns:

```javascript
import Analytics from '../../utils/analyticsTracking';

// Track feature usage
Analytics.trackFeatureUsage('Search Filter', {
  filterType: 'advanced',
  criteriaCount: 3
});

// Track user sign-up
Analytics.trackSignUp('email', {
  userType: 'business',
  referral: 'direct'
});
```

### Identifying Users

After successful authentication:

```javascript
// Set user identity
Mixpanel.identify(user.email);

// Set user properties
Mixpanel.people.set({
  '$email': user.email,
  '$name': `${user.firstName} ${user.lastName}`,
  'Plan': user.planType,
  'Company': user.company
});
```

### Page View Tracking

Page views are automatically tracked using the `RouteChangeTracker` component in the Router.

## Related Documentation

For detailed information about customer journey tracking implementation, please refer to the [Customer Journey Tracking Documentation](./CUSTOMER_JOURNEY_TRACKING.md). 