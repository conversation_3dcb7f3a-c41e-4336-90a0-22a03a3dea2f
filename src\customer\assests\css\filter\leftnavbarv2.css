@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    /* Regular */
}


.new-sidebar {
    width: 50px;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    background: linear-gradient(0deg, rgba(245, 245, 245, 1) 0%, rgba(255, 255, 255, 1) 50%);
    box-shadow: 3px 0 2px rgba(0, 0, 0, 0.1);
}

.main {
    /* background-color: #fff; */
    padding: 8px 6px 8px 0;
    min-height: 50px;
    margin: 0px 0 0 3rem;
    /* box-shadow: inset 0px 4px 7px 0px #00000029; */
    /* border: 1px solid #00000029; */
}


.main-header {
    position: fixed;
    top: 0;
    right: 0;
    left: 50px;
    z-index: 1000;
    background-color: #fff;
    padding: 8px 5px;
    box-shadow: inset 0 2px 2px rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid #00000029;
    margin: 0 0 0 2px;
}


/* .sidebar-divider {
    width: 30px;
    border-top: 1px solid #dee2e6;
    margin: 6px 0;
} */

.animated-btn {
    position: relative;
    background-color: #115EA3;
    color: white;
    overflow: hidden;
    border: none;
    z-index: 1;
    padding: 6px 20px 5px 20px;
    font-weight: 500;
    margin: 0 20px 0 0px;
    border-radius: 24px;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;

}

.animated-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -150%;
    width: 200%;
    height: 100%;
    background: linear-gradient(80deg,
            transparent 30%,
            rgba(255, 255, 255, 0.6) 50%,
            transparent 70%);
    animation: slide-diagonal-glow 2s linear infinite;
    z-index: 2;
    pointer-events: none;
}

@keyframes slide-diagonal-glow {
    0% {
        left: -150%;
    }

    100% {
        left: 150%;
    }
}


.profile-dropdown-container {
    position: relative;
}

.profile-menu {
    position: absolute;
    top: 45px;
    right: 0;
    width: 220px;
    background: #fff;
    border-radius: 12px;
    padding: 0;
    z-index: 999;
    animation: fadeInDrop 0.3s ease;
    box-shadow: 0 0 6px #8D8D8D61;
}

@keyframes fadeInDrop {
    0% {
        opacity: 0;
        transform: translateY(-8px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.profile-header {
    margin: 10px 0 5px 0;
}


.profile-menu ul li {
    padding: 8px 8px 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #919191;
    cursor: pointer;
    transition: background 0.2s ease;
    font-size: 12px;
    border-bottom: 1px solid #c9c9c969;
}

.profile-menu ul li:hover {
    background-color: #C9C9C9;
    /* border-radius: 6px; */
}

.profile-menu .highlight {
    background-color: #115EA3;
    color: #fff;
    /* border-radius: 6px; */
}

.profile-menu .highlight:hover {
    background-color: #0051cc;
}

.sign-out {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #555;
    padding: 5px 15px 5px 15px;
    cursor: pointer;
    transition: background 0.2s ease;
    margin: 10px auto;
    border: 0;
    font-size: 14px;
    background-color: #fff;
    border-radius: 30px;
    box-shadow: rgba(0, 0, 0, 0.10) 0px 2px 5px 0px;
    outline: none;
}



p.user-name {
    font-size: 12px;
    margin: 0;
    font-weight: 600;
}

/* img.upload-pp {
    padding: 0px 0 0 0px;
    border-radius: 23px;
    box-shadow: 0 0 6px #8D8D8D40;
    margin: 10px 0 0 10px;
} */



.hover-icon:hover {
    transform: scale(1.2);
    color: #007bff !important;
    /* Bootstrap primary blue */
}

@keyframes fadeInDrop {
    0% {
        opacity: 0;
        transform: translateY(-8px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.unique-avatar-dropdown-wrapper {
    position: relative;
    display: inline-block;
}

.unique-avatar-dropdown-menu {
    position: absolute;
    top: 40px;
    right: -45px;
    z-index: 1000;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px 13px 15px 13px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-width: 250px;

    opacity: 0;
    pointer-events: none;
}

/* Add this class when dropdown is shown */
.unique-avatar-dropdown-wrapper.show .unique-avatar-dropdown-menu {
    animation: fadeInDrop 0.3s ease forwards;
    pointer-events: auto;
}

p.v2-account-credits {
    font-size: 12px;
    margin: 0 0 8px 0;
}

p.line {
    margin: 4px 10px 0 10px;
    font-size: 12px;
    color: #d7e4ff;
}

p.used-proggress {
    font-size: 12px;
    margin: 4px 0 10px 0;
}

p.calculated {
    font-size: 10px;
    margin: 15px 0 0 0;
    color: #8B8B8B;
}

p.calculated a {
    color: #0655FF;
    text-decoration: none;
}

.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    color: var(--bs-progress-bar-color);
    text-align: center;
    white-space: nowrap;
    transition: var(--bs-progress-bar-transition);
    background: rgb(7, 197, 209);
    background: linear-gradient(97deg, rgba(7, 197, 209, 1) 0%, rgba(0, 70, 179, 1) 55%);
}


/* Wrapper for each icon (optional if needed) */
.active-sidebar-icon {
    background: linear-gradient(270deg, rgba(141, 183, 251, 1) 0%, rgba(255, 255, 255, 1) 50%);
    border-radius: 8px;
}

.sidebar-icon-wrapper {
    width: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}





.sidebar-active-indicator {
    position: absolute;
    left: 37px;
    top: 3px;
    bottom: 0;
    width: 3px;
    background: #07C5D1;
    background: linear-gradient(180deg, rgba(7, 197, 209, 1) 0%, rgba(20, 110, 246, 1) 50%);
    border-radius: 6px 0px 0px 6px;
    height: 30px;
}


.icon-tooltip-wrapper {
    position: relative;
    cursor: pointer;
}



.icon-tooltip-wrapper:hover .tooltip-text {
    opacity: 1;
}

.hover-icon {
    transition: transform 0.2s ease;
}

.hover-icon:hover {
    transform: scale(1.1);
}


.icon-tooltip-wrapper img {
    /* fixed height */
    object-fit: contain;
    /* maintain aspect ratio */
    display: block;
}


.sidebar {
    /* width: 60px; */
    /* background: #ffffff; */
    /* display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 10px;
    height: 100vh; */
}

/* .icon-wrapper {
    position: relative;
    width: 53px;
    height: 35px;
    margin: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 0px;
    transition: background 0.3s;
} */



.icon-wrapper.active {
    background: linear-gradient(270deg, rgb(211 228 255) 0%, rgba(255, 255, 255, 1) 60% 60%);
    transition: background 0.5s;
}


/* .active-indicator {
    position: absolute;
    top: 0;
    right: 0px;
    width: 4px;
    height: 100%;
    background: #07C5D1;
    background: linear-gradient(180deg, rgba(7, 197, 209, 1) 0%, rgba(20, 110, 246, 1) 50%);
    border-radius: 5px;
    left: 52px;
} */

/* .sidebar-divider {
    width: 100%;
    height: 1px;
    background-color: #e3e3e3;
    margin: 8px 0;
    border: none;
} */





/* .tooltip-text {
    position: absolute;
    left: 58px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #fff;
    color: #000;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    z-index: 999;
    transition: opacity 0.2s ease, transform 0.3s ease;
    transform: translateY(-50%) scale(0.95);
    box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 8px;
} */

.icon-wrapper:hover .tooltip-text {
    opacity: 1;
    visibility: visible;
}

.icon-wrapper:hover .tooltip-text {
    transform: translateY(-50%) scale(1);
}

.sidebar,
.new-sidebar {
    overflow: visible !important;
    position: relative;
    z-index: 10;
}


.icon-wrapper {
    position: relative;
    width: 56px;
    height: 30px;
    margin: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 0px;
    transition: background 0.3s;
}

.sidebar-icon {
    /* width: 18px; */
    /* transition: transform 0.2s ease; */
}

/* .icon-wrapper:hover .sidebar-icon {
    transform: scale(1.1);
} */

.active-indicator {
    position: absolute;
    top: 0px;
    right: 0px;
    width: 3px;
    height: 30px;
    background: #07C5D1;
    background: linear-gradient(180deg, rgba(7, 197, 209, 1) 0%, rgba(20, 110, 246, 1) 50%);
    border-radius: 5px;
    left: 51px;
}

.sidebar-divider {
    width: 80%;
    margin: 12px auto;
    border: none;
    border-bottom: 1px solid #dcdcdc;
}

.tooltip-text {
    position: absolute;
    left: 60px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #fff;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 4px;
    white-space: nowrap;
    display: none;
    font-family: 'Poppins', sans-serif;


}

.icon-wrapper:hover .tooltip-text {
    display: block;
}


