import React, { useState } from "react";
import "../assests/css/layouts/enrich.css";

const EnrichCustomDropdown = ({ label, options, onSelect, selectedValue }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const handleSelect = (option) => {
    onSelect(option); // Call the passed onSelect function
    setIsOpen(false);
  };

  return (
    <div className="dropdown-container">
      <div className="dropdown-selected" onClick={() => setIsOpen(!isOpen)}>
        {selectedValue || label} {/* Show selectedValue if available, otherwise show label */}
        <span className="arrow">{isOpen ? "▲" : "▼"}</span>
      </div>

      {isOpen && (
        <div className="dropdown-options">
          {options.map((option, index) => (
            <div
              key={index}
              className="dropdown-option"
              onClick={() => handleSelect(option)}
            >
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EnrichCustomDropdown;