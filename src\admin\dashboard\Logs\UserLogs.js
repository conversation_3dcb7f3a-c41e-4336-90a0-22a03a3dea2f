import React, { useEffect, useState } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls";
import { ApiName } from "../../../customer/common-files/ApiNames";
import UseTabStore from "../../../customer/common-files/useGlobalState";
import loadingGif from '../../../customer/assests/waiting.gif';
import Alert from "../../../customer/common-files/alert.js";
import Pagination from "../../../customer/pagination/Pagination.js";

const UserLogs = () => {

    const [searchQuery, setSearchQuery] = useState("");
    const [loading, setLoading] = useState(true);
    const [totalPages, setToatalPages] = useState(0);
    const [totalCount, setTotalCount] = useState(0);
    const [data, setData] = useState([]);
    const itemsPerPage = 10; // You can adjust the number of items per page here
    const [currentPage, setCurrentPage] = useState(1);
    const {
        defaultAlert,
        defaultErrorMsg,
        setButtonType,
        setDefaultAlert,
        setDefaultErrorMsg,
    } = UseTabStore();

    useEffect(() => {
        getData();
    }, [currentPage, searchQuery]);

    const getData = async () => {
        try {
            let params = JSON.stringify({
                page: searchQuery ? 1 : currentPage,
                pageSize: searchQuery ? 100000 : itemsPerPage,
                sortBy: "createdAt",
                searchParams: {
                    user_id: 5444860,
                    action_type: "DOWNLOAD" // VIEW, DOWNLOAD,COUNT
                }
            });
            const res = await PostWithTokenNoCache(ApiName.fetchUserHistory, params);
            if (res && "status" in res) {
                if (res.status == 200) {
                    setLoading(false);
                    let response = res.data;
                    console.log(response);
                }
            }
        } catch (errors) {
            setLoading(false);
            setButtonType("error");
            setDefaultErrorMsg(errors?.response?.data?.message);
            setDefaultAlert(true);
        }
    };

    return (
        <>
            {!loading ? (
                <div className="p-3">
                    <div className="d-flex flex-row  justify-content-between">
                        <div>
                            <p className={S.UserInsightsparagraph}>Details of User Download Log</p>
                            <div className={S.underlinisights}></div>
                        </div>
                        <div>
                            <button type="button" className={S.DetailsOfUserback}>Back</button>
                        </div>
                    </div>
                    <div className="d-flex flex-row  justify-content-between mt-4">
                        <div>
                            <p className={S.Detailsouserdate}>27/02/2024</p>

                        </div>
                        <div>
                            <p className="detailsofusersavedlist"><span className={S.detailsofusercoontactlist}>Saved List:</span> Contact List 1</p>
                        </div>
                    </div>

                    <div className={S.detailsofuserisrtlayer}>
                        <div className="row mt-4">
                            <div className="col-md-4">
                                <p className={S.detailsofuserfilter}>Filter</p>
                            </div>
                            <div className="col-md-4">
                                <p className={S.detailsofuserfilter}>Filter Details </p>
                            </div>
                            <div className="col-md-4">
                                <p className={S.detailsofuserfilter}>Filter Specified</p>
                            </div>
                        </div>
                        <div className={S.detailsofusersecondlayer}>
                            <div className="d-flex flex-row justify-content-around mt-4">
                                <div>
                                    <p className={S.detailsofusercommunity}>Industry</p>
                                </div>
                                <div>
                                    <p className={S.detailsofusercommunity}>Industry</p>
                                    <p className={S.detailsofusercommunity}>Search Industry</p>
                                    <p className={S.detailsofusercommunity}>Enter 4 Digit SIC Code</p>
                                </div>
                                <div>
                                    <p className={S.detailsofusercommunity}>Director</p>
                                    <p className={S.detailsofusercommunity}>Product Manager</p>
                                    <p className={S.detailsofusercommunity}>Marketing</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="d-flex flex-row  justify-content-between mt-3">
                        <div>
                            <p className={S.UserInsightsparagraph}>Contacts Downloaded: <span style={{ color: "#000" }}>300</span></p>
                        </div>
                    </div>
                    <div className="detailsofusertablepadding">
                        <div className={S.detalsofuserscrollabe}>
                            <table className="your-refrel-lists">
                                <tbody>
                                    <tr className="table-headers-saved-list mt-2">
                                        <th style={{ padding: "0px 0px 0 18px" }}>Contact Name</th>
                                        <th>Job Title</th>
                                        <th>Email/Phone</th>
                                        <th>Company Name</th>
                                        <th>Location</th>
                                    </tr>
                                    <tr className="table-data-small">
                                        <td className={S.datagoesheresavedlist}>
                                            27/02/2024 - 10:00AM
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            Contact
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            <div className="d-flex flex-row">
                                                <div className="mr-2">
                                                    <img src="../../images/mail-unread.png" />Email
                                                </div>
                                                <div>
                                                    <img src="../../images/phone-alt.png" />Phone
                                                </div>
                                            </div>
                                        </td>
                                        <td className="" style={{ fontSize: "13px" }}>
                                            Electroninc And Design
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            United States, Masschusett, <br />Cambridge
                                        </td>
                                    </tr>

                                    <tr className="table-data-small">
                                        <td className={S.datagoesheresavedlist}>
                                            27/02/2024 - 10:00AM
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            Contact
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            <div className="d-flex flex-row">
                                                <div className="mr-2">
                                                    <img src="../../images/mail-unread.png" />Email
                                                </div>
                                                <div>
                                                    <img src="../../images/phone-alt.png" />Phone
                                                </div>
                                            </div>
                                        </td>
                                        <td className="" style={{ fontSize: "13px" }}>
                                            Electroninc And Design
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            United States, Masschusett, <br />Cambridge
                                        </td>
                                    </tr>
                                    <tr className="table-data-small">
                                        <td className={S.datagoesheresavedlist}>
                                            27/02/2024 - 10:00AM
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            Contact
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            <div className="d-flex flex-row">
                                                <div className="mr-2">
                                                    <img src="../../images/mail-unread.png" />Email
                                                </div>
                                                <div>
                                                    <img src="../../images/phone-alt.png" />Phone
                                                </div>
                                            </div>
                                        </td>
                                        <td className="" style={{ fontSize: "13px" }}>
                                            Electroninc And Design
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            United States, Masschusett, <br />Cambridge
                                        </td>
                                    </tr>
                                    <tr className="table-data-small">
                                        <td className={S.datagoesheresavedlist}>
                                            27/02/2024 - 10:00AM
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            Contact
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            <div className="d-flex flex-row">
                                                <div className="mr-2">
                                                    <img src="../../images/mail-unread.png" />Email
                                                </div>
                                                <div>
                                                    <img src="../../images/phone-alt.png" />Phone
                                                </div>
                                            </div>
                                        </td>
                                        <td className="" style={{ fontSize: "13px" }}>
                                            Electroninc And Design
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            United States, Masschusett, <br />Cambridge
                                        </td>
                                    </tr>
                                    <tr className="table-data-small">
                                        <td className={S.datagoesheresavedlist}>
                                            27/02/2024 - 10:00AM
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            Contact
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            <div className="d-flex flex-row">
                                                <div className="mr-2">
                                                    <img src="../../images/mail-unread.png" />Email
                                                </div>
                                                <div>
                                                    <img src="../../images/phone-alt.png" />Phone
                                                </div>
                                            </div>
                                        </td>
                                        <td className="" style={{ fontSize: "13px" }}>
                                            Electroninc And Design
                                        </td>
                                        <td style={{ fontSize: "13px" }} className="">
                                            United States, Masschusett, <br />Cambridge
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div className="d-flex flex-row justify-content-between mt-3">
                            <div>
                                <p className={S.insightsSearchResultFound}>Search Result Found<span className={S.insightscontacts}> 200 Contacts</span></p>
                            </div>
                            <div>
                                <p>Pagination goes here</p>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="d-flex flex-row justify-content-center mt-5" style={{ display: loading }}>
                    <img src={loadingGif} alt="Loading" className="loader" width="400" />
                </div>
            )}
            {searchQuery ? (
                ""
            ) : (
                <Pagination
                    className="pagination-bar"
                    currentPage={currentPage}
                    totalCount={totalPages}
                    pageSize={1}
                    onPageChange={(page) => {
                        setCurrentPage(page);
                    }}
                />
            )}
            {defaultAlert && defaultErrorMsg ? (
                <Alert data={defaultErrorMsg} />
            ) : (
                <></>
            )}
        </>
    )
}
export default UserLogs;

