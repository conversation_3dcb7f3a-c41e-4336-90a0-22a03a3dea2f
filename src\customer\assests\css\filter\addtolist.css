/* .maindivetobecenter {
    margin: auto;
    width: 600px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
} */

p.noflist {
    text-align: center;
    color: rgb(15, 59, 78);
    font-weight: bold;
    padding: 10px 0px 0px 4rem;
    font-size: 18px;
}

p.filtercontacts {
    text-align: center;
    font-size: 14px;
    margin: auto;
    padding: 0px 14rem 0px 18rem;
}

button.createnewListclone {
    text-align: center;
    background-color: #093D54;
    color: #fff;
    border: 0;
    outline: none;
    font-size: 16px;
    padding: 5px 25px 5px 25px;
    border-radius: 8px;
    cursor: pointer;
    margin: 26px 3px 3px 4rem;
}

.blankspace {
    background-color: #31313126;
    padding: 18px 0 14px 0;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    margin: 8px 0 0 0;
}

tr.addtolisttr {
    box-shadow: inset 0 0 9px #afafaf38, 0 0 0 0 #0c20350d;
    /* opacity: 0.3; */
}

thead.addtolisthead {
    background-color: #D5EEEE;
}

th.addltolistheadnames {
    font-size: 14px;
    padding: 10px 0 10px 10px;
}

tbody.addtolistbody {
    box-shadow: inset 0 0 9px #afafaf38, 0 0 0 0 #0c20350d;
    background-color: #fff;
}

td.addtolisttbody {
    font-size: 14px;
    padding: 10px 0 10px 10px;
    font-weight: 600;
}

button.savedlistDownloadbutton {
    background-color: #093D54;
    border: 0;
    font-size: 12px;
    color: #fff;
    padding: 2px 12px 2px 12px;
    border-radius: 2px;
    margin: 0 0px 0 0px;
    outline: none;
}

.savedlistverticalline {
    border-left: 1px solid #e2e2e2;
    padding: 0 0px 0 0px;
}

button.addtolisttopbutton {
    position: absolute;
    top: 0;
    margin: 2px 0 0 -3rem;
    font-size: 20px;
    outline: none;
    border: 0;
    padding: 6px 15px 6px 15px;
    background-color: #55C2C3;
    color: #fff;
    border-radius: 8px;
}


