
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap');
.body{
    background-color: #E8F7F7;
    height: auto;
}

.card1 {
    max-width: 30rem;
    border-radius: 32px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
    border: 0;
    margin: auto;
    height: 30rem;
    margin-top: 10%;
}



/* p.semi-header {
    text-align: center;
    font-size: 20px;
    font-weight: 500;
    color: #000;
    margin: 0;

} */

.card1 h3 {
    text-align: center;
    margin-top: 30px;
    color: #093D54;
    margin-bottom: 23px;
}

p.email-link {
    text-align: center;
}

p.email-link a {
    font-size: 19px;
    text-decoration: none;
    color: #55C2C3;
}

p.semi-header {
    text-align: center;
    font-size: 18px;
    margin-bottom: 8px;
    color: #000;
    padding: 0 28px 0 28px;
    font-weight: 600;
}

p.email-issue {
    text-align: center;
    font-size: 14px;
    margin-top: 16px;
}

p.email-issue a {
    color: #55C2C3;
    text-decoration: none;
}

p.activate-account {
    text-align: center;
    font-size: 14px;
    margin: 11px 0 30px 0px;
    color: #7E8C9C;
}

.receive-link p {
    text-align: center;
    margin-top: 24px;
    line-height: 30px;
    color: #707070;
}

.receive-link a {
    text-decoration: none;
    color: #55C2C3;
}

.card>hr {
    margin-right: 15%;
    margin-left: 15%;
    color: #********;
}

.payment-page-logo img {
    margin-bottom: 0%;
}