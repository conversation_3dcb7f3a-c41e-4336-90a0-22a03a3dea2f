@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap');

.form-box3 {
    /* max-width: 480px; */
    margin: 0px 17px 4px;
    padding: 0 25px 0 25px;
    background: #ffffff;
    border: 6px solid #fff;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
    border-radius: 19px;


}

.didnt {
    margin-top: 15px;
}

.form-box3 h3 {
    text-align: center;
    padding: 0 0 9px 0px;
    font-weight: 600;
    margin-top: 10%;
    color: #093D54;
}


/* span.email-address p {
    margin: 0px 0 10px 0px;
} */

span.password p {
    margin: 0 0 10px 0;
}

p.semi-header a {
    text-decoration: none;
    color: #55C2C3;
    line-height: 27px;
}

p.semi-header4 a {
    text-decoration: none;
    color: #55C2C3;
}

.saver-plane4 {
    margin-top: 7%;
}

p.semi-header a:hover {
    color: #55C2C3;
}

p.password-label {
    padding: 8px 0 0 0;
}

p.password-label a {
    text-decoration: none;
}

p.password-label a:hover {
    color: #007bff;
}



.verify-email-banner img {
    width: 500px;
    margin: 0 auto;
    display: block;
    padding-top: 5%;
    padding-bottom: 25%;
}



.bg-color {
    background-color: #E8F7F7;
    height: auto;
}


@media (min-width: 768px) {
    .offset-md-2 {
        margin-left: 12.666667%;
    }


    p.text-end1 {
        text-align: end;
        /* padding: 0 43px 0 10px; */
        margin-top: 10%;
    }


}

.inputs input {
    background-color: #fff;
    border: 1px solid #c9c9c9;
    height: 50px;
    outline: none;
    width: 35px;
}


p.text-end a {
    padding: 0 53px 0 3px;
}

height-100 {
    height: 100vh
}


input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0
}

.card-2 {
    background-color: #fff;
    padding: 10px;
    width: 350px;
    height: 100px;
    bottom: -50px;
    left: 20px;
    position: absolute;
    border-radius: 5px
}

.card-2 .content {
    margin-top: 50px
}



.form-control:focus {
    box-shadow: none;
    /* border: 2px solid red */
    background-color: #F1FFFF;
    outline: none;
}

.validate {
    border-radius: 20px;
    height: 40px;
    background-color: red;
    /* border: 1px solid red; */
    width: 140px
}


.resend-code.offset-md-2 span {
    font-size: 12px;
    color: #C2C8CE;
    padding: 0 0 0 16px;
}

.didnt p {
    text-align: center;
    color: #0C243C;
    font-size: 14px;
}


.spam-footer p {
    text-align: center;
    margin-top: 15px;
    font-size: 12px;
    color: #7E8C9C;
    margin-bottom: 30px;
    line-height: 30px;
}

p.text-end a {
    text-decoration: none;
    color: #55C2C3;
}


.didnt a {
    text-decoration: none;
    color: #55C2C3;
}


.extra {
    padding: 0 0 0 15px;
}


@media only screen and (max-width: 600px) {
    p.text-end1 {
        padding: 0 0 0 0px;
        margin-top: 23px;
        font-size: 17px;
    }

}


p.text-end1 a {
    text-decoration: none;
    color: #55C2C3;
}

.timing {
    padding-left: 1rem;
    margin: 0 0 0 14px;
}

.counter {
    font-size: 12px;
    color: #C2C8CE;
    margin-bottom: 6%;
    margin: 0 0 0 3px;
}

.resend-code {
    font-size: 12px;
    color: #C2C8CE;
    margin-bottom: 6%;
    padding-left: 1rem;
    margin: 0 0 0 16px;
    inline-size: 20rem;
}


@media only screen and (min-width: 280px) and (max-width: 767px) {

    .form-box3 {
        margin: 0;
        padding: 0;
    }

    .inputs input {
        width: 28px;
        padding: 0;
    }
}

p.semi-header4 {
    text-align: center;
    color: #000;
    font-size: 14px;
    font-weight: 500;
}


.verify-email-banner-1 {
    margin-top: 17%;
    margin-bottom: 28%;
}

.verify-your-email- {
    /* max-width: 500px; */
    margin: 0px 17px 4px;
    padding: 0 0px 0 21px;
    background: #ffffff;
    /* border: 6px solid #fff; */
    /* box-shadow: rgb(50 50 93 / 25%) 0px 6px 12px -2px, rgb(0 0 0 / 30%) 0px 3px 7px -3px; */
    border-radius: 19px;
    margin-top: 8%;
}

.disable-resend-action a {
    text-decoration: none;
    color: gray !important; /* Set to gray color */
    pointer-events: none !important; /* Disables click functionality */
    cursor: not-allowed !important; /* Changes the cursor to indicate it's not clickable */
  }