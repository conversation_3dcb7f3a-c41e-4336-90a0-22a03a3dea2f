.example {
    position: relative;
}

p.SearchbyFilters {
    position: absolute;
    top: 27%;
    left: 34%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
    color: #093D54;
    font-weight: bold;
    font-size: 20px;
    /* font-family: Quicksand, sans-serif; */
}

p.<PERSON>earch {
    position: absolute;
    top: 35%;
    left: 34%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 12px 19rem 0 20rem;
    color: #000;
    /* font-weight: bold; */
    font-size: 14px;
    /* font-family: Quicksand, sans-serif; */
    text-align: center;
}


button.nextbutton {
    position: absolute;
    top: 44%;
    left: 31%;
    border: 0;
    padding: 5px 25px 5px 25px;
    color: #fff;
    background-color: #093D54;
    border-radius: 10px;
    cursor: pointer;
    outline: none;
}

.example img {
    width: 100%;
}

p.skiptour {
    position: absolute;
    top: 23%;
    left: 35%;
    font-size: 14px;
    transform: translate(164%, -8%);
    color: #55C2C3;
    cursor: pointer;
}

/* demo 2 */

.exampletwo {
    position: relative;
}

p.skiptour2 {
    position: absolute;
    top: 10%;
    left: 34%;
    font-size: 14px;
    transform: translate(164%, -8%);
    color: #55C2C3;
    cursor: pointer;
}


p.sort {
    position: absolute;
    top: 13%;
    left: 34%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
    color: #093D54;
    font-weight: bold;
    font-size: 20px;
    /* font-family: Quicksand, sans-serif; */
}

p.Sortcontactdetails {
    position: absolute;
    top: 20%;
    left: 34%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 12px 19rem 0 20rem;
    color: #000;
    /* font-weight: bold; */
    font-size: 14px;
    /* font-family: Quicksand, sans-serif; */
    text-align: center;
}

.sortbuttons {
    position: absolute;
    top: 29%;
    left: 28%;
}


button.sortback {
    border-radius: 10px;
    padding: 4px 17px 4px 20px;
    border: 1px solid #093D54;
    background-color: #fff;
    color: #093D54;
    font-weight: 600;
    cursor: pointer;
    outline: none;
    margin: 0 20px 0 0px;
}

button.sortnext {
    border: 0;
    padding: 5px 20px 5px 20px;
    background-color: #093D54;
    color: #fff;
    border-radius: 10px;
    outline: none;
    cursor: pointer;
}

/* demo 3 */

.examplethree {
    position: relative;
}

p.skiptour3 {
    position: absolute;
    top: 16%;
    left: 36%;
    font-size: 14px;
    transform: translate(164%, -8%);
    color: #55C2C3;
    cursor: pointer;
}

p.select {
    position: absolute;
    top: 20%;
    left: 37%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
    color: #093D54;
    font-weight: bold;
    font-size: 20px;
    /* font-family: Quicksand, sans-serif; */
}

p.Youcanselect {
    position: absolute;
    top: 25%;
    left: 37%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 12px 20rem 0 21rem;
    color: #000;
    font-size: 14px;
    text-align: center;
}


.sortbuttonss {
    position: absolute;
    top: 32%;
    left: 31%;
}

/* demo 4 */

.examplefour {
    position: relative;
}

p.skiptour4 {
    position: absolute;
    top: 18%;
    left: 46%;
    font-size: 14px;
    transform: translate(164%, -8%);
    color: #55C2C3;
    cursor: pointer;
}

p.ADD {
    position: absolute;
    top: 22%;
    left: 45%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
    color: #093D54;
    font-weight: bold;
    font-size: 20px;
    /* font-family: Quicksand, sans-serif; */
}


p.UseAddtoList {
    position: absolute;
    top: 28%;
    left: 45%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 12px 14rem 0 15rem;
    color: #000;
    font-size: 14px;
    text-align: center;
}

.addtbuttonss2 {
    position: absolute;
    top: 37%;
    left: 39%;
}

.addtbuttonss4 {
    position: absolute;
    top: 37%;
    left: 39%;
}

/* demo 5 */

.examplefive {
    position: relative;
}

p.skiptour5 {
    position: absolute;
    top: 18%;
    left: 84%;
    font-size: 14px;
    transform: translate(164%, -8%);
    color: #55C2C3;
    cursor: pointer;

}

p.SaveandDownload {
    position: absolute;
    top: 21%;
    left: 83%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
    color: #093D54;
    font-weight: bold;
    font-size: 20px;
    /* font-family: Quicksand, sans-serif; */
}

p.Checkyoursavedlistshere {
    position: absolute;
    top: 27%;
    left: 74%;
    transform: translate(-50%, -50%);
    margin: 4px 0 0 8rem;
    /* padding: 18px 0rem 0px 5rem; */
    color: #000;
    font-size: 14px;
    text-align: center;
}

.addtbuttonss {
    position: absolute;
    top: 34%;
    left: 77%;
}


@media screen and (min-width: 1980px) {
    p.Sortcontactdetails {
        padding: 20px 28rem 0 27rem;
        font-size: 22px;
    }    
}



@media screen and (min-width: 1920px) {
    p.skiptour4 {
        font-size: 22px;
    }

    p.skiptour5{
        font-size: 22px;
    }
       
}
@media screen and (min-width: 1920px) {
    p.skiptour {
        font-size: 18px;
    }

    p.SearchbyFilters {
        font-size: 28px;
    }

    p.Youcansearch {
        font-size: 20px;
        padding: 0 27rem 0 27rem;
    }

    button.nextbutton {
        font-size: 26px;
        padding: 8px 35px 8px 35px;
    }
    

    p.skiptour2 {
        font-size: 20px;
    }

    p.UseAddtoList {
        font-size: 21px;
    }


    p.Sortcontactdetails {
        font-size: 22px;
    }

    p.sort {
        font-size: 30px;
    }

    button.sortback {
        font-size: 24px;
    }
    
    button.sortnext {
        font-size: 24px;
    }
    
    p.skiptour3 {
        font-size: 20px;
    }

    p.select {
        font-size: 28px;
    }

    p.Youcanselect {
        font-size: 22px;
        padding: 30px 27rem 0 27rem;
    }
    
    p.Checkyoursavedlistshere {
        font-size: 18px;
    }

    p.SaveandDownload {
        font-size: 25px;
    }
    
    
}

@media screen and (min-width: 1600px) {
    p.Sortcontactdetails {
        padding: 10px 27rem 0 27rem;
        font-size:20px;
    }

    .sortbuttons {
        position: absolute;
        top: 29%;
        left: 29%;
    }

    .sortbuttonss {
        margin: 0 0 0 10px;
    }

    .addtbuttonss2 {
        margin: 0 0 0 13px;
    }

    p.Checkyoursavedlistshere {
        margin: 0 0 0 11rem;
    }


    
  }