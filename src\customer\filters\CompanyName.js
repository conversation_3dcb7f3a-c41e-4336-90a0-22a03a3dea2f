import React, { useEffect, useState } from 'react';
import AsyncSelect from 'react-select/async';
import { ApiName } from '../common-files/ApiNames.js';
import { PostWithTokenNoCache, postWithToken } from '../common-files/ApiCalls.js';
import UseTabStore from '../common-files/useGlobalState.js';

const CompanyName = () => {
	// State to store selected values
	const {
		setSelectedCompany,
		companyName,
		companyURL,
		setSelectedCompanyURL,
		setCurrentPage,
		setIsFiltered
	} = UseTabStore();
	const [selectedValues, setSelectedValues] = useState([]);
	const [selectedCompanyURLValues, setSelectedCompanyURLValues] = useState([]);
	const [isCompanyNameAutoFocus, setIsCompanyNameAutoFocus] = useState(false); // Set one of them to autofocus initially
	const [isCompanyURLAutoFocus, setIsCompanyURLAutoFocus] = useState(false); // Set one of them to autofocus initially
	const [disableSelect1, setDisableSelect1] = useState(false);
	const [disableSelect2, setDisableSelect2] = useState(false);

	useEffect(() => {
		if (Object.keys(companyName).length < 1) {
			setDisableSelect2(false);
			setSelectedValues([]);
		} else {
			setDisableSelect2(true);
			const selectedValues = Object.keys(companyName).map(key => {
				return { label: companyName[key], value: companyName[key] };
			});
			setSelectedValues(selectedValues);
		}
	}, [companyName])

	useEffect(() => {
		if (Object.keys(companyURL).length < 1) {
			setDisableSelect1(false);
			setSelectedCompanyURLValues([]);
		} else {
			setDisableSelect1(true);
			const selectedValues = Object.keys(companyURL).map(key => {
				return { label: companyURL[key], value: companyURL[key] };
			});
			setSelectedCompanyURLValues(selectedValues);
		}
	}, [companyURL])

	const convertToProperCase = async (data) => {
		let properCaseData = [];
		if (data) {
			properCaseData = data.map(val => {
				// Trim the input string to remove leading and trailing whitespace
				val = val.trim();

				// Match words while preserving non-alphanumeric characters in between
				const words = val.match(/\b\w+\b/g);
				let result = "";
				let lastIndex = 0;

				words.forEach(word => {
					// Find the position of each word in the original string
					const start = val.indexOf(word, lastIndex);

					// Append special characters or spaces before the current word
					result += val.slice(lastIndex, start);

					// Capitalize the word
					result += word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();

					// Update lastIndex to the end of this word
					lastIndex = start + word.length;
				});

				// Append any remaining special characters or spaces after the last word
				result += val.slice(lastIndex);

				return result;
			});
		}
		return properCaseData;
	};


	const loadOptions = (inputValue, callback) => {
		if (inputValue && inputValue.length >= 2) {
			const dataPost = {
				company_company_name: inputValue // Pass the inputValue to your API request
			};
			// Make an API call here to fetch suggestions based on the inputValue
			PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
				.then(async (res) => {
					if (res.data.status === 200) {
						const company = JSON.parse(res.data.data);

						let properCaseCompany = await convertToProperCase(company);

						// Convert the array to a Set to remove duplicates and then convert it back to an array
						const uniqueCompany = [...new Set(properCaseCompany)];

						const options = uniqueCompany.map((option) => ({
							label: option,
							value: option
						}));
						callback(options);
					}
				})
				.catch((error) => {
					console.error('Error fetching data:', error);
					callback([]); // Clear options on error
				});
		}
	};

	const loadCompanyURLOptions = (inputValue, callback) => {
		// First check if input is empty or just URL prefixes
		if (!inputValue.trim() || /^(https?:\/\/)?(www\.)?$/i.test(inputValue)) {
			callback([]);
			return;
		}
	
		// Advanced URL sanitization
		const sanitizedInput = inputValue
			.replace(/^(https?:\/\/)?(www\.)?/i, '')  // Remove http://, https://, www.
			.replace(/\/.*$/, '')                     // Remove everything after first slash
			.replace(/^\.+|\.+$/g, '')               // Remove leading/trailing dots
			.trim();
		
			console.log("sanitizedInput",sanitizedInput);

		// Final check for valid domain (at least one dot or special cases like 'localhost')
		if (!sanitizedInput) {
			callback([]);
			return;
		}
	
		const dataPost = {
			company_website: sanitizedInput 
		};
		PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
			.then((res) => {
				if (res.data.status === 200) {
					const website = JSON.parse(res.data.data);
					const uniqueCompanyURL = [...new Set(website)];
	
					const options = uniqueCompanyURL.map((option) => ({
						label: option,
						value: option
					}));
					callback(options);
				}
			})
			.catch((error) => {
				console.error('Error fetching data:', error);
				callback([]);
			});
	};
	


	const SearchCompanyName = ({ autoFocus, onFocus, onBlur }) => {

		// Handle value change when an option is selected or deselected
		const handleValueChange = (selectedOptions) => {
			setDisableSelect2(true);
			setSelectedValues(selectedOptions);
			setIsFiltered(true);

			let updatedCompanyNames = {};

			// Update the company names based on selected options
			selectedOptions.forEach((item, index) => {
				updatedCompanyNames[index] = item.value;
			});
			setSelectedCompany(updatedCompanyNames);
		};

		return (
			<div className="mb-2 mt-3 d-flex flex-column">
				<div className="">
					<AsyncSelect
						cacheOptions
						defaultOptions
						loadOptions={loadOptions}
						isMulti
						placeholder="Search by Company Name"
						onChange={handleValueChange} // Handle selected value changes
						value={selectedValues} // Pass selected values
						styles={SearchJobtitleStyles}
						autoFocus={autoFocus}
						onFocus={onFocus}
						onBlur={onBlur}
						isClearable
						isDisabled={disableSelect1} // Use the isDisabled prop for disabling
						className={`async-select ${disableSelect1 ? 'disabled' : ''}`} // Add a custom class for styling
					/>
				</div>
				<div className="search-icon">
					<i className="fas fa-search"></i>
				</div>

			</div>
		)
	}

	const SearchCompanyURL = ({ autoFocus, onFocus, onBlur }) => {

		// Handle value change when an option is selected or deselected
		const handleCompanyURLValueChange = (selectedOptions) => {
			setDisableSelect1(true);
			setCurrentPage(1);
			setSelectedCompanyURLValues(selectedOptions);
			let updatedCompanyURL = [];

			// Update the company names based on selected options
			selectedOptions.forEach((item, index) => {
				updatedCompanyURL[index] = item.value;
			});
			setSelectedCompanyURL(updatedCompanyURL);
		};

		return (
			<div className="mb-2 d-flex flex-column">
				<div className="mt-2">

					<AsyncSelect
						cacheOptions
						defaultOptions
						loadOptions={loadCompanyURLOptions}
						isMulti
						placeholder="Search by Company URL"
						onChange={handleCompanyURLValueChange} // Handle selected value changes
						value={selectedCompanyURLValues} // Pass selected values
						styles={SearchJobtitleStyles}
						autoFocus={autoFocus}
						onFocus={onFocus}
						onBlur={onBlur}
						isClearable
						isDisabled={disableSelect2} // Use the isDisabled prop for disabling
						className={`async-select ${disableSelect2 ? 'disabled' : ''}`} // Add a custom class for styling
					/>

				</div>
				<div className="search-icon mt-2">
					<i className="fas fa-search"></i>
				</div>

			</div>
		)
	}


	const SearchJobtitleStyles = {
		control: (provided, state) => ({
			...provided,

			marginLeft: "26px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
			cursor: "text",

			":hover": {
				borderColor: "#000",
			},
			":focus": {
				border: "1px solid #000",
			},
		}),

		placeholder: (baseStyles, state) => ({
			...baseStyles,
			gridArea: "1/1/2/3",
			color: "#A3AEBB",
			marginLeft: "6px",
			fontSize: "14px",
			width: "187px"
		}),


		singleValue: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			backgroudColor: "red",
		}),

		multiValue: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			backgroundColor: "white",
			marginTop: "8px",
			border: "1px solid",
			fontSize: "70%",

		}),


		multiValueGeneric: (provided, state) => ({
			...provided,
			paddingLeft: "8",
			paddingTop: "10px",
			padding: "1px",
			marginTop: "12px",


		}),

		IndicatorsContainer: (provided, state) => ({
			...provided,
			visibility: "hidden",
			padding: "0"

		}),

		indicatorsContainer: (provided, state) => ({
			...provided,
			visibility: "hidden",
			padding: "0"
		}),

		loadingIndicator: (provided, state) => ({
			...provided,
			visibility: "hidden",
			width: "0",
			fontSize: "0"
		}),


		menuList: (provided, state) => ({
			...provided,
			"&:hover:": {
				color: "#55C2C3",
				marginTop: "-6px",
				marginBottom: "14px",
				backgroudColor: "transperent",
				zIndex: "1000",
			},

			"&:hover": {
				backgroundColor: "inherit",
			},
		}),

		menu: (provided, state) => ({
			...provided,

			border: "1px solid #093D54",
			marginLeft: "26px",
			marginRight: "2rem",
			fontSize: "14px",
			marginRight: "0px",
			position: "relative",
			width: "-webkit-fill-available",
			backgroudColor: "#fff",

			":hover": {
				color: "inherit",
				textDecoration: "none",
			},
		}),

		multiValueRemove: (base, state) => ({
			...base,
			backgroundColor: "#fff",
			setColor: "#000",
			borderRadius: "50%",
			borderRadius: "10px",
			paddingLeft: "-60",
			paddingRight: "0",
			innerHeight: "10px",
			border: "1px solid #08CEDC",
			marginTop: "-7px",
			marginBottom: "2px",
			font: "black !important",
			backgroudColor: "#ffffff",
			width: "11px",
			height: "11px",
			"&:hover": {
				backgroundColor: "#55C2C3",
				color: "black",
			},
		}),

		indicatorSeparator: (provided, state) => ({
			...provided,
			backgroundColor: "hsl(0deg 0% 100%)",
		}),

		MenuList: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#fff",
			outline: "none",
		}),



		valueContainer: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#000",
			outline: "none",
			padding: "0px 5px 0 0",
			width: "300px"
		}),
		container: (provided, state) => ({
			...provided,
			padding: "0px 11px 0 0",
		}),
		group: (provided, state) => ({
			...provided,
			width: "262px",
			// paddingLeft: '23px',
			marginLeft: "32px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
		}),

	};


	return (
		<div>
			<SearchCompanyName
				autoFocus={isCompanyNameAutoFocus}
				onFocus={() => setIsCompanyNameAutoFocus(true)}
				onBlur={() => setIsCompanyNameAutoFocus(false)}
			/>
			<SearchCompanyURL
				autoFocus={isCompanyURLAutoFocus}
				onFocus={() => setIsCompanyURLAutoFocus(false)}
				onBlur={() => setIsCompanyURLAutoFocus(false)}
			/>
		</div>
	)
}
export default CompanyName;