// Open or create an IndexedDB database
export const openDB = (dbName, storeName) => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(dbName, 1);
  
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains(storeName)) {
          db.createObjectStore(storeName, { keyPath: 'id' });
        }
      };
  
      request.onsuccess = () => resolve(request.result);
      request.onerror = (error) => reject(error);
    });
  };
  
  // Store data in bulk
  export const bulkStoreData = async (dbName, storeName, data) => {
    const db = await openDB(dbName, storeName);
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
  
      transaction.oncomplete = () => resolve();
      transaction.onerror = (event) => reject(event.target.error);
  
      data.forEach(item => {
        store.put(item);
      });
    });
  };
  
  // Get all data from IndexedDB
  export const getAllData = async (dbName, storeName) => {
    const db = await openDB(dbName, storeName);
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();
  
      request.onsuccess = (event) => resolve(event.target.result);
      request.onerror = (event) => reject(event.target.error);
    });
  };
  
  // Clear all data from a store
  export const clearStore = async (dbName, storeName) => {
    const db = await openDB(dbName, storeName);
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();
  
      request.onsuccess = () => resolve();
      request.onerror = (event) => reject(event.target.error);
    });
  };