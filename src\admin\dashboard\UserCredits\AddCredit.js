import React, { useEffect, useState } from 'react';
import { PostWithTokenNoCache } from '../../../customer/common-files/ApiCalls';
import { ApiName } from '../../../customer/common-files/ApiNames';
import S from '../../assets/css/layouts/admin-header.module.css';
import 'react-datepicker/dist/react-datepicker.css';
import { useNavigate } from 'react-router-dom';
import AdminHeader from '../../layouts/AdminHeader';
import LeftSideNav from '../../layouts/LeftSideNav';
import Alert from "../../../customer/common-files/alert.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import loadingGif from '../../../customer/assests/waiting.gif';

const AddCredit = () => {

    const navigate = useNavigate();
    const [success, setSuccess] = useState();
    const [failure, setFailure] = useState();
    const [message, setMessage] = useState();
    const [totalAssignedContactView, setTotalAssignedContactView] = useState(0);
    const [totalAssignedCredit, setTotalAssignedCredit] = useState(0);
    const [totalBalanceContactView, setTotalBalanceContactView] = useState(0);
    const [totalBalanceCredit, setTotalBalanceCredit] = useState(0);
    const [addCredit, setAddCredit] = useState(null);
    const [loading, setLoading] = useState(true);
    const {
        selectedEmail,
        defaultAlert,
        defaultErrorMsg,
        setButtonType,
        setDefaultAlert,
        setDefaultErrorMsg,
    } = UseTabStore();

    const refreshData = async (message) => {
        try {
            if (!selectedEmail || Object.keys(selectedEmail).length <= 0) {
                navigate('/admin/usage-details');
                return;
            }
            setLoading(true);
            const params = { "user_email": selectedEmail };
            const res = await PostWithTokenNoCache(ApiName.activeCreadits, params);

            if (res?.status === 200) {
                const dataObj = JSON.parse(res.data.data);
                const {
                    total_assigned_contact_view,
                    total_assigned_credit,
                    total_balance_contact_view,
                    total_balance_credit
                } = dataObj;

                total_assigned_contact_view && setTotalAssignedContactView(total_assigned_contact_view);
                total_assigned_credit && setTotalAssignedCredit(total_assigned_credit);
                total_balance_contact_view && setTotalBalanceContactView(total_balance_contact_view);
                total_balance_credit && setTotalBalanceCredit(total_balance_credit);

                setSuccess(true);
                setFailure();
                setAddCredit('');
                setMessage(message);
                setLoading(false);

            }
        } catch (error) {
            console.log(error);
            // Handle any errors
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            try {
                if (!selectedEmail || Object.keys(selectedEmail).length <= 0) {
                    navigate('/admin/usage-details');
                    return;
                }
                setLoading(true);
                const params = { "user_email": selectedEmail };
                const res = await PostWithTokenNoCache(ApiName.activeCreadits, params);

                if (res?.status === 200) {
                    const dataObj = JSON.parse(res.data.data);
                    const {
                        total_assigned_contact_view,
                        total_assigned_credit,
                        total_balance_contact_view,
                        total_balance_credit
                    } = dataObj;

                    total_assigned_contact_view && setTotalAssignedContactView(total_assigned_contact_view);
                    total_assigned_credit && setTotalAssignedCredit(total_assigned_credit);
                    total_balance_contact_view && setTotalBalanceContactView(total_balance_contact_view);
                    total_balance_credit && setTotalBalanceCredit(total_balance_credit);
                    setLoading(false);
                }
            } catch (error) {
                console.log(error);
                // Handle any errors
            }
        };

        fetchData();
    }, [selectedEmail]);



    const handleSave = async () => {
        setSuccess();
        setFailure();
        setMessage();

        // Display a confirmation dialog before proceeding with the action
        const confirmed = window.confirm(
            `Are you sure you want to add ${addCredit} credits to user ${selectedEmail} ?`
        );

        if (!confirmed) {
            // User canceled the delete operation
            return;
        }

        setMessage("Updating in progress..");
        // Perform validation checks before saving
        if (
            !totalBalanceCredit ||
            !totalBalanceContactView ||
            !selectedEmail ||
            !addCredit
        ) {
            // If any required field is missing, show an error message or take appropriate action
            setFailure(true);
            setMessage("All fields Required.!");
            return;
        }
        try {
            const timestamp = Date.now();
            const params = {
                total_balance_credit: parseInt(totalBalanceCredit) + parseInt(addCredit),
                total_balance_contact_view: totalBalanceContactView,
                additional_credit_assigned: addCredit,
                additional_credit_assigned_at: timestamp,
                user_email: selectedEmail
            };
            const res = await PostWithTokenNoCache(ApiName.updateCreadits, params);

            if (res && 'status' in res && res.status === 200) {
                refreshData(res.data.message);
            } else {
                setSuccess();
                setFailure(true);
                setMessage("Update Failed.!");
                // Handle error response
                console.log('Error:', res.error); // Access the error message
            }
        } catch (error) {
            setSuccess();
            setFailure(true);
            setMessage("Update Failed.!");
            // Handle any other errors
            console.log('Error:', error);
        }
    };

    return (
        <div className={S.app}>
            <AdminHeader />
            <div className={S.main_container}>
                <div className="d-flex flex-row">
                    <div style={{ width: "350px" }}>
                        <LeftSideNav />
                    </div>
                    {!loading ? (
                        <div style={{ width: "100%" }}>
                            <div className={S.content}>
                                {/* Main content */}
                            </div>
                            <aside className={S.right_sidebar} style={{ padding: "3rem 0 0 0" }}>

                                <div className={S.formlayout}>
                                    <div className={S.headname}>
                                        <h2>Add Credit!</h2>
                                    </div>

                                    <form>
                                        <div>
                                            <label>Email</label>
                                            <input className="form-control mb-2"
                                                type="text"
                                                value={selectedEmail}
                                                readOnly
                                            />
                                        </div>


                                        <div>
                                            <label>Total Balance Credit</label>
                                            <input className="form-control mb-2"
                                                type="text"
                                                value={totalBalanceCredit}
                                                readOnly
                                            />
                                        </div>
                                        <div>
                                            <label>Add credit</label>
                                            <input className="form-control mb-2"
                                                type="text"
                                                value={addCredit}
                                                onChange={(e) => setAddCredit(e.target.value)}
                                            />
                                        </div>
                                        <div className="mt-4">
                                            <button
                                                className={S.clickandsave}
                                                type="button"
                                                onClick={handleSave}
                                                disabled={message && !success && !failure}
                                            >
                                                Save
                                            </button>

                                            <br />
                                            <br />
                                            {success ? message ? <div className="alert alert-success">{message}</div> : null : null}
                                            {failure ? message ? <div className="alert alert-danger">{message}</div> : null : null}
                                            {!failure && !success ? message ? <div className="alert alert-primary">{message}</div> : null : null}
                                        </div>
                                    </form>
                                </div>
                            </aside>
                        </div>
                    ) : (
                        <div className="d-flex flex-row justify-content-center mt-5" style={{ display: loading }}>
                            <img src={loadingGif} alt="Loading" className="loader" width="400" />
                        </div>
                    )}
                </div>
                {defaultAlert && defaultErrorMsg ? (
                    <Alert data={defaultErrorMsg} />
                ) : (<></>)}

            </div>
        </div>
    );
};

export default AddCredit;