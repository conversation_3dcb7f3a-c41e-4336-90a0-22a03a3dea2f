import React, { useState, useContext, useEffect, useRef } from "react";
import {
  axiosPost,
  AxiosPostBearer,
  postWithToken,
  PostWithTokenNoCache,
} from "../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import { CSVLink } from "react-csv";
import UseTabStore from "../common-files/useGlobalState.js";
import Alert from "../common-files/alert.js";

const ContactModal = (props) => {
  let isLowViewCredits = props.isLowViewCredits;
  let isContactList = props.contactList ? true : false;
  // Get the current domain name
  const pathname = window.location.pathname.split('/').filter(Boolean);
  const csvLinkRef = useRef();
  const modalRef = useRef();
  const contactModalRef = useRef();
  const [downloadedRows, setDownloadedRows] = useState([]);
  const {
    revealedEmail,
    emailRevealedHistory,
    selectedContact,
    defaultAlert,
    viewModal,
    defaultErrorMsg,
    setSampleDownloadedIds,
    setDefaultError,
    setViewModal,
    selectedTab,
    setDefaultErrorMsg,
    setDefaultAlert,
    setSelectedContact,
    setContactModelId,
    setButtonType,
    setEmailRevealedHistory,
    setRevealedEmail
  } = UseTabStore();
  const headers = [
    { label: "Contact Name", key: "contact_name" },
    { label: "First Name", key: "contact_first_name" },
    { label: "Middle Name", key: "contact_middle_name" },
    { label: "Last Name", key: "contact_last_name" },
    { label: "Job Title", key: "contact_job_title_1" },
    { label: "Job Level", key: "contact_job_title_level_1" },
    { label: "Job Department Name", key: "contact_job_dept_name_1" },
    { label: "Job Function Name", key: "contact_job_function_name_1" },
    { label: "Email", key: "contact_email_1" },
    { label: "Phone Number", key: "contact_phone_1" },
    { label: "Company Name", key: "company_company_name" },
    { label: "Website", key: "company_website" },
    { label: "Company Phone Number", key: "company_phone_1" },
    { label: "Address", key: "company_address_street" },
    { label: "City", key: "company_address_city" },
    { label: "State", key: "company_address_state" },
    { label: "Zipcode", key: "company_address_zipcode" },
    { label: "Country", key: "company_address_country" },
    { label: "Company Employee Size", key: "company_employee_size" },
    { label: "Company Annual Revenue Amount", key: "company_annual_revenue_amount" },
    { label: "SIC Code", key: "company_sic_code" },
    { label: "Industry Category", key: "company_industries" },
    { label: "Contact LinkedIn", key: "contact_social_linkedin" },
    { label: "NPI Number", key: "npi_number" }

  ];

  const [showDownloadLink, setShowDownloadLink] = useState(false);
  const [viewRevealedEmail, setViewRevealedEmail] = useState(null);
  const [isRevealIdExist, setIsRevealIdExist] = useState(null);
  const [loading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isLowViewCredits && viewModal) {
      setDefaultErrorMsg("You have exceeded your view credits")
      setDefaultAlert(true);
      setButtonType("upgrade");
    } else {
      contactModalRef.current.click();
    }
  }, [])
  useEffect(() => {
    // Add event listeners when the component mounts
    document.addEventListener('mousedown', handleOutsideClick);
    document.addEventListener('keydown', handleEscKeyPress);

    // Remove event listeners when the component unmounts
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
      document.removeEventListener('keydown', handleEscKeyPress);
    };
  }, []);

  const emailVerifyHistory = async () => {
    try {
      await PostWithTokenNoCache(ApiName.emailVerifyHistory, {})
        .then(function (response) {
          if (response.data.status === 200) {
            const dataObj = JSON.parse(response.data.data);
            setEmailRevealedHistory(dataObj);
          }
        }).catch(function (errors) {
        });
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {

    let revealIdExist = emailRevealedHistory.filter(data => {
      return data.dataId === selectedContact.id;
    });
    setIsRevealIdExist(revealIdExist)
    setTimeout(() => {
      setIsLoading(false)
    }, 1)

  }, [emailRevealedHistory])


  const handleOutsideClick = (e) => {
    // Check if the click is outside the modal
    if (!e.target.closest('.modal')) {
      closeModal();
    }
  };

  const handleEscKeyPress = (e) => {
    // Check if the pressed key is ESC
    if (e.key === 'Escape') {
      closeModal();
    }
  };

  const convertToProperCase = (val) => {
    if (val) {
      // Trim the input to remove any leading or trailing whitespace
      val = val.trim();

      // Match words, keeping special characters in place
      const words = val.match(/\w+|\W+/g);

      // Capitalize the first letter of each alphanumeric word, preserving special characters
      const capitalizedWords = words.map(word =>
        /\w/.test(word) ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : word
      );

      // Join all parts back into a single string
      return capitalizedWords.join('');
    } else {
      return val;
    }
  };


  const closeModal = () => {
    setButtonType("");
    setSelectedContact(null);
    setViewModal(false);
    setContactModelId(null);
    modalRef.current.click();
  };

  const csvDownloadHandler = async () => {
    const addActionKey = {
      id: selectedContact ? selectedContact.id : null,
      action: "download",
      searchBy: selectedTab,
    }
    const urlSingleFetch = ApiName.fetchSingleRecord;
    try {
      const res = await PostWithTokenNoCache(urlSingleFetch, addActionKey);
      const responseStatus = JSON.parse(res.data.status);

      if (responseStatus === 200) {
        setDownloadedRows(JSON.parse(res.data.data));
        setShowDownloadLink(true);
        refreshGrayOut();
      } else {
        setDefaultErrorMsg("You have exceeded your download credits")
        setDefaultAlert(true);
        setButtonType("upgrade");
        setShowDownloadLink(false);
        modalRef.current.click();
      }
    } catch (error) {
      if (error.response.data.status == 402 && error.response.data.message == "Insufficient credit balance") {
        setDefaultErrorMsg("You have insufficient download credits")
        setDefaultAlert(true);
        setButtonType("upgrade");
        setShowDownloadLink(false);
        modalRef.current.click();
      } else {
        setDefaultErrorMsg("You have insufficient download credits")
        setDefaultAlert(true);
        setButtonType("upgrade");
        setShowDownloadLink(false);
        modalRef.current.click();
      }
    }
  }

  useEffect(() => {
    if (showDownloadLink) {

      csvLinkRef.current.link.click();
    } else {
      const hideDownloadButton = document.getElementById("hideDownload");
      if (hideDownloadButton) {
        hideDownloadButton.style.display = "none";
      }
    }
  }, [downloadedRows]);

  const refreshGrayOut = async () => {
    const user = JSON.parse(localStorage.getItem("user"));
    try {
      await PostWithTokenNoCache(ApiName.downloadedIds, {})
        .then(function (response) {
          if (response.data.status == 200) {
            const dataObj = JSON.parse(response.data.data);
            const dataArray = Object.values(dataObj);
            setSampleDownloadedIds(dataArray);
          }
        }).catch(function (errors) {
          setTimeout(() => {
            setDefaultError('');
          })
          setDefaultError('Failed to load counts, Try again.!')
        });
    } catch (error) {
      console.error(error);
    }
  };

  const openLinkedIn = () => {
    const linkedinUrl = /* 'https://www.linkedin.com/in/' + */ selectedContact.contact_social_linkedin;
    selectedContact && selectedContact.contact_social_linkedin && window.open(linkedinUrl, '_blank');
  }
  const openTwitter = () => {
    const linkedinUrl =/*  'https://twitter.com/' +  */selectedContact.contact_social_twitter;
    selectedContact && selectedContact.contact_social_twitter && window.open(linkedinUrl, '_blank');
  }
  const openFacebook = () => {
    const linkedinUrl = /* 'https://www.facebook.com/' + */ selectedContact.contact_social_facebook;
    selectedContact && selectedContact.contact_social_facebook && window.open(linkedinUrl, '_blank');
  }

  const revealMail = async (id) => {
    const params = {
      dataIds: [id],
      singleDownload: true
    };
    setRevealedEmail([...new Set([...revealedEmail, id])]);
    await PostWithTokenNoCache(ApiName.revealEmail, params)
      .then(function (response) {
        if (response.data.status === 200) {
          emailVerifyHistory();
        }
      })
      .catch(function (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors.response.data.message);
        setDefaultAlert(true);
      });
  }

  const viewMail = async (id) => {
    // Get the hidden input field by its id
    const hiddenInput = document.getElementById(`hiddenInput-${id}`);
    if (hiddenInput) {
      const textToCopy = hiddenInput.value;
      if (textToCopy) {
        try {
          // Use the Clipboard API for copying (modern approach)
          await navigator.clipboard.writeText(textToCopy);
          // console.log("Text copied to clipboard successfully!");
        } catch (err) {
          console.error("Failed to copy text: ", err);
        }
      } else {
        // console.warn("Hidden input does not contain a value.");
      }
    } else {
      // console.warn(`Element with ID hiddenInput-${id} not found.`);
    }
    viewRevealedEmail === id ? setViewRevealedEmail("") : setViewRevealedEmail(id);
    setTimeout(() => {
      setViewRevealedEmail("");
    }, 3000)
  };


  const closeEmail = async (id) => {
    console.log(id, viewRevealedEmail);
    viewRevealedEmail === id ? setViewRevealedEmail("") : setViewRevealedEmail(id);
  }

  const isDisabled = pathname[0] === 'contact-list';

  const disabledStyle = {
    pointerEvents: 'none',
    opacity: 0.5,
    cursor: 'not-allowed',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '0.5rem', // Spacing between text and icon
  };

  return (
    <>
      {viewModal && !defaultAlert ? (
        <>
          <button
            type="button"
            className="btn btn-info btn-md"
            data-toggle="modal"
            data-target="#exampleModalCenter"
            ref={contactModalRef}
            style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
            data-backdrop="true"
          ></button>
          <div
            className="modal fade show"
            id="exampleModalCenter"
            tabIndex="-1"
            role="dialog"
            aria-labelledby="exampleModalCenterTitle"
            aria-hidden="true"
          >
            <div className="modal-dialog modal-lg justify-content-center modal-dialog-centered " role="document">
              <div
                className="modal-content"
                style={{
                  backgroundImage: "url('../images/popup_banner_2.png')",
                  backgroundPosition: "center",
                  border: "0",
                  backgroundSize: "cover",
                  width: "fit-content",
                  backgroundRepeat: "no-repeat",
                  // width: "-webkit-fill-available",
                }}
              >
                <div className="modal-header1">
                  {isLowViewCredits ? <span className="ml-3 text-danger">You have exceeded your view credits</span> : ""}
                  <button
                    ref={modalRef}
                    type="button"
                    className="close"
                    data-dismiss="modal"
                    aria-label="Close"
                    onClick={closeModal}
                  >
                    <span aria-hidden="true">
                      <img src="../images/cancel.png" />
                    </span>
                    &nbsp;
                  </button>
                </div>
                <div className="dropdown-divider"></div>
                <div className="modal-body1">
                  <div className="back-color">
                    <div className="row">
                      <div className="d-image col-sm-3 pt-2 pr-0" style={{ padding: "0px 0 0 43PX" }}>
                        <img
                          src="../images/avetar.png"
                          className="img-fluid"
                          width="115px"
                        />
                      </div>

                      <div className="col-sm-9 pr-4 pl-0 ">
                        <div className="container">
                          <div className="row align-items-start">
                            <div className="col">
                              <label className="label-1">First Name</label>
                            </div>
                            <div className="col">
                              <label className="label-1">Middle Name</label>
                            </div>
                            <div className="col">
                              <label className="label-1">Last Name</label>
                            </div>
                          </div>
                          <div className="row align-items-center">
                            <div className="col">
                              <p className="Douglas">
                                {selectedContact
                                  ? convertToProperCase(
                                    selectedContact.contact_first_name ||
                                    (selectedContact.contact_name ? selectedContact.contact_name.split(" ")[0] : "")
                                  )
                                  : ""}
                              </p>
                            </div>
                            <div className="col">
                              <p className="Douglas">
                                {selectedContact
                                  ? convertToProperCase(
                                    selectedContact.contact_middle_name ||
                                    (selectedContact.contact_name && selectedContact.contact_name.split(" ").length > 2
                                      ? selectedContact.contact_name.split(" ")[1]
                                      : "")
                                  )
                                  : ""}
                              </p>
                            </div>
                            <div className="col">
                              <p className="Douglas">
                                {selectedContact
                                  ? convertToProperCase(
                                    selectedContact.contact_last_name ||
                                    (selectedContact.contact_name
                                      ? selectedContact.contact_name.split(" ").slice(-1)[0]
                                      : "")
                                  )
                                  : ""}
                              </p>
                            </div>
                          </div>
                          <div className="row align-items-end">
                            <div className="col">
                              <p className="hide">One of three columns</p>
                            </div>
                            <div className="col">
                              <p className="hide">One of three columns</p>
                            </div>
                            <div className="col">
                              <p className="hide">One of three columns</p>
                            </div>
                          </div>
                        </div>

                        <div className="container">
                          <div className="row">
                            <div className="col">
                              <label className="label-1">Job Title</label>
                              <p className="Douglas">
                                {selectedContact
                                  ? convertToProperCase(selectedContact.contact_job_title_1)
                                  : ""}
                              </p>
                            </div>
                            <div className="col order-5">
                              <label className="label-1">Job Function</label>
                              <p className="Douglas">
                                {selectedContact
                                  ? convertToProperCase(selectedContact.contact_job_function_name_1)
                                  : ""}
                              </p>
                            </div>
                            <div className="col order-1">
                              <label className="label-1">Job Department</label>
                              <p className="Douglas">
                                {selectedContact
                                  ? convertToProperCase(selectedContact.contact_job_dept_name_1)
                                  : ""}
                              </p>
                            </div>
                          </div>
                        </div>
                        <hr className="small-horizontal" />
                      </div>
                    </div>

                    <div className="row">
                      <div className="col-md-3">
                        <div className="d-flex justify-content-center">
                          <div className="social-media">
                            {selectedContact.contact_social_linkedin === null || selectedContact.contact_social_linkedin === "" || selectedContact.contact_social_linkedin === "--" || selectedContact.contact_social_linkedin === ",," ? (<img src="../images/linkedin.png" className="img-fluid no-cursor" style={{ opacity: "0.5" }}
                            />) : (<img src="../images/linkedin.png" className="img-fluid"
                              onClick={openLinkedIn} style={{ cursor: "pointer" }}
                            />)}

                          </div>
                          <div className="social-media">
                            {selectedContact.contact_social_twitter === null || selectedContact.contact_social_twitter === "" || selectedContact.contact_social_twitter === "--" || selectedContact.contact_social_twitter === ",," ? (<img src="../images/new-blue-twitter-img.png" className="img-fluid no-cursor" style={{ opacity: "0.6", width: "21px" }}

                            />) : (<img src="../images/twitter.png" className="img-fluid"
                              onClick={openTwitter} style={{ cursor: "pointer" }}
                            />)}

                          </div>
                          <div className="social-media">
                            {selectedContact.contact_social_facebook === null || selectedContact.contact_social_facebook === "" || selectedContact.contact_social_facebook === "--" || selectedContact.contact_social_facebook === ",," ? (<img src="../images/facebook.png" style={{ opacity: "0.5" }} className="img-fluid no-cursor"
                            />) : (<img src="../images/facebook.png" style={{ opacity: "0.5", cursor: "pointer" }} className="img-fluid"
                              onClick={openFacebook}
                            />)}

                          </div>
                        </div>
                        <div style={{ paddingLeft: "20px" }}>
                          <button className="btn btn-" onClick={csvDownloadHandler}>
                            Download
                          </button>
                        </div>
                        <div id="hideDownload">
                          <CSVLink
                            data={downloadedRows}
                            headers={headers}
                            ref={csvLinkRef}
                            filename="ReachSteam-Contacts.csv"
                          />
                        </div>
                      </div>

                      <div className="col-md-3 ml-1">
                        <div className="buisness">
                          <p>
                            <img src="../images/Buisness.png" />
                            <span className="owners">
                              {selectedContact
                                ? convertToProperCase(selectedContact.contact_job_role_name_1)
                                : ""}
                            </span>
                          </p>
                        </div>
                        <div className="buisness">
                          <p>
                            <img src="../images/location.png" />
                            <span className="owners">
                              {selectedContact
                                ? convertToProperCase(selectedContact.contact_address_country)
                                : ""}
                            </span>
                          </p>
                        </div>
                        <div className="buisness">
                          <p>
                            <img src="../images/npi.png" />
                            <span className="owners">
                              {selectedContact ? selectedContact.npi_number : ""}
                            </span>
                          </p>
                        </div>
                      </div>

                      <div className="col-md-4">

                        <div className="buisness">
                          <p>
                            <div className="d-flex flex-row">
                              <div className="m-0 p-0"><img src="../images/Envelope.png" alt="Envelope" /></div>
                              {!loading ? (
                                isRevealIdExist && isRevealIdExist.length < 1 && revealedEmail.includes(selectedContact?.id) ?
                                  (
                                    <button className="Verifyingemail-contact">
                                      <img
                                        src="./images/loading-path.gif"
                                        style={{
                                          padding: "0px 0px 0 0px",
                                          color: "#B7B7B7",
                                          width: "18px",
                                          position: "relative",
                                          top: "-1px",
                                          margin: "0 6px 0 0px",
                                        }}
                                      />
                                      Verifying email <span className="percentage"></span>
                                    </button>

                                  ) : isRevealIdExist.length === 0 ||
                                    isRevealIdExist[0].dataId !== selectedContact.id ? (
                                    <button
                                      disabled={isDisabled}
                                      type="button"
                                      className="RevealEmailmodel"
                                      onClick={() => revealMail(selectedContact.id)}
                                      style={isDisabled ? disabledStyle : {}}
                                    >
                                      Reveal Email
                                    </button>
                                  ) : isRevealIdExist[0].dataId === selectedContact.id &&
                                    isRevealIdExist[0].verifierStatus.toLowerCase() !== "valid" ? (
                                    <button
                                      type="button"
                                      className="dashboardEmailNotfound"
                                    >
                                      Email Not found
                                    </button>
                                  ) : isRevealIdExist[0].dataId === selectedContact.id &&
                                    (isRevealIdExist[0].verifierStatus.toLowerCase() !== "valid") ? (
                                    <>
                                      <input
                                        type="hidden"
                                        id={`hiddenInput-${selectedContact.id}`}
                                        value={isRevealIdExist[0].emailId}
                                      />
                                      {viewRevealedEmail === selectedContact.id ? (
                                        <div className="row">
                                          {/* <div className="col-5"></div> */}
                                          <div className="col-5">
                                            <p className="successmessage7">

                                              Copied Successfully
                                            </p>
                                          </div>
                                          <div className="col-3"></div>
                                        </div>
                                      ) : (
                                        <div
                                          className="d-flex flex-row"
                                        >
                                          <div>
                                            {/* <i className="fa fa-copy" style={{ color: "#55C2C3", fontSize: "14px", padding: "0 5px 0 6px" }}></i>&nbsp; */}
                                          </div>
                                          <div>
                                            <p className="dashboardemaill">
                                              <img style={{ cursor: "pointer" }} src="images/copytoclip.png" width="10" onClick={() => viewMail(selectedContact.id)} />
                                              &nbsp;
                                              {isRevealIdExist[0].emailId}
                                            </p>
                                          </div>
                                        </div>
                                      )}
                                    </>
                                  ) : isRevealIdExist[0].dataId === selectedContact.id &&
                                    isRevealIdExist[0].verifierStatus.toLowerCase() == "valid" ? (
                                    <>
                                      <input
                                        type="hidden"
                                        id={`hiddenInput-${selectedContact.id}`}
                                        value={isRevealIdExist[0].emailId}
                                      />
                                      {viewRevealedEmail === selectedContact.id ? (
                                        <div className="row">
                                          {/* <div className="col-5"></div> */}
                                          <div className="col-5">
                                            <p className="successmessage8">
                                              Copied Successfully
                                            </p>
                                          </div>
                                          <div className="col-3"></div>
                                        </div>
                                      ) : (
                                        <div
                                          className="d-flex flex-row"
                                        >
                                          <div>
                                            {/* <i className="fa fa-copy" style={{ color: "#55C2C3", fontSize: "14px", padding: "0 5px 0 6px" }}></i>&nbsp;&nbsp;&nbsp; */}
                                          </div>
                                          <div>
                                            <p className="dashboardemaill"> &nbsp;&nbsp;&nbsp;
                                              <img style={{ cursor: "pointer" }} onClick={() => viewMail(selectedContact.id)} src="images/copytoclip.png" width="10" />
                                              &nbsp;
                                              {isRevealIdExist[0].emailId}
                                            </p>
                                          </div>
                                        </div>
                                      )}
                                    </>
                                  ) : (
                                    <button
                                      disabled={isDisabled}
                                      type="button"
                                      className="RevealEmailmodel"
                                      onClick={() => revealMail(selectedContact.id)}
                                      style={isDisabled ? disabledStyle : {}}
                                    >
                                      Reveal Email
                                    </button>
                                  )
                              ) : (
                                <>Loading...</>
                              )}
                            </div>


                          </p>
                          <div className="buisness">
                            <p>
                              <img src="../images/phone-callc.png" />
                              <span className="owners">
                                {selectedContact ? selectedContact.contact_phone_1 : ""}
                              </span>
                            </p>
                          </div>
                        </div>


                      </div>
                    </div>
                  </div>

                  <div className="dropdown-divider"></div>

                  <div className="bottom-section">
                    <div className="detailing">
                      <h4>Company Details</h4>
                      <hr className="hr-detail" />
                    </div>

                    <div className="">
                      <div className="row mb-3">
                        <div className="col-6">
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Company Name</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_company_name)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Address</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_address_street)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">City</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_address_city)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">State</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_address_state)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Zipcode</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? selectedContact.company_address_zipcode
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Country</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_address_country)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="vl"></div>
                        <div className="col-5">
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Industry Category</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? convertToProperCase(selectedContact.company_industries)
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Website</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href={
                                  selectedContact
                                    ? selectedContact.company_website.startsWith("http")
                                      ? selectedContact.company_website
                                      : `https://${selectedContact.company_website}`
                                    : "#"
                                }
                                  target={selectedContact?.company_website ? "_blank" : ""}
                                  style={{ cursor: selectedContact?.company_website ? "pointer" : "" }}>
                                  {selectedContact
                                    ? selectedContact.company_website
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Company size</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? selectedContact.company_employee_size
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Revenue</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact
                                    ? selectedContact.company_annual_revenue_amount
                                    : ""}

                                </a>
                              </p>
                            </div>
                          </div>

                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">SIC Code</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact ? selectedContact.company_sic_code : ""}
                                </a>
                              </p>
                            </div>
                          </div>

                          <div className="d-flex flex-row">
                            <div>
                              <p className="company-name">
                                <a href="#!" className="no-cursor">Company Phone</a>
                              </p>
                            </div>
                            <div>
                              <p className="company-name1">
                                <a href="#!" className="no-cursor">
                                  {selectedContact ? selectedContact.company_phone_1 : ""}
                                </a>
                              </p>
                            </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <Alert data={defaultErrorMsg} />
        </>
      )}
    </>
  );
};

export default ContactModal;

export const updateUserCreadits = async (token, totalBalance, totalViewCredit) => {
  /* const requestBody = {
     total_balance_credit: totalBalance,
     total_balance_contact_view: totalViewCredit,
   };
 
   const response = AxiosPostBearer(ApiName.updateCreadits, requestBody, token)
     .then(function (response) {
       if (response.status == 200) {
         // alert('susccess');
       }
     })
     .catch(function (errors) {
       // console.log(errors);
     });*/
};
export const selectedRecordsIds = (selectedIds, token) => {
  //alert('selectedRecordsIds');

  const requestBody = {
    ids: selectedIds,
  };

  const response = AxiosPostBearer(ApiName.passingRecordIds, requestBody, token)
    .then(function (response) {
      if (response.status == 200) {
        //alert('susccess');
      }
    })
    .catch(function (errors) {
      // console.log(errors);
    });
};
