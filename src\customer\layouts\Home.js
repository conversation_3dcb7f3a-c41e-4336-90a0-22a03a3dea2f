import { useEffect, useState } from "react";
import "../assests/css/layouts/homev2.css";
import { PostWithTokenNoCache, AxiosPostBearer } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";
import loadingGif from "../assests/waiting.gif";
import Analytics from "../../utils/analyticsTracking";
import { useNavigate } from "react-router-dom";
import Header from "./Header";

const Home = () => {
  const navigate = useNavigate();
  const [userCredits, setUserCredits] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [contactData, setContactData] = useState({
    firstName: null,
    lastName: null,
    email: null,
    token: null,
  });
  const [userMembership, setUserMembership] = useState(
    localStorage.getItem("userMembership") || "Free"
  );
  const [lastCalculated, setLastCalculated] = useState(
    new Date().toLocaleString()
  );

  // Format credit values consistently
  const formatCreditValue = (value) => {
    if (value === null || value === undefined) return "0";
    if (typeof value === "number") return value.toLocaleString("en-IN");
    if (typeof value === "string" && value.toLowerCase() === "unlimited")
      return "Unlimited";
    return value;
  };

  // Fetch user data from API if not in localStorage
  const fetchUserData = async () => {
    try {
      const localUser = JSON.parse(localStorage.getItem("user"));
      if (localUser && localUser.firstName) {
        setUser(localUser);
        return;
      }

      const response = await AxiosPostBearer(
        ApiName.userData,
        { method: "GET" },
        localUser?.token
      );

      if (response?.data?.status === 200) {
        const jsonData = JSON.parse(response.data.data);
        const mergedUser = { ...jsonData, ...localUser };
        setUser(mergedUser);
        setContactData(prev => ({ ...prev, ...mergedUser }));
        localStorage.setItem("user", JSON.stringify(mergedUser));
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      Analytics.track("User Data Fetch Error", {
        error_message: error?.response?.data?.message || "Unknown error",
      });
    }
  };

  // Fetch user credits
  const fetchUserCredits = async () => {
    try {
      const res = await PostWithTokenNoCache(ApiName.activeCreadits, {});

      if (res?.data?.status === 200 && res.data.data) {
        const creditData = JSON.parse(res.data.data);
        setUserCredits(creditData);
      }
    } catch (error) {
      console.error("Error fetching credits:", error);
      Analytics.track("Credits Fetch Error", {
        error_message: error?.response?.data?.message || "Unknown error",
      });
    }
  };

  // Load all required data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      await fetchUserData();
      await fetchUserCredits();
      setIsLoading(false);
    };
    loadData();
  }, []);

  const gotoSearch = () => {
    navigate("/search")
  }

  const learnMore = () => {
    navigate("/search")
  };

  const getUserDisplayName = () => {
    // First check user.firstName, then contactData.firstName as fallback
    if (user?.firstName && user.firstName !== null) {
      return user.firstName + (user.lastName ? " " + user.lastName : "");
    }
    if (contactData?.firstName && contactData.firstName !== null) {
      return (
        contactData.firstName +
        (contactData.lastName ? " " + contactData.lastName : "")
      );
    }
    return "User";
  };

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center mt-5">
        <img src={loadingGif} alt="Loading" width="400" />
      </div>
    );
  }

  return (
    <>
      <Header />
      <div className="homev2-wrapper">
        <div className="unlock">
          <div className="d-flex flex-row justify-content-between">
            <div>
              <p className="worldwide">
                Unlock 150M+ High Potential Prospects Worldwide!
              </p>
              <p className="prospecting">
                Access verified B2B contact and company data to streamline your
                prospecting and close deals faster.
              </p>
            </div>
            <div>
              <button
                type="button"
                className="home-learn-more"
                onClick={() => {
                  learnMore()
                }}
              >
                learn more
              </button>
            </div>
          </div>
        </div>

        <div className="row">
          <div className="col-md-3">
            <div className="welcome-to-home-page-1">
              <p className="home-name">
                Welcome <br /> {getUserDisplayName()}
              </p>

              <div>
                <p className="ur-plan">Your Plan</p>
                <p className="free-plan">{userMembership}</p>
                <p className="sept">
                  <span className="renewal-plan">Renewal plan</span>
                </p>
              </div>

              <hr className="horizontal-plan" />

              <div className="accounts-credits-plan">
                <p className="account-credits-plan">Account credits</p>

                <div className="d-flex flex-row">
                  <div>
                    <img
                      src="images/accounts-credits-download.png"
                      className="img-fluid"
                      width="12"
                      alt="download-icon"
                    />
                  </div>
                  <div>
                    <p className="line">|</p>
                  </div>
                  <div>
                    <p className="used-proggress">
                      {userCredits
                        ? `${formatCreditValue(
                          userCredits.total_assigned_credit -
                          userCredits.total_balance_credit
                        )} used of ${formatCreditValue(
                          userCredits.total_assigned_credit
                        )}`
                        : "Loading..."}
                    </p>
                  </div>
                </div>

                <div className="progress" style={{ height: "5px" }}>
                  <div
                    className="progress-bar"
                    role="progressbar"
                    style={{
                      width: userCredits
                        ? `${((userCredits.total_assigned_credit -
                          userCredits.total_balance_credit) /
                          userCredits.total_assigned_credit) *
                        100
                        }%`
                        : "0%",
                    }}
                    aria-valuenow={
                      userCredits ? userCredits.total_balance_credit : 0
                    }
                    aria-valuemin="0"
                    aria-valuemax={
                      userCredits ? userCredits.total_assigned_credit : 100
                    }
                  ></div>
                </div>

                <div className="d-flex flex-row mt-3">
                  <div>
                    <img
                      src="images/account-credits-view-icon.png"
                      className="img-fluid"
                      width="12"
                      alt="view-icon"
                    />
                  </div>
                  <div>
                    <p className="line">|</p>
                  </div>
                  <div>
                    <p className="used-proggress">
                      {userCredits
                        ? userCredits.user_plan_name === "freemium"
                          ? `${formatCreditValue(
                            userCredits.total_assigned_email_credits -
                            userCredits.total_balance_email_credits
                          )} used of ${formatCreditValue(
                            userCredits.total_assigned_email_credits
                          )}`
                          : "Unlimited views"
                        : "Loading..."}
                    </p>
                  </div>
                </div>

                <div className="progress mb-2" style={{ height: "5px" }}>
                  <div
                    className="progress-bar"
                    role="progressbar"
                    style={{
                      width:
                        userCredits && userCredits.user_plan_name === "freemium"
                          ? `${((userCredits.total_assigned_email_credits -
                            userCredits.total_balance_email_credits) /
                            userCredits.total_assigned_email_credits) *
                          100
                          }%`
                          : "100%",
                    }}
                  ></div>
                </div>

                <p className="calculated">
                  Last calculated: {lastCalculated}{" "}
                  <span onClick={fetchUserCredits}>
                    <a href="">Refresh</a>
                  </span>
                </p>
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="welcome-to-home-page-1">
              <p className="explore-plans">Explore Your Prospects</p>
              <img
                src="images/top-penguin.png"
                className="img-fluid d-block mx-auto"
                alt="ReachStream mascot"
              />
              <button
                type="button"
                className="start-your-search-plan"
                onClick={() => {
                  gotoSearch()
                }}
              >
                Start Your Search
              </button>
            </div>
          </div>

          <div className="col-md-3">
            <div className="welcome-to-home-page-1">
              <p className="suggeted-plan">Suggested for you</p>

              <div className="d-flex flex-row">
                <div>
                  <img
                    src="images/Freemium-facebook.png"
                    className="img-fluid"
                    alt="Chrome extension"
                  />
                </div>
                <div>
                  <p className="install-plan">Install our Chrome Extension</p>
                  <p className="install-plan-2">
                    Work with Reachstream across the web
                  </p>
                </div>
              </div>
            </div>

            <div className="welcome-to-home-page-2">
              <div className="d-flex flex-row justify-content-end">
                <img
                  src="images/pricing-cancel.png"
                  className="img-fluid"
                  width="10"
                  alt="Close"
                />
              </div>
              <div className="d-flex flex-row">
                <div>
                  <img
                    src="images/Freemium-facebook.png"
                    className="img-fluid"
                    width="25"
                    alt="Feedback"
                  />
                </div>
                <div>
                  <p className="feedback-plan">
                    {getUserDisplayName()}, is Reachstream <br /> working well for
                    you?
                  </p>
                </div>
              </div>

              <div className="d-flex flex-row justify-content-center mt-2">
                <div>
                  <button
                    className="yes-home"
                    onClick={() => {
                      Analytics.track("Feedback Provided", {
                        response: "positive",
                      });
                    }}
                  >
                    Yes!
                  </button>
                </div>
                <div>
                  <button
                    className="yes-home"
                    onClick={() => {
                      Analytics.track("Feedback Provided", {
                        response: "negative",
                      });
                    }}
                  >
                    Not Really
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Home;