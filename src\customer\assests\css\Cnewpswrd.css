@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap");

.form-box4 {
  max-width: 500px;
  margin: 0px 17px 4px;
  padding: 0 25px 0 25px;
  background: #ffffff;
  border: 6px solid #fff;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px,
    rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
  border-radius: 19px;
}

p.semi-header3 {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}

.form-box4 h3 {
  text-align: center;
  padding: 0 0 9px 0px;
  font-weight: 600;
  margin-top: 10%;
  color: #093d54;
}

/* span.email-address p {
     margin: 0px 0 10px 0px;
 } */

span.password p {
  margin: 0 0 10px 0;
}

p.semi-header {
  text-align: center;
  font-size: 16px;
}

p.semi-header a {
  text-decoration: none;
  color: #55c2c3;
  line-height: 27px;
}

p.semi-header a:hover {
  color: #55c2c3;
}

p.password-label {
  padding: 8px 0 0 0;
}

p.password-label a {
  text-decoration: none;
}

p.password-label a:hover {
  color: #007bff;
}

.crate-new-passwrd-banner img {
  width: 500px;
  margin: 0 auto;
  display: block;
  padding-top: 6%;
  padding-bottom: 20%;
}

.saver-plane3 {
  margin-top: 11%;
}

.bg-color {
  background-color: #e8f7f7;
  height: auto;
}

@media (min-width: 768px) {
  .offset-md-2 {
    margin-left: 12.666667%;
  }

  p.text-end a {
    padding: 0 26px 0 0px;
  }
}

@media only screen and (max-width: 653px) {
  input.cp-pluss1 {
    padding: 0;
  }

  input.cp-pluss1.cust-disabled {
    margin: 0 auto;
    display: block;
    padding: 10px 12px;
  }
}

span.Shift input {
  margin-top: 35px;
}

p.text-end a {
  padding: 0 53px 0 3px;
}

height-100 {
  height: 100vh;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.resend-code.offset-md-2 span {
  font-size: 12px;
  color: #c2c8ce;
  padding: 0 0 0 16px;
}

p.text-end {
  float: right;
  margin-top: 11%;
}

p.text-end a {
  text-decoration: none;
  color: #55c2c3;
}

.didnt a {
  text-decoration: none;
  color: #55c2c3;
}

span.err {
  font-size: 12px;
  color: #7e8c9c;
}

span.error {
  font-size: 12px;
  color: #de350b;
}

label.n-password {
  color: #000;
  font-family: Lato;
  font-weight: 500;
}

.fa-eye {
  /* position: absolute;SS */
  right: 10px;
  top: 15px;
  font-size: 15px;
  cursor: pointer;
}

form.crnpwrsd {
  margin-top: 26px;
  /* margin-bottom: 25px; */
}

/* input.form-control {
    padding: 8px 0 8px 0px;
} */

input.cp-pluss1 {
  margin-top: 44px;
  margin-bottom: 22px;
}

.invalid-input-text {
  /* border: 2px solid gray;*/
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid red;
  border-radius: 5px;
  padding: 0 0px 0px 12px;

  background-color: #fff9f9;
}

.text-end a {
  text-decoration: none;
  color: #55c2c3;
}

.text-end p {
  text-align: end;
}

.text-end {
  max-width: 500px;
  /* margin: 0px 17px 4px; */
  padding: 0 11px 0 25px;
  background: #ffffff;
  /* border: 6px solid #fff; */
  /* box-shadow: rgb(50 50 93 / 25%) 0px 6px 12px -2px, rgb(0 0 0 / 30%) 0px 3px 7px -3px; */
  border-radius: 19px;
  margin-top: 11%;
}

.crate-new-passwrd-banner {
  margin-top: 7%;
  margin-bottom: 7%;
}

span.resend {
  color: #55c2c3;
  padding: 0 0 0 3px;
}