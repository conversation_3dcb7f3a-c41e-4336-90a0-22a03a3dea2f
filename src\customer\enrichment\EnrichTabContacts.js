import <PERSON> from 'papaparse';
import { useEffect, useRef, useState } from 'react';
import { BsChevronDown, BsDownload } from 'react-icons/bs';
import { Link, useNavigate } from 'react-router-dom';
import penguineLoadingGif from "../../customer/assests/waiting.gif";
import "../assests/css/layouts/enrichmnttabcontacts.css";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";
import CommonAlert from '../common-files/commonAlert.js';
import UseTabStore from '../common-files/useGlobalState.js';
import DeletePopup from '../list/DeletePopup.js';
import Pagination from '../pagination/Pagination.js';
import EnrichCustomDropdown from "./EnrichCustomDropdown.js";

const fieldOptions = ["First Name", "Last Name", "Job title", "Company", "ZipCode", "Phone", "Email Address", "Business Type"];
const companyFieldOptions = [
  "Company Name",
  "Website",
  "Company Size",
  "Address",
  "Phone Number",
  "SIC Code",
  "Industry",
  "Social Media URL"
];
const optionsData = [
  { id: 'emails', label: 'Emails', icon: 'images/open-envi.png' },
  { id: 'phones', label: 'Phones', icon: 'images/phone-envi.png' },
  { id: 'company', label: 'Company Attributes', icon: 'images/company-env.png' },
  { id: 'technology', label: 'Technology', icon: 'images/technology-env.png' },
];

const EnrichTabContacts = () => {
  const [activeTab, setActiveTab] = useState('contact');
  const [showCrmTab, setShowCrmTab] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [enrichmentData, setEnrichmentData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const pageSize = 10;

  // File upload states
  const [selectedFile, setSelectedFile] = useState(null);
  const [csvHeaders, setCsvHeaders] = useState([]);
  const [csvData, setCsvData] = useState([]);
  const [isDragging, setIsDragging] = useState(false);
  const [step, setStep] = useState(1);
  const [fieldMappings, setFieldMappings] = useState({});
  const [selectedOptions, setSelectedOptions] = useState(['emails']);
  const [apiStatus, setApiStatus] = useState({
    loading: false,
    success: false,
    error: false,
    message: ''
  });

  // Get user token from localStorage
  const userData = JSON.parse(localStorage.getItem('user'));
  const [deleteId, setDeleteId] = useState("");
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const fileInputRef = useRef(null);
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultErrorMsg,
    setDefaultAlert
  } = UseTabStore();

  const navigate = useNavigate();
  const checkEnrichmentData = async (tab) => {
    setIsLoading(true);
    try {
      let params = {
        page: currentPage,
        pageSize: pageSize,
        searchParams: {
          enrichmentType: tab.toUpperCase(),
        },
        sortBy: "desc"
      };

      const response = await PostWithTokenNoCache(ApiName.fetchAllEnrich, params);
      if (response?.status == 200 && response.data?.data) {
        let res = JSON.parse(response.data.data);
        if (res.enrichments?.records?.length > 0) {
          setEnrichmentData(res.enrichments.records);
          setTotalPages(res?.enrichments?.totalCount);
          setIsLoading(false);
        } else {
          setEnrichmentData([]);
          setTotalPages(res?.enrichments?.totalCount);
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error("Error checking enrichment data:", error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkEnrichmentData(activeTab);
  }, [currentPage]);

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    setIsLoading(true);
    setEnrichmentData([]);
    setCurrentPage(1);
    setTimeout(() => {
      checkEnrichmentData(tab);
    }, 1000);
  };

  // File upload handlers
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (file.name.endsWith('.csv')) {
      setSelectedFile(file);
      parseCSV(file);
    } else {
      alert("Please upload a valid CSV file");
    }
  };

  const parseCSV = (file) => {
    Papa.parse(file, {
      header: true,
      preview: 5,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.data.length > 0) {
          const headers = Object.keys(results.data[0]);
          setCsvHeaders(headers);
          setCsvData(results.data);

          const defaultMappings = {};
          headers.forEach(header => {
            defaultMappings[header] = header;
          });
          setFieldMappings(defaultMappings);
        }
      },
      error: (error) => {
        console.error("Error parsing CSV:", error);
        alert("Error parsing CSV file. Please check the file format.");
      }
    });
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    const file = e.dataTransfer.files[0];
    if (file?.name.endsWith(".csv")) {
      setSelectedFile(file);
      parseCSV(file);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const openFileDialog = () => fileInputRef.current.click();

  const reuploadCSV = () => {
    setSelectedFile(null);
    setCsvHeaders([]);
    setCsvData([]);
    setFieldMappings({});
    setStep(1);
    setTimeout(() => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    }, 100);
  };

  const handleFieldMappingChange = (field, csvHeader) => {
    setFieldMappings(prev => ({
      ...prev,
      [field]: csvHeader
    }));
  };

  const toggleOption = (id) => {
    setSelectedOptions(prev =>
      prev.includes(id) ? prev.filter(o => o !== id) : [...prev, id]
    );
  };

  const goBack = () => {
    // const loaderContactTable = document.querySelector("#cust-account-details");
    // if (loaderContactTable) {
    //   loaderContactTable.style.display = "none";
    // }

    // if (selectedTab == "contact") {
    //   navigate("/dashboard");
    // } else {
    //   navigate("/company-filters");
    // }
    navigate("/enrich");
  };

  const downloadSampleCSV = () => {
    const csvContent = activeTab === 'company' ? [
      ['Company Name', 'Website', 'Company Size', 'Address', 'Phone Number', 'SIC Code', 'Industry', 'Social Media URL']
    ] : [
      ['First name', 'Last name', 'Job title', 'Company', 'Personal Linkedin URL', 'Email', 'Website']
    ];

    const blob = new Blob([csvContent.map(row => row.join(',')).join('\n')], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `sample_${activeTab}_enrichment.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDownload = async (fileId) => {
    try {
      setIsLoading(true);
      const enrichmentRecord = enrichmentData.find(item => item.id === fileId);
      if (!enrichmentRecord?.enhancedDataCount || enrichmentRecord.enhancedDataCount <= 0) {
        setButtonType("error");
        setDefaultErrorMsg("No data found to enrich");
        setDefaultAlert(true);
        return;
      }

      if (!userData || !userData.token) {
        throw new Error('Authentication required');
      }

      const downloadUrl = `${ApiName.downloadEnrichedFile}?id=${fileId}`;
      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${userData.token}`,
          'Accept': 'text/csv'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to download file: ${response.status}`);
      }

      const contentDisposition = response.headers.get('Content-Disposition');
      let fileName = enrichmentRecord?.fileName || `enriched_${fileId}.csv`;

      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename="?(.+)"?/);
        if (fileNameMatch && fileNameMatch[1]) {
          fileName = fileNameMatch[1];
          if (!fileName.toLowerCase().endsWith('.csv')) {
            fileName += '.csv';
          }
        }
      }

      const csvData = await response.text();
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();

      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);

    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error.message || "Download failed. Please try again.");
      setDefaultAlert(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (id) => {
    setIsPopupOpen(true);
    setDeleteId(id);
  };

  const onCloseDelete = () => {
    setIsPopupOpen(false);
  };

  const onDeleteSuccess = async () => {
    setIsPopupOpen(false);
    try {
      setIsLoading(true);
      const response = await PostWithTokenNoCache(ApiName.deleteEnrichment, { id: deleteId });
      if (response?.status == 200) {
        const updatedData = enrichmentData.filter(item => item.id !== deleteId);
        setEnrichmentData(updatedData);
        setIsLoading(false);
      }
    } catch (error) {
      setIsLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message || "Failed to delete enrichment job");
      setDefaultAlert(true);
    }
  };

  const renderPreviewCell = (field) => {
    const mappedHeader = fieldMappings[field];
    if (!mappedHeader || !csvData.length || !csvData[0][mappedHeader]) {
      return <td className="table-last-column">No data</td>;
    }

    const firstRecord = csvData[0][mappedHeader];
    const truncated = firstRecord.length > 25
      ? `${firstRecord.substring(0, 25)}...`
      : firstRecord;

    return (
      <td className="table-last-column">
        <span
          className="preview-text"
          title={firstRecord.length > 30 ? firstRecord : ''}
        >
          {truncated}
        </span>
      </td>
    );
  };

  const submitEnrichment = async () => {
    try {
      setApiStatus({
        loading: true,
        success: false,
        error: false,
        message: 'Processing your file...'
      });

      const fullData = await new Promise((resolve, reject) => {
        Papa.parse(selectedFile, {
          header: true,
          complete: (results) => {
            if (results.data.length > 0) {
              resolve(results.data);
            } else {
              reject(new Error("CSV file is empty"));
            }
          },
          error: (error) => reject(error)
        });
      }).catch(error => {
        setButtonType("error");
        setDefaultErrorMsg(error.message || "Failed to parse CSV file");
        setDefaultAlert(true);
        throw error;
      });

      const hasValues = (obj) => {
        return Object.values(obj).some(
          value => value !== null && value !== undefined && value !== ''
        );
      };

      let payload;
      if (activeTab === 'company') {
        const filteredCompanyData = fullData
          .map(row => ({
            company_company_name: row[fieldMappings["Company Name"]] || '',
            company_website: row[fieldMappings["Website"]] || '',
            company_size: row[fieldMappings["Company Size"]] || '',
            company_street_adderess: row[fieldMappings["Address"]] || '',
            company_phone_1: row[fieldMappings["Phone Number"]] || '',
            company_sic_code: row[fieldMappings["SIC Code"]] || '',
            company_industries: row[fieldMappings["Industry"]] || '',
            company_social_media_url: row[fieldMappings["Social Media URL"]] || ''
          }))
          .filter(record => hasValues(record));

        payload = {
          enrichmentType: "COMPANY",
          fileName: selectedFile?.name || "company_enrichment.csv",
          requiredFields: [
            "company_company_name",
            "company_website",
            "company_phone_1"
          ],
          uploadData: filteredCompanyData
        };

        if (payload.uploadData.length > 5000) {
          closeModal();
          setButtonType("error");
          setDefaultErrorMsg("Maximum 5000 valid companies can be enriched at once");
          setDefaultAlert(true);
          return
        }

      } else {
        const filteredContactData = fullData
          .map(row => ({
            contact_first_name: row[fieldMappings["First name"]] || '',
            contact_last_name: row[fieldMappings["Last name"]] || '',
            contact_job_title_1: row[fieldMappings["Job title"]] || '',
            company_company_name: row[fieldMappings["Company"]] || '',
            company_social_linkedin: row[fieldMappings["Personal Linkedin URL"]] || '',
            contact_email_1: row[fieldMappings["Email"]] || '',
            company_website: row[fieldMappings["Website"]] || ''
          }))
          .filter(record => hasValues(record));

        payload = {
          enrichmentType: "CONTACT",
          fileName: selectedFile?.name || "enrichment.csv",
          requiredFields: [
            "contact_first_name",
            "contact_last_name",
            "contact_job_title_1",
            "contact_job_title_level_1",
            "contact_email_1"
          ],
          uploadData: filteredContactData
        };
      }

      if (payload.uploadData.length === 0) {
        throw new Error("No valid records found after filtering empty values");
      }

      const res = await PostWithTokenNoCache(ApiName.dataEnrich, JSON.stringify(payload));

      if (res?.status == 200) {
        setApiStatus({
          loading: false,
          success: true,
          error: false,
          message: 'Submitted successfully! Your data is being enriched.'
        });

        setStep(4);
        try {
          let params = {
            page: 1,
            pageSize: 10,
            searchParams: {
              enrichmentType: activeTab.toUpperCase(),
            },
            sortBy: "desc"
          };

          const response = await PostWithTokenNoCache(ApiName.fetchAllEnrich, params);
          if (response?.status == 200 && response.data?.data) {
            let res = JSON.parse(response.data.data);
            if (res.enrichments?.records?.length > 0) {
              setEnrichmentData(res.enrichments.records);

              setTotalPages(res?.enrichments?.totalCount);
              setIsLoading(false);
            }
          }
        } catch (error) {
          console.error("Error checking enrichment data:", error);
          setIsLoading(false);
        }
      } else {
        console.log(res)
        closeModal();
        setButtonType("error");
        setDefaultErrorMsg(res?.response?.data?.message || "Enrichment failed");
        setDefaultAlert(true);
      }
    } catch (error) {
      console.log(error)
      closeModal();
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message || error.message || "Enrichment failed");
      setDefaultAlert(true);
      setApiStatus({
        loading: false,
        success: false,
        error: true,
        message: error.message || 'Failed to process. Please try again.'
      });
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <div className="enrich-custom-modal-body-1">
            <div
              className={`choose-border text-center ${isDragging ? 'dragging' : ''}`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <img
                src={selectedFile ? "images/csv-select.png" : "images/csv-image.png"}
                className="img-fluid"
                alt="CSV Icon"
                style={{ maxWidth: "150px", marginBottom: "10px" }}
              />
              {!selectedFile ? (
                <>
                  <p className="drag-and-drop">Drag & drop your CSV file</p>
                  <label onClick={openFileDialog} className="custom-file-trigger">
                    <span className="the-or-plan">or</span>  Upload from device
                  </label>
                </>
              ) : (
                <>
                  <p className="file-name">{selectedFile.name}</p>
                  <span className="replace-file" onClick={openFileDialog}>
                    Replace file
                  </span>
                </>
              )}
              <input
                type="file"
                accept=".csv"
                ref={fileInputRef}
                className="hidden-file"
                style={{ display: "none" }}
                onChange={handleFileChange}
              />
            </div>
            <div>
              <p className="uolpade-your-csv-here">Upload Your CSV {activeTab === 'company' ? "company" : "contact"} list</p>
              <p className="select-csv">
                Select a CSV of {activeTab === 'company' ? "companies" : "contacts"}  to enrich it with information <br /> from ReachStream.
              </p>
            </div>
          </div>
        );
      case 2:
        return (
          <div className="next-step-container">
            <div className="display-csv-file">
              <div className="d-flex flex-row justify-content-between">
                <div className="d-flex flex-row">
                  <img src="images/noun-csv.png" className="img-fluid custom-width-csv" alt="" />
                  <p className="referred-csv">{selectedFile?.name || "No file selected"}</p>
                </div>
                <div className="d-flex flex-row" onClick={reuploadCSV} style={{ cursor: 'pointer' }}>
                  <img src="images/reupload-csv.png" className="img-fluid custom-width" alt="" />
                  <p className="reupload-csv">Reupload CSV</p>
                </div>
              </div>
            </div>
            <div className="scrollable-table-wrapper-box">
              <div className="scrollable-table-wrapper">
                <table className="custom-scroll-table">
                  <thead>
                    <tr>
                      <th>ReachStream field</th>
                      <th>CSV column header</th>
                      <th>Preview</th>
                    </tr>
                  </thead>
                  <tbody>
                    {activeTab === 'company' ? (
                      <>
                        <tr>
                          <td>Company Name*</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Company Name"
                              options={csvHeaders.length ? csvHeaders : companyFieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Company Name", option)}
                              selectedValue={fieldMappings["Company Name"]}
                            />
                          </td>
                          {renderPreviewCell("Company Name")}
                        </tr>
                        <tr>
                          <td>Website*</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Website"
                              options={csvHeaders.length ? csvHeaders : companyFieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Website", option)}
                              selectedValue={fieldMappings["Website"]}
                            />
                          </td>
                          {renderPreviewCell("Website")}
                        </tr>
                        <tr>
                          <td>Company Size</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Company Size"
                              options={csvHeaders.length ? csvHeaders : companyFieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Company Size", option)}
                              selectedValue={fieldMappings["Company Size"]}
                            />
                          </td>
                          {renderPreviewCell("Company Size")}
                        </tr>
                        <tr>
                          <td>Address</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Address"
                              options={csvHeaders.length ? csvHeaders : companyFieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Address", option)}
                              selectedValue={fieldMappings["Address"]}
                            />
                          </td>
                          {renderPreviewCell("Address")}
                        </tr>
                        <tr>
                          <td>Phone Number*</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Phone Number"
                              options={csvHeaders.length ? csvHeaders : companyFieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Phone Number", option)}
                              selectedValue={fieldMappings["Phone Number"]}
                            />
                          </td>
                          {renderPreviewCell("Phone Number")}
                        </tr>
                        <tr>
                          <td>SIC Code</td>
                          <td>
                            <EnrichCustomDropdown
                              label="SIC Code"
                              options={csvHeaders.length ? csvHeaders : companyFieldOptions}
                              onSelect={(option) => handleFieldMappingChange("SIC Code", option)}
                              selectedValue={fieldMappings["SIC Code"]}
                            />
                          </td>
                          {renderPreviewCell("SIC Code")}
                        </tr>
                        <tr>
                          <td>Industry</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Industry"
                              options={csvHeaders.length ? csvHeaders : companyFieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Industry", option)}
                              selectedValue={fieldMappings["Industry"]}
                            />
                          </td>
                          {renderPreviewCell("Industry")}
                        </tr>
                        <tr>
                          <td>Social Media URL</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Social Media URL"
                              options={csvHeaders.length ? csvHeaders : companyFieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Social Media URL", option)}
                              selectedValue={fieldMappings["Social Media URL"]}
                            />
                          </td>
                          {renderPreviewCell("Social Media URL")}
                        </tr>
                      </>
                    ) : (
                      <>
                        <tr>
                          <td>First name*</td>
                          <td>
                            <EnrichCustomDropdown
                              label="First Name"
                              options={csvHeaders.length ? csvHeaders : fieldOptions}
                              onSelect={(option) => handleFieldMappingChange("First name", option)}
                              selectedValue={fieldMappings["First name"]}
                            />
                          </td>
                          {renderPreviewCell("First name")}
                        </tr>
                        <tr>
                          <td>Last name*</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Last Name"
                              options={csvHeaders.length ? csvHeaders : fieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Last name", option)}
                              selectedValue={fieldMappings["Last name"]}
                            />
                          </td>
                          {renderPreviewCell("Last name")}
                        </tr>
                        <tr>
                          <td>Job title*</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Job Title"
                              options={csvHeaders.length ? csvHeaders : fieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Job title", option)}
                              selectedValue={fieldMappings["Job title"]}
                            />
                          </td>
                          {renderPreviewCell("Job title")}
                        </tr>
                        <tr>
                          <td>Company*</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Company"
                              options={csvHeaders.length ? csvHeaders : fieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Company", option)}
                              selectedValue={fieldMappings["Company"]}
                            />
                          </td>
                          {renderPreviewCell("Company")}
                        </tr>
                        <tr className="dotted-row">
                          <td colSpan="3">
                            <div className="dotted-line"></div>
                          </td>
                        </tr>
                        <tr>
                          <td>Personal Linkedin URL</td>
                          <td>
                            <EnrichCustomDropdown
                              label="select"
                              options={csvHeaders.length ? csvHeaders : fieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Personal Linkedin URL", option)}
                              selectedValue={fieldMappings["Personal Linkedin URL"]}
                            />
                          </td>
                          {renderPreviewCell("Personal Linkedin URL")}
                        </tr>
                        <tr>
                          <td>Email</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Email"
                              options={csvHeaders.length ? csvHeaders : fieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Email", option)}
                              selectedValue={fieldMappings["Email"]}
                            />
                          </td>
                          {renderPreviewCell("Email")}
                        </tr>
                        <tr>
                          <td>Website</td>
                          <td>
                            <EnrichCustomDropdown
                              label="Website"
                              options={csvHeaders.length ? csvHeaders : fieldOptions}
                              onSelect={(option) => handleFieldMappingChange("Website", option)}
                              selectedValue={fieldMappings["Website"]}
                            />
                          </td>
                          {renderPreviewCell("Website")}
                        </tr>
                      </>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        );
      case 3:
        return (
          <div className="enrich-custom-modal-body-1">
            <div className="d-flex gap-3 flex-wrap">
              {optionsData
                .filter(option => activeTab === "contact" || option.id !== "emails")
                .map(option => (
                  <div
                    key={option.id}
                    className={`option-card ${selectedOptions.includes(option.id) ? 'active' : ''}`}
                    onClick={() => toggleOption(option.id)}
                  >
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={selectedOptions.includes(option.id)}
                      readOnly
                    />
                    <img src={option.icon} alt={option.label} />
                    <div className="label-text">{option.label}</div>
                  </div>
                ))
              }
            </div>
            <div>
              <p className="what-whould-you-like">What would you like to enrich?</p>
              {activeTab == "contact" && (
                <p className="charged">You will only be charged for contacts matched in your file.</p>
              )}
            </div>
          </div>
        );
      case 4:
        return (
          <div className="enrich-custom-modal-body-1">
            <div className="mt-4 mb-4">
              {activeTab == "contact" ? (
                <img src="images/final-csv.png" className="img-fluid mx-auto d-block" width="300" alt="CSV processing" />
              ) : (
                <img src="images/enrich-companies.png" className="img-fluid mx-auto d-block" width="300" alt="CSV processing" />
              )}
            </div>
            <>
              <p className="enriching-contacts">We are enriching your {activeTab}</p>
              <p className="notify-you">
                Your enriched  {activeTab === 'company' ? "company" : "contact"} list is being prepared. We'll notify you at <br />
                <span className="make-it-bold">{userData.email}</span> when it's complete.
              </p>
            </>
            <button
              className="upload-another-csv"
              onClick={() => {
                reuploadCSV()
              }}
            >
              Upload another CSV
            </button>
          </div>
        );
      default:
        return null;
    }
  };

  const renderModalFooter = () => {
    if (step == 1) {
      return (
        <div className="d-flex flex-row justify-content-end mt-3 mb-4">
          <button
            className="next-button"
            disabled={!selectedFile}
            onClick={() => setStep(2)}
          >
            Next
          </button>
        </div>
      );
    }
    if (step == 2) {
      return (
        <div className="d-flex flex-row justify-content-between mt-3 mb-3">
          <button className="csv-back-button" onClick={() => setStep(step - 1)}>
            &lt;  Back
          </button>
          <button className="next-button" onClick={() => setStep(step + 1)}>
            Next
          </button>
        </div>
      );
    }
    if (step == 3) {
      return (
        <div className="d-flex flex-row justify-content-between mt-3 mb-3">
          <button className="csv-back-button" onClick={() => setStep(step - 1)}>
            &lt; Back
          </button>
          <button
            className="next-button"
            onClick={submitEnrichment}
            disabled={apiStatus.loading}
          >
            {apiStatus.loading ? 'Processing...' : 'Submit'}
          </button>
        </div>
      );
    }
    if (step == 4) {
      return (
        <div className="d-flex flex-row justify-content-end mt-3 mb-4">
          <button
            className="next-button"
            onClick={() => {
              closeModal();
            }}
            disabled={apiStatus.loading}
          >
            Done
          </button>
        </div>
      );
    }
    return null;
  };

  const closeModal = () => {
    setShowModal(false);
    setStep(1);
    setSelectedFile(null);
    setCsvHeaders([]);
    setCsvData([]);
    setFieldMappings({});
    setApiStatus({
      loading: false,
      success: false,
      error: false,
      message: ''
    });
  };

  return (
    <>
      <div className="ml-3 mr-3">
        <div className="data-enrichment">
          <div>
            <div className="d-flex flex-row">
              <div>
                <span className="enrich-back" onClick={goBack}>
                  <Link to="#">
                    <img src="./images/arrow-left.png" />
                  </Link>
                </span>

              </div>
              <div>
                <p className="data-enrichment-head-one">Data enrichment</p>
              </div>
            </div>
          </div>
        </div>
        <div className="d-flex flex-row justify-content-between mt-3">
          <div className="d-flex flex-row">
            {showCrmTab && (
              <div
                className={`enrich-tab-button py-2 ms-3 ${activeTab == "CRM" ? "active-tab" : ""}`}
              >
                CRM
              </div>
            )}
          </div>

          <div className="d-flex flex-row">
            <div className="d-flex flex-row">
              <div><img src="images/e-sortby-2.png" width="15" className="" /></div>
              <div>
                <p className="download-sample" onClick={downloadSampleCSV}>
                  Download sample
                </p>
              </div>
              <div><BsChevronDown size={10} /></div>

              <div className="d-flex flex-row">
                <div><img src="images/e-sortby.png" width="15" className="ml-4" /></div>
                <div>
                  <p className="download-sample" onClick={() => setShowPopup(true)}>
                    Upload
                  </p>
                </div>
                <div><BsChevronDown size={10} /></div>
              </div>
            </div>
          </div>

          {showPopup && (
            <div className="popup-overlay">
              <div className="popup-box">
                <button
                  onClick={() => setShowPopup(false)}
                  style={{
                    position: 'absolute',
                    top: '0px',
                    right: '0px',
                    border: 'none',
                    background: 'transparent',
                    fontSize: '20px',
                    cursor: 'pointer'
                  }}
                >
                  <img src="images/enrichpopup.png" className="img-fluid" width="22" alt="" />
                </button>

                <div className="">
                  <div className="row">
                    <div className="col-md-6">
                      <div className="white-boxx container my-4 rounded shadow-sm bg-white">
                        <div className="">
                          <img src="images/enrichment.png" className="img-fluid d-block mx-auto enrich-img" alt="" />
                        </div>
                        <div>
                          <p className="enrich-contacts">Enrich Contacts</p>
                          <p className="information">Select a CSV of contacts and enrich their information.</p>
                        </div>
                        <button
                          type="button"
                          className="select-csv"
                          onClick={() => {
                            setShowModal(true);
                            setShowPopup(false);
                            setActiveTab("contact");
                          }}
                        >
                          Select CSV File
                        </button>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="white-boxx container my-4 rounded shadow-sm bg-white">
                        <div className="">
                          <img src="images/enrich-companies.png" className="d-block mx-auto enrich-img" width="230" alt="" />
                        </div>
                        <div>
                          <p className="enrich-company">Enrich Companies</p>
                          <p className="information">Select a CSV of companies and enrich their information.</p>
                        </div>
                        <button
                          type="button"
                          className="select-csv"
                          onClick={() => {
                            setShowModal(true);
                            setShowPopup(false);
                            setActiveTab("company");
                          }}
                        >
                          Select CSV File
                        </button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <p className="csv-companies-enrich">Enrich contact or companies in a CSV</p>
                    <p className="reachstream-database">Select a CSV file of contacts or companies to enrich using ReachStream's <br /> database.</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="enrich-tab-wrapper mt-2 mb-3">
          <button
            className={`enrich-tab-list-button ${activeTab == 'contact' ? 'active' : ''}`}
            onClick={() => handleTabClick('contact')}
          >
            Contact
          </button>
          <button
            className={`enrich-tab-list-button ${activeTab == 'company' ? 'active' : ''}`}
            onClick={() => handleTabClick('company')}
          >
            Company
          </button>
        </div>
         {/* <div className="d-flex flex-row justify-content-center mt-5">
            <img src={penguineLoadingGif} alt="Loading" className="loader" width="400" />
          </div> */}
        {isLoading ? (
          <div className="d-flex flex-row justify-content-center mt-5">
            <img src={penguineLoadingGif} alt="Loading" className="loader" width="400" />
          </div>
        ) : enrichmentData.length == 0 ? (
          <div className="no-data-message">
            <p>No enrichment jobs found. Upload a CSV file to get started.</p>
          </div>
        ) : (
          <>
            <div className="enrich-table-container">
              <table className="enrich-custom-table">
                <thead className="enrich-table-head">
                  <tr>
                    <th className="enrich-table-header text-nowrap" style={{ width: "50px" }}>Sl No</th>
                    <th className="enrich-table-header text-nowrap" >File Name</th>
                    <th className="enrich-table-header text-nowrap">Scanned Rows</th>
                    <th className="enrich-table-header text-nowrap">Enriched</th>
                    <th className="enrich-table-header text-nowrap" >Uploaded At</th>
                    <th className="enrich-table-header">Actions</th>
                  </tr>
                </thead>
                <tbody className="enrich-table-body">
                  {enrichmentData.map((item, index) => {
                    return (
                      <tr key={index} className="enrich-table-row">
                        <td className="enrich-table-cell">{(currentPage - 1) * pageSize + index + 1}</td>
                        <td className="enrich-table-cell">{item.fileName.length > 10 ? item.fileName.slice(0, 14) + "..." : item.fileName}</td>
                        <td className="enrich-table-cell">{item.scannedRows}</td>
                        <td className="enrich-table-cell">
                          {item.status == 'COMPLETED' || item.status == 'DELIVERED'
                            ? item.enhancedDataCount || item.enriched || '-'
                            : '-'}
                        </td>
                        <td className="enrich-table-cell">
                          {item.createdAt ? new Date(item.createdAt).toLocaleDateString() : '-'}
                        </td>
                        <td className="enrich-table-cell">
                          <div style={{ width: "230px" }}>
                            <span>
                              {item.status == "INIT" || item.status == "PROCESSING" || item.status == "PENDING_EMAIL_VERIFICATION" ? (
                                <button className="Verifyingemailv2" style={{ cursor: "auto" }}>
                                  <img
                                    src="./images/loading-path.gif"
                                    style={{
                                      padding: "0px 0px 0 0px",
                                      color: "#55C2C3 !important",
                                      width: "18px",
                                      position: "relative",
                                      top: "0px",
                                      margin: "0 6px 0 0px",
                                    }}
                                    alt="Loading"
                                  />
                                  Enriching<span className="percentage"></span>
                                </button>
                              ) : item.status == "COMPLETED" ||
                                item.status == "DELIVERED" ? (
                                <button
                                  onClick={() => handleDownload(item.id)}
                                  type="button"
                                  className="savedlistDownloadbuttonnow-2"
                                  style={{ cursor: "pointer", outline: "none" }}
                                >
                                  <BsDownload className="mr-1" />
                                  Ready to download
                                </button>
                              ) : item.status == "INSUFFICIENT_CREDITS" || item.status == "FAILED" ? (
                                <button
                                  className="Verifyingemail"
                                  style={{ cursor: "auto", opacity: 0.6 }}
                                  disabled
                                >
                                  Ready to download
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleDownload(item.id)}
                                  type="button"
                                  className="savedlistDownloadbutton"
                                >
                                  Verify and Download
                                </button>
                              )}
                            </span>

                            {item.status !== "INIT" && item.status !== "PROCESSING" && (
                              <>
                                <span className="ml-2">
                                  <img
                                    onClick={() => handleDelete(item.id)}
                                    src="../images/delete.png"
                                    style={{ cursor: "pointer" }}
                                    alt="Delete"
                                  />
                                </span>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            <Pagination
              className="pagination-bar"
              currentPage={currentPage}
              totalCount={totalPages}
              pageSize={pageSize}
              onPageChange={(page) => {
                setCurrentPage(page);
              }}
            />
          </>
        )}
      </div>

      {showModal && (
        <div className="custom-modal-overlay">
          <div className="custom-modal">
            <div className="d-flex flex-row justify-content-between">
              <div className="enrich-border">
                <p className="enrich-popup-header">Enrich {activeTab === 'company' ? 'Company' : 'Contact'}</p>
              </div>
              <span className="enrich-close-button" onClick={closeModal}>
                <img src="images/enrichpopup.png" className="img-fluid" width="28" alt="enrichpopup-img" />
              </span>
            </div>
            {renderStepContent()}
            <div className="">
              {renderModalFooter()}
            </div>
          </div>
        </div>
      )}

      {isPopupOpen && (
        <DeletePopup
          onCloseDelete={onCloseDelete}
          onDeleteSuccess={onDeleteSuccess}
        />
      )}
      {defaultAlert && defaultErrorMsg && (
        <CommonAlert data={defaultErrorMsg} />
      )}
    </>
  );
};

export default EnrichTabContacts;