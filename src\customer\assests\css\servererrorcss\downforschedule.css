.down h1 {
    font-weight: 500;
    -webkit-text-stroke-width: thin;
    color: #093d54;
    margin: 25px 0 20px 0;
    font-size: 32px;
}


.expect h4 {
    color: #a1a1a1;
    font-size: 20px;
}


.email-input {
    width: 500px;
    margin: auto;
    padding: 15px 0 0 0;
}

.notify {
    background-color: #093d54;
    border: 0;
    color: #fff;
    border-radius: 7px;
    padding: 0 20px 0 20px;
    cursor: pointer;
}

.email-input-feild {
    border: 1px solid #a1a1a1;
    border-right-color: #fff;
    padding: 10px 1px 10px 15px;
    border-radius: 5px;
    box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
}


.input-group-append {
    margin-left: -10px;
    position: inherit;
}