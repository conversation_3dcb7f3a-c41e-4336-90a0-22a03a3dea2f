import React, { useState, useContext, useEffect, useRef } from "react";
import "../assests/css/filter/contactstable.css";
import { Link, useNavigate } from "react-router-dom";
import { ApiName } from "../common-files/ApiNames.js";
import UseTabStore from "../common-files/useGlobalState.js";
import {
  AxiosPostBearer,
  PostWithTokenNoCache,
  postWithToken,
} from "../common-files/ApiCalls.js";
const ListPopup = (props) => {
  let setOpenPopup = props.setOpenPopup;
  let setCheckboxes = props.setOpenPopup;
  let setSelectAll = props.setSelectAll;
  const [listData, setListData] = useState([]);
  const [listName, setListName] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);
  const [waitingMessage, setWaitingMessage] = useState("Saving in progress");
  const [listId, setListId] = useState("");
  const [listSave, setListSave] = useState(false);
  const navigate = useNavigate();
  const filterAlert = useRef();
  const {
    redisKey,
    advancedFilter,
    unSelectedRows,
    foundCounts,
    setButtonType,
    setDefaultErrorMsg,
    setDefaultAlert,
    selectedTab,
    searchPattern,
    selectedRows,
    setSelectedRows,
    setUnSelectedRows,
    setAdvancedFilter,
    setAdvancedFilterSearchPattern,
    setCurrentPage
  } = UseTabStore();

  const closeAlertRef = useRef();
  const loaderElement = document.querySelector("#table-loader");
  const loaderContactTable = document.querySelector("#cust-contact-table");
  const [userCredits, setUserCredits] = useState("");

  useEffect(() => {
    const creditsUpdate = async () => {
      try {
        const res = await PostWithTokenNoCache(ApiName.activeCreadits, {});
        if (res && "status" in res) {
          if (res.data.status == 200 && "data" in res.data) {
            setUserCredits(JSON.parse(res.data.data).total_balance_credit);
          }
        }
      } catch (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
      }
    };
    creditsUpdate();
  }, []);

  useEffect(() => {
    // Add event listeners when the component mounts
    document.addEventListener('mousedown', handleOutsideClick);
    document.addEventListener("keydown", handleEscKeyPress);

    // Remove event listeners when the component unmounts
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
      document.removeEventListener("keydown", handleEscKeyPress);
    };
  }, []);

  const handleOutsideClick = (e) => {
    // Check if the click is outside the modal
    if (!e.target.closest('.modal')) {
      close();
    }
  };

  const handleEscKeyPress = (e) => {
    // Check if the pressed key is ESC
    if (e.key === 'Escape') {
      close();
    }
  };

  const close = () => {
    setSelectedRows([]);
    setCheckboxes([]);
    setSelectAll(false);
    filterAlert.current.click();
    setOpenPopup(false);
    setUnSelectedRows([]);
    setAdvancedFilter(false);
    setAdvancedFilterSearchPattern({})
    setCurrentPage(1);
  };

  useEffect(() => {
    filterAlert.current.click();
  }, []);
  useEffect(() => {
    const getAllList = async () => {
      const param = JSON.stringify({
        page: 1,
        pageSize: 500000,
        searchParams: {
          status: "OPEN",
          wishListSection: selectedTab === "contact" ? "CONTACT" : "COMPANY",
        },
      });
      try {
        const res = await PostWithTokenNoCache(ApiName.fetchAllList, param);
        if (res && "status" in res) {
          if (res.status == 200) {
            let record = JSON.parse(res.data.data);
            setListData(record.wish_list.items);
          } else {
            setButtonType("error");
            setDefaultErrorMsg(res.response.data.message);
            setDefaultAlert(true);
          }
        } else {
          setButtonType("error");
          setDefaultErrorMsg(res.response.data.message);
          setDefaultAlert(true);
        }
      } catch (error) {
        setButtonType("error");
        setDefaultErrorMsg(error?.response?.data?.message);
        setDefaultAlert(true);
      }
    };
    getAllList();
  }, []);
  // Helper function to chunk the array
  const chunkArray = (array, chunkSize) => {
    const result = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      result.push(array.slice(i, i + chunkSize));
    }
    return result;
  };

  // Toggle loading state
  const toggleLoadingState = (isLoading) => {
    if (loaderElement) {
      loaderElement.style.display = isLoading ? "block" : "none";
    }
    if (loaderContactTable) {
      loaderContactTable.style.display = isLoading ? "none" : "block";
    }
  };

  // Handle errors
  const handleError = (error) => {
    const errorMessage = error?.response?.data?.message || "An unexpected error occurred";

    if (errorMessage === "Company List Cannot Exceed 5,000 records") {
      setDefaultErrorMsg(errorMessage);
      setButtonType("warning-error");
    } else {
      setDefaultErrorMsg(errorMessage);
      setButtonType("error");
    }

    setDefaultAlert(true);
    setListSave(false);
    filterAlert.current.click();
    setOpenPopup(false);
  };


  // Update the list count after creating list items
  const updateListCount = async (listId, currentCount, itemName, section) => {
    const param = JSON.stringify({
      id: listId,
      listName: itemName,
      wishListSection: section,
      status: "OPEN",
      listCount: currentCount,
    });

    try {
      const res = await PostWithTokenNoCache(ApiName.updateList, param);
      return res?.status === 200;
    } catch (error) {
      handleError(error);
      return false;
    }
  };

  // Create filter history after updating the list
  const createFilterHistory = async (listId, selectedTab, searchPattern) => {
    const params = JSON.stringify({
      filterType: selectedTab === "contact" ? "CONTACT" : "COMPANY",
      filterApplied: JSON.stringify(searchPattern),
      filterCount: selectedRows.length,
      responseStatus: 200,
      wishListId: listId,
      actionType: "WISH_LISTED_FILTER",
    });

    try {
      const res = await PostWithTokenNoCache(ApiName.filterHistoryCreate, params);
      return res?.status === 200;
    } catch (error) {
      handleError(error);
      return false;
    }
  };

  // Process a chunk of items for creating wishlist items
  const processChunk = async (chunk, listId) => {
    // Create the JSON structure with wishListId and array of dataId
    const wishListItem = {
      wishListId: listId,
      dataId: chunk,
    }
    const params = JSON.stringify(wishListItem);

    try {
      // Send the chunk data to the API
      const res = await PostWithTokenNoCache(ApiName.listItemCreate, params);
      // Return parsed response data if the status is 200
      return res?.status === 200 ? JSON.parse(res?.data?.data) : 0;
    } catch (error) {
      // Handle errors using a dedicated error handler
      handleError(error);
      return null;
    }
  };


  // Main function to update the list
  const updateList = async () => {
    if (!listId) return;

    if (selectedTab == "CONTACT") {
      if (parseInt(selectedRows.length) > parseInt(userCredits)) {
        setButtonType("credit-message");
        setDefaultErrorMsg("Insufficient Credits to perform this action.");
        setDefaultAlert(true);
        setListSave(false);
        filterAlert.current.click();
        setOpenPopup(false);
        return false;
      }
    }

    setListSave(true);
    // Prepare chunks of data to process (chunks of 5000 rows)
    const chunks = chunkArray(selectedRows, 5000);

    let totalCount = 0;
    let processedCount = 0; // To track how many rows have been processed

    for (const [index, chunk] of chunks.entries()) {
      const data = await processChunk(chunk, listId);
      // If data is successfully processed, accumulate it
      if (data) {
        totalCount = data.totalCount;

        // Update the waiting message with progress
        setWaitingMessage(
          `${(index + 1).toLocaleString()} / ${chunks.length.toLocaleString()} (${processedCount.toLocaleString()} data processed)`
        );

        // Update processed count
        processedCount += chunk.length;

        // Update the waiting message after each chunk with formatted numbers
        setWaitingMessage(
          `${processedCount.toLocaleString()} / ${selectedRows.length.toLocaleString()} data processed`
        );

      } else {
        // If a chunk fails, stop processing and show error
        toggleLoadingState(false);
        return;
      }
    }
    // After processing all chunks, update the list count and filter history
    const item = listData.find((item) => item.id === listId);

    const listUpdated = await updateListCount(listId, totalCount, item.listName, item.wishListSection);
    if (listUpdated) {
      const filterHistoryCreated = await createFilterHistory(listId, selectedTab, searchPattern);
      if (filterHistoryCreated) {
        // If successful, reset states and navigate
        close();
        setSelectedRows([]);
        setListName(""); // Clear the list name
        setSuccessMessage(true); // Set success message
        setListSave(false); // Disable saving state

        navigate("/saved-list");
      } else {
        toggleLoadingState(false);
      }
    } else {
      toggleLoadingState(false);
    }
  };

  const createNewList = async () => {
    if (listName.length > 1) {
      if (selectedTab =="CONTACT" && parseInt(selectedRows.length) > parseInt(userCredits)) {
        setListSave(false);
        filterAlert.current.click();
        setOpenPopup(false);

        setButtonType("credit-message");
        setDefaultErrorMsg("Insufficient Credits to perform this action.");
        setDefaultAlert(true);
        return false;
      }

      setListSave(true);

      const param = JSON.stringify({
        listName: listName,
        status: "OPEN",
        wishListSection: selectedTab === "contact" ? "CONTACT" : "COMPANY",
        listCount: selectedRows.length,
        maxCompanies: parseInt(selectedRows.length + unSelectedRows.length),
        includedIds: selectedRows,
        excludedIds: Array.isArray(unSelectedRows) ? unSelectedRows : [],
        searchPattern: searchPattern
      });
      if (selectedTab === "company") {
        const chunks = chunkArray(selectedRows, 5000);
        let processedCount = 0;
        for (let i = 0; i < chunks.length; i++) {
          processedCount++;

          // Only update every 100 for smoother UI performance
          if (processedCount % 100 === 0 || processedCount === selectedRows.length) {
            setWaitingMessage(
              `${processedCount.toLocaleString()} / ${selectedRows.length.toLocaleString()} data processed`
            );
            console.log(`${processedCount.toLocaleString()} / ${selectedRows.length.toLocaleString()} data processed`);
            await new Promise((resolve) => setTimeout(resolve, 1));
          }
        }
      }
      if (advancedFilter && selectedTab == "contact") {
        try {
          const param = {
            listName: listName,
            status: "NOT_READY_FOR_VERIFICATION",
            wishListSection: selectedTab === "contact" ? "CONTACT" : "COMPANY",
            listCount: 0,
            maxCompanies: 0,
            excludedIds: Array.isArray(unSelectedRows) ? unSelectedRows : [],
            searchPattern: searchPattern
          };
          let url = ApiName.createList;
          await PostWithTokenNoCache(url, param).then(async (response) => {
            const record = JSON.parse(response.data.data);
            const id = record.id;
            const param = {
              wishListId: id,
              key: redisKey,
              wishListSection: selectedTab === "contact" ? "CONTACT" : "COMPANY",
              count: selectedRows.length,
              excludedIds: Array.isArray(unSelectedRows) ? unSelectedRows : [],
              searchPattern: searchPattern,
              includedIds: [],
              searchPattern: searchPattern
            }
            const res = await PostWithTokenNoCache(ApiName.listItemCreateV2, param);
            if (res?.status === 200) {
              close();
              setListName(""); // Clear the list name
              setSuccessMessage(true); // Set success message
              setListSave(false); // Disable saving state
              navigate("/saved-list");

            } else {
              closeOnError(res?.response?.data?.message || 'Error creating wishlist item');
            }
          })
        } catch (error) {
          closeOnError(error?.response?.data?.message || 'An unexpected error occurred during wishlist item creation');
        }
      } else {

        try {
          let url = selectedTab === "contact" ? ApiName.createList : ApiName.createCompanyWishList;
          const res = await PostWithTokenNoCache(url, param);

          if (res?.status === 200) {
            const record = JSON.parse(res.data.data);
            const id = record.id;

            // Helper function to chunk the data into smaller arrays of specified size
            const chunkArray = (array, chunkSize) => {
              const result = [];
              for (let i = 0; i < array.length; i += chunkSize) {
                result.push(array.slice(i, i + chunkSize));
              }
              return result;
            };

            const chunks = chunkArray(selectedRows, 5000); // Divide selectedRows into chunks of 5000
            let processedCount = 0; // Counter for processed data rows

            // Process chunks sequentially
            for (const chunk of chunks) {
              // Map each chunk into the required JSON structure
              const wishListItems = {
                wishListId: id,
                dataId: chunk,
              }

              const params = JSON.stringify(wishListItems);

              // Update the progress message
              setWaitingMessage(
                `${processedCount.toLocaleString()} / ${selectedRows.length.toLocaleString()} data processed`
              );
              // Send the chunk to the API
              if (selectedTab === "contact")
                await createWishListItem(params, id)


              // Update the processed count
              processedCount += chunk.length;

              // Refresh progress message after processing the chunk
              setWaitingMessage(
                `${processedCount.toLocaleString()} / ${selectedRows.length.toLocaleString()} data processed`
              );
            }

            // All operations are completed, update the UI
            setWaitingMessage(""); // Clear the waiting message
            setListName(""); // Reset the list name
            setSuccessMessage(true); // Show success message
            setListSave(false); // Turn off saving state
          } else {
            // Handle non-200 responses
            closeOnError(res.response?.data?.message || "An error occurred");
          }
        } catch (error) {
          // Handle unexpected errors
          closeOnError(error?.response?.data?.message || "An unexpected error occurred");
        }
      }
    }
  };

  const createWishListItem = async (params, id) => {
    try {
      const res = await PostWithTokenNoCache(ApiName.listItemCreate, params);
      if (res?.status === 200) {
        // After creating list items, record filter history
        const filterParams = JSON.stringify({
          filterType: selectedTab === "contact" ? "CONTACT" : "COMPANY",
          filterApplied: JSON.stringify(searchPattern),
          filterCount: selectedRows.length,
          responseStatus: 200,
          wishListId: id,
          actionType: "WISH_LISTED_FILTER",
        });

        const filterRes = await PostWithTokenNoCache(ApiName.filterHistoryCreate, filterParams);
        if (filterRes?.status === 200) {
          // Successfully updated filter history, no further action needed
        } else {
          closeOnError(filterRes?.response?.data?.message || 'Error saving filter history');
        }
      } else {
        closeOnError(res?.response?.data?.message || 'Error creating wishlist item');
      }
    } catch (error) {
      closeOnError(error?.response?.data?.message || 'An unexpected error occurred during wishlist item creation');
    }
  };

  const closeOnError = (error) => {
    let errorMessage = "List name exists — please use a new name";

    if (error === "Wish list name can't be duplicate") {
      errorMessage = "List name exists — please use a new name";
    } else if (error === "Insufficient credit balance") {
      errorMessage = "Insufficient Credits to perform this action.";
    } else if (error) {
      errorMessage = error;
    }

    setDefaultErrorMsg(errorMessage);
    setButtonType("warning-error");
    setDefaultAlert(true);
    setListSave(false);
    filterAlert.current.click();
    setOpenPopup(false);
  };

  useEffect(() => {
    let timer;
    if (successMessage) {
      timer = setTimeout(() => {
        setSuccessMessage(false);
      }, 5000);
    }
    return () => clearTimeout(timer);
  }, [successMessage]);
  const check = {
    color: "#6DE1A4",
    backgroundColor: "white",
    borderRadius: "50%",
    padding: "3px",
    fontSize: "10px",
    position: "absolute",
    top: "3px",
    left: "3px",
  };
  const checkblock = {
    backgroundColor: "#6DE1A4",
    padding: "3px 11px",
    marginRight: "5px",
    borderRadius: "4px",
    position: "relative",
  };
  const successbox = {
    display: "flex",
    justifyContent: "center",
    marginTop: "5px",
  };

  return (
    <>
      <button
        ref={filterAlert}
        style={{ display: "none" }}
        type="button"
        data-toggle="modal"
        data-target="#exampleModal"
        data-backdrop="static"
        data-keyboard="false"
      />
      <div
        className="modal fade show"
        id="exampleModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="exampleModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog" role="document">
          <div
            className="modal-content"
            style={{
              width: "30%",
              margin: "9rem auto",
              backgroundColor: "#F5F5F5",
              padding: "0 8px 0 8px",
            }}
          >
            <div className="modal-header" style={{ borderBottom: "0" }}>
              <h5 className="modal-title" id="exampleModalLabel">
                Save {selectedTab === "contact" ? "Contacts" : "Companies"}
              </h5>
              <span className="savecontactsunderline"></span>
              <button
                type="button"
                className="close-button"
                onClick={() => {
                  if (!listSave) {
                    close();
                  }
                }}
                ref={closeAlertRef}
                disabled={listSave}
                style={{
                  cursor: listSave ? "not-allowed" : "pointer",
                  opacity: listSave ? 0.6 : 1,
                }}
              >
                <span aria-hidden="true" className="cansl">&times;</span>
              </button>
            </div>
            <div className="modal-body">
              <p className="AddToNewList">Add To New List:</p>
              <div className="form-group">
                <input
                  type="text"
                  className="AddToNewListinput"
                  aria-describedby="emailHelp"
                  placeholder="Name Your List"
                  value={listName}
                  onChange={(event) => setListName(event.target.value)}
                />
              </div>

              <div className="d-flex flex-row-reverse">
                <div>
                  <button
                    type="button"
                    onClick={createNewList}
                    className="Savepopup"
                    style={{
                      opacity: listName && !listSave ? "1" : "0.3",
                      cursor: !listName || listSave ? "not-allowed" : "pointer",
                    }}
                    disabled={!listName || listSave ? true : false}
                  >
                    {listSave ? (
                      <div className="loader" style={{
                        display: 'inline-block',
                        width: '16px',
                        height: '16px',
                        border: '2px solid rgba(97, 218, 251, 0.3)',
                        borderRadius: '50%',
                        borderTopColor: '#61dafb',
                        animation: "spin 1s linear infinite",
                        marginRight: "8px",
                      }}></div>
                    ) : (
                      "Save"
                    )}
                  </button>
                </div>

                <div className="mr-2">
                  <button
                    onClick={close}
                    type="submit"
                    className="cancel"
                    style={{
                      opacity: listName && !listSave ? "1" : "0.3",
                      cursor: !listName || listSave ? "not-allowed" : "pointer",
                    }}
                    disabled={!listName || listSave ? true : false}
                  >
                    Cancel
                  </button>
                </div>
              </div>
              <div className="d-flex flex-row mt-3 mb-2">
                <div>
                  <p className="AddToYourLists">Add To Your Existing List:</p>
                </div>
                <div>
                  {successMessage && (
                    <div className="d-flex flex-row justify-content-end" style={successbox}>
                      <div style={{ backgroundColor: "rgb(238, 255, 246)", border: "1px solid rgb(109, 225, 164)", padding: "5px 6px", borderRadius: "7px", fontSize: "12px", width: "fit-content", margin: "0px 0px 0px 1rem", }}>
                        <span style={checkblock}>
                          <i className="fa fa-check" style={check}></i>
                        </span>
                        Saved Successfully
                      </div>
                    </div>
                  )}
                  {listSave && waitingMessage !== "" && (
                    <div className="d-flex flex-row justify-content-end" style={successbox}>
                      <div
                        style={{
                          backgroundColor: "rgb(238, 255, 246)",
                          border: "1px solid rgb(109, 225, 164)",
                          padding: "5px 6px",
                          borderRadius: "7px",
                          fontSize: "12px",
                          width: "fit-content",
                          margin: "0px 0px 0px 1rem",
                        }}
                      >
                        {waitingMessage}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <div className="fixpopuphead">
                  <ul className="unorderedlist">
                    {listData.map((item, i) => (
                      <li className="orderedlist">
                        <div className="fleayerbox">
                          <div className="d-flex flex-row justify-content-between">
                            <div>
                              <p className="ContactList1">{item.listName}</p>
                            </div>
                            <div className="mt-2 mr-2">
                              <input
                                type="radio"
                                name="list"
                                onClick={() => setListId(item.id)}
                              />
                            </div>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="d-flex flex-row-reverse mt-3">
                <div>
                  <button
                    type="button"
                    onClick={updateList}
                    className="Savepopup"
                    style={{
                      opacity: listId ? "1" : "0.3",
                      cursor: !listId ? "not-allowed" : "pointer", // Set cursor based on listSave state
                    }}
                  >
                    Save
                  </button>
                </div>

                <div className="mr-2">
                  <button
                    onClick={close}
                    type="button"
                    className="cancel"
                    style={{
                      opacity: listId ? "1" : "0.3",
                      cursor: !listId ? "not-allowed" : "pointer",
                    }}
                    disabled={listId ? false : true}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
};
export default ListPopup;