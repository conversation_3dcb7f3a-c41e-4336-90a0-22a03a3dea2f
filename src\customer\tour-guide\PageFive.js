import React from "react";
import "../assests/css/layouts/demo.css";
import { useNavigate, useParams } from "react-router-dom";
import Header from "../layouts/Header";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../customer/common-files/ApiCalls.js";

const PageFive = () => {
  let data_params = useParams();
  const navigate = useNavigate();
  const goBack = () => {
    navigate("/tour-guide-wishlist/" + data_params.id);
  };

  const close = async () => {
    const params = JSON.stringify({
      id: data_params.id,
      actionType: "CLOSE",
      repeatTimeDefault: 1,
      currentRepeatedTime: 1,
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.appTourUpdate, params);
      if (res.data.status == "200") {
        navigate("/dashboard");
      }
    } catch (errors) {}
  };

  const skipTour = async () => {
    const params = JSON.stringify({
      id: data_params.id,
      actionType: "SKIP",
      repeatTimeDefault: 1,
      currentRepeatedTime: 1,
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.appTourUpdate, params);
      if (res.data.status == "200") {
        navigate("/dashboard");
      }
    } catch (errors) {}
  };

  return (
    <>
      <div>
        <Header />
        <div className="examplefive">
          <img
            src="../../images/demo-image-5.png"
            class="img-fluid"
            alt="Responsive image"
            style={{ width: "100%" }}
          />

          <div onClick={skipTour}>
            <p className="skiptour5">Skip Tour</p>
          </div>

          {/* <p className="SaveandDownload">Save and Download</p>
          <p className="Checkyoursavedlistshere">
            Check your saved lists here. You can also verify and download your
            lists.
          </p> */}

          <div className="addtbuttonss">
            <div className="d-flex flex-row justify-content-center">
              <div>
                <button type="button" onClick={goBack} className="sortback">
                  Back
                </button>
              </div>
              <div>
                <button type="button" onClick={close} className="sortnext">
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PageFive;
