import React, { useEffect, useMemo, useRef, useState, useContext } from "react";
import { Link } from "react-router-dom";
import {
  AxiosPostBearer,
  PostWithTokenNoCache,
  fetchProfilePicture,
  postWithToken,
  uploadProfilePicture,
} from "../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import Header from "../../customer/layouts/Header.js";
import "../assests/css/layouts/account_details.css";
import ContactUsPopup from "../filters/ContactUsPopup";
import UseTabStore from "../common-files/useGlobalState.js";
import { useNavigate } from "react-router-dom";
import { removeSessionItem } from "../common-files/LocalStorage.js";
import LeftNavbar from "../../customer/filters/LeftNavbar.js";
import penguineLoadingGif from "../../customer/assests/waiting.gif";
import <PERSON>ert from "../../customer/common-files/alert.js";
import DashboardContext from "../common-files/ContextDashboard";
import { useMicrosoftLoginLogic } from "../common-files/SocialMediaAuth.js";
import WebhookModal from "../common-files/WebhookModal.js";

// For freemium plan (your original formatCreditValue)
function formatCreditValue(value) {
  if (value === null || value === undefined) return "0";
  if (typeof value === "number") return value.toLocaleString("en-IN");
  if (typeof value === "string" && value.toLowerCase() === "unlimited") return "Unlimited";
  return value;
}

// For non-freemium plans (modified to match your special logic)
function formatViewValue(value, isTotal = true) {
  if (value === null || value === undefined) return "0";

  if (typeof value === "number") {
    return value.toLocaleString("en-IN");
  }

  if (typeof value === "string") {
    // Special case: For total views in non-freemium, "Unlimited" becomes "0"
    if (isTotal && value.toLowerCase() === "unlimited") {
      return "0";
    }
    return value;
  }

  return "0";
}

const Account_details = () => {
  const [user, setUser] = useState(JSON.parse(localStorage.getItem("user")));
  const [userCreadits, setUserCreadits] = useState("");
  const [fname, setFname] = useState("");
  const [fnameErr, setFnameErr] = useState("");
  const [validFname, setValidFname] = useState(false);

  const [lName, setLname] = useState("");
  const [lnameErr, setLnameErr] = useState("");
  const [validLname, setValidLname] = useState(false);

  const [email, setEmail] = useState("");
  const [emailErr, setEmailErr] = useState("");
  const [validEmail, setValidEmail] = useState(false);
  const [phone, setPhone] = useState("");
  const [phoneErr, setPhoneErr] = useState("");
  const [validPhone, setValidPhone] = useState(false);
  const [password, setPassword] = useState("");
  const [passwordErr, setPasswordErr] = useState("");
  const [validPassword, setValidPassword] = useState(false);
  const [validLength, setValidLength] = useState(false);
  const [validCase, setValidCase] = useState(false);
  const [validWord, setValidWord] = useState(false);
  const [jobtitle, setJobtitle] = useState("");
  const [linkedin, setLinkedin] = useState("");
  const [facebook, setFacebook] = useState("");
  const [twitter, setTwitter] = useState("");
  const [Instagram, setInstagra] = useState("");
  const [companyName, setCompany] = useState("");
  const [website, setWebsite] = useState("");
  const [industry, setindustry] = useState("");
  const [employeeSize, setEmployeesize] = useState("");
  const [revenue, setRevenue] = useState("");
  const [companyAddress, setCompanyAddres] = useState("");
  const [state, setState] = useState("");
  const [city, setCity] = useState("");
  const [zipcode, setZipcode] = useState("");
  const [successMsg, setSuccessMsg] = useState("");
  const [successError, setSuccessError] = useState(false);
  const inputRef = useRef(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [profilePicture, setProfilePicture] = useState(null);
  const [uploadStatusMessage, setUploadStatusMessage] = useState(null);
  const [isParaDisabled, setIsParaDisabled] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [apiKey, setApiKey] = useState("");
  const [isCreateApiKey, setIsCreateApiKey] = useState(false);
  const [displayCreditUsed, setDisplayCreditUsed] = useState(false);
  const [userCreditsShow, setUserCreaditsShow] = useState("");
  const [copiedCode, setCopiedCode] = useState(false);
  const [showCompanyDetails, setShowCompanyDetails] = useState(false);
  const { dataDC, setDataDC } = useContext(DashboardContext);
  const [deactivateProfile, setDeactivateProfile] = useState(false);
  const textAreaRef = useRef(null);
  const { Microsoftlogout } = useMicrosoftLoginLogic();
  const {
    buttonType,
    defaultAlert,
    defaultErrorMsg,
    selectedTab,
    setProfileUpdated,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
    profileUpdated,
    setSelectedCompanyAddressCountry,
    setSelectedCompanyAddressState,
    setSelectedCompanyAddressCity,
    setSelectedCompanyZipCode,
    setSelectedContactJobTitle1,
    setSelectedContactJobTitleLevel1,
    setSelectedContactJobDeptName1,
    setSelectedContactJobFunctionName1,
    setCheckedItems,
    setCompanyTypeCheckedBoxes,
    setSelectedCompanyEmployeeSize,
    setSelectedCompanyAnnualRevenueAmount,
    setSelectedCompanyTechKeyWordsList,
    setSelectedCompany,
    setSortingBy,
    setDefaultError,
    setNumber,
    setSearchPattern,
    setWithDownloaded,
    setResetPage,
    setCheckboxesGlobal,
    setSelectedData,
    setSelectedRows,
    setAllRecordsNew,
    setSampleDownloadedIds,
    setLoadedData,
    setViewModal,
    setViewSelected,
    setSelectedValues,
    setCheckedBoxes,
    setSelectedOptions,
    setSicCode,
    setIndustryData,
    setShowDownloadedData,
    setSelectedCompanyType,
    setSelectedCompanyURL
  } = UseTabStore();
  const navigate = useNavigate();
  const [isAuth, setIsAuth] = useState(false);
  const [promocode, setPromocode] = useState(null);
  const [isSuccessCoupon, setIsSuccessCoupon] = useState(null);
  const [isSuccessCouponMsg, setIsSuccessCouponMsg] = useState(null);

  const scrollableElementRef = useRef(null);
  const containerRef = useRef(null);
  const [prevScrollPos, setPrevScrollPos] = useState(0);
  const [hideGif, setHideGif] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isWebhookModalOpen, setIsWebhookModalOpen] = useState(false);


  useEffect(() => {
    // Add event listeners when the component mounts
    document.addEventListener("mousedown", handleOutsideClick);
    document.addEventListener("keydown", handleEscKeyPress);

    // Remove event listeners when the component unmounts
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
      document.removeEventListener("keydown", handleEscKeyPress);
    };
  }, []);

  const closeAlertRef = useRef();

  const toggleWebHookModal = () => {
    setIsWebhookModalOpen(!isWebhookModalOpen);
  };


  const close = () => {
    if (defaultAlert && defaultErrorMsg && buttonType == "error")
      closeAlertRef.current.click();
  };

  const handleOutsideClick = (e) => {
    // Check if the click is outside the modal
    if (defaultAlert && !e.target.closest(".modal")) {
      close();
    }
  };

  const handleEscKeyPress = (e) => {
    // Check if the pressed key is ESC
    if (e.key === "Escape") {
      close();
    }
  };

  const handleScroll = () => {
    const scrollableElement = scrollableElementRef.current;

    if (scrollableElement) {
      const currentScrollPos = scrollableElement.scrollTop;

      if (currentScrollPos > prevScrollPos) {
        handleScrollDown();
      } else {
        handleScrollUp();
      }

      setPrevScrollPos(currentScrollPos);
    }
  };

  const handleScrollDown = () => {
    setHideGif(true);
    containerRef.current.scrollIntoView();
  };

  const handleScrollUp = () => {
    setHideGif(false);
  };
  useMemo(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    if (!user) {
      window.location.href = "/";
    } else {
      setIsAuth(true);
    }
  }, []);

  useEffect(() => {
    const getAggrement = async () => {
      let params = {
        method: "GET",
      };
      let res = await PostWithTokenNoCache(ApiName.getAggrement, params);
      if (res) {
        let jsonObj = JSON.parse(res.data.data);
        setPhone(jsonObj.phone ? jsonObj.phone : "");
        setIsChecked(
          jsonObj.agreement
            ? jsonObj.agreement == "true"
              ? true
              : false
            : false
        );
      }
    }
    getAggrement();
  }, []);

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user"));
    const getUserProfile = async (data) => {
      // You can await here
      const url = ApiName.userProfile;
      try {
        const res = await PostWithTokenNoCache(url, data);
        if (res && "status" in res) {
          if (res.data.status == 200 && "data" in res.data) {
            let jsonObj = JSON.parse(res.data.data);
            if (jsonObj) {
              setCity(jsonObj.city ? jsonObj.city : "");
              setCompany(jsonObj.companyName ? jsonObj.companyName : "");
              setCompanyAddres(
                jsonObj.companyAddress ? jsonObj.companyAddress : ""
              );
              setEmployeesize(jsonObj.employeeSize ? jsonObj.employeeSize : "");
              setFacebook(jsonObj.facebook ? jsonObj.facebook : "");
              setInstagra(jsonObj.instagram ? jsonObj.instagram : "");
              setJobtitle(jsonObj.jobTitle ? jsonObj.jobTitle : "");
              setLinkedin(jsonObj.linkedIn ? jsonObj.linkedIn : "");
              setRevenue(jsonObj.revenue ? jsonObj.revenue : "");
              setState(jsonObj.state ? jsonObj.state : "");
              setTwitter(jsonObj.twitter ? jsonObj.twitter : "");
              setWebsite(jsonObj.websiteAddress ? jsonObj.websiteAddress : "");
              setZipcode(jsonObj.zipCode ? jsonObj.zipCode : "");
              setindustry(jsonObj.industry ? jsonObj.industry : "");
              // setSggreement(jsonObj.industry ? jsonObj.industry : "");
            }
          }
        }
      } catch (errors) {
      }
    }
    if (user) {
      if ("firstName" in user) setFname(user.firstName);
      if ("lastName" in user) setLname(user.lastName);
      if ("email" in user) setEmail(user.email);
      if ("phone" in user) setPhone(user.phone);
    }
    const getUserApiKey = async () => {
      // You can await here
      const url = ApiName.getApiKey;
      try {
        const res = await PostWithTokenNoCache(url);
        if (res && "status" in res) {
          if (res.data.status == 200 && "data" in res.data) {
            let jsonObj = JSON.parse(res.data.data);
            if (jsonObj) {
              setApiKey(jsonObj.api_key);
            }
          }
        }
      } catch (errors) {
      }
    }
    getUserApiKey();
    getUserProfile({});
    getUserProfilePicture({});
    return () => {
      URL.revokeObjectURL(profilePicture);
    };
  }, []);

  const getUserProfilePicture = async (data) => {
    const url = ApiName.getProfilePicture;
    const imageUrlTemp = await fetchProfilePicture(url, data);
    if (imageUrlTemp) {
      setProfilePicture(imageUrlTemp);
    }
  }

  const handleClick = () => {
    inputRef.current.click();
    setUploadStatusMessage(null);
  };

  const handleSelectedRemove = async () => {
    setIsButtonDisabled(true);
    const url = ApiName.removeProfilePicture;
    try {
      const res = await PostWithTokenNoCache(url);
      if (res && "status" in res) {
        if (res.data.status == 200 && "data" in res.data) {
          if (res) {
            URL.revokeObjectURL(selectedFile);
            setSelectedFile(null);
            inputRef.current.value = "";
            setUploadStatusMessage(res?.data?.message);
            setIsParaDisabled(true);
            setIsButtonDisabled(true);
            getUserProfilePicture();
            setProfilePicture("");
            setIsLoading(false);
            setProfileUpdated(res?.data?.message);
          }
        }
      }
    } catch (errors) {
      setButtonType("error");
      setDefaultErrorMsg(errors?.response?.data?.message);
      setDefaultAlert(true);
      getUserProfilePicture();
      setIsButtonDisabled(false);
    }

  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file == undefined || null) {
      return;
    }
    if ("name" in file) {
      // console.log("Selected file Name:", file.name);
      setIsParaDisabled(false);
      setIsButtonDisabled(false);
    }
    // console.log('Selected file:', file.name);
    setSelectedFile(event.target.files[0]);
  };
  const handlePictureSave = () => {
    setIsButtonDisabled(true);
    // setIsLoading(true);
    const url = ApiName.profilePictureUpload;
    const submitForm = async (data) => {
      // You can await here
      const res = await uploadProfilePicture(url, data);
      if (res == undefined || null) {
        setUploadStatusMessage("Server Error, Something Went Wrong");
        setIsButtonDisabled(false);
        setIsLoading(false);

        return;
      }
      if (res && "status" in res) {
        if (res.status == 200) {
          const imageURL = URL.createObjectURL(data);
          setProfilePicture(imageURL);
          setUploadStatusMessage(res.message);
          setProfileUpdated(res.message);
          setIsButtonDisabled(false);
          setIsLoading(false);
          return;
        }
        if (
          res.status == 400 &&
          res.message == "The picture must be at least 100x100 pixels."
        ) {
          setUploadStatusMessage(
            "The image resolution is too low. Kindly upload an image with a resolution of min 100px*100px."
          );
          setIsButtonDisabled(false);
          setIsLoading(false);
          return;
        }
        if (res.status == 400 && res.message == "ERR_AUTHORIZATION_HEADER") {
          setUploadStatusMessage("AUTHORIZATION ISSUE");
          setIsLoading(false);
          setIsButtonDisabled(false);
          return;
        }
        if (res.status == 500 || 404) {
          setUploadStatusMessage("Server Error, Something Went Wrong");
          setIsLoading(false);
          setIsButtonDisabled(false);
          return;
        }
      } else {
        setIsLoading(false);
        setIsButtonDisabled(false);
        return;
      }
    }
    submitForm(selectedFile);
  };

  const onChageHandler = (event) => {
    const name = event.target.name;
    const specialChars = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~*********]/;
    if ("firstName" == name) {
      let fname = event.target.value;
      setFname(fname);
      if (fname.length < 3) {
        setValidFname(false);
        setFnameErr("Enter minimum 3 characters");
      } else if (specialChars.test(fname)) {
        setValidFname(false);
        setFnameErr("Enter a valid first name,avoid using 123@$!");
      } else {
        setValidFname(true);
        setFnameErr("");
      }
    } else if (name == "lastName") {
      let lName = event.target.value;
      setLname(lName);
      if (lName.length < 1) {
        setValidLname(false);
        setLnameErr("Enter minimum 1 character");
      } else if (specialChars.test(lName)) {
        setValidLname(false);
        setLnameErr("Enter a valid last name,avoid using 123@$!");
      } else {
        setValidLname(true);
        setLnameErr("");
      }
    } else if (name == "email") {
      let email = event.target.value;

      setEmail(email);

      // Check if the email is valid
      const regex = new RegExp(
        "^.+@((?!hotmail)(?!gmail)(?!ymail)(?!googlemail)(?!live)(?!gmx)(?!yahoo)(?!outlook)(?!msn)(?!icloud)(?!facebook)(?!aol)(?!zoho)(?!mail)(?!yandex)(?!hushmail)(?!lycox)(?!lycosmail)(?!inbox)(?!myway)(?!aim)(?!fastmail)(?!goowy)(?!juno)(?!shortmail)(?!atmail)(?!protonmail)(?!postmaster)(?!abuse)(?!admin)(?!all)(?!everyone)(?!ftp)(?!googlegroups.com)(?!investorrelations)(?!jobs)(?!marketing)(?!media)(?!noc)(?!prime)(?!privacy)(?!remove)(?!request)(?!root)(?!sales)(?!security)(?!spam)(?!subscribe)(?!usenet)(?!users)(?!uucp)(?!webmaster)(?!www)(?!info)(?!enquiries)(?!mail)(?!office)(?!head)(?!headteacher)(?!reception)(?!enquiry)(?!post)(?!contact)(?!email)(?!accounts)(?!london)(?!general)(?!enquires)(?!design)(?!support)(?!mailbox)(?!law)(?!service)(?!reservations)(?!information)(?!schooladmin)(?!secretary)(?!enq)(?!advice)(?!studio)(?!bristol)(?!headoffice)(?!bookings)(?!help)(?!main.department).)+..+$"
      );
      let result = regex.test(email);

      if (result) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        let checkDomain = emailRegex.test(email);

        if (!checkDomain) {
          setEmailErr("Please enter a valid email address");
          setValidEmail(false);
        } else {
          setValidEmail(true);
          setEmailErr("");
        }
      } else {
        setEmailErr("Please enter a valid  email address");
        setValidEmail(false);
      }
    } else if (name == "phoneNumber") {
      let phone = event.target.value;
      setPhone(phone);
      if (phone.length < 10) {
        setValidPhone(false);

        setPhoneErr("Please enter a valid phone number");
      } else if (phone.length > 10) {
        setValidPhone(false);

        setPhoneErr("Please enter a valid 10 digits phone number");
      } else {
        setValidPhone(true);
        setPhoneErr("");
      }
    } else if (name == "password") {
      let password = event.target.value.trim();
      setPassword(password);

      const minLength = 8;
      const hasUppercase = /[A-Z]/.test(password);
      const hasLowercase = /[a-z]/.test(password);
      const hasNumber = /(?=.*[0-9]).*$/.test(password);
      const hasSymbol =
        /^(?=.*[~`!@#$%^&*()--+={}\[\]|\\:;"'<>,.?/_₹]).*$/.test(password);
      const isDictionaryWord =
        /password|123456|qwerty|letmein|monkey|football/.test(password);

      if (password.length < minLength) {
        setValidLength(false);
      } else {
        //alert('hii');
        setValidLength(true);
      }

      if (
        password.length < minLength ||
        !hasUppercase ||
        !hasLowercase ||
        !hasNumber ||
        !hasSymbol
      ) {
        // USED THIS ONLY
        setValidCase(false);
      } else {
        setValidCase(true);
      }

      if (isDictionaryWord) {
        setValidWord(false);
      } else {
        setValidWord(true);
      }

      if (validLength && validCase && validWord) {
        setValidPassword(true);
      }
    } else if (name == "jobtitle") {
      let email = event.target.value;

      setJobtitle(email);
    } else if (name == "linkedin") {
      let email = event.target.value;

      setLinkedin(email);
    } else if (name == "facebook") {
      let email = event.target.value;

      setFacebook(email);
    } else if (name == "twitter") {
      let email = event.target.value;

      setTwitter(email);
    } else if (name == "instagram") {
      let email = event.target.value;

      setInstagra(email);
    } else {
    }
  };
  const companyOnchangeHandler = (event) => {
    const name = event.target.name;
    if (name == "company_name") {
      let email = event.target.value;

      setCompany(email);
    } else if (name == "website_address") {
      let email = event.target.value;

      setWebsite(email);
    } else if (name == "industry") {
      let email = event.target.value;

      setindustry(email);
    } else if (name == "employeesize") {
      let email = event.target.value;

      setEmployeesize(email);
    } else if (name == "revenue") {
      let email = event.target.value;

      setRevenue(email);
    } else if (name == "company_address") {
      let email = event.target.value;

      setCompanyAddres(email);
    } else if (name == "state") {
      let email = event.target.value;

      setState(email);
    } else if (name == "city") {
      let email = event.target.value;

      setCity(email);
    } else if (name == "zipcode") {
      let email = event.target.value;

      setZipcode(email);
    } else {
    }
  };
  const onSubmitHandler = (event) => {
    event.preventDefault();

    const allData = {
      email: email,
      password: password,
      phoneNumber: phone,
      agreement: "true",
      instagram: Instagram,
      twitter: twitter,
      facebook: facebook,
      linkedIn: linkedin,
      jobTitle: jobtitle,
      companyName: companyName,
      websiteAddress: website,
      industry: industry,
      employeeSize: employeeSize,
      revenue: revenue,
      companyAddress: companyAddress,
      state: state,
      city: city,
      zipCode: zipcode,
    };
    let result = AxiosPostBearer(ApiName.profileUpdate, allData, user.token)
      .then(async function (response) {
        if (response.data.status == 200) {
          await PostWithTokenNoCache(ApiName.updateAggrement, {
            aggrement: isChecked ? "true" : "false", firstName: fname,
            lastName: lName
          });
          const user = JSON.parse(localStorage.getItem("user"));

          // Modify the user object
          user.firstName = fname;
          user.lastName = lName;

          // Update the session storage
          localStorage.setItem("user", JSON.stringify(user));

          let cacheKey = `["${ApiName.userProfile}",{}]`;
          removeSessionItem(cacheKey);
          setSuccessMsg(response.data.message);
          setSuccessError(false);
        } else {
          setSuccessError(true);
        }
      })
      .catch(function (errors) {
        if (
          errors.response.data.status == 400 &&
          errors.response.data.message == "Email is already registered"
        ) {
          setButtonType("error");
          setDefaultErrorMsg(errors.response.data.message);
          setDefaultAlert(true);

          setEmailErr(errors.response.data.message);
          setValidEmail(false);
        } else {
          setValidEmail(true);
          setEmailErr("");
        }
      });
  };
  useEffect(() => {
    const creditsUsageDetails = async () => {
      try {
        const res = await PostWithTokenNoCache(ApiName.APICreditUsed, {});
        if (res && "status" in res) {
          if (res.data.status == 200 && "data" in res.data) {
            setUserCreaditsShow(JSON.parse(res.data.data));
          }
        }
      } catch (errors) {
        if (errors.response.data.status == 404) {
          setButtonType("error");
          setDefaultErrorMsg(errors.response.data.message);
          setDefaultAlert(true);
        }
      }
    };
    creditsUsageDetails();
  }, []);

  useEffect(() => {
    const creditsUpdate = async () => {
      try {
        setIsLoading(true);
        const res = await PostWithTokenNoCache(ApiName.activeCreadits, {});
        if (res && "status" in res) {
          if (res.data.status == 200 && "data" in res.data) {
            setUserCreadits(JSON.parse(res.data.data));
          }
        }
        setIsLoading(false);
      } catch (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors.response.data.message);
        setDefaultAlert(true);
        setIsLoading(false);
      }
    };
    creditsUpdate();
  }, []);

  const generateApiKey = async () => {
    setIsCreateApiKey(true);
    // You can await here
    const res = await PostWithTokenNoCache(ApiName.createApiKey);
    if (res && "status" in res) {
      if (res.data.status == 200 && "data" in res.data) {
        let jsonObj = JSON.parse(res.data.data);
        if (jsonObj) {
          setApiKey(jsonObj.api_key);
          setIsCreateApiKey(false);
          setDisplayCreditUsed(true);
        }
      }
    }
  };

  const displayCreditUsedCount = () => {
    setDisplayCreditUsed(false);
  };
  const copyText = () => {
    const textToCopy = textAreaRef.current.textContent;
    // Create a temporary textarea element to copy the text to the clipboard
    const tempTextArea = document.createElement("textarea");
    tempTextArea.value = textToCopy;
    document.body.appendChild(tempTextArea);
    tempTextArea.select();
    document.execCommand("copy");
    document.body.removeChild(tempTextArea);
    setCopiedCode(true);
    setTimeout(() => {
      setCopiedCode(false);
    }, 5000);
  };

  const goBack = () => {
    const loaderContactTable = document.querySelector("#cust-account-details");
    if (loaderContactTable) {
      loaderContactTable.style.display = "none";
    }

    if (selectedTab == "contact") {
      navigate("/dashboard");
    } else {
      navigate("/company-filters");
    }
  };

  const applyAppSumoCoupon = async () => {
    const timestamp = Date.now();
    const params = {
      total_balance_credit: parseInt(userCreadits.total_balance_credit) + 9000,
      total_balance_contact_view: 0,
      additional_credit_assigned: 9000,
      additional_contact_view_assigned: "Unlimited",
      promocode: promocode,
      additional_credit_assigned_at: timestamp,
      user_email: user.email,
    };

    try {
      const res = await PostWithTokenNoCache(ApiName.updateCredit, params);
      if (res?.status === 200) {
        setIsSuccessCoupon(true);
        // setIsSuccessCouponMsg(res.data.message);
        setIsSuccessCouponMsg(
          "Congratulations! You have unlocked new benefits"
        );

        const response = await PostWithTokenNoCache(ApiName.activeCreadits, {});
        if (response && "status" in response) {
          //console.log(JSON.parse(res.data.data));
          if (response.data.status == 200 && "data" in response.data) {
            setUserCreadits(JSON.parse(response.data.data));
          }
        }
        setTimeout(() => {
          setPromocode("");
          setIsSuccessCoupon(null);
          setIsSuccessCouponMsg(null);
        }, 3000);
      } else {
        setTimeout(() => {
          setPromocode("");
          setIsSuccessCoupon(null);
          setIsSuccessCouponMsg(null);
        }, 3000);
      }
    } catch (error) {
      if (error.response.status == 404) {
        setIsSuccessCoupon(false);
        setIsSuccessCouponMsg(error.response.data.message);
      }
      if (error.response.status == 403) {
        setIsSuccessCoupon(false);
        setIsSuccessCouponMsg("You have already claimed benefits.!");
      }
      setTimeout(() => {
        setPromocode("");
        setIsSuccessCoupon(null);
        setIsSuccessCouponMsg(null);
      }, 3000);
    }
  };

  const changeAggreement = (event) => {
    setIsChecked(event.target.checked);
  };

  const clearSessionStorage = async () => {
    return new Promise((resolve) => {
      sessionStorage.clear();
      resolve();
    });
  };

  const logout = () => {

    sessionStorage.removeItem("jobDepartments");
    sessionStorage.removeItem("jobFunction");

    dataDC.jobTitle = {}; // Set jobTitle to an empty object

    dataDC.jobLevel = {}; // Set jobTitle to an empty object

    dataDC.DeptFunc = {}; // Set jobTitle to an empty object

    if ("company_employee_size" in dataDC && dataDC.company_employee_size !== undefined) {
      dataDC.company_employee_size = []; // Set company_employee_size to an empty object
    }

    if ("sic_code" in dataDC && dataDC.sic_code !== undefined) {
      dataDC.sic_code = []; // Set sic_code to an empty object
    }

    if ("company_annual_revenue_amount" in dataDC && dataDC.company_annual_revenue_amount !== undefined) {
      dataDC.company_annual_revenue_amount = []; // Set company_annual_revenue_amount to an empty object
    }

    setCheckedItems({});
    setCheckedBoxes([]);
    setSelectedValues([]);

    setSelectedOptions(null);
    setSicCode([]);
    setIndustryData([]);

    setSelectedCompanyAddressCountry({});
    setSelectedCompanyAddressState({});
    setSelectedCompanyAddressCity({});
    setSelectedCompanyZipCode({});
    setSelectedContactJobTitle1({});
    setSelectedContactJobTitleLevel1({});
    setSelectedContactJobDeptName1({});
    setSelectedContactJobFunctionName1({});
    setSelectedCompanyEmployeeSize({});
    setSelectedCompanyAnnualRevenueAmount({});
    setSelectedCompanyTechKeyWordsList({});
    setSelectedCompany({});
    setSortingBy({});
    setDefaultError({});
    setNumber({});
    setSearchPattern({});
    setCheckboxesGlobal([]);
    setSelectedData([]);
    setAllRecordsNew('');
    setSelectedRows([]);
    setSampleDownloadedIds('');
    setLoadedData([]);
    setViewModal(false);
    setViewSelected(false);
    setViewModal(false);
    setLoadedData([]);
    setResetPage(true);
    setShowDownloadedData(true);
    setWithDownloaded(true);
    setCompanyTypeCheckedBoxes([]);
    setSelectedCompanyType({});
    setSelectedCompanyURL({});

    if (localStorage.getItem("signin-type") == "microsoft") {
      Microsoftlogout();
    }
    PostWithTokenNoCache(ApiName.deviceLogout, {}).then((res) => {
      if (res && "status" in res) {
        if (res.data.status == 200) {
          clearSessionStorage();
          navigate("/");
        }
      }
    });
  };

  const deactivateAccount = (e) => {
    e.preventDefault();
    setDeactivateProfile(true);
    setIsPopupOpen(true);
  };
  const onCloseDeactivate = () => {
    setIsPopupOpen(false);
  };
  const onDeactivateSuccess = async () => {
    setIsPopupOpen(false);
    let params = {
      method: "POST",
    };
    try {
      const res = await PostWithTokenNoCache(
        ApiName.deactivateSubscriptions,
        params
      );

      if (res?.status === 200) {
        logout();
      }
    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
    }
  };

  const getCreditValues = (value) => {
    const totalAssignedCredit = userCreadits.total_assigned_credit
      ? parseInt(userCreadits.total_assigned_credit)
      : 0;
    const totalBalanceCredit = userCreadits.total_balance_credit
      ? parseInt(userCreadits.total_balance_credit)
      : 0;
    const assignedContactView = userCreadits.total_assigned_contact_view
      ? parseInt(userCreadits.total_assigned_contact_view)
      : 0;
    const balanceCotactView = userCreadits.total_balance_contact_view
      ? parseInt(userCreadits.total_balance_contact_view)
      : 0;
    const additional_credit_assigned = userCreadits.additional_credit_assigned
      ? parseInt(userCreadits.additional_credit_assigned)
      : 0;
    let point;
    switch (value) {
      case "total-download":
        point =
          additional_credit_assigned + totalAssignedCredit > totalBalanceCredit
            ? additional_credit_assigned +
            totalAssignedCredit -
            totalBalanceCredit
            : totalBalanceCredit -
            (additional_credit_assigned + totalAssignedCredit);
        break;
      case "remaining-download":
        point = userCreadits.total_balance_credit
          ? parseInt(userCreadits.total_balance_credit)
          : 0;
        break;
      case "total-viewd":
        point =
          userCreadits.additional_contact_view_assigned == "Unlimited"
            ? userCreadits.total_balance_contact_view
            : userCreadits.total_assigned_contact_view == "Unlimited"
              ? userCreadits.total_balance_contact_view
              : assignedContactView - balanceCotactView;
        break;
      case "remaining-view":
        point =
          userCreadits.additional_contact_view_assigned == "Unlimited"
            ? "Unlimited"
            : userCreadits.total_assigned_contact_view == "Unlimited"
              ? "Unlimited"
              : parseInt(userCreadits.total_balance_contact_view);
        break;
    }
    return point;
  };
  return (
    <>
      {isAuth ? (
        <div>
          <Header />

          <div style={{ display: "none" }}>
            <LeftNavbar></LeftNavbar>{" "}
          </div>
          {!isLoading ? (
            <div className="" id="cust-account-details">
              <div
                className="modal fade"
                id="exampleModalLong"
                tabIndex="-1"
                role="dialog"
                aria-labelledby="exampleModalLongTitle"
                aria-hidden="true"
              >
                <div className="-my-modal-dialog modal-dialog" role="document">
                  <div className="modal-content">
                    <div className="my-modal-header">
                      <button
                        type="button"
                        className="close"
                        data-dismiss="modal"
                        aria-label="Close"
                      >
                        <span aria-hidden="true">&times;&nbsp;</span>
                      </button>
                      <h5 className="modal-title" id="exampleModalLongTitle">
                        Change Your Profile Picture
                      </h5>
                    </div>
                    <div className="modal-body">
                      <div className="d-flex flex-row justify-content-center">
                        <div className="cursor">
                          {selectedFile ? (
                            <img
                              src={URL.createObjectURL(selectedFile)}
                              alt="Preview"
                              className="img-fluid rounded-image"
                              style={{
                                padding: "4px 4px 4px 4px",
                                width: "106px",
                                height: "106px",
                                opacity: profilePicture ? null : "0.5",
                                objectFit: "scale-down", // 🔥 Important line
                                borderRadius: "58px", // optional
                              }}
                            />
                          ) : (
                            <img
                              src={
                                profilePicture
                                  ? profilePicture
                                  : "../images/avetar.png"
                              }
                              className="img-fluid"
                              style={{

                                padding: "4px 4px 4px 4px",
                                width: "106px",
                                height: "106px",
                                opacity: profilePicture ? null : "0.5",
                                objectFit: "scale-down", // 🔥 Important line
                                borderRadius: "58px", // optional
                              }}
                            />
                          )}
                        </div>

                        <div className="small-up">
                          <p className="upload-text" onClick={handleClick}>
                            <span className="upld-img">
                              <img src="./images/upload.png" width="17px" />
                            </span>
                            Upload Picture
                          </p>
                          <input
                            type="file"
                            accept=".jpg,.jpeg,.png"
                            ref={inputRef}
                            style={{ display: "none" }}
                            onChange={handleFileChange}
                          />
                          <p
                            className={`disabled-paragraph ${!profilePicture ? "disabled" : ""
                              }`}
                            onClick={handleSelectedRemove}
                          >
                            <span className="upld-img">
                              <img
                                className={`${!profilePicture ? "dim-image" : ""
                                  }`}
                                src="./images/delete v.png"
                                width="14px"
                              />
                            </span>
                            Remove Picture
                          </p>
                        </div>
                      </div>

                      {uploadStatusMessage && (
                        <div
                          className={
                            uploadStatusMessage == "File uploaded"
                              ? "highlight-blue-gray-shade"
                              : "highlight-red"
                          }
                        >
                          {uploadStatusMessage == "File uploaded"
                            ? "Profile picture uploaded successfully"
                            : uploadStatusMessage}
                        </div>
                      )}
                      {isLoading && (
                        <div className="highlight-blue-gray-shade">
                          {"Loading..."}
                        </div>
                      )}
                      <div className="save">
                        <button
                          type="submit"
                          className={`${isParaDisabled ? "disabledBtn" : "enabledBtn"
                            }`}
                          disabled={isButtonDisabled}
                          onClick={handlePictureSave}
                        >
                          {isButtonDisabled ? "Save" : "Save"}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="row">
                <div className="account-details-page">
                  <div className="arrow d-flex flex-row justify-content-between">
                    <div>
                      <div className="d-flex flex-row">
                        <div>
                          <span onClick={goBack}>
                            <Link to="#">
                              <img src="./images/arrow-left.png" />
                            </Link>
                          </span>
                        </div>
                        <div>
                          <h3>Account Details</h3>
                        </div>
                      </div>
                    </div>
                    <div className="mt-2">
                      {userCreadits.user_plan_name == "APP_SUMO_59" && (
                        <Link
                          to="#"
                          data-toggle="modal"
                          data-target="#exampleModal"
                          data-whatever="@fat"
                          className="unlock"
                        >
                          Unlock additional benefits
                        </Link>
                      )}
                    </div>
                  </div>
                  {/* modal start */}
                  <div
                    class="modal fade"
                    id="exampleModal"
                    tabindex="-1"
                    role="dialog"
                    aria-labelledby="exampleModalLabel"
                    aria-hidden="true"
                  >
                    <div class="modal-dialog" role="document">
                      <div class="modal-content">
                        <div class="modal-header">
                          <h5 class="modal-title" id="exampleModalLabel">
                            Unlock additional benefits
                          </h5>
                          <button
                            type="button"
                            class="close"
                            data-dismiss="modal"
                            aria-label="Close"
                          >
                            <span aria-hidden="true">&times;</span>
                          </button>
                        </div>
                        <div class="modal-body">
                          <form>
                            <div class="form-group">
                              <label for="message-text" class="col-form-label">
                                Coupon:
                              </label>
                              <input
                                type="text"
                                value={promocode}
                                onChange={(e) => setPromocode(e.target.value)}
                                className="form-control"
                                id="message-text"
                              />
                            </div>
                          </form>
                        </div>
                        <div class="modal-footer">
                          {isSuccessCoupon && isSuccessCoupon == true ? (
                            <div className="alert alert-success success">
                              {isSuccessCouponMsg}
                            </div>
                          ) : (
                            <></>
                          )}
                          {!isSuccessCoupon && isSuccessCoupon == false ? (
                            <div className="alert alert-danger Danger">
                              {isSuccessCouponMsg}
                            </div>
                          ) : (
                            <></>
                          )}
                          <button
                            type="button"
                            onClick={applyAppSumoCoupon}
                            class="apply-butto"
                          >
                            Apply
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* modal end  */}
                  <div className="sticky-container">
                    <div className="sticky-box">
                      <div className="primary-square ">
                        <div className="secondary-square ">
                          <div class="row">
                            <div class="col-sm">
                              <div className="d-flex flex-row ">
                                <div
                                  className=" upload-profile ml-3"
                                  data-toggle="modal"
                                  data-target="#exampleModalLong"
                                >
                                  {profilePicture ? (
                                    <img
                                      src={profilePicture}
                                      alt="Preview"
                                      className="pro rounded-image"
                                      style={{
                                        width: "106px",
                                        height: "106px",
                                      }}
                                    />
                                  ) : (
                                    <img
                                      src="../images/avetar.png"
                                      className="pro rounded-image"
                                      style={{
                                        width: "106px",
                                        height: "106px",
                                        opacity: "0.5",
                                        objectFit: "cover",
                                      }}
                                    />
                                  )}
                                  {/* change profile pic profilePicture*/}
                                  <img
                                    src="../images/pen.png"
                                    className="profiles"
                                  />
                                </div>

                                <div className=" Ken-Hogan">
                                  <h3>
                                    {"firstName" in user
                                      ? user.firstName + " "
                                      : "Loading..." + " "}
                                    {"lastName" in user ? user.lastName : ""}
                                  </h3>
                                  <p>
                                    {"email" in user
                                      ? user.email
                                      : "Loading..."}
                                  </p>
                                </div>

                                <div class="main-card">
                                  <div class="section downloads">
                                    <h3>Downloads</h3>
                                    <div class="sub-card">
                                      <div style={{ borderRight: "1px solid #00000016" }}>
                                        <p className="colored-number">
                                          {typeof getCreditValues("total-download") === "number"
                                            ? getCreditValues("total-download").toLocaleString("en-IN")
                                            : getCreditValues("total-download")}
                                        </p>
                                        <p className="TotalDownloads">Total Downloads</p>
                                      </div>
                                      <div>
                                        <p className="number-highlight">
                                          {typeof getCreditValues("remaining-download") === "number"
                                            ? getCreditValues("remaining-download").toLocaleString("en-IN")
                                            : getCreditValues("remaining-download")}
                                        </p>
                                        <p className="TotalDownloads">Remaining Credits</p>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="section views">
                                    <h3>Views</h3>
                                    <div className="stats">
                                      {/* Total Views */}
                                      <div style={{ borderRight: "1px solid #00000016" }}>
                                        <p className="colored-number">
                                          {userCreadits?.user_plan_name === "freemium"
                                            ? formatCreditValue(userCreadits?.total_assigned_email_credits)
                                            : formatViewValue(getCreditValues("total-viewd"), true)}
                                        </p>
                                        <p className="TotalDownloads">Total Views</p>
                                      </div>

                                      {/* Remaining Views */}
                                      <div>
                                        <p className="number-highlight">
                                          {userCreadits?.user_plan_name === "freemium"
                                            ? formatCreditValue(userCreadits?.total_balance_email_credits)
                                            : formatViewValue(getCreditValues("remaining-view"), false)}
                                        </p>
                                        <p className="TotalDownloads">Remaining Views</p>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="button-container">
                                    <button class="WantMoreCredits" data-toggle="modal"
                                      data-target="#contactUsModalCenter">Want More Credits?</button>
                                  </div>
                                </div>



                                {/* <div className="download">
                                  <div className="row">
                                    <div className="col-md-9 pr-0">
                                      <div className="grid">
                                        <div className="one">
                                          <div className="mbnm">
                                            <p className="downloadnewtext">
                                              Downloads
                                            </p>
                                          </div>
                                        </div>
                                        <div className="two">
                                          <div className="mbnm">
                                            <p className="viewsnewtext">Views</p>
                                          </div>
                                        </div>
                                      </div>

                                      <div className="grid">
                                        <div className="one-one">
                                          <div className="dkfjl">
                                            <p className="twothousand">
                                              {getCreditValues("total-download")}
                                            </p>
                                            <p className="TotalDownloads">
                                              Total Downloads
                                            </p>
                                          </div>
                                        </div>
                                        <div className="two-two">
                                          <div className="dkfjl">
                                            <p className="RemainingCredits">
                                              {getCreditValues(
                                                "remaining-download"
                                              )}
                                            </p>
                                            <p className="TotalDownloads">
                                              Remaining Credits
                                            </p>
                                          </div>
                                        </div>

                                        <div className="three-three">
                                          <div className="dkfjl">
                                            <p className="twothousand">
                                              {getCreditValues("total-viewd")}
                                            </p>
                                            <p className="TotalDownloads">
                                              Total Views
                                            </p>
                                          </div>

                                        </div>

                                        <div className="four-four">
                                          <div className="dkfjl">
                                            <p className="RemainingCredits">
                                              {getCreditValues("remaining-view")}
                                            </p>
                                            <p className="TotalDownloads">
                                              Remaining Views
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="col-md-3 pl-0 text-center">

                                      <button
                                        href="#"
                                        className="WantMoreCredits?"
                                        data-toggle="modal"
                                        data-target="#contactUsModalCenter"
                                      >
                                        Want More Credits?
                                      </button>

                                    </div>
                                  </div>
                                  <div className="row">
                                    <div className="">
                                    </div>
                                  </div>
                                </div> */}
                              </div>
                            </div>
                          </div>
                          <div className="d-flex flex-row">
                            <div style={{ width: "308px" }}></div>
                            <div className="accountdetailspagewidth">
                              <div className="api">
                                <h2>API</h2>
                              </div>
                              <div className="Totals-1">
                                <div className="availables">
                                  <span
                                    className="creits-sell"
                                    onClick={displayCreditUsedCount}
                                  >
                                    <div className="d-flex flex-row justify-content-between">
                                      <div>
                                        <span className="creits-used">
                                          Credits Used
                                        </span>
                                      </div>
                                      <div>
                                        <h1 className="user-count">
                                          {userCreditsShow.api_credit_usage
                                            ? userCreditsShow.api_credit_usage
                                            : 0}
                                        </h1>
                                      </div>
                                    </div>
                                  </span>
                                </div>
                              </div>
                            </div>

                            <div className="accountdetailspagewidth2">
                              <div className="copy-api">
                                <div className="row">
                                  <div className="col-md-2">
                                    <span className="api-name">API Key: </span>
                                  </div>
                                  <div className="col-md-7 p-0 ">
                                    <div className="generated-color">
                                      <div className="d-flex flex-row justify-content-between">
                                        <div className="">
                                          <p
                                            className="generated-key"
                                            ref={textAreaRef}
                                          >
                                            {apiKey}
                                          </p>
                                        </div>
                                        <div className=" ">
                                          <span
                                            className="copied-api text-left"
                                            onClick={copyText}
                                          >
                                            <img
                                              src="./images/copy-icon.png"
                                              alt="Copy"
                                              width="10"
                                            />
                                            &nbsp;&nbsp;&nbsp;Copy
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="col-sm p-0">
                                    <button
                                      type="button"
                                      onClick={generateApiKey}
                                      className="Regenerate"
                                    >
                                      {apiKey ? 'Regenerate' : "Generate"}
                                    </button>
                                  </div>
                                </div>
                              </div>
                              {copiedCode ? (
                                <div className="row">
                                  <div className="col-5"></div>
                                  <div className="col-5">
                                    <p className="successmessage3">
                                      <img src="./images/promocode-success.png" />
                                      Copied Successfully
                                    </p>
                                  </div>
                                  <div className="col-3"></div>
                                </div>
                              ) : null}
                            </div>
                            <div>
                              <button onClick={toggleWebHookModal} type="submit" className="webook" data-toggle="modal" data-target="#exampleModalCenter">
                                <span className="api-plus">+</span> Add Webhook</button>
                              {isWebhookModalOpen && <WebhookModal
                                closeModal={toggleWebHookModal}
                                setIsWebhookModalOpen={setIsWebhookModalOpen}
                              />}
                              <div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-12 text-center">
                      <div className="godown">
                        {!hideGif ? (
                          <img
                            src="./images/Scroll-Animation.gif"
                            width="40px"
                            style={{ cursor: "pointer" }}
                            onClick={handleScrollDown}
                          />
                        ) : (
                          ""
                        )}
                        {/* <a  href="#v-pills-home"></a> <img src="./images/Scroll-Animation.gif" width="40" /> */}
                      </div>
                    </div>
                    <div
                      ref={scrollableElementRef}
                      className="scrollable-content"

                      onScroll={handleScroll}
                    >
                      <div className="content-aria">
                        <div className="personal-information">
                          <h4>Personal Information</h4>
                          <span className="horizontal3"></span>
                        </div>

                        <div>
                          <form>
                            <div className="row">
                              <div className="col-sm-4">
                                <div className="form-group">
                                  <label htmlFor="firstName" className="label">
                                    First Name
                                  </label>
                                  <input
                                    type="text"
                                    value={fname}
                                    className="form-control"
                                    id="firstName"
                                    aria-describedby="firstnameHelp"
                                    name="firstName"
                                    onChange={onChageHandler}
                                  />
                                  {!validFname ? (
                                    <span className="signup-errors">
                                      {fnameErr && <p>{fnameErr}</p>}
                                    </span>
                                  ) : (
                                    <></>
                                  )}
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-sm-4">
                                <div className="form-group">
                                  <label htmlFor="lastName" className="label">
                                    Last Name
                                  </label>
                                  <input
                                    type="text"
                                    value={lName}
                                    className="form-control"
                                    id="lastname"
                                    aria-describedby="lastnameHelp"
                                    name="lastName"
                                    onChange={onChageHandler}
                                  />
                                  {!validLname ? (
                                    <span className="signup-errors">
                                      {lnameErr && <p>{lnameErr}</p>}
                                    </span>
                                  ) : (
                                    <></>
                                  )}
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-sm-4">
                                <div className="form-group">
                                  <label
                                    htmlFor="exampleInputEmail21"
                                    className="label"
                                  >
                                    Email
                                  </label>
                                  <input
                                    type="email"
                                    value={email}
                                    className="form-control"
                                    id="exampleInputEmail21"
                                    aria-describedby="emailHelp"
                                    name="email"
                                    onChange={onChageHandler}
                                    readOnly
                                  />
                                  <small
                                    id="emailHelp"
                                    className="form-text text-muted"
                                  ></small>
                                  {!validEmail ? (
                                    <span className="signup-errors">
                                      {emailErr && <p>{emailErr}</p>}
                                    </span>
                                  ) : (
                                    <></>
                                  )}
                                </div>
                              </div>
                              <div className="col-sm-4">
                                <div className="form-group">
                                  <label htmlFor="phone" className="label">
                                    Phone Number
                                  </label>
                                  <input
                                    type="number"
                                    value={phone}
                                    className="form-control"
                                    id="phone"
                                    aria-describedby="phoneHelp"
                                    name="phoneNumber"
                                    onChange={onChageHandler}
                                  />
                                  {!validPhone ? (
                                    <span className="signup-errors">
                                      {phoneErr && <p>{phoneErr}</p>}
                                    </span>
                                  ) : (
                                    <></>
                                  )}
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-sm-4">
                                <div className="form-group">
                                  <label htmlFor="Password" className="label">
                                    Password
                                  </label>
                                  <input
                                    type="password"
                                    value="********"
                                    className="form-control"
                                    id="Password"
                                    name="password"
                                    onChange={onChageHandler}
                                    readOnly
                                  />

                                  {password && !validCase ? (
                                    <span className="errr">
                                      <p>
                                        Password must be at least 8 charcters
                                        long, should contain at least one
                                        number, one special character, one upper
                                        case & lower case letter{" "}
                                      </p>
                                    </span>
                                  ) : (
                                    <></>
                                  )}
                                </div>

                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-sm-4">
                                <div className="form-group">
                                  <label htmlFor="Jobtitle" className="label">
                                    Job Title
                                  </label>
                                  <input
                                    type="text"
                                    value={jobtitle}
                                    className="form-control"
                                    id="Jobtitle"
                                    aria-describedby="Jobtitle"
                                    name="jobtitle"
                                    onChange={onChageHandler}
                                  />
                                </div>
                              </div>
                              <div className="col-sm-4">
                                <div className="form-group">
                                  <label htmlFor="linkedin" className="label">
                                    LinkedIn ID
                                  </label>
                                  <div className="input-container">
                                    <img
                                      src="./images/linkedin.png"
                                      alt="Image"
                                    />
                                    <input
                                      type="link"
                                      value={linkedin}
                                      className="form-control linked-in"
                                      id="linkedin"
                                      name="linkedin"
                                      onChange={onChageHandler}
                                    />
                                  </div>
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label htmlFor="facebook" className="label">
                                    Facebook ID
                                  </label>
                                  <div className="input-container">
                                    <img
                                      src="./images/facebook.png"
                                      alt="Image"
                                    />
                                    <input
                                      type="link"
                                      value={facebook}
                                      className="form-control"
                                      id="facebook"
                                      name="facebook"
                                      onChange={onChageHandler}
                                    />
                                  </div>
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label htmlFor="twitter" className="label">
                                    Twitter ID
                                  </label>
                                  <div className="input-container">
                                    <img
                                      src="./images/new-blue-twitter-img.png"
                                      alt="Image"
                                    />
                                    <input
                                      type="link"
                                      value={twitter}
                                      className="form-control"
                                      id="twitter"
                                      name="twitter"
                                      onChange={onChageHandler}
                                    />
                                  </div>
                                </div>
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label htmlFor="instagram" className="label">
                                    Instagram ID
                                  </label>
                                  <div className="input-container">
                                    <img
                                      src="./images/instagram.png"
                                      alt="Image"
                                    />
                                    <input
                                      type="link"
                                      value={Instagram}
                                      className="form-control"
                                      id="instagram"
                                      name="instagram"
                                      onChange={onChageHandler}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* company information starts from here */}

                            <div
                              className="personal-information"
                              ref={containerRef}
                            >
                              <h4>Company Information</h4>
                              <hr className="horizontal2" />
                            </div>

                            <div className="row">
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label
                                    htmlFor="company_name"
                                    className="label"
                                  >
                                    Company Name
                                  </label>
                                  <input
                                    type="text"
                                    value={companyName}
                                    className="form-control"
                                    id="company_Name"
                                    name="company_name"
                                    onChange={companyOnchangeHandler}
                                  />
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label
                                    htmlFor="website_address"
                                    className="label"
                                  >
                                    Website Address
                                  </label>
                                  <input
                                    type="text"
                                    value={website}
                                    className="form-control"
                                    id="website_address"
                                    name="website_address"
                                    onChange={companyOnchangeHandler}
                                  />
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label htmlFor="industry" className="label">
                                    Industry
                                  </label>
                                  <input
                                    type="email"
                                    value={industry}
                                    className="form-control"
                                    id="Industry"
                                    name="industry"
                                    onChange={companyOnchangeHandler}
                                  />
                                  <small
                                    id="emailHelp"
                                    className="form-text text-muted"
                                  ></small>
                                </div>
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label
                                    htmlFor="employee-size"
                                    className="label"
                                  >
                                    Employee Size
                                  </label>
                                  <input
                                    type="text"
                                    value={employeeSize}
                                    className="form-control"
                                    id="employee-size"
                                    name="employeesize"
                                    onChange={companyOnchangeHandler}
                                  />
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>

                              <div className="col-md-4">
                                <div className="form-group">
                                  <label htmlFor="revenue" className="label">
                                    Revenue
                                  </label>
                                  <input
                                    type="text"
                                    value={revenue}
                                    className="form-control"
                                    id="revenue"
                                    name="revenue"
                                    onChange={companyOnchangeHandler}
                                  />
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label
                                    htmlFor="company_address"
                                    className="label"
                                  >
                                    Company Address
                                  </label>
                                  <input
                                    type="text"
                                    value={companyAddress}
                                    className="form-control"
                                    id="company_address"
                                    name="company_address"
                                    onChange={companyOnchangeHandler}
                                  />
                                  <small
                                    id="emailHelp"
                                    className="form-text text-muted"
                                  ></small>
                                </div>
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label htmlFor="state" className="label">
                                    State
                                  </label>
                                  <input
                                    type="text"
                                    value={state}
                                    className="form-control"
                                    id="state"
                                    name="state"
                                    onChange={companyOnchangeHandler}
                                  />
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label htmlFor="city" className="label">
                                    City
                                  </label>
                                  <input
                                    type="text"
                                    value={city}
                                    className="form-control"
                                    id="city"
                                    name="city"
                                    onChange={companyOnchangeHandler}
                                  />
                                </div>
                                {/* <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div> */}
                              </div>
                              <div className="col-md-4">
                                <div className="form-group">
                                  <label htmlFor="zip_code" className="label">
                                    Zip Code
                                  </label>
                                  <input
                                    type="text"
                                    value={zipcode}
                                    className="form-control"
                                    id="zip_code"
                                    name="zipcode"
                                    onChange={companyOnchangeHandler}
                                  />
                                  <small
                                    id="emailHelp"
                                    className="form-text text-muted"
                                  ></small>
                                </div>
                              </div>
                            </div>

                            <div className="form-check">
                              <div className="d-flex flex-row">
                                <div className="d-flex flex-column mt-1">
                                  <input
                                    type="checkbox"
                                    className="form-check-input"
                                    checked={isChecked}
                                    onChange={changeAggreement}
                                  />
                                </div>
                                <div className="d-flex flex-column ml-2">
                                  <label
                                    className="form-check-label"
                                    htmlFor="exampleCheck12"
                                  >
                                    I would like to receive newsletters and
                                    promotional updates from ReachStream.
                                  </label>
                                </div>
                              </div>
                            </div>
                            {!successError ? (
                              <span className="signup-errors-b">
                                {successMsg && <p>{successMsg}</p>}
                              </span>
                            ) : (
                              <></>
                            )}
                            <div className="d-flex flex-row justify-content-between">
                              <div className="update-&-save">
                                <button onClick={onSubmitHandler}>
                                  Update & Save Changes
                                </button>
                              </div>
                              <div>
                                <button
                                  type="button"
                                  className="deactivate"
                                  onClick={deactivateAccount}
                                >
                                  Cancel Subscription
                                </button>
                              </div>
                            </div>
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                {/* </div> */}
              </div>
            </div>
          ) : (
            <div className="d-flex flex-row justify-content-center mt-5" style={{ display: isLoading }}>
              <img src={penguineLoadingGif} alt="Loading" className="loader" width="400" />
            </div>
          )}
          <ContactUsPopup />
          {isPopupOpen && (
            <Popup
              onCloseDeactivate={onCloseDeactivate}
              onDeactivateSuccess={onDeactivateSuccess}
            />
          )}
        </div>
      ) : (
        <></>
      )}
      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </>
  );
};

export default Account_details;

const Popup = ({ onCloseDeactivate, onDeactivateSuccess }) => {
  return (
    <div
      class="modal"
      style={{
        display: "block",
        important: "true",
        backgroundColor: "#0000007d",
      }}
      id="exampleModalCenter"
      tabindex="-1"
      role="dialog"
      aria-labelledby="exampleModalCenterTitle"
    >
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div
            class="modal-header"
            style={{ borderBottom: "0", padding: "6px 11px 0 0px" }}
          >
            <h5 class="modal-title" id="exampleModalLongTitle"></h5>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              onClick={onCloseDeactivate}
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" style={{ borderBottom: "0", padding: "0" }}>
            <p className="areyousure">
              Are you sure you want to cancel<br />your subscription?
            </p>

            <div className="d-flex flex-row justify-content-center">
              <div className="mr-5">
                <button
                  type="submit"
                  onClick={onDeactivateSuccess}
                  className="yesno"
                >
                  Yes
                </button>
              </div>
              <div>
                <button
                  type="submit"
                  onClick={onCloseDeactivate}
                  className="no"
                >
                  No
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
