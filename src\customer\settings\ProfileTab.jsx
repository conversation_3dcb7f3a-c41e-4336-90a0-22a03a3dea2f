import React, { useState, useEffect } from "react";
import PropTypes from 'prop-types';
import { AxiosPostBearer, PostWithTokenNoCache } from "../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../customer/common-files/ApiNames.js";

const ProfileTab = ({ profilePicture }) => {
    const [user, setUser] = useState(JSON.parse(localStorage.getItem("user")));
    const [fname, setFname] = useState("");
    const [lName, setLname] = useState("");
    const [email, setEmail] = useState("");
    const [phone, setPhone] = useState("");
    const [jobtitle, setJobtitle] = useState("");
    const [linkedin, setLinkedin] = useState("");
    const [facebook, setFacebook] = useState("");
    const [twitter, setTwitter] = useState("");
    const [Instagram, setInstagra] = useState("");
    const [companyName, setCompany] = useState("");
    const [website, setWebsite] = useState("");
    const [industry, setindustry] = useState("");
    const [employeeSize, setEmployeesize] = useState("");
    const [revenue, setRevenue] = useState("");
    const [companyAddress, setCompanyAddres] = useState("");
    const [state, setState] = useState("");
    const [city, setCity] = useState("");
    const [zipcode, setZipcode] = useState("");
    const [isChecked, setIsChecked] = useState(false);
    const [profileUpdated, setProfileUpdated] = useState("");

    // Fetch user profile data
    useEffect(() => {
        const getUserProfile = async (data) => {
            const url = ApiName.userProfile;
            try {
                const res = await PostWithTokenNoCache(url, data);
                if (res && "status" in res) {
                    if (res.data.status == 200 && "data" in res.data) {
                        let jsonObj = JSON.parse(res.data.data);
                        if (jsonObj) {
                            setCity(jsonObj.city ? jsonObj.city : "");
                            setCompany(jsonObj.companyName ? jsonObj.companyName : "");
                            setCompanyAddres(jsonObj.companyAddress ? jsonObj.companyAddress : "");
                            setEmployeesize(jsonObj.employeeSize ? jsonObj.employeeSize : "");
                            setFacebook(jsonObj.facebook ? jsonObj.facebook : "");
                            setInstagra(jsonObj.instagram ? jsonObj.instagram : "");
                            setJobtitle(jsonObj.jobTitle ? jsonObj.jobTitle : "");
                            setLinkedin(jsonObj.linkedIn ? jsonObj.linkedIn : "");
                            setRevenue(jsonObj.revenue ? jsonObj.revenue : "");
                            setState(jsonObj.state ? jsonObj.state : "");
                            setTwitter(jsonObj.twitter ? jsonObj.twitter : "");
                            setWebsite(jsonObj.websiteAddress ? jsonObj.websiteAddress : "");
                            setZipcode(jsonObj.zipCode ? jsonObj.zipCode : "");
                            setindustry(jsonObj.industry ? jsonObj.industry : "");
                            setIsChecked(jsonObj.aggrement === "true");
                        }
                    }
                }
            } catch (errors) {
                console.error("Error fetching user profile:", errors);
            }
        };

        if (user) {
            if ("firstName" in user) setFname(user.firstName);
            if ("lastName" in user) setLname(user.lastName);
            if ("email" in user) setEmail(user.email);
            if ("phone" in user) setPhone(user.phone);
        }

        getUserProfile({});
    }, []);

    // Handle form submission
    const onSubmitHandler = (event) => {
        event.preventDefault();

        const allData = {
            firstName: fname,
            lastName: lName,
            email: email,
            phoneNumber: phone,
            agreement: isChecked ? "true" : "false",
            instagram: Instagram,
            twitter: twitter,
            facebook: facebook,
            linkedIn: linkedin,
            jobTitle: jobtitle,
            companyName: companyName,
            websiteAddress: website,
            industry: industry,
            employeeSize: employeeSize,
            revenue: revenue,
            companyAddress: companyAddress,
            state: state,
            city: city,
            zipCode: zipcode,
        };

        AxiosPostBearer(ApiName.profileUpdate, allData, user.token)
            .then(async function (response) {
                if (response.data.status == 200) {
                    await PostWithTokenNoCache(ApiName.updateAggrement, {
                        aggrement: isChecked ? "true" : "false",
                        firstName: fname,
                        lastName: lName
                    });

                    const user = JSON.parse(localStorage.getItem("user"));
                    user.firstName = fname;
                    user.lastName = lName;
                    localStorage.setItem("user", JSON.stringify(user));

                    setProfileUpdated("Profile updated successfully!");
                    setTimeout(() => setProfileUpdated(""), 3000);
                } else {
                    setProfileUpdated("Error updating profile. Please try again.");
                    setTimeout(() => setProfileUpdated(""), 3000);
                }
            })
            .catch(function (errors) {
                setProfileUpdated("Error updating profile. Please try again.");
                setTimeout(() => setProfileUpdated(""), 3000);
            });
    };

    return (
        <div className="profile-box-2">
            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box">
                        <div className="d-flex flex-row justify-content-between">
                            <div>
                                <p className="account-information">Account Information</p>
                            </div>
                            <div>
                                <img
                                    src={profilePicture || "images/profile-picture.png"}
                                    className="img-fluid"
                                    style={{ opacity: profilePicture ? null : "0.5" }}
                                />
                            </div>
                        </div>

                        <div className="pl-5 pr-5">
                            <div className="row">
                                <div className="col-md-6">
                                    <div className="input-wrapper">
                                        <label htmlFor="firstname">First Name</label>
                                        <input
                                            type="text"
                                            id="firstname"
                                            className="input-bottom-border"
                                            placeholder="Enter your first name"
                                            value={fname}
                                            onChange={(e) => setFname(e.target.value)}
                                        />
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper">
                                        <label htmlFor="lastname">Last Name</label>
                                        <input
                                            type="text"
                                            id="lastname"
                                            className="input-bottom-border"
                                            placeholder="Enter your last name"
                                            value={lName}
                                            onChange={(e) => setLname(e.target.value)}
                                        />
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper">
                                        <label htmlFor="jobtitle">Job Title</label>
                                        <input
                                            type="text"
                                            id="jobtitle"
                                            className="input-bottom-border"
                                            placeholder="Enter your job title"
                                            value={jobtitle}
                                            onChange={(e) => setJobtitle(e.target.value)}
                                        />
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper">
                                        <label htmlFor="email">Business Email Address</label>
                                        <input
                                            type="email"
                                            id="email1"
                                            className="input-bottom-border"
                                            placeholder="Enter your email address"
                                            value={email}
                                            onChange={(e) => setEmail(e.target.value)}
                                        />
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper">
                                        <label htmlFor="phone">Phone Number</label>
                                        <input
                                            type="tel"
                                            id="phone"
                                            className="input-bottom-border"
                                            placeholder="Enter your phone number"
                                            pattern="[0-9]*"
                                            inputMode="numeric"
                                            value={phone}
                                            onChange={(e) => setPhone(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>

                            <h6 className="my-account-social-media">Social Media</h6>

                            <div className="row">
                                <div className="col-md-6">
                                    <div className="input-wrapper">
                                        <div className="label-with-icon">
                                            <label htmlFor="linkedin">LinkedIn</label>
                                            <img src="images/Rs_linkedin.png" alt="LinkedIn" className="social-icon" />
                                        </div>
                                        <input
                                            type="text"
                                            id="linkedin"
                                            className="input-bottom-border-two"
                                            placeholder="Linkedin ID"
                                            value={linkedin}
                                            onChange={(e) => setLinkedin(e.target.value)}
                                        />
                                    </div>
                                </div>

                                <div className="col-md-6">
                                    <div className="input-wrapper">
                                        <div className="label-with-icon">
                                            <label htmlFor="twitter">Twitter</label>
                                            <img src="images/Rs_linkedin.png" alt="Twitter" className="social-icon" />
                                        </div>
                                        <input
                                            type="text"
                                            id="twitter"
                                            className="input-bottom-border-two"
                                            placeholder="Twitter ID"
                                            value={twitter}
                                            onChange={(e) => setTwitter(e.target.value)}
                                        />
                                    </div>
                                </div>

                                <div className="col-md-6">
                                    <div className="input-wrapper">
                                        <div className="label-with-icon">
                                            <label htmlFor="facebook">Facebook</label>
                                            <img src="images/Rs_linkedin.png" alt="Facebook" className="social-icon" />
                                        </div>
                                        <input
                                            type="text"
                                            id="facebook"
                                            className="input-bottom-border-two"
                                            placeholder="Facebook ID"
                                            value={facebook}
                                            onChange={(e) => setFacebook(e.target.value)}
                                        />
                                    </div>
                                </div>

                                <div className="col-md-6">
                                    <div className="input-wrapper">
                                        <div className="label-with-icon">
                                            <label htmlFor="instagram">Instagram</label>
                                            <img src="images/Rs_linkedin.png" alt="Instagram" className="social-icon" />
                                        </div>
                                        <input
                                            type="text"
                                            id="instagram"
                                            className="input-bottom-border-two"
                                            placeholder="Instagram ID"
                                            value={Instagram}
                                            onChange={(e) => setInstagra(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="col-md-2"></div>
            </div>

            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box mt-3">
                        <div className="d-flex flex-row justify-content-between">
                            <div>
                                <p className="account-information">Company Information</p>
                            </div>
                            <div>
                                <img src="images/no-image.png" className="img-fluid" />
                            </div>
                        </div>

                        <div className="company-information">
                            <div className="row">
                                <div className="col-md-4">
                                    <div className="input-wrapper-2">
                                        <label htmlFor="companyName">Company Name</label>
                                    </div>
                                </div>
                                <div className="col-md-6 ">
                                    <div className="input-wrapper-2">
                                        <input
                                            type="text"
                                            id="companyName"
                                            className="input-bottom-border-2"
                                            placeholder="Company Name"
                                            value={companyName}
                                            onChange={(e) => setCompany(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="company-information">
                            <div className="row">
                                <div className="col-md-4">
                                    <div className="input-wrapper-2">
                                        <label htmlFor="website">WebSite Address</label>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper-2">
                                        <input
                                            type="text"
                                            id="website"
                                            className="input-bottom-border-2"
                                            placeholder="WebSite Address"
                                            value={website}
                                            onChange={(e) => setWebsite(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="company-information">
                            <div className="row">
                                <div className="col-md-4">
                                    <div className="input-wrapper-2">
                                        <label htmlFor="industry">Industry</label>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper-2">
                                        <input
                                            type="text"
                                            id="industry"
                                            className="input-bottom-border-2"
                                            placeholder="Industry"
                                            value={industry}
                                            onChange={(e) => setindustry(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="company-information">
                            <div className="row">
                                <div className="col-md-4">
                                    <div className="input-wrapper-2">
                                        <label htmlFor="employeeSize">Employee Size</label>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper-2">
                                        <input
                                            type="text"
                                            id="employeeSize"
                                            className="input-bottom-border-2"
                                            placeholder="Employee Size"
                                            value={employeeSize}
                                            onChange={(e) => setEmployeesize(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="company-information">
                            <div className="row">
                                <div className="col-md-4">
                                    <div className="input-wrapper-2">
                                        <label htmlFor="revenue">Revenue Size</label>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper-2">
                                        <input
                                            type="text"
                                            id="revenue"
                                            className="input-bottom-border-2"
                                            placeholder="Revenue Size"
                                            value={revenue}
                                            onChange={(e) => setRevenue(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="company-information">
                            <div className="row">
                                <div className="col-md-4">
                                    <div className="input-wrapper-2">
                                        <label htmlFor="companyAddress">Company Address</label>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper-2">
                                        <input
                                            type="text"
                                            id="companyAddress"
                                            className="input-bottom-border-2"
                                            placeholder="Company Address"
                                            value={companyAddress}
                                            onChange={(e) => setCompanyAddres(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="company-information">
                            <div className="row">
                                <div className="col-md-4">
                                    <div className="input-wrapper-2">
                                        <label htmlFor="state">State</label>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper-2">
                                        <input
                                            type="text"
                                            id="state"
                                            className="input-bottom-border-2"
                                            placeholder="State"
                                            value={state}
                                            onChange={(e) => setState(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="company-information">
                            <div className="row">
                                <div className="col-md-4">
                                    <div className="input-wrapper-2">
                                        <label htmlFor="city">City</label>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper-2">
                                        <input
                                            type="text"
                                            id="city"
                                            className="input-bottom-border-2"
                                            placeholder="City"
                                            value={city}
                                            onChange={(e) => setCity(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="company-information">
                            <div className="row">
                                <div className="col-md-4">
                                    <div className="input-wrapper-2">
                                        <label htmlFor="zipcode">ZIP Code</label>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                    <div className="input-wrapper-2">
                                        <input
                                            type="text"
                                            id="zipcode"
                                            className="input-bottom-border-2"
                                            placeholder="ZIP Code"
                                            value={zipcode}
                                            onChange={(e) => setZipcode(e.target.value)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="col-md-2"></div>

            </div>




            {profileUpdated && (
                <div style={{
                    margin: '10px 0',
                    padding: '10px 15px',
                    borderRadius: '4px',
                    fontWeight: '500',
                    textAlign: 'center',
                    backgroundColor: '#e6f7e6',
                    color: '#28a745',
                    border: '1px solid #28a745'
                }}>
                    {profileUpdated}
                </div>
            )}

            <div className="row mt-3">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="my-last-step">
                        <div>
                            <label className="custom-checkbox">
                                <input
                                    type="checkbox"
                                    checked={isChecked}
                                    onChange={(e) => setIsChecked(e.target.checked)}
                                />
                                <span className="i-agree">I agree to receive newsletters and promotional updates from ReachStream</span>
                            </label>
                        </div>
                        <button
                            type="button"
                            className="update-and-save"
                            onClick={onSubmitHandler}
                        >
                            Update & Save Changes
                        </button>
                    </div>
                </div>
                <div className="col-md-2"></div>
            </div>

        </div>
    );
};

export default ProfileTab;