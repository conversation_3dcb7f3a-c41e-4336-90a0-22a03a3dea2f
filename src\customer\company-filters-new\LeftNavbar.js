import React, { useEffect, useState, useContext } from "react";
import Industry from "../../customer/filters/Industry.js";
import EmployeeSize from "../../customer/filters/EmployeeSize.js";
import RevenueSize from "../../customer/filters/RevenueSize.js";
// import { useState} from "react";
import { Link } from "react-router-dom";
import "../assests/css/filter/job_title.css";
import { useNavigate } from "react-router-dom";
import DashboardContext from "../common-files/ContextDashboard.js";
import Technology from "../filters/Technology.js";
import Location from "../filters/Location.js";
import UseTabStore from "../common-files/useGlobalState.js";
import { PostWithTokenNoCache } from "../common-files/ApiCalls.js";
import { ApiName } from "../common-files/ApiNames.js";
import { clearStore } from "../common-files/indexedDBUtils.js";
const Dashboard = () => {
  const [data, setData] = useState("");
  const { dataDC, setDataDC } = useContext(DashboardContext);
  const navigate = useNavigate();
  const [accordionOpen, setAccordionOpen] = useState(false);
  const [otherState, setOtherState] = useState(null);
  const [classNameArrow, setClassNameArrow] = useState("fa fa-caret-right");

  const [accordionOpenTwo, setAccordionOpenTwo] = useState(false);
  const [otherStateTwo, setOtherStateTwo] = useState(null);
  const [classNameArrow2, setClassNameArrow2] = useState("fa fa-caret-right");

  const [accordionOpenThird, setAccordionOpenThird] = useState(false);
  const [otherStateThird, setOtherStateThird] = useState(null);
  const [classNameArrow3, setClassNameArrow3] = useState("fa fa-caret-right");

  const [accordionOpenFourth, setAccordionOpenFourth] = useState(false);
  const [otherStateFourth, setOtherStateFourth] = useState(null);
  const [classNameArrow4, setClassNameArrow4] = useState("fa fa-caret-right");

  const [accordionOpenTech, setAccordionOpenTech] = useState(false);
  const [otherStateTech, setOtherStateTech] = useState(null);
  const [classNameArrowTech, setClassNameArrowTech] = useState("fa fa-caret-right");

  const [accordionOpenFifth, setAccordionOpenFifth] = useState(false);
  const [otherStateFifth, setOtherStateFifth] = useState(null);
  const [classNameArrow5, setClassNameArrow5] = useState("fa fa-caret-right");

  const [otherStateLocationCountry, setOtherStateLocationCountry] = useState(null);

  const [otherStateLocationState, setOtherStateLocationState] = useState(null);

  const [otherStateLocationCity, setOtherStateLocationCity] = useState(null);

  const [otherStateLocationZipCode, setOtherStateLocationZipCode] = useState(null);

  const [selectedIndustry, setSelectedIndustry] = useState(false);
  const [selectedTechnology, setSelectedTechnology] = useState(false);
  const [selectedEmployeeSize, setSelectedEmployeeSize] = useState(false);
  const [selectedRevenueSize, setSelectedRevenueSize] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(false);

  const [accordionCompanyKeyword, setAccordionCompanyKeyword] = useState(false);
  const [stateCompanyKeyword, setStateCompanyKeyword] = useState(null);
  const [classNameCompanyKeyword, setClassNameCompanyKeyword] = useState("fa fa-caret-right");

  const {
    advancedFilter,
    companyKeyword,
    isFiltered,
    companyEmployeeSize,
    companyAnnualRevenueAmount,
    companyURL,
    companyName,
    sicCode,
    industryData,
    companyTechKeyWordsList,
    resetPage,
    selectedTab,
    companyAddressCountry,
    companyAddressState,
    companyAddressCity,
    companyZipCode,
    companyType,
    applyFilters,
    setSelectedCompanyAddressCountry,
    setSelectedCompanyAddressState,
    setSelectedCompanyAddressCity,
    setSelectedCompanyZipCode,
    setSelectedContactJobTitle1,
    setSelectedContactJobTitleLevel1,
    setSelectedContactJobDeptName1,
    setSelectedContactJobFunctionName1,
    setSelectedCompanyEmployeeSize,
    setSelectedCompanyAnnualRevenueAmount,
    setSelectedCompanyTechKeyWordsList,
    setSelectedCompany,
    setSortingBy,
    setDefaultError,
    setNumber,
    setSearchPattern,
    setSelectedTab,
    setResetPage,
    setCheckboxesGlobal,
    setSelectedData,
    setSelectedRows,
    setAllRecordsNew,
    setSampleDownloadedIds,
    setLoadedData,
    setViewModal,
    setViewSelected,
    setSelectedValues,
    setCheckedBoxes,
    setSelectedOptions,
    setSicCode,
    setIndustryData,
    setShowDownloadedData,
    setCheckedItems,
    setSelectedCompanyURL,
    setSelectedCompanyType,
    setCompanyTypeCheckedBoxes,
    setApplyFilters,
    setIsFiltered,
    setCurrentPage,
    setAdvancedFilter,
    setAdvancedFilterSearchPattern,
    setFoundCounts,
    setNoOfContact,
    setCompanyKeyword,
    setAllPlanDetails,
    setUnSelectedRows
  } = UseTabStore();

  let contact_job_title_1 = null;
  let contact_job_title_level_1 = null;
  let company_industries = null;
  let company_employee_size = null;
  let company_annual_revenue_amount = null;
  let company_address_country = null;
  let company_address_state = null;
  let company_address_city = null;
  let company_sic_code = null;
  let contact_job_dept_name_1 = null;
  let contact_job_function_name_1 = null;
  let dept = [];
  let func = [];
  let company_type = null;
  useEffect(() => {
    if (!accordionOpen) {

      if ("jobTitle" in dataDC && Array.isArray(dataDC.jobTitle)) {
        contact_job_title_1 = dataDC.jobTitle.map((v) => v.value);
      } else {
        // Handle the case where jobTitle is not an array, e.g., set contact_job_title_1 to an empty array
        contact_job_title_1 = [];
      }

      if ("jobLevel" in dataDC && Array.isArray(dataDC.jobLevel)) {
        contact_job_title_level_1 = dataDC.jobLevel.map((v) => v.value);
      } else {
        // Handle the case where jobLevel is not an array, e.g., set contact_job_title_level_1 to an empty array
        contact_job_title_level_1 = [];
      }
      if ("DeptFunc" in dataDC && dataDC.DeptFunc !== undefined) {
        let obj = dataDC.DeptFunc;
        for (const key in obj) {
          dept.push(key);
          for (const nestedKey in obj[key]) {
            func.push(nestedKey);
          }
        }
      }
      if (
        contact_job_title_1 ||
        contact_job_title_level_1 ||
        dept.length > 0 ||
        func.length > 0
      ) {
        setOtherState({
          contact_job_title_1,
          contact_job_title_level_1,
          dept,
          func,
        });
      }
    }
  }, [accordionOpen, dataDC, resetPage]);
  useEffect(() => {
    if (!accordionOpenTech) {
      if (companyTechKeyWordsList) {
        let collectName = [];
        for (const key in companyTechKeyWordsList) {
          if (companyTechKeyWordsList.hasOwnProperty(key)) {
            collectName.push(companyTechKeyWordsList[key]);
          }
        }
        setOtherStateTech({ companyTechKeyWordsList: collectName });
      }
    }
  }, [accordionOpenTech, companyTechKeyWordsList, resetPage]);
  useEffect(() => {
    if (!accordionOpenTwo) {

      if (industryData) {
        let collectName = [];
        for (const key in industryData) {
          if (industryData.hasOwnProperty(key)) {
            collectName.push(industryData[key]);
          }
        }
        company_industries = collectName;
      }
      if (sicCode) {
        let collectName = [];
        for (const key in sicCode) {
          if (sicCode.hasOwnProperty(key)) {
            collectName.push(sicCode[key]);
          }
        }
        company_sic_code = collectName;
      }
      if (companyType && companyType.length >= 0) {
        company_type = companyType.map((v) => v);
      }
      if (company_industries || company_sic_code || company_type) {
        setOtherStateTwo({ company_industries, company_sic_code, company_type });
      }
    }
  }, [accordionOpenTwo, dataDC, resetPage, companyType, industryData, sicCode]);
  useEffect(() => {
    if (!accordionCompanyKeyword) {
      if (companyKeyword) {
        let collectName = [];
        for (const key in companyKeyword) {
          if (companyKeyword.hasOwnProperty(key)) {
            collectName.push(companyKeyword[key]);
          }
        }
        // console.log("companyKeyword: collectName", companyKeyword, collectName);
        setStateCompanyKeyword({ companyKeyword: collectName });
      }
    }
  }, [accordionCompanyKeyword, companyKeyword, resetPage]);
  useEffect(() => {
    if (!accordionOpenThird) {
      if (
        "company_employee_size" in dataDC &&
        dataDC.company_employee_size !== undefined
      ) {
        company_employee_size = dataDC.company_employee_size.map((v) => v);
      }
      if (company_employee_size) {
        setOtherStateThird({ company_employee_size });
      }
    }
  }, [accordionOpenThird, dataDC, resetPage]);
  useEffect(() => {
    if (!accordionOpenFourth) {
      if (
        "company_annual_revenue_amount" in dataDC &&
        dataDC.company_annual_revenue_amount !== undefined
      ) {
        company_annual_revenue_amount =
          dataDC.company_annual_revenue_amount.map((v) => v);
      }
      if (company_annual_revenue_amount) {
        setOtherStateFourth({ company_annual_revenue_amount });
      }
    }
  }, [accordionOpenFourth, dataDC, resetPage]);
  useEffect(() => {
    if (!accordionOpenFifth) {
      if (companyAddressCountry) {
        let collectName = [];
        for (const key in companyAddressCountry) {
          if (companyAddressCountry.hasOwnProperty(key)) {
            collectName.push(companyAddressCountry[key]);
          }
        }
        setOtherStateLocationCountry({ companyAddressCountry: collectName });
      }
    }
  }, [accordionOpenFifth, companyAddressCountry, resetPage]);
  useEffect(() => {
    if (!accordionOpenFifth) {
      if (companyZipCode) {
        let collectName = [];
        for (const key in companyZipCode) {
          if (companyZipCode.hasOwnProperty(key)) {
            collectName.push(companyZipCode[key]);
          }
        }
        // console.log({ companyZipCode: collectName });
        setOtherStateLocationZipCode({ companyZipCode: collectName });
      }
    }
  }, [accordionOpenFifth, companyZipCode, resetPage]);
  useEffect(() => {
    if (!accordionOpenFifth) {
      if (companyAddressCity) {
        let collectName = [];
        for (const key in companyAddressCity) {
          if (companyAddressCity.hasOwnProperty(key)) {
            collectName.push(companyAddressCity[key]);
          }
        }
        setOtherStateLocationCity({ companyAddressCity: collectName });
      }
    }
  }, [accordionOpenFifth, companyAddressCity, resetPage]);
  useEffect(() => {
    if (!accordionOpenFifth) {
      if (companyAddressState) {
        let collectName = [];
        for (const key in companyAddressState) {
          if (companyAddressState.hasOwnProperty(key)) {
            collectName.push(companyAddressState[key]);
          }
        }
        setOtherStateLocationState({ companyAddressState: collectName });
      }
    }
  }, [accordionOpenFifth, companyAddressState, resetPage]);
  useEffect(() => {
    if (!accordionOpenFifth) {
      company_address_country = companyAddressCountry;
      company_address_state = companyAddressState;
      company_address_city = companyAddressCity
      if (
        company_address_country ||
        company_address_state ||
        company_address_city
      ) {
        setOtherStateFifth({
          company_address_country,
          company_address_state,
          company_address_city,
        });
      }
    }
  }, [accordionOpenFifth, dataDC, resetPage]);

  useEffect(() => {

    let company_employee_size = null;
    let company_annual_revenue_amount = null;
    let dept = [];
    let func = [];

    if ("DeptFunc" in dataDC && dataDC.DeptFunc !== undefined) {
      let obj = dataDC.DeptFunc;
      for (const key in obj) {
        dept.push(key);
        for (const nestedKey in obj[key]) {
          func.push(nestedKey);
        }
      }
      setSelectedContactJobDeptName1(dept);
      setSelectedContactJobFunctionName1(func);
    }

    if ("company_employee_size" in dataDC && dataDC.company_employee_size !== undefined) {
      company_employee_size = dataDC.company_employee_size.map((v) => v);
    }
    if ("company_annual_revenue_amount" in dataDC && dataDC.company_annual_revenue_amount !== undefined) {
      company_annual_revenue_amount = dataDC.company_annual_revenue_amount.map((v) => v);
    }


    const searchPatterns = {
      "company_address_country": { ...companyAddressCountry },
      "company_address_state": { ...companyAddressState },
      "company_address_city": { ...companyAddressCity },
      "company_address_zipcode": { ...companyZipCode },
      "contact_job_dept_name_1": { ...dept },
      "contact_job_function_name_1": { ...func },
      "company_industries": { ...industryData },
      "company_sic_code": { ...sicCode },
      "company_employee_size": { ...company_employee_size },
      "company_annual_revenue_amount": { ...company_annual_revenue_amount },
      "company_tech_keywords_list": { ...companyTechKeyWordsList },
      "company_website": { ...companyURL },
      "company_type": { ...companyType },
      "company_buzzwords_list": { ...companyKeyword }
    };

    const keysToCheck = [
      "company_address_country",
      "company_address_state",
      "company_address_city",
      "company_address_zipcode",
      "contact_job_dept_name_1",
      "contact_job_function_name_1",
      "company_industries",
      "company_sic_code",
      "company_employee_size",
      "company_annual_revenue_amount",
      "company_tech_keywords_list",
      "company_website",
      "company_type",
      "company_buzzwords_list"
    ];

    const hasValues = keysToCheck.some(key => Object.keys(searchPatterns[key]).length > 0);

    if (!hasValues) {
      setIsFiltered(false);
    } else {
      setIsFiltered(true);
      setResetPage(null);
    }

  }, [
    companyName,
    companyURL,
    companyType,
    companyEmployeeSize,
    sicCode,
    companyTechKeyWordsList,
    companyAddressCountry,
    companyAddressState,
    companyAddressCity,
    companyZipCode,
    companyAnnualRevenueAmount,
    dataDC,
  ])

  const getAllPlanDetails = async () => {
    try {
      let params = {
        method: "GET",
      };
      const response = await PostWithTokenNoCache(ApiName.packagePricePlan, params).then((response) => {
        if (response.status == 200) {
          const jsonData = response.data.data;
          setAllPlanDetails(JSON.parse(jsonData));
        }
      })
    } catch (error) {

    }
  };

  const handleAccordionClickTwo = () => {

    if (!accordionOpenTwo) {
      setSelectedIndustry(true);
      setClassNameArrow2("fa fa-caret-down");
    } else {
      setSelectedIndustry(false);
      setClassNameArrow2("fa fa-caret-right");
    }
    setAccordionOpenTwo(!accordionOpenTwo);
  };

  const handleAccordionClickThird = () => {


    if (!accordionOpenThird) {
      setSelectedEmployeeSize(true);
      setClassNameArrow3("fa fa-caret-down");
    } else {
      setSelectedEmployeeSize(false);
      setClassNameArrow3("fa fa-caret-right");
    }
    setAccordionOpenThird(!accordionOpenThird);
  };

  const handleAccordionClickFourth = () => {

    if (!accordionOpenFourth) {
      setSelectedRevenueSize(true);
      setClassNameArrow4("fa fa-caret-down");
    } else {
      setSelectedRevenueSize(false);
      setClassNameArrow4("fa fa-caret-right");
    }
    setAccordionOpenFourth(!accordionOpenFourth);
  };

  const handleAccordionClickFifth = () => {

    if (!accordionOpenFifth) {
      setSelectedLocation(true)
      setClassNameArrow5("fa fa-caret-down");
    } else {
      setSelectedLocation(false)
      setClassNameArrow5("fa fa-caret-right");
    }
    setAccordionOpenFifth(!accordionOpenFifth);
  };

  const handleAccordionClickTech = () => {

    if (!accordionOpenTech) {
      setSelectedTechnology(true);
      setClassNameArrowTech("fa fa-caret-down");
    } else {
      setSelectedTechnology(false);
      setClassNameArrowTech("fa fa-caret-right");
    }
    setAccordionOpenTech(!accordionOpenTech);
  };

  const removeValueCompanyKeyword = (value) => {
    let prevList = stateCompanyKeyword?.companyKeyword ? stateCompanyKeyword.companyKeyword : null;
    let updateList = prevList.filter((item) => item !== value);
    setCompanyKeyword(updateList);
  };

  const removeValueEmpSize = (value) => {
    let prevList =
      "company_employee_size" in dataDC ? dataDC.company_employee_size : null;
    let updateList = prevList.filter((item) => item !== value);
    setDataDC({ ...dataDC, company_employee_size: updateList });
  };
  const removeValueRevenueAmt = (value) => {
    let prevList =
      "company_annual_revenue_amount" in dataDC
        ? dataDC.company_annual_revenue_amount
        : null;
    let updateList = prevList.filter((item) => item !== value);
    setDataDC({ ...dataDC, company_annual_revenue_amount: updateList });
  };
  const removeValueSic = (value) => {
    let prevList = otherStateTwo.company_sic_code ? otherStateTwo.company_sic_code : null;
    let updateList = prevList.filter((item) => item !== value);
    setSicCode({ ...updateList });
  };
  const removeValueIndustry = (value) => {
    let prevList = otherStateTwo.company_industries ? otherStateTwo.company_industries : null;
    let updateList = prevList.filter((item) => item !== value);
    setIndustryData({ ...updateList });
  };
  const removeValueCompanyType = (value) => {
    let updateList = companyType.filter((item) => item !== value);
    setSelectedCompanyType(updateList);
    setCompanyTypeCheckedBoxes(updateList);
  };
  const removeValuecompanyAddressCountry = (value) => {
    let prevList = otherStateLocationCountry.companyAddressCountry ? otherStateLocationCountry.companyAddressCountry : null;
    let updateList = prevList.filter((item) => item !== value);
    setDataDC({ ...dataDC, companyAddressCountry: updateList });
    setSelectedCompanyAddressCountry({ ...updateList });
  };
  const removeValuecompanyZipCode = (value) => {
    let prevList = otherStateLocationZipCode.companyZipCode ? otherStateLocationZipCode.companyZipCode : null;
    let updateList = prevList.filter((item) => item !== value);
    setDataDC({ ...dataDC, companyZipCode: updateList });
    setSelectedCompanyZipCode({ ...updateList });
  };
  const removeValuecompanyAddressCity = (value) => {
    let prevList = otherStateLocationCity.companyAddressCity ? otherStateLocationCity.companyAddressCity : null;
    let updateList = prevList.filter((item) => item !== value);
    setDataDC({ ...dataDC, companyAddressCity: updateList });
    setSelectedCompanyAddressCity({ ...updateList });
  };
  const removeValuecompanyAddressState = (value) => {
    let prevList = otherStateLocationState.companyAddressState ? otherStateLocationState.companyAddressState : null;
    let updateList = prevList.filter((item) => item !== value);
    setDataDC({ ...dataDC, companyAddressState: updateList });
    setSelectedCompanyAddressState({ ...updateList });
  };

  const removeValuecompanyTechKeyWordsList = (value) => {
    let prevList = otherStateTech.companyTechKeyWordsList ? otherStateTech.companyTechKeyWordsList : null;
    let updateList = prevList.filter((item) => item !== value);
    setDataDC({ ...dataDC, companyTechKeyWordsList: updateList });
    setSelectedCompanyTechKeyWordsList({ ...updateList });
  };

  const applyFilter = () => {
    clearStore("AdvancedFilterData", "Company")
    setCurrentPage(1);
    setApplyFilters(!applyFilters);
    setAdvancedFilter(false);
    setAdvancedFilterSearchPattern({});
    setNoOfContact(null);
    if (advancedFilter) {
      setSelectedRows([]);
      setUnSelectedRows([]);
    }
  };

  const resetButton = () => {
    sessionStorage.removeItem("jobDepartments");
    sessionStorage.removeItem("jobFunction");

    dataDC.jobTitle = {}; // Set jobTitle to an empty object

    dataDC.jobLevel = {}; // Set jobTitle to an empty object

    dataDC.DeptFunc = {}; // Set jobTitle to an empty object

    if ("company_employee_size" in dataDC && dataDC.company_employee_size !== undefined) {
      dataDC.company_employee_size = []; // Set company_employee_size to an empty object
    }

    if ("company_annual_revenue_amount" in dataDC && dataDC.company_annual_revenue_amount !== undefined) {
      dataDC.company_annual_revenue_amount = []; // Set company_annual_revenue_amount to an empty object
    }
    getAllPlanDetails();
    const resetStore = UseTabStore.getState().reset;
    resetStore();
    clearStore("AdvancedFilterData","Company")
    setSelectedTab("company");
    setTimeout(() => {
      // Your state update code here
      setResetPage(null);
    }, 1); // 1000 milliseconds = 1 second
  };

  const openContact = () => { 
    
    setSelectedTab("contact");
    setDefaultError({});
    setNumber({});
    setCheckboxesGlobal([]);
    setSelectedData([]);
    setAllRecordsNew('');
    setSelectedRows([]);
    setSampleDownloadedIds('');
    setLoadedData([]);
    setViewModal(false);
    setViewSelected(false);
    setShowDownloadedData(true);
    setFoundCounts(null);
    setAdvancedFilterSearchPattern({});
    setAdvancedFilter(false);
    setNoOfContact(null);
    setUnSelectedRows([]);
    navigate("/search");
  }

  return (
    <div>
      <div className="dashboard">
        <div class="sidebar">
          <div class="header">
            <div className="" >
              <div className="filter-section">
                <div className="d-flex justify-content-between mb-2">
                  <div className="Filter">
                    <p>
                      <img src="../images/filter.png" className="img-fluid" />
                      &nbsp;Filters
                    </p>
                  </div>
                  <div className="d-flex align-items-end">
                    <p>
                      <button className="reset" onClick={resetButton}>
                        Reset
                      </button>
                    </p>
                  </div>
                </div>

                <div className="tabuler">
                  <ul className="nav nav-tabs justify-content-between" id="myTab" role="tablist">
                    <span onClick={openContact}>
                      <li className="nav-item">
                        <Link to="" className={`nav-link ${selectedTab == 'contact' || Object.keys(selectedTab) == 0 ? 'active' : ''}`} id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Contact</Link>
                      </li>
                    </span>
                    <li className="nav-item">
                      <a className={`nav-link ${selectedTab == 'company' ? 'active' : ''}`} id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Company</a>
                      {/* <Link to="#" className="nav-link" id="profile-tab" data-toggle="tab" role="tab" aria-controls="profile" aria-selected="false" onClick={handleClickAndNavigate}>Company</Link> */}
                    </li>

                  </ul>
                </div>
              </div>
              <div className={selectedIndustry ? "drop-button-1" : ""} style={{ padding: "6px 0px 6px 0px", borderBottom: "1px solid #e5e5e5", margin: "0" }} >
                <button
                  onClick={handleAccordionClickTwo}
                  className="Job-Titles"
                  type="button"
                  data-toggle="collapse"
                  data-target="#industry"
                  aria-expanded="false"
                  aria-controls="collapseExample"
                  style={{backgroundColor:selectedIndustry ? "#fff" : ""}}
                >
                  <div className="d-flex flex-row">
                    <div>
                      <span className="suitcase">
                        <img src="./images/industry.png" />
                      </span>
                    </div>
                    <div style={{ margin: "2px 0 0 0", fontSize: "14px" }}>
                      Industry <i className={classNameArrow2}></i>
                    </div>
                  </div>


                </button>
                {!accordionOpenTwo &&
                  (otherStateTwo || stateCompanyKeyword) &&
                  (stateCompanyKeyword?.companyKeyword !== null ||
                    otherStateTwo?.company_sic_code !== null ||
                    otherStateTwo?.company_type !== null ||
                    otherStateTwo?.company_industries !== null) && ((!otherStateTwo?.company_sic_code?.length == 0 || undefined || !otherStateTwo?.company_type?.length == 0) ||
                      (!otherStateTwo?.company_industries?.length == 0 || undefined) || (!stateCompanyKeyword?.companyKeyword?.length == 0 || undefined)) && (
                    <>
                      <div className="title-2">
                        <div className="d-flex flex-row ml-4">
                          <div className="d-flex flex-column">
                            <p className="focused">
                              {otherStateTwo?.company_industries?.length > 0 && (
                                <>{`Industry: `}

                                </>
                              )}
                            </p>

                            <div className="d-flex flex-column">
                              <p>
                                {otherStateTwo?.company_industries &&
                                  otherStateTwo?.company_industries.map(
                                    (tag) => (
                                      <>
                                        <span key={tag} className={` roundedTag `}>
                                          <span key={"s_" + tag} className="">
                                            {tag}
                                          </span>
                                          <span
                                            key={"s_rm_" + tag}
                                            className="cancelled"
                                            onClick={() => removeValueIndustry(tag)}
                                          >
                                            <span
                                              key={"s_rounded_" + tag}
                                              className="roundedX"
                                            >

                                              <svg
                                                height="9"
                                                width="10"
                                                viewBox="0 0 20 20"
                                                aria-hidden="true"
                                                focusable="false"
                                                className="css-tj5bde-Svg"
                                              >
                                                <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                              </svg>
                                            </span>
                                          </span>
                                        </span>
                                      </>
                                    )
                                  )}
                              </p>
                            </div>
                          </div>


                        </div>
                      </div>

                      <div className="title-2">
                        <div className="d-flex flex-row">
                          <div className="d-flex flex-column">
                            <p className="focused">
                              {otherStateTwo?.company_sic_code?.length > 0 && (
                                <>{`SIC: `}

                                </>
                              )}

                            </p>
                            <div className="d-flex flex-column">
                              <p>
                                {otherStateTwo?.company_sic_code &&
                                  otherStateTwo?.company_sic_code.map((tag) => (
                                    <>
                                      <span key={tag} className={` roundedTag `}>
                                        <span key={"s_" + tag} className="">
                                          {tag}
                                        </span>
                                        <span
                                          key={"s_rm_" + tag}
                                          className="cancelled"
                                          onClick={() => removeValueSic(tag)}
                                        >
                                          <span
                                            key={"s_rounded_" + tag}
                                            className="roundedX"
                                          >

                                            <svg
                                              height="9"
                                              width="10"
                                              viewBox="0 0 20 20"
                                              aria-hidden="true"
                                              focusable="false"
                                              className="css-tj5bde-Svg"
                                            >
                                              <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                            </svg>
                                          </span>
                                        </span>
                                      </span>
                                    </>
                                  ))}
                              </p>
                            </div>
                          </div>

                        </div>
                      </div>

                      <div className="title-2">
                        <div className="d-flex flex-row ml-4">
                          <div className="d-flex flex-row">
                            <div>
                              <p className="focused">
                                {otherStateTwo?.company_type?.length > 0 && (
                                  <>{`Company Type: `}

                                  </>
                                )}
                              </p>

                              <div className="d-flex flex-row">
                                <p>
                                  {otherStateTwo?.company_type &&
                                    otherStateTwo?.company_type.map((tag) => (
                                      <>
                                        <span key={tag} className={` roundedTag `}>
                                          <span key={"s_" + tag} className="">
                                            {tag}
                                          </span>
                                          <span
                                            key={"s_rm_" + tag}
                                            className="cancelled"
                                            onClick={() => removeValueCompanyType(tag)}
                                          >
                                            <span
                                              key={"s_rounded_" + tag}
                                              className="roundedX"
                                            >

                                              <svg
                                                height="9"
                                                width="10"
                                                viewBox="0 0 20 20"
                                                aria-hidden="true"
                                                focusable="false"
                                                className="css-tj5bde-Svg"
                                              >
                                                <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                              </svg>
                                            </span>
                                          </span>
                                        </span>
                                      </>
                                    ))}
                                </p>
                              </div>
                            </div>

                          </div>

                        </div>
                      </div>

                      <div className="title-2">
                        <div className="d-flex flex-row ml-4">
                          <div className="d-flex flex-column">
                            <p className="focused">
                              {stateCompanyKeyword?.companyKeyword?.length > 0 && (
                                <>{`Company Keyword: `}

                                </>
                              )}
                            </p>

                            <div className="d-flex flex-column">
                              <p>
                                {" "}
                                {/* { console.log(otherStateSixth)} */}
                                {stateCompanyKeyword?.companyKeyword &&
                                  stateCompanyKeyword.companyKeyword.map(
                                    (tag) => (
                                      <>
                                        <span key={tag} className={` roundedTag `}>
                                          <span key={"s_" + tag} className="">
                                            {tag}
                                          </span>
                                          <span
                                            key={"s_rm_" + tag}
                                            className="cancelled"
                                            onClick={() =>
                                              removeValueCompanyKeyword(tag)
                                            }
                                          >
                                            <span
                                              key={"s_rounded_" + tag}
                                              className="roundedX"
                                            >

                                              <svg
                                                height="9"
                                                width="10"
                                                viewBox="0 0 20 20"
                                                aria-hidden="true"
                                                focusable="false"
                                                className="css-tj5bde-Svg"
                                              >
                                                <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                              </svg>
                                            </span>
                                          </span>
                                        </span>
                                      </>
                                    )
                                  )}
                              </p>
                            </div>
                          </div>

                        </div>
                      </div>

                    </>
                  )}
              </div>
              <div className="collapse" id="industry">
                <div className="card card-body-container">
                  <Industry />
                </div>
              </div>

              <div className={selectedTechnology ? "drop-button-1" : ""} style={{ padding: "6px 0px 6px 0px", borderBottom: "1px solid #e5e5e5", margin: "0" }}>
                <button
                  onClick={handleAccordionClickTech}
                  className="Job-Titles"
                  type="button"
                  data-toggle="collapse"
                  data-target="#technology"
                  aria-expanded="false"
                  aria-controls="collapseExample"
                  style={{backgroundColor:selectedTechnology ? "#fff" : ""}}

                >
                  <span className="suitcase">
                    <img src="./images/technology.png" />
                  </span>
                  Technology <i className={classNameArrowTech}></i>
                </button>
                {otherStateTech &&
                  otherStateTech?.companyTechKeyWordsList.length > 0 && (
                    <div className="title-2">
                      <div className="d-flex flex-row ml-4">
                        <div className="d-flex flex-column">
                          <p className="focused">
                            Technology:
                          </p>
                          <div className="d-flex flex-column">
                            <p>
                              {" "}
                              {/* { console.log(otherStateTech)} */}
                              {otherStateTech?.companyTechKeyWordsList &&
                                otherStateTech.companyTechKeyWordsList.map(
                                  (tag) => (
                                    <>
                                      <span key={tag} className={` roundedTag `}>
                                        <span key={"s_" + tag} className="">
                                          {tag}
                                        </span>
                                        <span
                                          key={"s_rm_" + tag}
                                          className="cancelled"
                                          onClick={() =>
                                            removeValuecompanyTechKeyWordsList(tag)
                                          }
                                        >
                                          <span
                                            key={"s_rounded_" + tag}
                                            className="roundedX"
                                          >

                                            <svg
                                              height="9"
                                              width="10"
                                              viewBox="0 0 20 20"
                                              aria-hidden="true"
                                              focusable="false"
                                              className="css-tj5bde-Svg"
                                            >
                                              <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                            </svg>
                                          </span>
                                        </span>
                                      </span>
                                    </>
                                  )
                                )}
                            </p>
                          </div>
                        </div>

                      </div>
                    </div>
                  )}
              </div>
              <div className="collapse" id="technology">
                <div className="card card-body-container">
                  <Technology />
                </div>
              </div>
              <div className={selectedEmployeeSize ? "drop-button-1" : ""} style={{ padding: "6px 0px 6px 0px", borderBottom: "1px solid #e5e5e5", margin: "0" }}>
                <button
                  onClick={handleAccordionClickThird}
                  className="Job-Titles"
                  type="button"
                  data-toggle="collapse"
                  data-target="#collapseExampleEmpSize"
                  aria-expanded="false"
                  aria-controls="collapseExampleEmpSize"
                  style={{backgroundColor:selectedEmployeeSize ? "#fff" : ""}}

                >
                  <span className="suitcase">
                    <img src="./images/employee.png" />
                  </span>
                  Employee Size <i className={classNameArrow3}></i>
                </button>
                {!accordionOpenThird &&
                  otherStateThird &&
                  otherStateThird.company_employee_size.length > 0 && (
                    <div>
                      <div className="title-2">
                        <div className="d-flex flex-row ml-4">
                          <div className="d-flex flex-column">
                            <p className="focused">
                              Employee Size:
                            </p>

                            <div>
                              {" "}
                              <p>
                                {otherStateThird.company_employee_size &&
                                  otherStateThird.company_employee_size.map((tag) => (
                                    <>
                                      <span key={tag} className={` roundedTag `}>
                                        <span key={"s_" + tag} className="">
                                          {tag}
                                        </span>
                                        <span
                                          key={"s_rm_" + tag}
                                          className="cancelled"
                                          onClick={() => removeValueEmpSize(tag)}
                                        >
                                          <span
                                            key={"s_rounded_" + tag}
                                            className="roundedX"
                                          >
                                            <svg
                                              height="9"
                                              width="10"
                                              viewBox="0 0 20 20"
                                              aria-hidden="true"
                                              focusable="false"
                                              className="css-tj5bde-Svg"
                                            >
                                              <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                            </svg>
                                          </span>
                                        </span>
                                      </span>
                                    </>
                                  )
                                  )}
                              </p>
                            </div>
                          </div>


                        </div>
                      </div>
                    </div>
                  )}
              </div>
              <div className="collapse" id="collapseExampleEmpSize">
                <div className="card card-body-container">
                  <EmployeeSize />
                </div>
              </div>

              <div className={selectedRevenueSize ? "drop-button-1" : ""} style={{ padding: "6px 0px 6px 0px", borderBottom: "1px solid #e5e5e5", margin: "0" }}>
                <button
                  onClick={handleAccordionClickFourth}
                  className="Job-Titles"
                  type="button"
                  data-toggle="collapse"
                  data-target="#Revenue"
                  aria-expanded="false"
                  aria-controls="collapseExample"
                  style={{backgroundColor:selectedRevenueSize ? "#fff" : ""}}

                >
                  <span className="suitcase">
                    <img src="./images/doller.png" />
                  </span>
                  Revenue Size <i className={classNameArrow4}></i>
                </button>
                {!accordionOpenFourth &&
                  otherStateFourth &&
                  otherStateFourth.company_annual_revenue_amount.length > 0 && (
                    <div>
                      <div className="title-2">
                        <div className="d-flex flex-row ml-4">
                          <div className="d-flex flex-column">
                            <p className="focused">
                              Revenue Size:
                            </p>

                            <div>

                              {" "}
                              {/* {console.log(otherStateFourth)} */}

                              {otherStateFourth.company_annual_revenue_amount &&
                                otherStateFourth.company_annual_revenue_amount.map(
                                  (tag) => (
                                    <>
                                      <span key={tag} className={` roundedTag `}>
                                        <span key={"s_" + tag} className="">
                                          {tag}
                                        </span>
                                        <span
                                          key={"s_rm_" + tag}
                                          className="cancelled"
                                          onClick={() => removeValueRevenueAmt(tag)}
                                        >
                                          <span
                                            key={"s_rounded_" + tag}
                                            className="roundedX"
                                          >
                                            {/* &nbsp;×&nbsp; */}
                                            <svg
                                              height="9"
                                              width="10"
                                              viewBox="0 0 20 20"
                                              aria-hidden="true"
                                              focusable="false"
                                              className="css-tj5bde-Svg"
                                            >
                                              <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                            </svg>
                                          </span>
                                        </span>
                                      </span>
                                    </>
                                  )
                                )}
                            </div>
                          </div>

                        </div>



                      </div>
                    </div>
                  )}
              </div>
              <div className="collapse" id="Revenue">
                <div className="card card-body-container">
                  <RevenueSize />
                </div>
              </div>

              <div className={selectedLocation ? "drop-button-1" : ""} style={{ padding: "6px 0px 6px 0px", borderBottom: "1px solid #e5e5e5", margin: "0" }}>
                <button
                  onClick={handleAccordionClickFifth}
                  className="Job-Titles"
                  type="button"
                  data-toggle="collapse"
                  data-target="#Location"
                  aria-expanded="false"
                  aria-controls="collapseExample"
                  style={{backgroundColor:selectedLocation ? "#fff" : ""}}

                >
                  <div className="d-flex flex-row">
                    <div>
                      <span className="suitcase">
                        <img src="./images/pin.png" />
                      </span>
                    </div>
                    <div style={{ margin: "2px 0 0 0" }}>
                      Location <i className={classNameArrow5}></i>

                    </div>
                  </div>

                </button>
                {!accordionOpenFifth && otherStateFifth && (
                  <div>
                    <div className="title-2">
                      <div className="d-flex flex-row ml-4">
                        <div className="d-flex flex-column">
                          <p className="focused">
                            {otherStateLocationCountry?.companyAddressCountry?.length > 0 && (
                              <>{`Country: `}

                              </>
                            )}
                          </p>

                          <div>
                            {otherStateLocationCountry.companyAddressCountry &&
                              otherStateLocationCountry.companyAddressCountry.map(
                                (tag) =>
                                  tag && (
                                    <>
                                      <span key={tag} className={` roundedTag `}>
                                        <span key={"s_" + tag} className="">
                                          {tag}
                                        </span>
                                        <span
                                          key={"s_rm_" + tag}
                                          className="cancelled"
                                          onClick={() => removeValuecompanyAddressCountry(tag)}
                                        >
                                          <span
                                            key={"s_rounded_" + tag}
                                            className="roundedX"
                                          >
                                            <svg
                                              height="9"
                                              width="10"
                                              viewBox="0 0 20 20"
                                              aria-hidden="true"
                                              focusable="false"
                                              className="css-tj5bde-Svg"
                                            >
                                              <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                            </svg>
                                          </span>
                                        </span>
                                      </span>
                                    </>
                                  )
                              )}
                          </div>
                        </div>

                      </div>

                      <div className="d-flex flex-row ml-4">
                        <div className="d-flex flex-column">
                          <p className="focused">
                            {otherStateLocationState?.companyAddressState?.length > 0 && (
                              <>{`State: `}

                              </>
                            )}
                          </p>
                          <div>
                            {otherStateLocationState.companyAddressState &&
                              otherStateLocationState.companyAddressState.map(
                                (tag) =>
                                  tag && (
                                    <>
                                      <span key={tag} className={` roundedTag `}>
                                        <span key={"s_" + tag} className="">
                                          {tag}
                                        </span>
                                        <span
                                          key={"s_rm_" + tag}
                                          className="cancelled"
                                          onClick={() => removeValuecompanyAddressState(tag)}
                                        >
                                          <span
                                            key={"s_rounded_" + tag}
                                            className="roundedX"
                                          >
                                            <svg
                                              height="9"
                                              width="10"
                                              viewBox="0 0 20 20"
                                              aria-hidden="true"
                                              focusable="false"
                                              className="css-tj5bde-Svg"
                                            >
                                              <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                            </svg>
                                          </span>
                                        </span>
                                      </span>
                                    </>
                                  )
                              )}
                          </div>
                        </div>

                      </div>

                      <div className="d-flex flex-row ml-4">
                        <div className="d-flex flex-column">
                          <p className="focused">
                            {otherStateLocationCity?.companyAddressCity?.length > 0 && (
                              <>{`City: `}

                              </>
                            )}
                          </p>
                          <div>
                            {" "}
                            {otherStateLocationCity.companyAddressCity &&
                              otherStateLocationCity.companyAddressCity.map(
                                (tag) =>
                                  tag && (
                                    <>
                                      <span key={tag} className={` roundedTag `}>
                                        <span key={"s_" + tag} className="">
                                          {tag}
                                        </span>
                                        <span
                                          key={"s_rm_" + tag}
                                          className="cancelled"
                                          onClick={() => removeValuecompanyAddressCity(tag)}
                                        >
                                          <span
                                            key={"s_rounded_" + tag}
                                            className="roundedX"
                                          >
                                            <svg
                                              height="9"
                                              width="10"
                                              viewBox="0 0 20 20"
                                              aria-hidden="true"
                                              focusable="false"
                                              className="css-tj5bde-Svg"
                                            >
                                              <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                            </svg>
                                          </span>
                                        </span>
                                      </span>
                                    </>
                                  )
                              )}
                          </div>
                        </div>

                      </div>

                      <div className="d-flex flex-row ml-4">
                        <div className="d-flex flex-column">
                          <p className="focused">
                            {otherStateLocationZipCode?.companyZipCode?.length > 0 && (
                              <>{`Zip Code: `}

                              </>
                            )}
                          </p>
                          <div>
                            {" "}
                            {otherStateLocationZipCode.companyZipCode &&
                              otherStateLocationZipCode.companyZipCode.map(
                                (tag) =>
                                  tag && (
                                    <>
                                      <span key={tag} className={` roundedTag `}>
                                        <span key={"s_" + tag} className="">
                                          {tag}
                                        </span>
                                        <span
                                          key={"s_rm_" + tag}
                                          className="cancelled"
                                          onClick={() => removeValuecompanyZipCode(tag)}
                                        >
                                          <span
                                            key={"s_rounded_" + tag}
                                            className="roundedX"
                                          >
                                            <svg
                                              height="9"
                                              width="10"
                                              viewBox="0 0 20 20"
                                              aria-hidden="true"
                                              focusable="false"
                                              className="css-tj5bde-Svg"
                                            >
                                              <path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
                                            </svg>
                                          </span>
                                        </span>
                                      </span>
                                    </>
                                  )
                              )}
                          </div>
                        </div>

                      </div>
                      <p>
                      </p>
                    </div>
                  </div>
                )}
              </div>
              <div className="collapse" id="Location">
                <div className="card card-body-container">
                  <Location />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="apply-back" onClick={applyFilter}>
          <button
            className={isFiltered ? "Apply-Filters-button" : "Apply-Filters-button-disable"}
            type="button"
            disabled={!isFiltered}
          >
            Apply Filters
          </button>
        </div>
      </div>



    </div>
  );
};
export default Dashboard; 