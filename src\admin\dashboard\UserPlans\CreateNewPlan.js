import React, { useState } from "react";
import S from '../../assets/css/layouts/admin-header.module.css';
import LeftSideNav from "../../layouts/LeftSideNav";
import { useNavigate } from "react-router-dom";
import { ApiName } from "../../../customer/common-files/ApiNames";
import { PostWithTokenNoCache } from '../../../customer/common-files/ApiCalls';
import AdminHeader from "../../layouts/AdminHeader";
import { currencyList } from "../../../customer/common-files/currencyList";

const CreateNewPlan = () => {

    const navigate = useNavigate();

    const [recurring, isSelectRecurring] = useState(false);
    const [oneTimePayment, isSelectOneTimePayment] = useState(true);
    const [paymentId, isGeneratePaymentId] = useState(false);
    const [package_name, setPackageName] = useState(null);
    const [package_price, setPackagePrice] = useState('');
    const [noOfUsers, setNoOfUsers] = useState('');
    const [freeTrialUntil, setFreeTrialUntil] = useState('');
    const [noOfCredits, setNoOfCredits] = useState('Unlimited');
    const [noOfProfileViews, setNoOfProfileViews] = useState('Unlimited');
    const [registerLink, setRegisterLink] = useState(null);
    const [currency, setCurrency] = useState('USD');
    const [billingPeriod, setBillingPeriod] = useState("daily");
    const [paymentDuration, setPaymentDuration] = useState(null);
    const [price_id, setPriceId] = useState(null);
    const [isCreated, setIsCreated] = useState(false);
    const [message, setMessage] = useState(null);
    const [customDays, setCustomDays] = useState(null);
    const [day, setDay] = useState(null);
    const [duration, setDuration] = useState(null);
    const [planNameErrorMessage, setPlanNameErrorMessage] = useState();
    const [priceErrorMessage, setPriceErrorMessage] = useState();
    const [noOfUsersErrorMessage, setNoOfUsersErrorMessage] = useState();
    const [noOfCreditErrorMessage, setNoOfCreditErrorMessage] = useState();
    const [noOfProfileErrorMessage, setNoOfProfileErrorMessage] = useState();
    const [freeTrialErrorMessage, setFreeTrialErrorMessage] = useState();
    const [dayErrorMessage, setDayErrorMessage] = useState();
    const [customCredit, setCustomCredit] = useState(false);
    const [customView, setCustomView] = useState(false);

    const signoutHandler = async () => {
        return new Promise((resolve) => {
            sessionStorage.clear();
            resolve();
            navigate("/");
        });
    };

    const selectRecurring = () => {
        isSelectOneTimePayment(false);
        isSelectRecurring(true);
    }

    const generatePaymentId = async () => {
        if (!oneTimePayment && customDays) {
            if (billingPeriod == "NULL") {
                alert("Please select billing period")
                return false;
            }
            if (!day) {
                alert("Please enter day")
                return false;
            }
            if (customDays && !duration) {
                alert("Please enter duration")
                return false;
            }
        }
        const postData = {
            billingPeriod: oneTimePayment ? "NULL" : billingPeriod,
            paymentDuration,
            currency,
            priceInCents: parseFloat(package_price) * 100,
            ...(oneTimePayment
                ? { paymentDuration: "one_time" }
                : { freeTrialDays: freeTrialUntil, paymentDuration: "recurring" }),
        };
        try {
            const res = await PostWithTokenNoCache(ApiName.createPaymentId, postData);
            if (res && 'status' in res && res.status === 200) {
                let data = JSON.parse(res.data.data);
                setPriceId(data.price_id);
                isGeneratePaymentId(true);
            } else {
                // Handle any other errors
                console.log('Error:', res);
            }
        } catch (error) {
            // Handle any other errors
            console.log('Error:', error);
        }
    }
    function toProperCase(str) {
        return str
            .toLowerCase()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    function convertToUrlFriendlyString(inputString) {
        // Convert the string to lowercase
        const lowercaseString = inputString.toLowerCase();

        // Replace spaces with hyphens
        const urlFriendlyString = lowercaseString.replace(/\s+/g, '-');

        return urlFriendlyString;
    }

    const createPlan = async () => {

        const postData = {
            package_name: package_name.trim(),
            billing_period: oneTimePayment ? "NULL" : billingPeriod,
            paymentDuration,
            currency,
            priceInCents: parseFloat(package_price) * 100,
            free_trial_until: freeTrialUntil,
            payment_duration: oneTimePayment ? "one_time" : "recurring",
            no_of_contact_views: noOfProfileViews,
            export: noOfCredits,
            no_of_users: noOfUsers,
            verified_email_phone: "Yes",
            access_database: "Yes",
            custom_filter: "Yes",
            data_fields: "23+",
            price_id,
            price: package_price,
        }
        try {
            const res = await PostWithTokenNoCache(ApiName.createPricePlan, postData);
            if (res && 'status' in res && res.status === 200) {

                // Get the current domain name
                const currentDomain = window.location.origin;
                // Generate the link by appending the package_name with a "+" character
                const link = `${currentDomain}/sign-up/${convertToUrlFriendlyString(package_name)}`;
                setRegisterLink(link);

                setMessage(res.data.message);
                isSelectRecurring(false)
                isSelectOneTimePayment(false)
                isGeneratePaymentId(false)
                setPackageName('');
                setPackagePrice('');
                setNoOfUsers('');
                setFreeTrialUntil('');
                setNoOfCredits('');
                setNoOfProfileViews('');
                setBillingPeriod("NULL")
                setPaymentDuration(null);
                setPriceId('');
                setIsCreated(true);
                setCustomDays(null);
                setDay(null);
                setDuration(null);

                navigate("/admin/all-plans")

            } else {
                if (res.response.status === 400) {
                    // Handle any other errors
                    console.log('Error:', res);
                    setPlanNameErrorMessage("This package name already created")
                }
            }
        } catch (error) {

            if (error.response.status === 400) {
                // Handle any other errors
                console.log('Error:', error);
                setPlanNameErrorMessage("This package name already created")
            }
        }
    }

    const selectOnetime = () => {
        isSelectRecurring(false);
        isSelectOneTimePayment(true);
        setFreeTrialUntil("");
        setFreeTrialErrorMessage("");
        setDayErrorMessage("");
    }

    const handleFreeTrialDaysChange = (e) => {

        const inputValue = e.target.value;

        // Regular expression to allow only positive integers
        const pattern = /^(\d+|\s)$/;

        if (!pattern.test(inputValue)) {
            setFreeTrialErrorMessage('Trial period must be between 1 and 730, Please enter numbers only');
            setFreeTrialUntil(inputValue);
        } else {
            const numericValue = parseInt(inputValue, 10);

            if (numericValue < 1 || numericValue > 730) {
                setFreeTrialErrorMessage('Trial period must be between 1 and 730, Please enter numbers only');
            } else {
                // Update the freeTrialUntil state
                setFreeTrialUntil(parseInt(inputValue));
                setFreeTrialErrorMessage('');
            }
        }

    };

    const handleBillingPeriod = (e) => {
        const input = e.target.value;
        if (!input) {
            setCustomDays(true);
            setDayErrorMessage("Please enter numbers only")
        } else {
            setBillingPeriod(input);
            setCustomDays(false);
        }
    }
    const handleCustomBillingPeriod = (e) => {
        const input = e.target.value;
        let billingPeriod = day + '_' + input;
        setDuration(input);
        setBillingPeriod(billingPeriod);

    }

    const handleDay = (e) => {
        const input = e.target.value;
        // Regular expression to allow only positive integers
        const pattern = /^\d+$/;

        if (pattern.test(input) || input === '') {
            if (input !== '0')
                setDay(input)
            setDayErrorMessage("");
        } else {
            setDayErrorMessage("Please enter numbers only")
        }
    }

    const handlePackageInput = (e) => {
        const inputValue = e.target.value;

        // Check if '@', '!', or '&' is present in the input
        if (/[^a-zA-Z0-9\s]/.test(inputValue)) {
            setPlanNameErrorMessage('Enter a valid plan name, avoid using @$!');
        } else {
            setPlanNameErrorMessage('');
        }

        // Update the package_name state
        setPackageName(inputValue);
    }

    const handleNofUsersInput = (e) => {
        const inputValue = e.target.value;

        // Regular expression to allow only positive integers or floating-point numbers
        const pattern = /^\d*\.?\d+$/;

        if (!pattern.test(inputValue)) {
            setNoOfUsersErrorMessage('Please enter numbers only');
        } else {
            setNoOfUsersErrorMessage('');
        }

        // Update the no of users state
        setNoOfUsers(inputValue);
    }
    const handlePriceInput = (e) => {
        const inputValue = e.target.value;

        // Regular expression to allow only positive integers or floating-point numbers
        const pattern = /^\d*\.?\d+$/;

        if (!pattern.test(inputValue)) {
            setPriceErrorMessage('Please enter numbers only');
        } else {
            setPriceErrorMessage('');
        }

        // Update the price state
        setPackagePrice(inputValue);
    }

    const handleFreeTrialInput = (e) => {
        const inputValue = e.target.value;

        // Check if '@', '!', or '&' is present in the input
        if (inputValue.includes('@') || inputValue.includes('!') || inputValue.includes('&')) {
            setPlanNameErrorMessage('Trial period must be between 1 and 730, Please enter numbers only');
        } else {
            setPlanNameErrorMessage('');
        }

        // Update the package_name state
        setPackageName(inputValue);
    }

    const handleNoOfCreditsInput = (e) => {
        const inputValue = e.target.value;

        // Check if '@', '!', or '&' is present in the input
        if (/[^a-zA-Z0-9\s]/.test(inputValue)) {
            setNoOfCreditErrorMessage('Enter a valid credits, avoid using @$!');
        } else if (/[a-zA-Z]/.test(inputValue) && inputValue.toLowerCase() !== 'unlimited') {
            setNoOfCreditErrorMessage('Enter a valid credits, avoid using @$!');
        } else {
            setNoOfCreditErrorMessage('');
        }

        // Update the credits state
        setNoOfCredits(inputValue);
    }

    const handleNoOfProfileInput = (e) => {
        const inputValue = e.target.value;

        // Check if '@', '!', or '&' is present in the input
        if (/[^a-zA-Z0-9\s]/.test(inputValue)) {
            setNoOfProfileErrorMessage('Enter a valid profile views, avoid using @$!');
        } else if (/[a-zA-Z]/.test(inputValue) && inputValue.toLowerCase() !== 'unlimited') {
            setNoOfProfileErrorMessage('Enter a valid profile views, avoid using @$!');
        } else {
            setNoOfProfileErrorMessage('');
        }

        // Update the credits state
        setNoOfProfileViews(inputValue);
    }

    const checkCustomCredits = () => {
        setCustomCredit(!customCredit)
        if (customCredit) {
            setNoOfCredits("Unlimited");
        } else {
            setNoOfCredits("");
        }
        setNoOfCreditErrorMessage("");
    }
    const checkCustomViews = () => {
        setCustomView(!customView)
        if (customView) {
            setNoOfProfileViews("Unlimited")
        } else {
            setNoOfProfileViews("")
        }
        setNoOfProfileErrorMessage("");
    }
    return (
        <>
            <div className={S.app}>
                <AdminHeader />

                <div className={S.main_container}>
                    <div className="d-flex flex-row">
                        <div style={{ width: "350px" }}>
                            <LeftSideNav />
                        </div>
                        <div style={{ width: "100%", padding: "0 10px 0 0" }}>
                            <div className={S.content}>{/* Main content */}</div>

                            <aside className={S.right_sidebar}>
                                <div className="pl-3 pr-3 d-flex flex-row justify-content-between">
                                    <div className={S.createcustomerplan}>
                                        <h4>Create Custom Plan</h4>
                                        <span className={S.underline}></span>
                                    </div>
                                    {message ? <div className="alert alert-success">{message}</div> : <></>}
                                </div>
                                <div className="pl-3 pr-3 mt-5">
                                    <div className="d-flex flex-row">
                                        <div>
                                            {/* <div className="col-md-3"> */}
                                            <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>Payment Duration</label>
                                            <div className="d-flex flex-row">

                                                <div className="form-group mr-3">
                                                    <button type="button" onClick={selectOnetime}
                                                        style={
                                                            !recurring
                                                                ? {
                                                                    width: "90px",
                                                                    cursor: "pointer",
                                                                    border: "1px solid #55C2C3",
                                                                    backgroundColor: "#E8F7F7",
                                                                    padding: "3px 0px"
                                                                }
                                                                : {
                                                                    width: "90px",
                                                                    cursor: "pointer",
                                                                    padding: "3px 0px",
                                                                }
                                                        }
                                                        className="form-control pure-button-active"
                                                        id="exampleInputEmail1"
                                                        aria-describedby="emailHelp"
                                                    >
                                                        One Time
                                                    </button>

                                                </div>
                                                <div className="mr-4">
                                                    <button type="button" onClick={selectRecurring}
                                                        style={
                                                            recurring
                                                                ? {
                                                                    width: "90px",
                                                                    cursor: "pointer",
                                                                    border: "1px solid #55C2C3",
                                                                    backgroundColor: "#E8F7F7",
                                                                    padding: "3px 0px"
                                                                }
                                                                : {
                                                                    width: "90px",
                                                                    cursor: "pointer",
                                                                    padding: "3px 0px",
                                                                }
                                                        }
                                                        className="form-control"
                                                        id="exampleInputEmail1"
                                                        aria-describedby="emailHelp"
                                                    >
                                                        Recurring
                                                    </button>
                                                </div>
                                            </div>
                                            {/* </div> */}
                                        </div>
                                        <div>
                                            <div className="d-flex flex-row ">
                                                <div className="" style={{ display: recurring ? "block" : "none" }}>
                                                    <div className="input-group  mb-3">
                                                        <div className="form-group">
                                                            <label for="exampleInputEmail1" className={S.Billing}>Billing Period</label>
                                                            <select className="" style={{ display: "block", width: "177px", fontSize: "14px", cursor: "pointer", padding: "5px 0px 5px 0", borderRadius: "4px", outline: "none", border: "1px solid #C9C9C9" }} id="inputGroupSelect02" onChange={handleBillingPeriod}>
                                                                <option value="day" defaultValue>Daily</option>
                                                                <option value="week">Weekly</option>
                                                                <option value="month">Monthly</option>
                                                                <option value="3_month">Every 3 Months</option>
                                                                <option value="6_month">Every 6 Months</option>
                                                                <option value="year">Yearly</option>
                                                                <option value="">Custom</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="mt-4" style={{ display: recurring && customDays ? "block" : "none", margin: "0 0 0 2rem" }}>
                                                    <div className="input-group mt-2" >
                                                        <div className="input-group-append">
                                                            <button className="" style={{ height: "30px", fontSize: "13px", backgroundColor: "#fff", cursor: "pointer", borderRadius: "3px", padding: "2px 25px 0 16px", outline: "none", border: "1px solid #C9C9C9" }} type="button">Every</button>
                                                        </div>
                                                        <div className="input-group-append">
                                                            <input type="text" className="" value={day} onChange={handleDay} style={{ height: "30px", backgroundColor: "#E8F7F7", padding: "0px 25px 0 16px", outline: "none", border: "1px solid #55C2C3", textAlign: "left", width: "70px" }} required />
                                                        </div>

                                                        <select className="custom-select" style={{ cursor: "pointer", fontSize: "13px", height: "30px" }} id="" onChange={handleCustomBillingPeriod}>
                                                            <option value="">Select</option>
                                                            <option value="month" defaultValue>Months</option>
                                                            <option value="day">Day</option>
                                                            <option value="week">Week</option>
                                                        </select>
                                                    </div>
                                                    {dayErrorMessage && dayErrorMessage !== '' ? (<p className="invalid" style={{ marginLeft: "0px" }}>{dayErrorMessage}</p>) : (<></>)}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="row mb-2">
                                        <div className="col-md-3">
                                            <div className="form-group">
                                                <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>Plan Name</label>
                                                <input style={{ display: "block", width: "240px", border: "1px solid #C9C9C9", borderRadius: "5px", outline: "none", padding: "2px 0 2px 5px" }} type="text" value={package_name} onChange={handlePackageInput} className={S.PlanName} id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Enter Plan Name" />
                                                {planNameErrorMessage && planNameErrorMessage !== '' ? (<p className="invalid">{planNameErrorMessage}</p>) : (<></>)}
                                            </div>
                                        </div>
                                        <div className="col-md-4 pl-4">
                                            <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>Price</label>
                                            <div className="input-group">
                                                <div className="d-flex flex-row">
                                                    <div>
                                                        <input type="text" value={package_price} onChange={handlePriceInput} style={{ display: "block", width: "220px", border: "1px solid #C9C9C9", borderRadius: "5px", outline: "none", padding: "2px 0 2px 5px" }} className={S.prices} id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Enter Price" />

                                                    </div>
                                                    <div className="input-group-append">
                                                        <span className="input-group-text" style={{ backgroundColor: "#E8F7F7", padding: "0", fontSize: "14px" }}>
                                                            <div className="input-group">

                                                                <select className={S.selectoption} id="inputGroupSelect01" onChange={(e) => setCurrency(e.target.value)}>
                                                                    {currencyList.map((item, index) => (
                                                                        <option value={item.code} className="option" selected={item.code === 'USD'}>
                                                                            {item.code} - {item.currency}
                                                                        </option>
                                                                    ))}
                                                                </select>
                                                            </div></span>
                                                    </div>
                                                </div>

                                            </div>
                                            {priceErrorMessage && priceErrorMessage !== '' ? (<p className="invalid">{priceErrorMessage}</p>) : (<></>)}

                                        </div>
                                        <div className="col-md-2 pl-4">
                                            <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>No of Users</label>
                                            <div className="input-group">
                                                <input type="number" min="0" value={noOfUsers} onChange={handleNofUsersInput} style={{ display: "block", width: "220px", border: "1px solid #C9C9C9", borderRadius: "5px", outline: "none", padding: "2px 0 2px 5px" }} className={S.prices} id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Enter No Of Usres" />
                                            </div>
                                            {noOfUsersErrorMessage && noOfUsersErrorMessage !== '' ? (<p className="invalid">{noOfUsersErrorMessage}</p>) : (<></>)}
                                        </div>

                                        <div className="col-md-3">
                                            <div className={oneTimePayment ? S.setopacity : ""} >
                                                <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>Free Trial Until</label>
                                                <div className="input-group">
                                                    <input
                                                        type="text"
                                                        value={freeTrialUntil}
                                                        onChange={handleFreeTrialDaysChange}
                                                        className="form-control"
                                                        id="exampleInputEmail1"
                                                        aria-describedby="emailHelp"
                                                        placeholder="Enter Free Trial Period"
                                                        readOnly={oneTimePayment}
                                                        style={{ display: "block", border: "1px solid #C9C9C9", borderRadius: "5px", outline: "none", padding: "2px 0 2px 5px" }}
                                                    />
                                                    <div className="input-group-append">
                                                        <span className="input-group-text" style={{ backgroundColor: "#E8F7F7", padding: "0 0 0 20px" }}>Days&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                    </div>
                                                </div>
                                            </div>

                                            {freeTrialErrorMessage && freeTrialErrorMessage !== '' ? (<p className="invalid">{freeTrialErrorMessage}</p>) : (<></>)}
                                        </div>
                                    </div>

                                    <div className="d-flex flex-row">

                                        <div className="" style={{ display: oneTimePayment || recurring ? "block" : "none" }}>
                                            <button
                                                type="button"
                                                onClick={generatePaymentId}
                                                style={{
                                                    border: "0",
                                                    color: "#fff",
                                                    cursor: "pointer",
                                                    backgroundColor: "#093D54",
                                                    padding: "5px 23px 5px 23px",
                                                    borderRadius: "10px",
                                                    outline: "none"
                                                }}
                                                className={`bttn-block ${oneTimePayment
                                                    ? !planNameErrorMessage &&
                                                        !priceErrorMessage &&
                                                        !freeTrialErrorMessage &&
                                                        !dayErrorMessage &&
                                                        package_name &&
                                                        package_price &&
                                                        !paymentId &&
                                                        !noOfUsersErrorMessage
                                                        ? ''
                                                        : 'cust-disabled'
                                                    : !planNameErrorMessage &&
                                                        !priceErrorMessage &&
                                                        !freeTrialErrorMessage &&
                                                        !dayErrorMessage &&
                                                        package_name &&
                                                        package_price &&
                                                        freeTrialUntil &&
                                                        !paymentId &&
                                                        !noOfUsersErrorMessage
                                                        ? ''
                                                        : 'cust-disabled'
                                                    }`}
                                            >
                                                Generate Payment ID
                                            </button>
                                        </div>

                                        <div className="ml-4" style={{ display: paymentId ? "block" : "none" }}>
                                            <input type="text" value={price_id} className="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" style={{ fontSize: "14px", border: "1px solid #55C2C3", color: "#55C2C3", backgroundColor: "#FFFFFF", padding: "5px 25px 5px 25px", borderRadius: "8px", width: "335px", textAlign: "center" }} readOnly />
                                            <span type="text"  ></span>
                                        </div>
                                    </div>

                                    <div className="d-flex flex-row mt-3">
                                        <div className="" style={{ margin: "0px 3rem 0px 0px" }}>

                                            <div className="form-group" style={{ display: paymentId ? "block" : "none" }}>
                                                <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>No of Downloads Credits</label>
                                                <div className="d-flex flex-row">
                                                    <div>
                                                        <input type="radio" onClick={checkCustomCredits} name="rd_no_of_credits" checked={!customCredit} aria-label="Radio button for following text input" />
                                                    </div>
                                                    <div>
                                                        <p className={S.UnlimitedCredits}>Unlimited Credits</p>
                                                    </div>
                                                </div>

                                                <div className="d-flex flex-row">
                                                    <div>
                                                        <input type="radio" onClick={checkCustomCredits} name="rd_no_of_credits" checked={customCredit} aria-label="Radio button for following text input" />
                                                    </div>
                                                    <div>
                                                        <p className={S.UnlimitedCredits}>Custom Credits</p>
                                                    </div>
                                                </div>
                                                <input
                                                    type="text"
                                                    value={noOfCredits}
                                                    onChange={handleNoOfCreditsInput}
                                                    className={S.nofcreds}
                                                    id="exampleInputText1"
                                                    aria-describedby="textHelp"
                                                    placeholder="Enter New Credits"
                                                    style={{ padding: "3px 0 3px 6px", width: "255px", display: "block", border: "1px solid #C9C9C9", borderRadius: "4px", outline: "none" }}
                                                    readOnly={!customCredit}
                                                />
                                                {noOfCreditErrorMessage && noOfCreditErrorMessage !== '' ? (<p className="invalid">{noOfCreditErrorMessage}</p>) : (<></>)}
                                            </div>

                                        </div>
                                        <div className="">
                                            <div className="form-group" style={{ display: paymentId ? "block" : "none" }}>
                                                <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>No of Profile Views</label>
                                                <div className="d-flex flex-row">
                                                    <div>
                                                        <input type="radio" onClick={checkCustomViews} checked={!customView} name="rd_no_of_profileview" aria-label="Radio button for following text input" />
                                                    </div>
                                                    <div>
                                                        <p className={S.UnlimitedCredits}>Unlimited Views</p>
                                                    </div>
                                                </div>

                                                <div className="d-flex flex-row">
                                                    <div>
                                                        <input type="radio" onClick={checkCustomViews} checked={customView} name="rd_no_of_profileview" aria-label="Radio button for following text input" />
                                                    </div>
                                                    <div>
                                                        <p className={S.UnlimitedCredits}>Custom Views</p>
                                                    </div>
                                                </div>
                                                <input
                                                    type="text"
                                                    value={noOfProfileViews}
                                                    onChange={handleNoOfProfileInput}
                                                    className={S.nofcreds} id="exampleInputText1"
                                                    aria-describedby="textHelp"
                                                    placeholder="Enter No of Profile Views"
                                                    style={{ padding: "3px 0 3px 6px", width: "255px", display: "block", border: "1px solid #C9C9C9", borderRadius: "4px", outline: "none" }}
                                                    readOnly={!customView}
                                                />
                                                {noOfProfileErrorMessage && noOfProfileErrorMessage !== '' ? (<p className="invalid">{noOfProfileErrorMessage}</p>) : (<></>)}
                                            </div>
                                        </div>
                                    </div>

                                    {/* <div className="d-flex flex-row mt-3">
                                        <div className={S.nofcreds} style={{ display: !paymentId ? "block" : "none", margin: "0px 3rem 0px 0px" }}>
                                            <div className="form-group">
                                                <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>No of Credits</label>
                                                <input type="text" value={noOfCredits} onChange={handleNoOfCreditsInput} className={S.nofcreds} id="exampleInputText1" aria-describedby="textHelp" placeholder="Enter New Credits" style={{ padding: "3px 0 3px 6px", width: "255px", display: "block", border: "1px solid #C9C9C9", borderRadius: "4px", outline: "none" }} />
                                                {noOfCreditErrorMessage && noOfCreditErrorMessage !== '' ? (<p className="invalid">{noOfCreditErrorMessage}</p>) : (<></>)}
                                            </div>
                                        </div>
                                        <div className={S.nofcreds} style={{ display: !paymentId ? "block" : "none" }}>
                                            <div className="form-group">
                                                <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>No of Profile Views</label>
                                                <input type="text" value={noOfProfileViews} onChange={handleNoOfProfileInput} className={S.nofcreds} id="exampleInputText1" aria-describedby="textHelp" placeholder="Enter No of Profile Views" style={{ padding: "3px 0 3px 6px", width: "255px", display: "block", border: "1px solid #C9C9C9", borderRadius: "4px", outline: "none" }} />
                                                {noOfProfileErrorMessage && noOfProfileErrorMessage !== '' ? (<p className="invalid">{noOfProfileErrorMessage}</p>) : (<></>)}
                                            </div>
                                        </div>
                                    </div> */}


                                    <div className="row mt-3">
                                        <div className="col-md-3 " style={{ display: registerLink ? "block" : "none" }}>
                                            <button type="button" style={{ border: "0", cursor: "pointer", color: "#fff", backgroundColor: "#093D54", padding: "7px 25px 7px 25px", cursor: "pointer", borderRadius: "10px" }} className="bttn-block">Registration Link</button>
                                        </div>
                                        <div className="col-5" style={{ display: registerLink ? "block" : "none" }}>
                                            <input type="text" value={registerLink} className="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" style={{ border: "1px solid #55C2C3", color: "#55C2C3", backgroundColor: "#FFFFFF", padding: "7px 25px 7px 25px", borderRadius: "10px" }} />

                                            {/* <button type="button"  className="bttn-block" >wrjsddu656;udkmkll/djntyfhfg9;hr</button> */}
                                            <span type="text"  ></span>
                                        </div>
                                    </div>

                                    <div className={noOfCredits && noOfProfileViews ? `${S.createcenter}` : `${S.createcenter} cust-disabled`} style={{ display: !price_id ? "none" : "block" }}>
                                        <button type="button" style={{ cursor: "pointer", outline: "none" }} onClick={createPlan}>Create</button>
                                    </div>
                                </div>
                            </aside>
                        </div>
                    </div>


                </div>
            </div>
        </>
    );
}

export default CreateNewPlan;