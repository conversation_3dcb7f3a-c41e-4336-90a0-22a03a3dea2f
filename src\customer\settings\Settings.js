import React, { useState, useEffect } from "react";
import "../assests/css/layouts/settings.css";
import UseTabStore from "../common-files/useGlobalState";
import { PostWithTokenNoCache, AxiosPostBearer, fetchProfilePicture } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";
import ProfileTab from "./ProfileTab";
import SecurityTab from "./SecurityTab";
import ApiTab from "./ApiTab";
import SubscriptionTab from "./SubscriptionTab";
import PlanInfoSidebar from "./PlanInfoSidebar";
import Header from "../layouts/Header";

const Settings = () => {
  const {
    settingsActiveTab,
    setSettingsActiveTab
  } = UseTabStore();

  // Dynamic plan data state
  const [userCredits, setUserCredits] = useState(null);
  const [user, setUser] = useState(null);
  const [userMembership, setUserMembership] = useState(localStorage.getItem("userMembership") || "Free");
  const [lastCalculated, setLastCalculated] = useState(new Date().toLocaleString());
  const [isLoading, setIsLoading] = useState(true);
  const [shake, setShake] = useState(false);
  const [profilePicture, setProfilePicture] = useState(null);

  // Get profile picture
  async function getUserProfilePicture(data) {
    const url = ApiName.getProfilePicture;
    const imageUrlTemp = await fetchProfilePicture(url, data);
    if (imageUrlTemp) {
      setProfilePicture(imageUrlTemp);
    } else {
      setProfilePicture("");
    }
  }

  useEffect(() => {
    getUserProfilePicture({});
  }, []);

  const handleClick = () => {
    setShake(true);
    setTimeout(() => setShake(false), 500);
  };

  // Fetch user data from API if not in localStorage
  const fetchUserData = async () => {
    try {
      const localUser = JSON.parse(localStorage.getItem("user"));
      if (localUser && localUser.firstName) {
        setUser(localUser);
        return;
      }

      const response = await AxiosPostBearer(
        ApiName.userData,
        { method: "GET" },
        localUser?.token
      );

      if (response?.data?.status === 200) {
        const jsonData = JSON.parse(response.data.data);
        const mergedUser = { ...jsonData, ...localUser };
        setUser(mergedUser);
        localStorage.setItem("user", JSON.stringify(mergedUser));
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  // Fetch user credits
  const fetchUserCredits = async () => {
    try {
      const res = await PostWithTokenNoCache(ApiName.activeCreadits, {});

      if (res?.data?.status === 200 && res.data.data) {
        const creditData = JSON.parse(res.data.data);
        setUserCredits(creditData);
        setLastCalculated(new Date().toLocaleString());
      }
    } catch (error) {
      console.error("Error fetching credits:", error);
    }
  };

  // Load all required data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      await fetchUserData();
      await fetchUserCredits();
      setIsLoading(false);
    };
    loadData();
  }, []);

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center mt-5">
        Loading...
      </div>
    );
  }

  return (
    <>
      <Header />
      <div className="container-fluid my-account-page">
        <div className="row">
          <div className="col-md-9 pl-0 pr-0">
            <div style={{ borderRight: "1px solid #DDDDDD" }}>
              <h6 className="may-my-account">My Account</h6>
              <div className="tabs-header d-flex">
                <button
                  className={`tab-btn ${settingsActiveTab === 'profile' ? 'active' : ''}`}
                  onClick={() => setSettingsActiveTab('profile')}
                >
                  <img src="images/my-profile-v2.png" alt="Profile" />
                  <span>My Profile</span>
                </button>
                <button
                  className={`tab-btn ${settingsActiveTab === 'security' ? 'active' : ''}`}
                  onClick={() => setSettingsActiveTab('security')}
                >
                  <img src="images/security-v2.png" alt="Security" />
                  <span>Security</span>
                </button>
                <button
                  className={`tab-btn ${settingsActiveTab === 'api' ? 'active' : ''}`}
                  onClick={() => setSettingsActiveTab('api')}
                >
                  <img src="images/api-v2.png" alt="API" />
                  <span>API</span>
                </button>
                <button
                  className={`tab-btn ${settingsActiveTab === 'subscription' ? 'active' : ''}`}
                  onClick={() => setSettingsActiveTab('subscription')}
                >
                  <img src="images/subscription.png" alt="Subscription" />
                  <span>Subscription & Payments</span>
                </button>
              </div>
            </div>

            <div style={{
              borderRight: "1px solid #DDDDDD",
              height: "100vh",
              overflowY: "auto",
              scrollBehavior: "smooth"
            }}>
              {settingsActiveTab === 'profile' && <ProfileTab profilePicture={profilePicture} />}
              {settingsActiveTab === 'security' && <SecurityTab />}
              {settingsActiveTab === 'api' && <ApiTab />}
              {settingsActiveTab === 'subscription' && <SubscriptionTab userCredits={userCredits} />}
            </div>
          </div>

          <div className="col-md-3 pl-0 pr-0 h-100">
            <div className="min-vh-100">
              <PlanInfoSidebar
                userCredits={userCredits}
                userMembership={userMembership}
                lastCalculated={lastCalculated}
                fetchUserCredits={fetchUserCredits}
                shake={shake}
                handleClick={handleClick}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Settings;