import React, { useEffect, useState } from "react";
import LeftSideNav from "../../layouts/LeftSideNav";
import S from "../../assets/css/layouts/admin-header.module.css";
import AdminHeader from "../../layouts/AdminHeader";
import { AxiosPostBearer, PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls";
import { ApiName } from "../../../customer/common-files/ApiNames";
import { useParams } from "react-router-dom";
import UseTabStore from "../../../customer/common-files/useGlobalState";
import Alert from "../../../customer/common-files/alert";
import loadingGif from '../../../customer/assests/waiting.gif';

const Action = () => {

    const { id } = useParams();
    const [loading, setLoading] = useState(true);
    const [user, setUser] = useState(true);
    const [adminDetails, setAdminDetails] = useState(
        JSON.parse(localStorage.getItem("user"))
    );
    const [data, setData] = useState(false);
    const [noOfDeviceMsg, setNoOfDeviceMsg] = useState("");
    const [successMsg, setSuccessMsg] = useState("");
    const [noOfDevice, setNoOfDevice] = useState(null);
    const {
        defaultAlert,
        defaultErrorMsg,
        setButtonType,
        setDefaultAlert,
        setDefaultErrorMsg,
    } = UseTabStore();

    useEffect(() => {
        getSignleUserDetails();
    }, [])

    const getSignleUserDetails = async () => {
        try {
            setLoading(true);
            const params = {
                user_id: id
            }
            const res = await PostWithTokenNoCache(ApiName.fetchSingleUser, params);

            if (res?.status === 200) {
                let data = JSON.parse(res.data.data);
                setUser(data);
                setNoOfDevice(data.no_of_device_allowed);
                setLoading(false);
            } else {
                setLoading(false);
                setButtonType("error");
                setDefaultErrorMsg("Sorry, Could not fetch user details");
                setDefaultAlert(true);
            }
        } catch (errors) {
            setLoading(false);
            setButtonType("error");
            setDefaultErrorMsg(errors.response.data.message);
            setDefaultAlert(true);
        }
    }

    const handleNoOfDevice = (e) => {
        e.preventDefault();
        const { id, value } = e.target;

        setNoOfDevice(value);

        const pattern = /^\d+$/;
        if (pattern.test(value) && parseInt(value)) {
            setNoOfDeviceMsg("");
        } else {
            setNoOfDeviceMsg("Only Positive Integers");
        }
        setData((prevData) => ({
            ...prevData,
            [id]: value,
        }));
    }

    const onSubmitHandler = async (e) => {
        e.preventDefault();
        let params = {
            ...data,
            user_id: id
        }
        setLoading(true);
        try {
            let response = await PostWithTokenNoCache(ApiName.noOfDeviceLogin, params);
            if (response && "status" in response) {
                if (response.data.status == 200) {
                    setSuccessMsg(response?.data?.message || "Saved successfully");
                    setTimeout(() => {
                        setSuccessMsg("");
                    }, 3000)
                    setLoading(false);
                }
            }
        } catch (errors) {
            setLoading(false);
            setButtonType("error");
            setDefaultErrorMsg(errors?.response?.data?.message);
            setDefaultAlert(true);
        }
    }

    return (
        <>
            <div className={S.app}>
                <AdminHeader />
                <div className={S.main_container}>
                    <div className="d-flex flex-row">
                        <div style={{ width: "350px" }}>
                            <LeftSideNav />
                        </div>
                        {!loading ? (
                            <div className={S.formlayout}>
                                <form className="mb-3">
                                    <h3 className={S.adminsignup}>Device login</h3>
                                    <div className="row">
                                        <div className="col-md-12">
                                            <div class="form-group">
                                                <label >Email</label>
                                                <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp"
                                                    value={user.email} readOnly="true"
                                                />
                                            </div>
                                        </div>
                                        <div className="col-md-12">
                                            <div className="Cnpwrd"></div>
                                            <div className="form-group">
                                                <label className="n-password" label="firstname">Allowed No of devices</label>
                                                <div className="password-input">
                                                    <input className="form-control"
                                                        type="text"
                                                        name="no_of_device_allowed"
                                                        id="no_of_device_allowed"
                                                        value={noOfDevice}
                                                        onChange={handleNoOfDevice}
                                                    ></input>
                                                </div>
                                                {noOfDeviceMsg && (
                                                    <div className={S.error_message}>
                                                        {noOfDeviceMsg}
                                                    </div>
                                                )}
                                            </div>
                                            <span className="Shift">
                                                <span>
                                                    {console.log(noOfDeviceMsg)}
                                                    {successMsg && (
                                                        <div className="alert alert-success">
                                                            {successMsg}
                                                        </div>
                                                    )}
                                                    {!noOfDevice || noOfDeviceMsg? (
                                                        <input type="button" onClick={onSubmitHandler} value="Save" className="cp-pluss1 cust-disabled" />
                                                    ) : (
                                                        <input type="button" onClick={onSubmitHandler} value="Save" className="cp-pluss1" />
                                                    )}
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        ) : (
                            <>
                                <div className="animation">
                                    <img src={loadingGif} alt="Loading" className="loader" width="400" />
                                </div>
                            </>
                        )}
                        {defaultAlert && defaultErrorMsg ? (
                            <Alert data={defaultErrorMsg} />
                        ) : (<></>)}

                    </div>
                </div>
            </div>
        </>
    )
}
export default Action;