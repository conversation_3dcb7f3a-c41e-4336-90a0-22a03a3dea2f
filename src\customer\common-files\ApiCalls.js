import axios from "axios";
import { getSessionItem, setSessionItem } from "./LocalStorage";
import { trackCreditUsage } from '../../utils/customerJourneyTracking';

export const axiosPost = (url, data) => {
  return new Promise((onSuccess, onFailure) => {
    // Check if loginAsCustomer exists in localStorage
    const loginAsCustomer = localStorage.getItem('loginAsCustomer');
    
    // Base headers
    const headers = {
      "Content-Type": "application/json"
    };
    
    // Add device_user_session_ignore parameter to the request body if loginAsCustomer exists
    let requestData = data || {}; // Default to empty object if null/undefined
    
    // Only proceed with this if we're not doing a login
    if (loginAsCustomer && !url.includes('api/AAA/v1/auth/login')) {
      // Check if data has numeric keys (character-by-character object)
      if (typeof requestData === 'object' && !Array.isArray(requestData) && requestData !== null) {
        const hasNumericKeys = Object.keys(requestData).some(key => !isNaN(parseInt(key)));
        if (hasNumericKeys) {
          try {
            // Attempt to rebuild the string from character-by-character object
            const rebuiltString = Object.values(requestData).join('');
            // Only try to parse if the string isn't empty
            if (rebuiltString && rebuiltString.trim()) {
              // Parse it to get a proper object
              const parsedObject = JSON.parse(rebuiltString);
              // Add the flag
              parsedObject.device_user_session_ignore = true;
              // Convert back to object or string as needed
              requestData = parsedObject;
            } else {
              // If empty string, just add the flag to an empty object
              requestData = { device_user_session_ignore: true };
            }
          } catch (e) {
            console.error("Error processing character-by-character object in axiosPost:", e);
            // Fall back to adding the property directly
            requestData.device_user_session_ignore = true;
          }
        } else {
          // Normal object, add property directly
          requestData = {...requestData, device_user_session_ignore: true};
        }
      } else if (typeof requestData === 'string') {
        try {
          // Only try to parse if the string isn't empty
          if (requestData && requestData.trim()) {
            // Parse the string, add the parameter, then stringify again
            let parsedData = JSON.parse(requestData);
            parsedData.device_user_session_ignore = true;
            requestData = parsedData;
          } else {
            // If empty string, create object with just the flag
            requestData = { device_user_session_ignore: true };
          }
        } catch (e) {
          console.error("Error parsing JSON string in axiosPost:", e);
          // If parsing fails, create new object with the flag
          requestData = { device_user_session_ignore: true };
        }
      } else {
        // If it's not a string or object, create new object with the flag
        requestData = { device_user_session_ignore: true };
      }
    }

    axios({
      url: url,
      method: "post",
      data: requestData,
      headers: headers,
    })
      .then(function (response) {
        onSuccess(response);
      })
      .catch(function (errors) {
        console.error("axiosPost Error:", errors);
        // Check if error status is 401 Unauthorized
        if (errors.response && errors.response.status === 401) {
          // Clear the user from localStorage and redirect to home
          localStorage.removeItem("user");
          localStorage.removeItem('loginAsCustomer'); // Also remove loginAsCustomer
          sessionStorage.clear();
          // window.location.href = "/";
        }
        onFailure(errors);
      });
  });
};

export const AxiosPostBearer = (url, data, token) => {
  return new Promise((onSuccess, onFailure) => {
    const loginAsCustomer = localStorage.getItem('loginAsCustomer');

    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };

    // Add device_user_session_ignore parameter to the request body if loginAsCustomer exists
    let requestData = data || {}; // Default to empty object if data is null/undefined
    
    // Only proceed with parsing if loginAsCustomer exists and we're not doing a login request
    if (loginAsCustomer && !url.includes('api/AAA/v1/auth/login')) {
      // Check if data has numeric keys (character-by-character object)
      if (typeof requestData === 'object' && !Array.isArray(requestData) && requestData !== null) {
        const hasNumericKeys = Object.keys(requestData).some(key => !isNaN(parseInt(key)));
        if (hasNumericKeys) {
          try {
            // Attempt to rebuild the string from character-by-character object
            const rebuiltString = Object.values(requestData).join('');
            // Only try to parse if the string isn't empty
            if (rebuiltString && rebuiltString.trim()) {
              // Parse it to get a proper object
              const parsedObject = JSON.parse(rebuiltString);
              // Add the flag
              parsedObject.device_user_session_ignore = true;
              // Convert back to string
              requestData = JSON.stringify(parsedObject);
            } else {
              // If empty string, create a new object with just the flag
              requestData = JSON.stringify({ device_user_session_ignore: true });
            }
          } catch (e) {
            console.error("Error processing character-by-character object:", e);
            // Fall back to adding the property directly
            requestData.device_user_session_ignore = true;
          }
        } else {
          // Normal object, add property directly
          requestData = {...requestData, device_user_session_ignore: true};
        }
      } else if (typeof requestData === 'string') {
        try {
          // Only try to parse if the string isn't empty
          if (requestData && requestData.trim()) {
            // Parse the string, add the parameter, then stringify again
            let parsedData = JSON.parse(requestData);
            parsedData.device_user_session_ignore = true;
            requestData = JSON.stringify(parsedData);
          } else {
            // If empty string, create a new object with just the flag
            requestData = JSON.stringify({ device_user_session_ignore: true });
          }
        } catch (e) {
          console.error("Error parsing JSON string in AxiosPostBearer:", e);
          // If parsing fails, create a new object with the flag
          requestData = JSON.stringify({ device_user_session_ignore: true });
        }
      } else {
        // If it's something else (like null, undefined, etc.), create a new object
        requestData = JSON.stringify({ device_user_session_ignore: true });
      }
    }

    axios({
      url: url,
      method: "post",
      data: requestData,
      headers: headers,
    })
      .then((response) => {
        onSuccess(response);
      })
      .catch((errors) => {
        console.error("AxiosPostBearer Error:", errors);
        if (errors.response && errors.response.status === 401) {
          localStorage.removeItem("user");
          localStorage.removeItem('loginAsCustomer');
          sessionStorage.clear();
          // window.location.href = "/";
        }
        onFailure(errors);
      });
  });
};

export const postWithToken = async (url, data) => {
  const loaderElement = document.querySelector("#table-loader");
  const loaderContactTable = document.querySelector("#cust-contact-table");

  let user = getSessionItem("user");
  let token = null;
  user = user ? JSON.parse(user) : null;
  if (user && "token" in user) {
    token = user.token;
  }
  let response = null;
  try {
    // Base headers
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    };

    // Add device_user_session_ignore parameter to the request body if loginAsCustomer exists
    const loginAsCustomer = localStorage.getItem('loginAsCustomer');
    let body = data || {}; // Default to empty object if data is null/undefined
    
    // Check if data is a string (pre-stringified JSON) or character-by-character object
    if (loginAsCustomer) {
      // Check if body has numeric keys (character-by-character object)
      if (typeof body === 'object' && !Array.isArray(body) && body !== null) {
        const hasNumericKeys = Object.keys(body).some(key => !isNaN(parseInt(key)));
        if (hasNumericKeys) {
          try {
            // Rebuild the string from individual characters
            const rebuiltString = Object.values(body).join('');
            // Only try to parse if the string isn't empty
            if (rebuiltString && rebuiltString.trim()) {
              // Parse the rebuilt string
              const parsedBody = JSON.parse(rebuiltString);
              // Add our flag
              parsedBody.device_user_session_ignore = true;
              // Use the parsed object
              body = parsedBody;
              console.log("Reconstructed body from character array:", body);
            } else {
              // If string is empty, create a new object with the flag
              body = { device_user_session_ignore: true };
            }
          } catch (e) {
            console.error("Error reconstructing string from characters:", e);
            // Add as a separate property
            body.device_user_session_ignore = true;
          }
        } else {
          // Normal object
          body = {...body, device_user_session_ignore: true};
        }
      } else if (typeof body === 'string') {
        try {
          // Only try to parse if the string isn't empty
          if (body && body.trim()) {
            // Parse the string, add the parameter, then stringify again
            let parsedData = JSON.parse(body);
            parsedData.device_user_session_ignore = true;
            body = parsedData; // Keep as object for caching
          } else {
            // Empty string, create a new object with the flag
            body = { device_user_session_ignore: true };
          }
        } catch (e) {
          console.error("Error parsing JSON string in postWithToken:", e);
          // If parsing fails, create a new object with the flag
          body = { device_user_session_ignore: true };
        }
      } else {
        // If it's not an object or string, create a new object with the flag
        body = { device_user_session_ignore: true };
      }
    }
    
    let cached = getSessionItem(JSON.stringify([url, body]));
    if (cached) {
      if (loaderElement) {
        loaderElement.style.display = "none";
      }
      if (loaderContactTable) {
        loaderContactTable.style.display = "block";
      }
      return JSON.parse(cached);
    }
    response = await axios.post(url, body, { headers });
    setSessionItem(JSON.stringify([url, body]), response);
    if (loaderElement) {
      loaderElement.style.display = "none";
    }
    if (loaderContactTable) {
      loaderContactTable.style.display = "block";
    }
    return response;
  } catch (error) {
    console.error("postWithToken Error:", error);
    if ("response" in error) {
      if ("data" in error.response) {
        if (
          error.response.status === 400 &&
          "message" in error.response.data &&
          error.response.data.message === "Unauthorized"
        ) {
          // Handle Unauthorized error
          if (loaderElement) {
            loaderElement.style.display = "none";
          }
          if (loaderContactTable) {
            loaderContactTable.style.display = "block";
          }
          localStorage.removeItem('user');
          localStorage.removeItem('loginAsCustomer'); // Also remove loginAsCustomer
          sessionStorage.clear();
          // window.location.href = "/";
          sessionStorage.clear();
        } else if (
          error.response.status === 401 &&
          "message" in error.response.data &&
          error.response.data.message === "Unauthorized"
        ) {
          // Redirect and remove user for 401 Unauthorized
          localStorage.removeItem('user');
          localStorage.removeItem('loginAsCustomer'); // Also remove loginAsCustomer
          sessionStorage.clear();
          // window.location.href = "/";
          return error;
        }
      }
    }
    if (loaderElement) {
      loaderElement.style.display = "none";
    }
    if (loaderContactTable) {
      loaderContactTable.style.display = "block";
    }
    return response;
  }
};

export const PostWithTokenNoCache = async (url, data) => {
  let user = getSessionItem("user");
  let token = null;
  user = user ? JSON.parse(user) : null;
  if (user && "token" in user) {
    token = user.token;
  }
  if (token == null && data && "token" in data) {
    token = data.token;
  }
  try {
    // Base headers
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    };

    // Add device_user_session_ignore parameter to the request body if loginAsCustomer exists
    const loginAsCustomer = localStorage.getItem('loginAsCustomer');
    let body = data || {}; // Default to empty object if data is null/undefined
    
    // Check for character-by-character object or string
    if (loginAsCustomer) {
      // Check for character-by-character object (has numeric keys like 0, 1, 2...)
      if (typeof body === 'object' && !Array.isArray(body) && body !== null) {
        const hasNumericKeys = Object.keys(body).some(key => !isNaN(parseInt(key)));
        if (hasNumericKeys) {
          try {
            // Rebuild the string from individual characters
            const rebuiltString = Object.values(body).join('');
            // Only try to parse if the string isn't empty
            if (rebuiltString && rebuiltString.trim()) {
              // Parse the rebuilt string
              const parsedBody = JSON.parse(rebuiltString);
              // Add our flag
              parsedBody.device_user_session_ignore = true;
              // Use the parsed object
              body = parsedBody;
            } else {
              // If string is empty, create a new object with just the flag
              body = { device_user_session_ignore: true };
            }
          } catch (e) {
            console.error("PostWithTokenNoCache - Error reconstructing string from characters:", e);
            // Add as a separate property
            body.device_user_session_ignore = true;
          }
        } else {
          // Normal object
          body = {...body, device_user_session_ignore: true};
        }
      } else if (typeof body === 'string') {
        try {
          // Only try to parse if the string isn't empty
          if (body && body.trim()) {
            // Parse the string, add the parameter, then stringify again
            let parsedData = JSON.parse(body);
            parsedData.device_user_session_ignore = true;
            body = parsedData;
          } else {
            // If empty string, create a new object with just the flag
            body = { device_user_session_ignore: true };
          }
        } catch (e) {
          console.error("Error parsing JSON string in PostWithTokenNoCache:", e);
          // If parsing fails, create a new object with the flag
          body = { device_user_session_ignore: true };
        }
      } else {
        // If it's not an object or string, create a new object with the flag
        body = { device_user_session_ignore: true };
      }
    }

    const response = await axios.post(url, body, { headers });

    if (response.status === 200) {
      return response;
    } else if (
      response.response && 
      response.response.status === 401 &&
      "message" in response.response.data &&
      response.response.data.message === "Unauthorized"
    ) {
      // Handle Unauthorized (401) error
      localStorage.removeItem('user');
      localStorage.removeItem('loginAsCustomer');
      sessionStorage.clear();
      // window.location.href = "/";
      return response;
    } else if (response.status === 400) {
      throw {
        status: response.status,
        message: response.data.message || "Bad Request",
      };
    }
    return response;
  } catch (error) {
    console.error("PostWithTokenNoCache Error:", error);
    if (error.response) {
      if (error.response.status === 401 &&
          "message" in error.response.data &&
          error.response.data.message === "Unauthorized") {
        // Handle Unauthorized (401) error
        localStorage.removeItem('user');
        localStorage.removeItem('loginAsCustomer');
        sessionStorage.clear();
        // window.location.href = "/";
      }
      // Return the error response for other error types
      throw {
        status: error.response.status,
        message: error.response.data.message || "Request failed",
        response: error.response
      };
    } else {
      throw error;
    }
  }
};

export const fetchProfilePicture = async (url, data) => {
  let user = getSessionItem("user");
  let token = null;
  user = user ? JSON.parse(user) : null;
  if (user && "token" in user) {
    token = user.token;
  }

  // Check if loginAsCustomer exists in localStorage
  const loginAsCustomer = localStorage.getItem('loginAsCustomer');

  let myHeaders = new Headers();
  myHeaders.append("Authorization", `Bearer ${token}`);
  myHeaders.append("Content-Type", "application/json");
  
  // Handle different types of data
  let requestBody = {};
  
  // Process the data parameter
  if (data) {
    // Check for character-by-character object (has numeric keys like 0, 1, 2...)
    if (typeof data === 'object' && !Array.isArray(data) && data !== null) {
      const hasNumericKeys = Object.keys(data).some(key => !isNaN(parseInt(key)));
      if (hasNumericKeys) {
        try {
          // Rebuild the string from individual characters
          const rebuiltString = Object.values(data).join('');
          // Try to parse it as JSON
          try {
            if (rebuiltString && rebuiltString.trim()) {
              requestBody = JSON.parse(rebuiltString);
            } else {
              requestBody = {}; // Empty object if string is empty
            }
          } catch (e) {
            // If not valid JSON, use as string
            requestBody = { data: rebuiltString };
          }
        } catch (e) {
          console.error("Error processing character-by-character object in fetchProfilePicture:", e);
          requestBody = { ...data }; // Just use as-is
        }
      } else {
        // Normal object, use directly
        requestBody = { ...data };
      }
    } else if (typeof data === 'string') {
      try {
        // Parse the string to get the object
        if (data && data.trim()) {
          requestBody = JSON.parse(data);
        } else {
          requestBody = {}; // Empty object if string is empty
        }
      } catch (e) {
        console.error("Error parsing JSON string in fetchProfilePicture:", e);
        // If parsing fails, create a new object with the string as a property
        requestBody = { data: data };
      }
    } else {
      requestBody = {}; // Default to empty object for null/undefined
    }
  }
  
  // Add device_user_session_ignore as a top-level property
  if (loginAsCustomer) {
    requestBody.device_user_session_ignore = true;
  }

  let requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: JSON.stringify(requestBody),
    redirect: "follow",
  };

  try {
    const response = await fetch(url, requestOptions);
    
    if (response.ok) {
      const blob = await response.blob();
      const imageURL = URL.createObjectURL(blob);
      return imageURL;
    } else {
      // Log error details for debugging
      console.error("Profile Picture Error:", {
        status: response.status,
        statusText: response.statusText
      });
      
      // Check if the status is 401 (Unauthorized)
      if (response.status === 401) {
        localStorage.removeItem('user');
        localStorage.removeItem('loginAsCustomer');
        sessionStorage.clear();
        // window.location.href = "/";
      }
      return null;
    }
  } catch (error) {
    console.error("Profile Picture Fetch Error:", error);
    return null;
  }
};

export const getProfilePictureUsingUserId = async (url, userId) => {
  let user = getSessionItem("user");
  let token = null;
  user = user ? JSON.parse(user) : null;
  if (user && "token" in user) {
    token = user.token;
  }

  // Check if loginAsCustomer exists in localStorage
  const loginAsCustomer = localStorage.getItem('loginAsCustomer');

  const myHeaders = new Headers();
  myHeaders.append("Content-Type", "application/json");
  myHeaders.append("Authorization", `Bearer ${token}`);

  let requestUrl;
  let requestOptions;
  let requestBody = {};

  // Process userId if needed (handle all possible formats)
  let processedUserId = userId;
  
  if (userId && typeof userId === 'object' && !Array.isArray(userId)) {
    const hasNumericKeys = Object.keys(userId).some(key => !isNaN(parseInt(key)));
    if (hasNumericKeys) {
      try {
        // Rebuild the string from individual characters
        const rebuiltString = Object.values(userId).join('');
        // Try to parse it if it's JSON
        try {
          if (rebuiltString && rebuiltString.trim()) {
            processedUserId = JSON.parse(rebuiltString);
          } else {
            processedUserId = null; // Default to null if empty string
          }
        } catch (e) {
          // If not JSON, use the string
          processedUserId = rebuiltString;
        }
      } catch (e) {
        console.error("Error processing character-by-character userId:", e);
        // Use original if reconstruction fails
      }
    }
  } else if (typeof userId === 'string') {
    try {
      // Try to parse in case it's a stringified JSON
      if (userId && userId.trim()) {
        processedUserId = JSON.parse(userId);
      } else {
        processedUserId = null; // Default to null if empty string
      }
    } catch (e) {
      // If parsing fails, use it as a regular string (not an error)
      processedUserId = userId;
    }
  }
  
  // Always use POST when logged in as customer
  if (loginAsCustomer) {
    // Build the request body with the processed userId
    if (processedUserId !== null && processedUserId !== undefined) {
      requestBody.user_id = processedUserId;
    } else {
      requestBody.user_id = null;
    }
    
    // Add device_user_session_ignore flag
    requestBody.device_user_session_ignore = true;
    
    requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: JSON.stringify(requestBody),
      redirect: "follow",
    };
    requestUrl = url; // Use base URL for POST
  } else {
    // Regular GET request with user_id as query parameter
    requestOptions = {
      method: "GET",
      headers: myHeaders,
      redirect: "follow",
    };
    requestUrl = `${url}?user_id=${encodeURIComponent(userId)}`;
  }

  console.log("Profile Picture By UserId Request:", {
    url: requestUrl,
    method: requestOptions.method,
    headers: Object.fromEntries(myHeaders.entries()),
    body: requestOptions.body
  });

  try {
    const response = await fetch(requestUrl, requestOptions);
    
    if (response.ok) {
      const blob = await response.blob();
      const imageURL = URL.createObjectURL(blob);
      return imageURL;
    } else {
      // Log error details for debugging
      console.error("Profile Picture By UserId Error:", {
        status: response.status,
        statusText: response.statusText
      });
      
      // Check if the status is 401 (Unauthorized)
      if (response.status === 401) {
        localStorage.removeItem('user');
        localStorage.removeItem('loginAsCustomer');
        sessionStorage.clear();
        // window.location.href = "/";
      }
      return null;
    }
  } catch (error) {
    console.error("Profile Picture By UserId Fetch Error:", error);
    return null;
  }
};

export const uploadProfilePicture = async (url, data) => {
  let user = getSessionItem("user");
  let token = null;
  user = user ? JSON.parse(user) : null;
  if (user && "token" in user) {
    token = user.token;
  }

  // Check if loginAsCustomer exists in localStorage
  const loginAsCustomer = localStorage.getItem('loginAsCustomer');

  let myHeaders = new Headers();
  myHeaders.append("Authorization", `Bearer ${token}`);

  let formdata = new FormData();
  
  // Handle different types of data for the picture
  if (data instanceof Blob || data instanceof File) {
    // If it's a Blob or File, use it directly
    formdata.append(
      "picture",
      data,
      "profile-picture.png"
    );
  } else if (typeof data === 'string' && data.startsWith('data:')) {
    // Handle base64 data URL
    try {
      const response = await fetch(data);
      const blob = await response.blob();
      formdata.append(
        "picture",
        blob,
        "profile-picture.png"
      );
    } catch (e) {
      console.error("Error converting data URL to blob:", e);
      return null;
    }
  } else if (typeof data === 'object' && data !== null) {
    // If it's a regular object, try to extract the file
    if ('picture' in data) {
      formdata.append("picture", data.picture);
    } else {
      // Add all properties
      Object.entries(data).forEach(([key, value]) => {
        formdata.append(key, value);
      });
    }
  }

  // Add device_user_session_ignore parameter as a direct property 
  if (loginAsCustomer) {
    formdata.append("device_user_session_ignore", "true");
  }

  let requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: formdata,
    redirect: "follow",
  };

  console.log("Upload Profile Picture Request:", {
    url,
    headers: Object.fromEntries(myHeaders.entries()),
    formData: "FormData object with device_user_session_ignore=" + (loginAsCustomer ? "true" : "false")
  });

  try {
    const response = await fetch(url, requestOptions);

    // Handle both successful and error responses
    const contentType = response.headers.get("content-type");
    
    if (response.ok) {
      // For successful responses, handle based on content type
      if (contentType && contentType.includes("application/json")) {
        const result = await response.json();
        return result;
      } else {
        // For non-JSON responses (like binary data)
        return { status: response.status, message: "Success" };
      }
    } else {
      // For error responses, try to get error details
      const errorData = contentType && contentType.includes("application/json") 
        ? await response.json()
        : { message: response.statusText };
        
      console.error("Upload Profile Picture Error:", {
        status: response.status,
        statusText: response.statusText,
        data: errorData
      });
      
      // Check if the status is 401 (Unauthorized)
      if (response.status === 401) {
        localStorage.removeItem('user');
        localStorage.removeItem('loginAsCustomer');
        sessionStorage.clear();
        // window.location.href = "/";
      }
      
      return { 
        status: response.status, 
        error: true, 
        message: errorData.message || "Upload failed" 
      };
    }
  } catch (error) {
    console.error("Upload Profile Picture Error:", error);
    return { status: 500, error: true, message: "Network or server error" };
  }
};

export const loginPost = async (url, data) => {
  let user = getSessionItem("user");
  let token = null;
  user = user ? JSON.parse(user) : null;
  if (user && "token" in user) {
    token = user.token;
  }
  if (token == null) {
    if ("token" in data) {
      token = data.token;
    }
  }

  let response = null;
  try {
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    };
    const body = data;
    response = await axios.post(url, body, { headers });
    return response;
  } catch (err) {
    // Check if the error is a response error and has status code 401
    if (err.response && err.response.status === 401) {
      // Remove user from localStorage if status is 401
      localStorage.removeItem('user');
      localStorage.removeItem('loginAsCustomer');
      sessionStorage.clear();
      // window.location.href = "/";
    }
    return err; // Return the error object
  }
};

export const PostReqWithTokenNoCache = async (url, data) => {
  let token = null;
  if ("token" in data) {
    token = data.token;
  }

  let response = null;
  try {
    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    };
    
    // Add device_user_session_ignore parameter if loginAsCustomer exists
    const loginAsCustomer = localStorage.getItem('loginAsCustomer');
    let body = {...data};
    if (loginAsCustomer) {
      body.device_user_session_ignore = true;
    }
    
    response = await axios.post(url, body, { headers });
    return response;
  } catch (err) {
    // Check if the error is a response error and has status code 401
    if (err.response && err.response.status === 401) {
      // Remove user from localStorage if status is 401
      localStorage.removeItem('user');
      localStorage.removeItem('loginAsCustomer');
      sessionStorage.clear();
      // window.location.href = "/";
    }
    return response;
  }
};

export const trackApiCreditsUsed = async (creditsUsed, totalCredits, endpoint) => {
  try {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user || !user.email) return;
    
    const percentageUsed = totalCredits ? Math.floor((creditsUsed / totalCredits) * 100) : 0;
    
    // Track the credit usage
    await trackCreditUsage(user.email);
    
    // Store credit usage for other components to use
    localStorage.setItem('credits_used', creditsUsed);
    localStorage.setItem('total_credits', totalCredits);
    localStorage.setItem('credits_percentage', percentageUsed);
    
    // Return the percentage for immediate use
    return percentageUsed;
  } catch (error) {
    console.error('Error tracking credit usage:', error);
    return null;
  }
};