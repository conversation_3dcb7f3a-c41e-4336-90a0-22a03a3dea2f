import React,{ useState,useEffect,useContext } from 'react';
import LeftNavbar from '../../customer/filters/LeftNavbar.js';
import '../assests/css/layouts/navbar.css';
import Header from '../../customer/layouts/Header.js';
import ContactTable from '../../customer/filters/Contactstable.js';
import {useNavigate,useLocation} from 'react-router-dom';
import { ApiName } from '../../customer/common-files/ApiNames.js';
import { axiosPost,AxiosPostBearer } from '../../customer/common-files/ApiCalls.js';
import DashboardContext from '../common-files/ContextDashboard.js';
import Account from '../../customer/layouts/Account_details.js';
const Navbar = () => {
		return (
		
		<div>
			<Header /> 
			<div className=""> 
				<div className="row">
					<div className="col-md-3 menu">
						{/* <LeftNavbar  /> */}
					
					</div>
					<div className="col-md-9">
						<Account />
					</div>
				</div>
			</div>
		</div>
	

	)
}
export default Navbar;