import React, { useEffect, useState } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import AdminHeader from "../../layouts/AdminHeader.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import Popup from "../Logs/Logspopup.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import loadingGif from "../../../customer/assests/waiting.gif";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import Alert from "../../../customer/common-files/alert.js";

const Logs = () => {
  const [data, setData] = useState([]);
  const itemsPerPage = 10; // You can adjust the number of items per page here
  const [currentPage, setCurrentPage] = useState(1);
  const [popupData, setPopupData] = useState("");
  const [popupItem, setPopupItem] = useState("");
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  useEffect(() => {
    getlog();
  }, [currentPage, searchQuery]);

  const getlog = () => {
    let params = {
      currentPage: searchQuery ? 1 : currentPage,
      pageSize: searchQuery ? 100000 : itemsPerPage,
      sortBy: "DESC",
    };
    if (searchQuery) {
      params.searchParam = {
        popupName: searchQuery,
      };
    }
    searchQuery ? setLoading(false) : setLoading(true);
    let alllogs = PostWithTokenNoCache(ApiName.alllogs, JSON.stringify(params))
      .then(async function (res) {
        if (res?.status === 200) {
          const body = res?.data?.data;
          // const body = JSON.stringify(res?.data?.data);
          const items = body["items"];
          const sortedData = [...items].sort(
            (a, b) => new Date(b.dateTime) - new Date(a.dateTime)
          );
          setData(sortedData);
          setTotalCount(body["totalItems"]);
          setTotalPages(body["totalPages"]);
          setLoading(false);
          setDefaultAlert(false);
        }
      })
      .catch(function (errors) {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
      });
  };

  const formatDate = (dateTime) => {
    const date = new Date(dateTime);
    const formattedDate = `${date.getFullYear()}/${(date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${date.getDate().toString().padStart(2, "0")} - ${
      date.getHours() % 12 || 12
    }:${date.getMinutes().toString().padStart(2, "0")} ${
      date.getHours() >= 12 ? "pm" : "am"
    }`;
    return formattedDate;
  };

  const openPopup = (index) => {
    let params = {
      userAction: data[index]["userAction"],
      notificationPopupId: data[index]["groupId"],
      createdAt: data[index]["dateTime"],
    };
    let item = PostWithTokenNoCache(ApiName.findUsersByNotificationList, params)
      .then(async function (res) {
        if (res?.status === 200) {
          let item = JSON.parse(res.data.data);
          setPopupItem(item);
          setPopupData(data[index]);
          setIsPopupOpen(true);
          setDefaultAlert(false);
        }
      })
      .catch(function (errors) {
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
      });
  };

  // Function to close the popup
  const closePopup = () => {
    setIsPopupOpen(false);
  };
  const handleOutsideClick = (e) => {
    if (!e.target.closest(".modal-content")) {
      setIsPopupOpen(false);
    }
  };
  const handleEscKeyPress = (e) => {
    // Check if the pressed key is ESC
    if (e.key === "Escape") {
      setIsPopupOpen(false);
    }
  };

  useEffect(() => {
    // Add event listeners when the component mounts
    document.addEventListener("mousedown", handleOutsideClick);
    document.addEventListener("keydown", handleEscKeyPress);

    // Remove event listeners when the component unmounts
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
      document.removeEventListener("keydown", handleEscKeyPress);
    };
  }, []);

  function removeHtmlTags(str) {
    if (str === undefined || str === null || str === "") return "-";
    return str.replace(/<[^>]*>?/gm, "");
  }

  return (
    <div className={S.app}>
      <AdminHeader />
      <div className="d-flex flex-row">
        <div className="" style={{ width: "350px" }}>
          <LeftSideNav />
        </div>
        {!loading ? (
          <div style={{ width: "100%", padding: "0 10px 0 5px" }}>
            <div className="adminpanelsearchbyduration">
              <div className={S.savedlistbackgroundcolors}>
                <div className="d-flex flex-row justify-content-between">
                  <div>
                    <p className={S.durationtext}>In-App Logs</p>
                  </div>
                  <div className="searchduriconsclass">
                    <span
                      className="fa fa-search"
                      style={{
                        position: "absolute",
                        color: "#7E8C9C",
                        margin: "21px 0 0 15px",
                        fontSize: "12px",
                      }}
                    ></span>
                    <input
                      placeholder="Search by Prompt Name"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className={S.searchitem}
                    />
                  </div>
                </div>
                <table
                  className="yourrefrellists ml-3"
                  style={{ width: "98%" }}
                >
                  <tbody>
                    <tr
                      className="table-headers-saved-list mt-2 "
                      style={{ backgroundColor: "#ebf6ff", height: "40px" }}
                    >
                      <th style={{ textAlign: "center" }}>Sl No</th>
                      <th>Data and Time</th>
                      <th>Sent</th>
                      <th>Prompt Name</th>
                      <th style={{ width: "300px" }}>Prompt Message</th>
                      <th>View</th>
                    </tr>
                    {data.map((item, i) => (
                      <tr
                        key={i}
                        className="tabledatasmall"
                        style={{
                          backgroundColor: "white",
                          boxShadow: "0px 4px 4px 0px #e2e2e2",
                          height: "40px",
                        }}
                      >
                        <td style={{ textAlign: "center" }}>
                          {(currentPage - 1) * 10 + i + 1}
                        </td>
                        <td style={{ fontSize: "13px" }} className="">
                          {formatDate(item?.dateTime)}
                        </td>
                        <td>{item?.userCount} Times</td>
                        <td style={{ fontSize: "13px" }} className="">
                          {item?.records?.popupName}
                        </td>
                        <td style={{ fontSize: "13px" }} className="">
                          {removeHtmlTags(item?.records?.content)}
                        </td>
                        <td
                          style={{ textAlign: "center", fontSize: "13px" }}
                          className=""
                        >
                          <div
                            onClick={() => openPopup(i)}
                            className="d-flex flex-row justify-content-start ml-2"
                          >
                            <img
                              src="../images/eyee.png"
                              className={S.logseye}
                            />
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {
                  <Pagination
                    className="pagination-bar"
                    currentPage={currentPage}
                    totalCount={totalPages}
                    pageSize={1}
                    onPageChange={(page) => {
                      setCurrentPage(page);
                    }}
                  />
                }
              </div>
            </div>
          </div>
        ) : (
          <div className="mx-auto mt-5" style={{ display: loading }}>
            <img
              src={loadingGif}
              alt="Loading"
              className="loader"
              width="400"
            />
          </div>
        )}
      </div>
      {isPopupOpen && (
        <Popup data={popupData} item={popupItem} onClose={closePopup} />
      )}
      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};

export default Logs;
