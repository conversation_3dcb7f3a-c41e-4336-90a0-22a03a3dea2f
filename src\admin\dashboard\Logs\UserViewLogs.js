import React, { useEffect, useState } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import loadingGif from "../../../customer/assests/waiting.gif";
import Alert from "../../../customer/common-files/alert.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import AdminHeader from "../../layouts/AdminHeader.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
const UserViewLogs = () => {
  const navigate = useNavigate();
  let data_params = useParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [totalPages, setToatalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [data, setData] = useState([]);
  const itemsPerPage = 10; // You can adjust the number of items per page here
  const [currentPage, setCurrentPage] = useState(1);
  const [formattedDate, setFormattedDate] = useState("");
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();
  const location = useLocation();
  const listHistory = location.state?.item || [];
  useEffect(() => {
    // console.log("listHistory", listHistory);
    // getData();
  }, [currentPage, searchQuery]);

  const getData = async () => {
    let dataParam = JSON.parse(data_params.id);
    try {
      let params = JSON.stringify({
        page: currentPage,
        pageSize: itemsPerPage,
        sortBy: "createdAt",
        searchParams: {
          user_id: dataParam.userid,
          view_record_id: dataParam.id,
          action_type: "VIEW", // VIEW, DOWNLOAD,COUNT
        },
      });
      const res = await PostWithTokenNoCache(ApiName.fetchUserHistory, params);
      console.log("res", res);
      if (res && "status" in res) {
        if (res.status == 200) {
          setLoading(false);
          let response = res.data;
          console.log(response);
        }
      }
    } catch (errors) {
      setLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(errors?.response?.data?.message);
      setDefaultAlert(true);
    }
  };
  const back = () => {
    let data_param = JSON.parse(data_params.id);
    let data = JSON.stringify({
      id: data_param.userid,
      userPlans: data_param.userPlans,
    });
    navigate("/admin/user-insights/" + data);
  };
  useEffect(() => {
    const currentDate = new Date();
    let date = format_Date(currentDate);
    setFormattedDate(date);
  }, []);

  const format_Date = (date) => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };
  const getTotalTimeOnFilter = (requestTime, responseTime) => {
    const requestDate = new Date(requestTime);
    const responseDate = new Date(responseTime);

    // Step 2: Calculate the difference in milliseconds
    const differenceInMilliseconds = responseDate - requestDate;

    // Step 3: Convert milliseconds to hours, minutes, seconds, and milliseconds
    const hours = Math.floor(differenceInMilliseconds / (1000 * 60 * 60));
    const minutes = Math.floor(
      (differenceInMilliseconds % (1000 * 60 * 60)) / (1000 * 60)
    );
    const seconds = Math.floor((differenceInMilliseconds % (1000 * 60)) / 1000);
    const milliseconds = differenceInMilliseconds % 1000;

    // Format the difference
    const formattedDifference = `${hours}h ${minutes}m ${seconds}s ${milliseconds}ms`;
    return formattedDifference;
  };
  const searchKeyValue = (key) => {
    switch (key) {
      case "contact_name":
        return "Contact Name";
      case "contact_first_name":
        return "First Name";
      case "contact_middle_name":
        return "Middle Name";
      case "contact_last_name":
        return "Last Name";
      case "contact_job_title_1":
        return "Job Title";
      case "contact_job_title_level_1":
        return "Job Title Level";
      case "contact_job_dept_name_1":
        return "Job Department Name";
      case "contact_job_function_name_1":
        return "Job Function Name";
      case "contact_email_1":
        return "email";
      case "contact_phone_1":
        return "Phone Number";
      case "company_company_name":
        return "Company Name";
      case "company_website":
        return "Website";
      case "company_address_street":
        return "Address";
      case "company_address_city":
        return "City";
      case "company_address_state":
        return "State";
      case "company_address_zipcode":
        return "Zipcode";
      case "company_address_country":
        return "Country";
      case "company_employee_size":
        return "Company Employee Size";
      case "company_annual_revenue_amount":
        return "Company Annual Revenue Amount";
      case "sic_code":
        return "Siccode";
      case "company_industry_categories_list":
        return "Industry Category";
      case "contact_social_linkedin":
        return "Contact Social Linkedin";
      case "npi_number":
        return "NPI Number";
      default:
        return key;
    }
  };
  const getFilterType = (string) => {
    let searchstring = "";
    let array = JSON.parse(string);
    let array1 = array?.searchPattern;
    if (array1 !== undefined) {
      Object.keys(array1).forEach((key) => {
        const value = array1[key];
        if (Object.keys(value).length !== 0 && key !== "searchBy") {
          if (searchstring === "") {
            searchstring = searchKeyValue(key);
          } else {
            return searchstring;
          }
        }
      });
      return searchstring;
    } else {
      return "--";
    }
  };
  const getFilterDetail = (string) => {
    let searchstring = "";
    let array = JSON.parse(string);
    let array1 = array?.searchPattern;
    if (array1 !== undefined) {
      Object.keys(array1).forEach((key) => {
        const value = array1[key];
        if (Object.keys(value).length !== 0 && key !== "searchBy") {
          searchstring += searchKeyValue(key) + '\n';
        }
      });
      return searchstring;
    } else {
      return "--";
    }
  };
  const getFilterValues = (string) => {
    let searchstring = "";
    let array = JSON.parse(string);
    let array1 = array?.searchPattern;
    if (array1 !== undefined) {
      Object.keys(array1).forEach((key) => {
        const value = array1[key];
        if (Object.keys(value).length !== 0 && key !== "searchBy") {
          const valuesString = Object.values(value).join(", ");

          searchstring += valuesString + "\n";
        }
      });
      return searchstring;
    } else {
      return "--";
    }
  };

  return (
    <>
      <div className={S.app}>
        <AdminHeader />
        <div className={S.main_container}>
          <div className="d-flex flex-row">
            <div style={{ width: "300px" }}>
              <LeftSideNav />
            </div>
            {!loading ? (
              <div className="p-3" style={{ width: "100%" }}>
                <div className="d-flex flex-row  justify-content-between">
                  <div>
                    <p className={S.UserInsightsparagraph}>
                      Details of User View Log
                    </p>
                    <div className={S.underlinisights}></div>
                  </div>
                  <div>
                    <button
                      type="button"
                      className={S.DetailsOfUserback}
                      onClick={() => back()}
                    >
                      Back
                    </button>
                  </div>
                </div>
                <div className="d-flex flex-row  justify-content-between mt-4">
                  <div>
                    <p className={S.Detailsouserdate}>{formattedDate}</p>
                  </div>
                  <div style={{ display: "flex" }}>
                    <p className="detailsofusersavedlist">
                      <span className={S.detailsofusercoontactlist}>
                        Filter Type:
                      </span>{" "}
                      {listHistory[0].filterType}
                    </p>
                    &nbsp;&nbsp;
                    <p className="detailsofusersavedlist">
                      <span className={S.detailsofusercoontactlist}>
                        Total time on filter:
                      </span>{" "}
                      {getTotalTimeOnFilter(
                        listHistory[0].requestTime,
                        listHistory[0].responseTime
                      )}
                    </p>
                  </div>
                </div>

                <div className={S.detailsofuserisrtlayer}>
                  <div className="container">
                  <div className="row mt-4">
                    <div className="col-md-4">
                      <p className={S.detailsofuserfilter}>Filter</p>
                    </div>
                    <div className="col-md-4">
                      <p className={S.detailsofuserfilter}>Filter Details </p>
                    </div>
                    <div className="col-md-4">
                      <p className={S.detailsofuserfilter}>Filter Specified</p>
                    </div>
                  </div>
                  </div>
                
                  <div className={S.detailsofusersecondlayer}>

                    <div class="container">
                      <div class="row mt-4">
                        <div class="col">
                          <p className={S.detailsofusercommunity}>
                            {getFilterType(listHistory[0].filterApplied)}
                          </p>
                        </div>
                        <div class="col">
                          <p className={S.detailsofusercommunity} style={{ margin: "0 0 5px 15px" }}>
                            {getFilterDetail(listHistory[0].filterApplied)
                              .split("\n")
                              .map((line, index) => (
                                <React.Fragment key={index}>
                                  {line}
                                  <br />
                                </React.Fragment>
                              ))}
                          </p>
                        </div>
                        <div class="col">
                          <p className={S.detailsofusercommunity} style={{ margin: "0 0 5px 26px" }}>
                            {getFilterValues(listHistory[0].filterApplied)
                              .split("\n")
                              .map((line, index) => (
                                <React.Fragment key={index}>
                                  {line}
                                  <br />
                                </React.Fragment>
                              ))}
                          </p>
                        </div>
                      </div>
                    </div>



                  </div>
                </div>
                <div className="d-flex flex-row  justify-content-between mt-3">
                  <div>
                    <p className={S.UserInsightsparagraph}>
                      Contacts Viewed:{" "}
                      <span style={{ color: "#000" }}>{listHistory[0].filterCount}</span>
                    </p>
                  </div>
                </div>
                {/*<div className="detailsofusertablepadding">
                  <div className={S.detalsofuserscrollabe}>
                    <table className="your-refrel-lists">
                      <tbody>
                        <tr className="table-headers-saved-list mt-2">
                          <th style={{ padding: "0px 0px 0 18px" }}>
                            Contact Name
                          </th>
                          <th>Job Title</th>
                          <th>Email/Phone</th>
                          <th>Company Name</th>
                          <th>Location</th>
                        </tr>
                        <tr className="table-data-small">
                          <td className={S.datagoesheresavedlist}>
                            27/02/2024 - 10:00AM
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            Contact
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            <div className="d-flex flex-row">
                              <div className="mr-2">
                                <img src="../../images/mail-unread.png" />
                                Email
                              </div>
                              <div>
                                <img src="../../images/phone-alt.png" />
                                Phone
                              </div>
                            </div>
                          </td>
                          <td className="" style={{ fontSize: "13px" }}>
                            Electroninc And Design
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            United States, Masschusett, <br />
                            Cambridge
                          </td>
                        </tr>

                        <tr className="table-data-small">
                          <td className={S.datagoesheresavedlist}>
                            27/02/2024 - 10:00AM
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            Contact
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            <div className="d-flex flex-row">
                              <div className="mr-2">
                                <img src="../../images/mail-unread.png" />
                                Email
                              </div>
                              <div>
                                <img src="../../images/phone-alt.png" />
                                Phone
                              </div>
                            </div>
                          </td>
                          <td className="" style={{ fontSize: "13px" }}>
                            Electroninc And Design
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            United States, Masschusett, <br />
                            Cambridge
                          </td>
                        </tr>
                        <tr className="table-data-small">
                          <td className={S.datagoesheresavedlist}>
                            27/02/2024 - 10:00AM
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            Contact
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            <div className="d-flex flex-row">
                              <div className="mr-2">
                                <img src="../../images/mail-unread.png" />
                                Email
                              </div>
                              <div>
                                <img src="../../images/phone-alt.png" />
                                Phone
                              </div>
                            </div>
                          </td>
                          <td className="" style={{ fontSize: "13px" }}>
                            Electroninc And Design
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            United States, Masschusett, <br />
                            Cambridge
                          </td>
                        </tr>
                        <tr className="table-data-small">
                          <td className={S.datagoesheresavedlist}>
                            27/02/2024 - 10:00AM
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            Contact
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            <div className="d-flex flex-row">
                              <div className="mr-2">
                                <img src="../../images/mail-unread.png" />
                                Email
                              </div>
                              <div>
                                <img src="../../images/phone-alt.png" />
                                Phone
                              </div>
                            </div>
                          </td>
                          <td className="" style={{ fontSize: "13px" }}>
                            Electroninc And Design
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            United States, Masschusett, <br />
                            Cambridge
                          </td>
                        </tr>
                        <tr className="table-data-small">
                          <td className={S.datagoesheresavedlist}>
                            27/02/2024 - 10:00AM
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            Contact
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            <div className="d-flex flex-row">
                              <div className="mr-2">
                                <img src="../../images/mail-unread.png" />
                                Email
                              </div>
                              <div>
                                <img src="../../images/phone-alt.png" />
                                Phone
                              </div>
                            </div>
                          </td>
                          <td className="" style={{ fontSize: "13px" }}>
                            Electroninc And Design
                          </td>
                          <td style={{ fontSize: "13px" }} className="">
                            United States, Masschusett, <br />
                            Cambridge
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div className="d-flex flex-row justify-content-between mt-3">
                    <div>
                      <p className={S.insightsSearchResultFound}>
                        Search Result Found
                        <span className={S.insightscontacts}>
                          {" "}
                          200 Contacts
                        </span>
                      </p>
                    </div>
                    <div>
                      <p>Pagination goes here</p>
                    </div>
                  </div>
                </div> */}
              </div>
            ) : (
              <div
                className="d-flex flex-row justify-content-center mt-5"
                style={{ display: loading, width: "100%" }}
              >
                <img
                  src={loadingGif}
                  alt="Loading"
                  className="loader"
                  width="400"
                  height="400"
                />
              </div>
            )}
            {defaultAlert && defaultErrorMsg ? (
              <Alert data={defaultErrorMsg} />
            ) : (
              <></>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default UserViewLogs;
