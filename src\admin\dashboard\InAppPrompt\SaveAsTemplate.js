import React from "react";
import S from "../../assets/css/layouts/admin-header.module.css";

const SendPromocodePopup = () => {
    return (
        <>

            <button type="button" className="btn btn-primary" data-toggle="modal" data-target="#exampleModalCenter">
               Save As New Template
            </button>
            

            <div className="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                <div className="modal-dialog modal-dialog-centered" role="document">
                    <div className="modal-content">
                        <div className="row">
                            <div className="col-md-10 text-center">
                                <h5 className={S.SentPrompt} >Save As Template</h5>

                            </div>
                            <div className="col-md-2">
                                <button type="button" className="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        </div>
                        <div className="modal-body">
                            <div className="form-group align-center">

                                <input type="text" className={S.savetemplate} id="exampleInputText1" aria-describedby="textHelp" />
                            </div>
                        </div>
                        <div className="text-center">
                            <button type="button" className={S.savechanges2}>Send</button>
                        </div>

                        <div className={S.successmessage}>
                            <div className="d-flex flex-row">
                                <div>
                                    <img src="./images/promocode.png" width="15" />
                                </div>
                                <div>
                                    <p className={S.que}>saved successfully</p>
                                </div>
                            </div>
                        </div>
                        <div className="mt-3"></div>
                    </div>
                </div>
            </div>
        </>
    );
}

export default SendPromocodePopup;