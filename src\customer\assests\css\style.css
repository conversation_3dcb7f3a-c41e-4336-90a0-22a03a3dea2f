@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap');

.form-box2 {
    max-width: 450px;
    margin: 0px 17px 4px;
    padding: 0 30px 0 30px;
    background: #ffffff;
    border: 6px solid #fff;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
    border-radius: 19px;
    /* margin-top: 14%; */
}

.margin-top-14 {
    margin-top: 10%;
}

.mouse-pointer {
    cursor: pointer;
}

.form-box3 {
    /* max-width: 450px; */
    margin: 0px 17px 4px;
    padding: 0 25px 0 25px;
    background: #ffffff;
    border: 6px solid #fff;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
    border-radius: 19px;
    margin-top: 14%;
}

.form-box3 h3 {
    text-align: center;
    padding: 0 0 9px 0px;
    font-weight: 600;
    margin-top: 23px;
    color: #093D54;
}

.form-box2 h3 {
    text-align: center;
    padding: 15px 0 9px 0px;
    font-weight: 600;
    margin-top: 23px;
    color: #093D54;
}

.dummy-class {
    margin-bottom: 10%;
}

span.manage {
    padding: 0 0 0 5px;
}



span.email-address p {
    margin: 0px 0 10px 0px;
}

span.password p {
    margin: 0 0 10px 0;
}

p.semi-header1 {
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #000000;
    margin: 0;
}

p.semi-header1 a {
    text-decoration: none;
    color: #55C2C3;
}


p.semi-header a:hover {
    color: #007bff;
}

p.password-label {
    padding: 8px 0 0 0;
}

p.password-label a {
    text-decoration: none;
}

p.password-label a:hover {
    color: #007bff;
}

input.cp-plusss {
    background-color: #093d54;
    border: none;
    border-radius: 15px;
    color: #fff;
    cursor: pointer;
    display: block;
    font-family: Roboto, sans-serif;
    /* margin: 0 auto 10px; */
    outline: 0;
    padding: 10px 40px;
    font-size: 14px;
}

/* .banner-1 img {
    width: 500px;
    margin: 0 auto;
    display: block;
    padding-top: 6%;
    padding-bottom: 25%;
} */

.signin-banner {
    margin-top: 6%;
    margin-bottom: 30%;
}

.offset-md-2.saver-plane2 {
    margin-top: 8%;
}

.bg-color {
    background-color: #E8F7F7;
    height: 600px;
}


@media (min-width: 768px) {
    .offset-md-2 {
        margin-left: 12.666667%;
    }
}


span.email-error-message p {
    font-size: 12px;
    padding: 5px 0 0 2px;
    color: #DE350B;
    margin: 0;
}

a.password-label {
    padding: 3px 0 0 0;
    color: #55C2C3;
    text-decoration: none;
}


p.lgp {
    margin: 0;
    padding: 12px 0 0 0;
}

.cust-disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

.space-0 {
    padding: 20px 0 0 0;
}

.invalid-input-text {
    background-color: #FFF9F9;
    width: 100%;
    height: 40px;
    outline: none;
    border: 1px solid red;
    border-radius: 5px;
    padding: 0 0px 0px 12px;

}

.saver-plane-10 {
    margin-top: 8%;
    margin-bottom: 14%;
}

.main_cont {
    max-width: 445px;
    margin: 0px 17px 4px;
    background-color: #EEFFF6;
    border: 6px solid #fff;
    border-radius: 8px;
    margin-top: 14%;
    border: 1px solid #6DE1A4;
    margin-bottom: 13px;
}

span.Congratulations {
    font-size: 12px;
    padding: 0px 2px 0 0px;
    -webkit-text-stroke-width: thin;
    color: #000;
}

span.my-account {
    font-size: 12px;
    color: #7E8C9C;
}

.out img {
    padding: 0;
    margin: 4px;
}


.multiply_by {
    position: relative;
    width: 19px;
    height: 15px;
}

.css-euljzj-MultiValueRemove {
    cursor: pointer;
}

.no-cursor {
    cursor: default !important;
}

.input-red {
    border-color: red !important;
}

.disabled-async-select {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
}

.signin-banner img {
    height: auto;
}

.saver-plane-10 img {
    margin: 0rem 0 0 6rem;
}




.image-container {
    background-size: cover;
    background-position: center;
    height: 100vh;
    background-color: #e8f7f7;
    padding: 0 20px 0 0px;
}

.form-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
}


.rs-logo-img img {
    margin: 0;
    position: absolute;
    top: 5%;
    left: 24%;
}

.make-img-center img {
    display: flex;
    justify-content: center;
    width: 75%;
}

.make-img-center {
    display: flex;
    justify-content: end;
    margin: 10rem 0 0 0;
    /* width: 80%; */
}