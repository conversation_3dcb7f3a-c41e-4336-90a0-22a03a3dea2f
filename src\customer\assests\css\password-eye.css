.password-input {
  position: relative;
}

.password-input input {
  padding-right: 2.5rem;
}

.password-input button {
  position: absolute;
  top: 50%;
  right: 0.5rem;
  transform: translateY(-50%);
  border: none;
  background-color: transparent;
  cursor: pointer;
  outline: none
}

.icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 2px;
}


.input-with-icon {
  position: relative;
}

.input-with-icon input {
  padding-left: 35px; /* Make space for the icon */
}

.input-with-icon .icon {
  /* Add the CSS class defined earlier */
  position: absolute;
  top: 50%;
  transform: translateY(-40%);
  left: 0px;
 
}
.input-with-icon-text input {
 padding-left: 10px;
}