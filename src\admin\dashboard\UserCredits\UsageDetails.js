import React, { useEffect, useState } from "react";
import AdminHeader from "../../layouts/AdminHeader.js";
import S from "../../assets/css/layouts/admin-header.module.css";
import { useNavigate } from "react-router-dom";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import {
  postWithToken,
  PostWithTokenNoCache,
} from "../../../customer/common-files/ApiCalls.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import Pagination from "../../../customer/pagination/Pagination.js";

import loadingGif from "../../../customer/assests/waiting.gif";

const UsageDetails = (props) => {
  const {
    foundCounts,
    pageNumber,
    dataPagi,
    loadingCount,
    paginationDataCount,
  } = props;
  const [foundContacts, setFoundContacts] = useState(foundCounts);
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const itemsPerPage = 10; // You can adjust the number of items per page here
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const {
    setSelectedEmail,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();
  const [searchQuery, setSearchQuery] = useState("");

  function toProperCase(str) {
    if (str) {
      return str
        .toLowerCase()
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
    } else {
      return str;
    }
  }

  useEffect(() => {
    const postData = async () => {
      try {
        searchQuery ? setLoading(false) : setLoading(true);
        const params = JSON.stringify({
          page: searchQuery ? 1 : currentPage,
          pageSize: searchQuery ? 100000 : itemsPerPage,
          searchParams: {
            userRole: "customer",
            planStatus: "active",
            planStatus: "active",
            email: searchQuery,
          },
          sortBy: "DESC",
        });

        const res = await PostWithTokenNoCache(
          ApiName.getAllUserPlansPagination,
          params
        );

        if (res && "status" in res) {
          if (res.status == 200) {
            let data = JSON.parse(res.data.data);
            const all_users = data.all_user.items;
            setTotalCount(data["all_user"]["totalItems"]);
            setTotalPages(data["all_user"]["totalPages"]);

            const mergedData = all_users
              .map((user) => {
                const date = new Date(user.user_created_at);
                const year = date.getFullYear();
                const month = date.getMonth() + 1; // months are zero-based, so add 1
                const day = date.getDate();

                const totalAssignedCredit = parseInt(
                  user.totalAssignedCredit
                );
                const totalBalanceCredit = parseInt(
                  user.totalBalanceCredit
                );
                const assignedContactView = parseInt(
                  user.totalAssignedContactView
                );
                const balanceCotactView = parseInt(
                  user.totalBalanceContactView
                );
                const additional_credit_assigned =
                  user.additionalCreditAssigned
                    ? parseInt(user.additionalCreditAssigned)
                    : 0;
                return {
                  id: user.userId,
                  email: user.email,
                  phone: user.phone,
                  first_name: user.firstName,
                  last_name: user.lastName,
                  total_assigned_credit: user.totalAssignedCredit,
                  // package_type: matchingUserPlan.user_plan_name == "super_saver" ? 'Super Saver Plan' : matchingUserPlan.user_plan_name == "freemium" ? "Freemium" ? matchingUserPlan.user_plan_name == "APP_SUMO_59" ? "App Sumo" : matchingUserPlan.user_plan_name : matchingUserPlan.user_plan_name : matchingUserPlan.user_plan_name,
                  status: user.userStatus,
                  createdAt: `${year}-${month < 10 ? "0" + month : month}-${day < 10 ? "0" + day : day
                    }`,
                  viewCreditsUsed:
                    user.additionalContactViewAssigned ==
                      "Unlimited"
                      ? user.totalBalanceContactView
                      : user.totalAssignedContactView ==
                        "Unlimited"
                        ? user.totalBalanceContactView
                        : assignedContactView - balanceCotactView,
                  downloadCreditUsed:
                    additional_credit_assigned + totalAssignedCredit >
                      totalBalanceCredit
                      ? additional_credit_assigned +
                      totalAssignedCredit -
                      totalBalanceCredit
                      : totalBalanceCredit -
                      (additional_credit_assigned + totalAssignedCredit),
                  avilableCreditView:
                    user.additionalContactViewAssigned ==
                      "Unlimited"
                      ? "Unlimited"
                      : user.totalAssignedContactView ==
                        "Unlimited"
                        ? "Unlimited"
                        : parseInt(user.totalBalanceContactView),
                  availableDownloadCredits:
                    user.totalBalanceCredit,
                  typeOfSubcriber:
                    user.userPlanName == "super_saver"
                      ? "Super Saver Plan"
                      : user.userPlanName == "freemium"
                        ? "Freemium"
                          ? user.userPlanName == "App_sumo_59"
                            ? "App Sumo"
                            : toProperCase(user.userPlanName)
                          : toProperCase(user.userPlanName)
                        : toProperCase(user.userPlanName),
                };
              })
              .filter(Boolean);
            setData(mergedData);
            setLoading(false);
          }
        }
      } catch (errors) {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(errors?.response?.data?.message);
        setDefaultAlert(true);
      }
    };
    postData();
  }, [currentPage, searchQuery]);
  
  const handleCredit = (email) => {
    setSelectedEmail(email);
    navigate("/admin/add-credit");
  };
  return (
    <div className={S.app}>
      <AdminHeader />
      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div style={{ width: "100%", padding: "0 10px 0 0" }}>
              <div className={S.content}>{/* Main content */}</div>

              <aside className={S.right_sidebar}>
                <div className="d-flex flex-row justify-content-between">
                  <div>
                    <h5 className={S.FreemiumUsers}>Usage Details</h5>
                  </div>
                  <div className="admin-search">
                    <input
                      type="text"
                      placeholder="Search by Email"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className={S.searchbyemail}
                    />
                  </div>
                </div>
                <table>
                  <thead>
                    <tr className="table-active text-center">
                      <th className={S.tableheaderspace}>Subscriber ID</th>
                      <th className={S.tableheaderspaces}>Email</th>
                      <th className={S.tableheaderspace}>Type of Subscriber</th>
                      <th className={S.tableheaderspace}>View Credits Used</th>
                      <th className={S.tableheaderspace}>
                        Available View Credits{" "}
                      </th>
                      <th className={S.tableheaderspace}>
                        Download Credits Used
                      </th>
                      <th className={S.tableheaderspace}>
                        Available Download Credits
                      </th>
                      <th className={S.tableheaderspace}>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((item, i) => (
                      <tr key={i + 1} className={S.tablelight}>
                        <td className={S.tabledataspaceing}>{item.id}</td>
                        <td className={S.tabledataspaceingg}>
                          {item.email}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {item.typeOfSubcriber}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {isNaN(item.viewCreditsUsed) &&
                            item.typeOfSubcriber == "Preemium"
                            ? isNaN(item.avilableCreditView)
                              ? "0"
                              : item.avilableCreditView
                            : item.viewCreditsUsed}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {item.avilableCreditView}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {item.downloadCreditUsed}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {item.availableDownloadCredits}
                        </td>
                        <td className={S.tabledataspaceing}>
                          <button
                            onClick={() => handleCredit(item.email)}
                            className="btn btn-outline-secondary mr-2"
                          >
                            Add Credit
                          </button>
                        </td>
                        <td className={S.tabledataspaceing}></td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </aside>
            </div>
          ) : (
            <div className="mx-auto mt-5" style={{ display: loading }}>
              <img
                src={loadingGif}
                alt="Loading"
                className="loader"
                width="400"
              />
            </div>
          )}
        </div>
      </div>
      {searchQuery ? (
        ""
      ) : (
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalPages}
          pageSize={1}
          onPageChange={(page) => {
            setCurrentPage(page);
          }}
        />
      )}
    </div>
  );
};
export default UsageDetails;
