import React, { useState, useEffect } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import LeftSideNav from '../../layouts/LeftSideNav.js';
import { useNavigate } from 'react-router-dom';
import AdminHeader from "../../layouts/AdminHeader.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import {
    postWithToken,
    PostWithTokenNoCache,
} from "../../../customer/common-files/ApiCalls.js";
import SaveAsTempalate from './SaveAsTemplate.js';
const AppPrompt = () => {
    const [templateNames, setTemplateNames] = useState(null);
    const [displayContent, setDisplayContent] = useState(null);
    const [readOnly, setReadOnly] = useState(true);
    const [showSaveAsNewTemplate, setShowSaveAsNewTemplate] = useState(false);
    const [newTemplateName, setNewTemplateName] = useState('');
    const [templateError, setTemplateError] = useState('');
    const [contentName, setContentName] = useState('');
    const [openPopup, setOpenPopup] = useState(false);
    const [saveTempalteMessage, setSaveTemplateMessage] = useState(false);
    const [isEdited, setIsEdited] = useState(false);
    const navigate = useNavigate();
    const [templateId, setTemplateId] = useState();
    const [tempalteNameEdit, setTemplateNameEdit] = useState(false);
    const [editSuccessMsg, setEditSccessmMsg] = useState(false);
    const [showSendEdit, setShowSendEdit] = useState(false);
    const [createNewBtnClicked, setCreateNewButtonClicked] = useState(false);
    const [successfullySubmitedTemplate, setSuccSubmittedTemp] = useState(false);

    const signoutHandler = async () => {
        return new Promise((resolve) => {
            sessionStorage.clear();
            resolve();
            navigate('/');
        });

    }
    useEffect(() => {
        try {
            PostWithTokenNoCache(ApiName.getAllTemplates, {})
                .then(function (response) {
                    if (response.data.status === 200) {
                        const dataObj = JSON.parse(response.data.data);
                        setTemplateNames(dataObj);
                        //console.log(dataObj);
                    }
                }).catch(function (errors) {
                });
        } catch (error) {
            console.error(error);
        }
        /*  const data={
             popupName:"Template 2",
             action:"create",
             content:"Do you have other suggestions",
         } */
        /* const data={
            popupName:["Template"],
            action:["create"],
            content:["Do you have other suggestions"],
        } */
        /* try {
         PostWithTokenNoCache(ApiName.createTemplate, data)
            .then(function (response) {
                if (response.data.status === 200) {
                    const dataObj = JSON.parse(response.data.data);
                   console.log('create-template',dataObj);
                }
            }).catch(function (errors) {
            });
        } catch (error) {
            console.error(error);
        } */


    }, [])
    const displayContentFunction = (index) => {
        setDisplayContent(templateNames[index].content);
        setTemplateId(templateNames[index].id);
        setReadOnly(true);
        setShowSaveAsNewTemplate(false);
        setTemplateError(false);
        setTemplateNameEdit(templateNames[index].popupName);
        setIsEdited(false);
        setShowSendEdit(true)
        setEditSccessmMsg(false);
        setCreateNewButtonClicked(false);

    }
    const createTemplate = () => {
        setDisplayContent("");
        setReadOnly(false);
        setTemplateId('');
        setShowSaveAsNewTemplate(true);
        setTemplateNameEdit(false);

        setEditSccessmMsg(false);
        setIsEdited(false);
        setCreateNewButtonClicked(true);




    }
    const handleChange = (event) => {
        setDisplayContent(event.target.value);
        setContentName(event.target.value);
    };
    const closePopup = () => {
        setTemplateError(null);
        setSaveTemplateMessage(false);
        setNewTemplateName(null);
        setSuccSubmittedTemp(false);
    }
    const submitNewTemplateInPopup = () => {
        if (1 >= newTemplateName.length) {
            setTemplateError('Enter Template Name');
        } else if (1 >= contentName.length) {
            setTemplateError('Enter Template Content');
        } else {

        }
        if (1 <= newTemplateName.length && 1 <= contentName.length) {
            const data = {
                popupName: newTemplateName,
                action: "create",
                content: contentName,
            }
            try {
                PostWithTokenNoCache(ApiName.createTemplate, data)
                    .then(function (response) {
                        if (response.data.status === 200) {
                            const dataObj = JSON.parse(response.data.data);
                            //console.log('create-template', dataObj);
                            setSaveTemplateMessage(true);
                            setSuccSubmittedTemp(true);
                            //console.log('templateNames',templateNames);
                            PostWithTokenNoCache(ApiName.getAllTemplates, {})
                                .then(function (response) {
                                    if (response.data.status === 200) {
                                        const dataObj = JSON.parse(response.data.data);
                                        setTemplateNames(dataObj);
                                        //console.log(dataObj);
                                    }
                                }).catch(function (errors) {
                                });
                        }

                    }).catch(function (errors) {
                    });
            } catch (error) {
                console.error(error);
            }

        }


    }
    const submitNewTemplate = () => {
        setOpenPopup(true);
    }
    const onChangeTemplateName = (event) => {
        setNewTemplateName(event.target.value);

    }
    const submitTemplate = () => {
        navigate('/admin/send-user-notifications', {
            state: {
                templateId: templateId,
            }
        });
    }
    const editTemplate = () => {
        setReadOnly(false);
        setIsEdited(true);
        setShowSendEdit(false);
    }
    const editTemplateData = () => {
        const data = {
            "id": templateId,
            "popupName": tempalteNameEdit,
            "action": "edited",
            "content": contentName
        }
        try {
            PostWithTokenNoCache(ApiName.updateTemplate, data)
                .then(function (response) {
                    if (response.data.status === 200) {
                        const dataObj = JSON.parse(response.data.data);
                        setReadOnly(true);
                        setEditSccessmMsg(true);
                        setIsEdited(false);
                        setShowSendEdit(true);
                        setTimeout(() => {
                            setEditSccessmMsg(false)
                        }, 3000)

                    }
                    //setSaveTemplateMessage(true);
                }).catch(function (errors) {
                });
        } catch (error) {
            console.error(error);
        }

    }

    return (
        <div className={S.app}>
            <AdminHeader />
            <div className={S.main_container}>
                <div className="d-flex flex-row">
                    <div className="" style={{ width: "350px" }}>
                        <LeftSideNav />
                    </div>
                    <div style={{ width: "100%" }}>
                        <div className={S.content}>
                            <div className={S.inapppromt}>
                                <h5>In-App Prompt</h5>
                            </div>
                            <div className={S.grayback}>
                                <div className="d-flex flex-row">
                                    <div className="dropdown show pl-4 mt-4">
                                        <a class="dropdown-toggle" href="#" role="button"
                                            id={tempalteNameEdit ? S.dropdownMenuLinkkkAfter : S.dropdownMenuLinkkk} data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            {tempalteNameEdit ? tempalteNameEdit : "Prompt Templates"}
                                        </a>

                                        <div className="dropdown-menu" id="arrow-top" style={{ width: "174px" }} aria-labelledby="dropdownMenuLinkkk">
                                            {templateNames && templateNames.map((item, index) => (
                                                <a key={index} className="dropdown-item" onClick={() => { displayContentFunction(index) }}>
                                                    {item.popupName}
                                                </a>
                                            ))}

                                        </div>
                                    </div>

                                    <div>

                                        {createNewBtnClicked ? (<button className={S.createnewClicked} type="button" onClick={createTemplate}>Create New&nbsp;&nbsp;&nbsp;<img src="../images/create-new-admin-2.png" width="13" style={{ color: '#093D54' }} />&nbsp;&nbsp;</button>) : (<button className={S.createnew} type="button" onClick={createTemplate}>Create New&nbsp;&nbsp;&nbsp;<img src="../images/createnew.png" width="13" />&nbsp;&nbsp;</button>)}

                                    </div>
                                </div>

                                <div className="form-outline p-4">
                                    <textarea className="form-control" id={S.rstextAreaExample1} rows="10" value={displayContent} onChange={handleChange} readOnly={readOnly} >

                                    </textarea>

                                    <div className="d-flex flex-row justify-content-between">
                                        <div>
                                            <div className="d-flex flex-row">
                                                <div>
                                                    {showSendEdit ? (<button type="submit" className={S.sendtemplate} onClick={submitTemplate}>Send</button>) : ("")}
                                                    {showSendEdit ? (<button type="submit" className={S.sendtemplate2} onClick={editTemplate}>Edit</button>) : ("")}
                                                    {isEdited ? (<button type="submit" className={S.sendtemplate} onClick={editTemplateData}>Save</button>) : ("")}

                                                    {editSuccessMsg && (
                                                        <div className="d-flex flex-row" style={{ border: "1px solid #6DE1A4", backgroundColor: "#EEFFF6", margin: "5px 0 0 0", padding: "0 0 0 8px", borderRadius: "5px" }}>
                                                            <div>
                                                                <img src="../images/promocode.png" width="15" />
                                                            </div>
                                                            <div>
                                                                <p className={S.que}>Saved successfully
                                                                </p>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>

                                            </div>
                                        </div>
                                        <div>
                                            {showSaveAsNewTemplate ? (<button type="submit" className={S.sendtemplate2} data-toggle="modal" data-target="#exampleModalCenter" onClick={submitNewTemplate}>Save As New Template</button>) : ""}

                                            <div className="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                                                <div className="modal-dialog modal-dialog-centered" role="document">
                                                    <div className="modal-content">
                                                        <div className="row">
                                                            <div className="col-md-10 text-center">
                                                                <h5 className={S.SentPrompt} >Save As Template</h5>

                                                            </div>
                                                            <div className="col-md-2">
                                                                <button type="button" className="close" data-dismiss="modal" aria-label="Close" onClick={closePopup}>
                                                                    <span aria-hidden="true">&times;&nbsp;</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div className="modal-body">
                                                            <div className="form-group align-center">
                                                                {showSaveAsNewTemplate ? (<input type="text" name="templateName" className={S.savetemplate} id="exampleInputText1" aria-describedby="textHelp" placeholder="Template Name" onChange={onChangeTemplateName} />) : ("")}
                                                                <p className={S.templateError}> {templateError ? templateError : ""}</p>

                                                            </div>
                                                        </div>
                                                        <div className="text-center">


                                                            {successfullySubmitedTemplate ? (<button type="button" className={S.savechanges2Disable} disabled>Save</button>) : (<button type="button" className={S.savechanges2} onClick={submitNewTemplateInPopup} >Save</button>)}
                                                            {saveTempalteMessage && (<div><img src="../images/promocode.png" width="15" /><p className={S.que}>New Template Created Successfully
                                                            </p></div>
                                                            )}
                                                        </div>
                                                        <div className="mt-3"></div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>

            </div>

        </div>
    );
}

export default AppPrompt;