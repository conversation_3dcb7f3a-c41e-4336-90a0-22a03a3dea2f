@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    /* Regular */
}

.sidebarv2 {
    background: #f9f9f9;
    width: 240px;
    max-width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 0px;
    position: relative;
    overflow: auto;
    box-shadow: 0 0 6px #80808029;
    border-right: 1px solid #8080805c;
}

button.Job-Titles-v2 {
    width: -webkit-fill-available;
    border: 0;
    height: 30px;
    text-align: inherit;
    padding: 0 0 0 12px;
    background-color: transparent;
    cursor: pointer;
    outline: none;
    color: #093d54;
    font-family: "Lato", Regular;
    font-weight: 600;
    font-size: 14px;
    margin: 0 0 0 0;
}

.apply-back-v2 {
    position: fixed;
    bottom: 0;
    left: 54px;
    width: 248px;
    padding: 0px;
    background: #f9f9f9;
    margin: auto;
    display: flex;
    justify-content: center;
}

p.filter-names-v2 {
    margin: 2px 0 0px 8px;
    font-size: 14px;
    font-weight: 400;
    font-family: 'Poppins', sans-serif;

}

button.Apply-Filters-button-disable-v2 {
    /* position: sticky; */
    /* bottom: 15px; */
    padding: 5px 30px 5px 30px;
    background-color: #c2c8ce;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    font-family: poppins;
}


.search-icon-v2 {
    position: absolute;
    transform: translateY(28%);
    left: 88%;
    margin-top: 0px;
}

button.Apply-Filters-button-v2 {
    margin: 20px auto 0px auto;
    display: block;
    background-color: #55C2C3;
    font-weight: 400;
    border: 0;
    font-size: 14px;
    padding: 8px 28px 8px 28px;
    outline: none;
    color: #fff;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Poppins';
}


button.Job-Titles-v2 {
    /* width: -webkit-fill-available; */
    width: -webkit-fill-available;
    padding: 2px 0 2px 8px;
    text-align: inherit;
    /* background-color: #fff; */
    margin-left: 0px;
    margin-right: 12px;
    outline: none;
    border: 0;
    border-radius: 3px;
    margin-top: 3px;
    margin-bottom: 5px;
    color: #093d54;
    margin-top: 3px;
    cursor: pointer;
    font-family: Lato, Regular;
    font-weight: 600;
    font-size: 14px;
    font-family: 'Poppins';
}

.job-checkbox-v2 {
    font-family: 'poppins';
    color: #000;
    font-size: 14px;
}

button.btn.btn-link-v2 {
    border: 0;
    width: -webkit-fill-available;
    text-align: inherit;
    outline: none;
    text-decoration: none;
    color: #093d54;
    padding: 7px 0 7px 8px;
    font-weight: 500;
    font-size: 13px;
    background: transparent;
    font-family: 'poppins';
}