import axios from 'axios';
import { useGoogleLogin } from '@react-oauth/google';
import { useLinkedIn } from 'react-linkedin-login-oauth2';
import linkedin from 'react-linkedin-login-oauth2/assets/linkedin.png';
import { PublicClientApplication } from '@azure/msal-browser';

export const googleLogin = async () => {
    return new Promise((onSuccess, onFailure) => {
        useGoogleLogin({
            onSuccess: (tokenResponse) => {
                console.log(tokenResponse);
                if (tokenResponse.access_token) {
                    onSuccess(tokenResponse);
                }
            },
            onFailure: (error) => {
                // console.error('Authentication failed:', error);
                onFailure(error);
                // Additional error handling logic
            }, clientId: '976853816302-e4s42t3j23mr4fint0g2ge3eu6bkd8m4.apps.googleusercontent.com',
        })
    });

}