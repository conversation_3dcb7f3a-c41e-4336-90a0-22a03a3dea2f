import mixpanel from 'mixpanel-browser';
import { getFromStorage } from './customerJourneyTracking';
import FallbackTracking from './fallbackTracking';
import { envConfig } from '../config';  // Import envConfig to get environment information

// Initialize Mixpanel with improved error handling and configuration
let mixpanelInstance;

// Add event queue for sequential tracking
const eventQueue = [];
let isProcessingQueue = false;

// Process the event queue sequentially
const processEventQueue = async () => {
  if (isProcessingQueue || eventQueue.length === 0) return;
  
  isProcessingQueue = true;
  
  try {
    const nextEvent = eventQueue.shift();
    const { name, props, callback } = nextEvent;
    
    // Use the actual Mixpanel library
    if (typeof mixpanel !== 'undefined' && mixpanel.__loaded === true) {
      await new Promise((resolve) => {
        mixpanel.track(name, props, () => {
          if (callback && typeof callback === 'function') {
            callback();
          }
          resolve();
        });
      });
    } else {
      // Use fallback if Mixpanel not loaded
      FallbackTracking.track(name, props);
      if (callback && typeof callback === 'function') {
        callback();
      }
    }
  } catch (error) {
    // Silently handle errors
  } finally {
    isProcessingQueue = false;
    
    // Process next event if queue isn't empty
    if (eventQueue.length > 0) {
      setTimeout(processEventQueue, 50); // Small delay between events
    }
  }
};

try {
  const MIXPANEL_TOKEN = process.env.REACT_APP_MIXPANEL_TOKEN || '';
  
  // First check if tracking is available
  const isTrackingAvailable = () => {
    try {
      const testKey = '__mixpanel_test__';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      return true;
    } catch (e) {
      return false;
    }
  };
  
  // Check if tracking is disabled by consent or browser settings
  const isTrackingDisabled = () => {
    // Check for browser Do Not Track setting
    const dnt = navigator.doNotTrack || 
               window.doNotTrack || 
               navigator.msDoNotTrack;
    
    if (dnt === '1' || dnt === 'yes') {
      return true; // Respect browser's DNT setting
    }
    
    // Check our own consent setting
    try {
      const consentStatus = localStorage.getItem('tracking_consent');
      return consentStatus === 'declined';
    } catch (e) {
      // Use fallback storage to check consent
      try {
        return !FallbackTracking.hasConsent();
      } catch (err) {
        return true; // Default to disabled if we can't check consent
      }
    }
  };

  // Check if we should use fallback tracking
  const shouldUseFallback = !isTrackingAvailable();
  
  // Check if Mixpanel initialized correctly 
  const isMixpanelInitialized = () => {
    try {
      return typeof mixpanel !== 'undefined' && mixpanel.__loaded === true;
    } catch (e) {
      return false;
    }
  };
  
  // Initialize Mixpanel if we're not using fallback
  if (!shouldUseFallback) {
    try {
      mixpanel.init(MIXPANEL_TOKEN, {
        debug: false, // Set to false to disable debug logs
        persistence: 'localStorage',
        cookie_domain: window.location.hostname,
        secure_cookie: true,
        ip: false,
        ignore_dnt: false,
        property_blacklist: ['$device_id'], // Prevent device ID from being sent
        cross_subdomain_cookie: false, // Prevent cross-subdomain cookies
        persistence_name: 'rs_mixpanel', // Custom persistence name to avoid conflicts
        disable_persistence: false, // Don't disable persistence completely
        identify_by_default: false // Don't automatically identify by device ID
      });
    } catch (e) {
      // Silently handle initialization errors
    }
  }

// Helper function to normalize user properties
const normalizeUserProperties = (props) => {
  const normalized = {...props};
  
  // Get user data from localStorage to ensure we have the latest values
  let userData = null;
  try {
    userData = JSON.parse(localStorage.getItem('user')) || {};
  } catch (e) {
    userData = {};
  }
  
  // Ensure email is set
  if (!normalized.$email && userData.email) {
    normalized.$email = userData.email;
  }
  
  // Ensure first name and last name are set
  if (!normalized.$first_name && userData.firstName) {
    normalized.$first_name = userData.firstName;
  }
  
  if (!normalized.$last_name && userData.lastName) {
    normalized.$last_name = userData.lastName;
  }
  
  // Ensure full name is set
  if (!normalized.$name) {
    const firstName = normalized.$first_name || userData.firstName || '';
    const lastName = normalized.$last_name || userData.lastName || '';
    if (firstName || lastName) {
      normalized.$name = (firstName && lastName) ? `${firstName} ${lastName}` : firstName || lastName;
      normalized['Full Name'] = normalized.$name;
    }
  }
  
  // Ensure company is set
  if (!normalized.Company && userData.companyName) {
    normalized.Company = userData.companyName;
  }
  
  return normalized;
};

  // Create our custom implementation
  mixpanelInstance = {
    // Flag to indicate if we're using fallback
    usingFallback: shouldUseFallback || !isMixpanelInitialized(),
    
    // Flag for tracking availability
    trackingAvailable: true, // We're always "available" since we have fallback
    
    // Debug method to directly set user profile properties
    debugSetUserProfile: function(userId, firstName, lastName, company) {
      try {
        // First identify the user
        if (isMixpanelInitialized()) {
          mixpanel.identify(userId);
          
          // Create full name for display
          const fullName = (firstName && lastName) 
            ? `${firstName} ${lastName}`
            : firstName || lastName || '';
          
          // Set the standard properties directly using normalized properties
          const props = normalizeUserProperties({
            "$first_name": firstName,
            "$last_name": lastName,
            "$name": fullName, 
            "Company": company,
            "$email": userId,
            "Full Name": fullName,
            "Last Debug Update": new Date().toISOString()
          });
          
          mixpanel.people.set(props);
          
          return true;
        } else {
          return false;
        }
      } catch (error) {
        return false;
      }
    },
    
    // Add a method to clean up device IDs and merge them into the email ID
    cleanupDeviceIds: function() {
      try {
        // Get user data from localStorage
        let userData = null;
        try {
          userData = JSON.parse(localStorage.getItem('user'));
        } catch (e) {
          return false;
        }
        
        // Only proceed if we have user data
        if (!userData || !userData.email) {
          return false;
        }
        
        // Identify the user with their email
        if (isMixpanelInitialized()) {
          // Get the current distinct ID before resetting
          const currentId = mixpanel.get_distinct_id();
          
          // If the current ID is a device ID and different from the email
          if (currentId !== userData.email && currentId.includes('$device:')) {
            // First alias the device ID to the email (this merges the profiles)
            try {
              mixpanel.alias(userData.email, currentId);
            } catch (e) {
              // Silently handle error
            }
          }
          
          // Then reset the identity completely
          mixpanel.reset();
          
          // Clear any Mixpanel cookies/storage to prevent device ID persistence
          try {
            // Clear localStorage items that might contain device IDs
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && (key.includes('mp_') || key.includes('mixpanel'))) {
                localStorage.removeItem(key);
              }
            }
            
            // Clear cookies that might contain device IDs
            document.cookie.split(';').forEach(function(c) {
              if (c.trim().startsWith('mp_') || c.trim().startsWith('mixpanel')) {
                document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
              }
            });
          } catch (e) {
            // Silently handle error
          }
          
          // Then identify with the email
          mixpanel.identify(userData.email);
          
          // Create full name
          const firstName = userData.firstName || '';
          const lastName = userData.lastName || '';
          const fullName = (firstName && lastName) 
            ? `${firstName} ${lastName}`
            : firstName || lastName || '';
          
          // Set all required properties
          const props = {
            "$email": userData.email,
            "$first_name": firstName,
            "$last_name": lastName,
            "$name": fullName,
            "Full Name": fullName,
            "Company": userData.companyName || '',
            "Device IDs Cleaned": true,
            "Device IDs Cleaned Date": new Date().toISOString(),
            "Primary ID Type": "email",
            "Has Anonymous IDs": false
          };
          
          // Apply the properties
          mixpanel.people.set(props);
          
          // Also track an event for analytics
          this.track('Device IDs Cleaned', {
            email: userData.email,
            cleaned_at: new Date().toISOString(),
            previous_id: currentId
          });
          
          return true;
        }
        return false;
      } catch (error) {
        // Silently handle error
        return false;
      }
    },
    
    // Add a method to fix user profiles with missing data
    fixUserProfile: function() {
      try {
        // Get user data from localStorage
        let userData = null;
        try {
          userData = JSON.parse(localStorage.getItem('user'));
        } catch (e) {
          return false;
        }
        
        // Only proceed if we have user data
        if (!userData || !userData.email) {
          return false;
        }
        
        // Identify the user
        if (isMixpanelInitialized()) {
          mixpanel.identify(userData.email);
          
          // Create full name
          const firstName = userData.firstName || '';
          const lastName = userData.lastName || '';
          const fullName = (firstName && lastName) 
            ? `${firstName} ${lastName}`
            : firstName || lastName || '';
          
          // Set all required properties
          const props = {
            "$email": userData.email,
            "$first_name": firstName,
            "$last_name": lastName,
            "$name": fullName,
            "Full Name": fullName,
            "Company": userData.companyName || '',
            "Profile Fixed": true,
            "Profile Fixed Date": new Date().toISOString()
          };
          
          // Apply the properties
          mixpanel.people.set(props);
          
          // Also track an event for analytics
          this.track('User Profile Fixed', {
            email: userData.email,
            fixed_fields: Object.keys(props).join(', ')
          });
          
          return true;
        }
        return false;
      } catch (error) {
        // Silently handle error
        return false;
      }
    },
    
    // Add a method to completely purge and recreate a user's profile
    purgeAndRecreateProfile: function() {
      try {
        // Get user data from localStorage
        let userData = null;
        try {
          userData = JSON.parse(localStorage.getItem('user'));
        } catch (e) {
          return false;
        }
        
        // Only proceed if we have user data
        if (!userData || !userData.email) {
          return false;
        }
        
        if (isMixpanelInitialized()) {
          // First, track an event that we're about to purge
          this.track('Profile Purge Initiated', {
            email: userData.email,
            timestamp: new Date().toISOString()
          });
          
          // Completely reset Mixpanel state
          mixpanel.reset();
          
          // Clear all Mixpanel data from localStorage and cookies
          try {
            // Clear localStorage items related to Mixpanel
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && (key.includes('mp_') || key.includes('mixpanel'))) {
                localStorage.removeItem(key);
              }
            }
            
            // Clear cookies related to Mixpanel
            document.cookie.split(';').forEach(function(c) {
              if (c.trim().startsWith('mp_') || c.trim().startsWith('mixpanel')) {
                document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
              }
            });
          } catch (e) {
            // Silently handle error
          }
          
          // Wait a moment to ensure reset is complete
          setTimeout(() => {
            try {
              // Create a fresh identity with the email
              mixpanel.identify(userData.email);
              
              // Create full name
              const firstName = userData.firstName || '';
              const lastName = userData.lastName || '';
              const fullName = (firstName && lastName) 
                ? `${firstName} ${lastName}`
                : firstName || lastName || '';
              
              // Set all user properties from scratch
              const props = {
                "$email": userData.email,
                "$first_name": firstName,
                "$last_name": lastName,
                "$name": fullName,
                "Full Name": fullName,
                "Company": userData.companyName || '',
                "Profile Recreated": true,
                "Profile Recreated Date": new Date().toISOString(),
                "Primary ID Type": "email",
                "Has Anonymous IDs": false,
                "Device IDs Purged": true
              };
              
              // Apply the properties
              mixpanel.people.set(props);
              
              // Track completion event
              this.track('Profile Recreated', {
                email: userData.email,
                recreated_at: new Date().toISOString(),
                properties_set: Object.keys(props).join(', ')
              });
            } catch (e) {
              // Silently handle error
            }
          }, 500);
          
          return true;
        }
        return false;
      } catch (error) {
        // Silently handle error
        return false;
      }
    },
    
    // Add a method to remove device IDs from a user's distinct ID list
    removeDeviceIdsFromDistinctId: function(email) {
      try {
        // If no email provided, try to get from localStorage
        if (!email) {
          try {
            const userData = JSON.parse(localStorage.getItem('user'));
            if (userData && userData.email) {
              email = userData.email;
            } else {
              return false; // No email available
            }
          } catch (e) {
            return false;
          }
        }
        
        if (isMixpanelInitialized() && email) {
          // This is a workaround to force Mixpanel to use only the email as distinct_id
          // First, track that we're attempting this operation
          this.track('Remove Device IDs Attempt', {
            email: email,
            timestamp: new Date().toISOString()
          });
          
          // Reset everything
          mixpanel.reset();
          
          // Clear all Mixpanel cookies and localStorage
          try {
            // Remove any Mixpanel-related items from localStorage
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && (key.includes('mp_') || key.includes('mixpanel'))) {
                localStorage.removeItem(key);
              }
            }
            
            // Remove any Mixpanel-related cookies
            document.cookie.split(';').forEach(function(c) {
              if (c.trim().startsWith('mp_') || c.trim().startsWith('mixpanel')) {
                document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
              }
            });
          } catch (e) {
            // Silently handle error
          }
          
          // Create a completely fresh instance with just the email
          setTimeout(() => {
            try {
              // Re-identify with just the email
              mixpanel.identify(email);
              
              // Set a property to indicate this action was taken
              mixpanel.people.set({
                'Device IDs Removed': true,
                'Device IDs Removed Date': new Date().toISOString(),
                'Clean Distinct ID': true
              });
              
              // Track success
              this.track('Device IDs Removed', {
                email: email,
                timestamp: new Date().toISOString()
              });
              
              // Force a page reload to ensure all changes take effect
              // This is optional and can be commented out if not desired
              // setTimeout(() => { window.location.reload(); }, 1000);
            } catch (e) {
              // Silently handle error
            }
          }, 500);
          
          return true;
        }
        return false;
      } catch (error) {
        // Silently handle error
        return false;
      }
    },
    
    // Core tracking methods
    track: function(event_name, properties = {}, callback) {
      // Don't track if user has opted out or browser prevents tracking
      if (isTrackingDisabled()) {
        // Remove console.log from success path
        if (callback && typeof callback === 'function') {
          callback();
        }
        return;
      }
      
      try {
        // Add timestamp and environment for all events
        const enhancedProps = {
          ...properties,
          timestamp: new Date().toISOString(),
          environment: envConfig.environment || 'unknown', // Add environment information
          sequence_id: Date.now() // Add sequence ID based on timestamp
        };

        // Add location information if available
        try {
          // Extract location data properly
          const locationData = {
            url: window.location.href,
            path: window.location.pathname,
            hostname: window.location.hostname,
            domain: window.location.hostname,
            hash: window.location.hash,
            search: window.location.search,
            page_title: document.title || ''
          };
          
          // Add location data to properties
          Object.assign(enhancedProps, locationData);
          
          // Safely parse referrer
          if (document.referrer) {
            try {
              const referrerUrl = new URL(document.referrer);
              enhancedProps.referrer = document.referrer;
              enhancedProps.referrer_domain = referrerUrl.hostname;
              enhancedProps.referrer_host = referrerUrl.hostname;
              enhancedProps.referrer_protocol = referrerUrl.protocol;
            } catch (referrerError) {
              // If referrer URL can't be parsed, still include the raw value
              enhancedProps.referrer = document.referrer;
              enhancedProps.referrer_parse_error = true;
            }
          } else {
            enhancedProps.referrer = 'direct';
            enhancedProps.referrer_domain = 'direct';
            enhancedProps.referrer_host = 'direct';
          }
        } catch (locationError) {
          // Silently handle error
        }
        
        // Add to queue for sequential processing
        eventQueue.push({
          name: event_name,
          props: enhancedProps,
          callback: callback
        });
        
        // Start processing queue if not already processing
        if (!isProcessingQueue) {
          processEventQueue();
        }
      } catch (error) {
        // Silently handle error
        // Try fallback if main tracking fails
        try {
          if (!this.usingFallback) {
            FallbackTracking.track(event_name, properties);
          }
        } catch (fallbackError) {
          // Silently handle error
        }
        
        // Still call callback if provided
        if (callback && typeof callback === 'function') {
          callback();
        }
      }
    },
    
    identify: function(userId) {
      if (isTrackingDisabled()) return;
      try {
        // Try to get email from localStorage if not provided
        let userIdToUse = userId;
        if (!userId) {
          try {
            const userData = JSON.parse(localStorage.getItem('user'));
            if (userData && userData.email) {
              userIdToUse = userData.email;
            }
          } catch (e) {
            // Silently handle error
          }
        }
        
        // Only proceed if we have a userId
        if (!userIdToUse) return;
        
        // Clean up any device IDs from the userId
        if (typeof userIdToUse === 'string' && userIdToUse.includes('$device:')) {
          // If it's a device ID, try to get the email again
          try {
            const userData = JSON.parse(localStorage.getItem('user'));
            if (userData && userData.email) {
              userIdToUse = userData.email;
            } else {
              // If no email, don't identify with device ID
              return;
            }
          } catch (e) {
            // If we can't get the email, don't identify with device ID
            return;
          }
        }
        
        if (this.usingFallback) {
          FallbackTracking.identify(userIdToUse);
        } else {
          if (isMixpanelInitialized()) {
            // Reset the device ID to prevent multiple device IDs
            try {
              // Force the distinct ID to be the email only
              mixpanel.reset();
              mixpanel.identify(userIdToUse);
              
              // Register a property to indicate this is the primary ID
              mixpanel.register({
                'Primary ID': true,
                'ID Type': 'email',
                'Last Identified': new Date().toISOString()
              });
            } catch (e) {
              // Fallback to standard identify
              mixpanel.identify(userIdToUse);
            }
          } else {
            FallbackTracking.identify(userIdToUse);
          }
        }
      } catch (error) {
        // Silently handle error
        // Try fallback if main tracking fails
        try {
          if (!this.usingFallback) {
            FallbackTracking.identify(userId);
          }
        } catch (fallbackError) {
          // Silently handle error
        }
      }
    },
    
    // People methods
    people: {
      set: function(props) {
        if (isTrackingDisabled()) return;
        try {
          // Normalize properties to ensure email and name are properly set
          const normalizedProps = normalizeUserProperties(props);
          
          if (mixpanelInstance.usingFallback) {
            FallbackTracking.people.set(normalizedProps);
          } else {
            if (isMixpanelInitialized()) {
              mixpanel.people.set(normalizedProps);
            } else {
              FallbackTracking.people.set(normalizedProps);
            }
          }
        } catch (error) {
          // Silently handle error
          // Try fallback
          try {
            if (!mixpanelInstance.usingFallback) {
              FallbackTracking.people.set(props);
            }
          } catch (fallbackError) {
            // Silently handle error
          }
        }
      },
      set_once: function(props) {
        if (isTrackingDisabled()) return;
        try {
          if (mixpanelInstance.usingFallback) {
            FallbackTracking.people.set_once(props);
          } else {
            if (isMixpanelInitialized()) {
              mixpanel.people.set_once(props);
            } else {
              FallbackTracking.people.set_once(props);
            }
          }
        } catch (error) {
          // Silently handle error
          // Try fallback
          try {
            if (!mixpanelInstance.usingFallback) {
              FallbackTracking.people.set_once(props);
            }
          } catch (fallbackError) {
            // Silently handle error
          }
        }
      }
    },
    
    // Other methods
    register: function(props) {
      if (isTrackingDisabled()) return;
      try {
        if (this.usingFallback) {
          FallbackTracking.register(props);
        } else {
          if (isMixpanelInitialized()) {
            mixpanel.register(props);
          } else {
            FallbackTracking.register(props);
          }
        }
      } catch (error) {
        // Silently handle error
      }
    },
    
    opt_in_tracking: function() {
      try {
        if (this.usingFallback) {
          return FallbackTracking.opt_in_tracking();
        } else {
          // Try to set in both systems for consistency
          FallbackTracking.opt_in_tracking();
          if (isMixpanelInitialized()) {
            return mixpanel.opt_in_tracking();
          } else {
            return FallbackTracking.opt_in_tracking();
          }
        }
      } catch (error) {
        // Silently handle error
        return false;
      }
    },
    
    opt_out_tracking: function() {
      try {
        if (this.usingFallback) {
          return FallbackTracking.opt_out_tracking();
        } else {
          // Try to set in both systems for consistency
          FallbackTracking.opt_out_tracking();
          if (isMixpanelInitialized()) {
            return mixpanel.opt_out_tracking();
          } else {
            return FallbackTracking.opt_out_tracking();
          }
        }
      } catch (error) {
        // Silently handle error
        return false;
      }
    },
    
    // Get events from fallback (for debugging)
    getFallbackEvents: function() {
      if (this.usingFallback) {
        return FallbackTracking.getEvents();
      }
      return [];
    }
  };
  
} catch (error) {
  // Silently handle error
  // Use fallback implementation if everything fails
  mixpanelInstance = FallbackTracking;
}

export default mixpanelInstance; 