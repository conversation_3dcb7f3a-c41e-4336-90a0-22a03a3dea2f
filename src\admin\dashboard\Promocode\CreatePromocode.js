import React, { useState } from 'react';
import { PostWithTokenNoCache } from '../../../customer/common-files/ApiCalls.js';
import { ApiName } from '../../../customer/common-files/ApiNames.js';
import S from '../../assets/css/layouts/admin-header.module.css';

import ReactSelect from 'react-select';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useNavigate } from 'react-router-dom';
import AdminHeader from '../../layouts/AdminHeader.js';
import LeftSideNav from "../../layouts/LeftSideNav.js";

const CreatePromocode = () => {

	const navigate = useNavigate();

	const [couponId, setCouponId] = useState('');
	const [name, setName] = useState('');
	const [percentage, setPercentage] = useState('');
	const [isActive, setIsActive] = useState('');
	const [expiryDate, setExpiryDate] = useState('');

	// Calculate the minimum date (e.g., today's date)
	const today = new Date();
	const minDate = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
	const signoutHandler = async () => {

		return new Promise((resolve) => {
			sessionStorage.clear();
			resolve();
			navigate('/');
		});

	}

	const handleSave = async () => {

		// Perform validation checks before saving
		if (!couponId || !name || !percentage || !isActive || !expiryDate) {
			// If any required field is missing, show an error message or take appropriate action
			alert('Error: All fields are required');
			return;
		}
		try {

			const formattedExpiryDate = new Date(expiryDate).toISOString().slice(0, 10);
			const params = {
				coupon_id: couponId,
				name: name,
				percentage: parseFloat(percentage),
				is_active: isActive,
				expiry_date: formattedExpiryDate,
			};
			const res = await PostWithTokenNoCache(ApiName.createPromocde, params);

			if (res && 'status' in res && res.status === 200) {
				navigate('/admin/promocode')
			} else {
				alert('Already exists')
				// Handle error response
				console.log('Error:', res.error); // Access the error message
			}
		} catch (error) {
			// Handle any other errors
			console.log('Error:', error);
		}
	};

	return (
		<div className={S.app}>
			<AdminHeader />
			<div className={S.main_container}>
				<div className="d-flex flex-row">
					<div style={{ width: "350px" }}>
						<LeftSideNav />
					</div>
					<div style={{ width: "100%" }}>
						<div className={S.content}>
							{/* Main content */}
						</div>
						<aside className={S.right_sidebar}>

							<div className={S.formlayout2}>
								<div className={S.headname}>
									<h2>Create Promocode!</h2>
								</div>

								<form>
									<div>
										<label>Coupon ID</label>
										<input className="form-control mb-2"
											type="text"
											value={couponId}
											onChange={(e) => setCouponId(e.target.value)}
										/>
									</div>
									<div>
										<label>Name</label>
										<input className="form-control mb-2"
											type="text"
											value={name}
											onChange={(e) => setName(e.target.value)}
										/>
									</div>
									<div>
										<label>Percentage</label>
										<input className="form-control mb-2"
											type="number"
											value={percentage}
											onChange={(e) => setPercentage(e.target.value)}
										/>
									</div>
									<div>
										<label>Status</label>
										<select className="form-control mb-2"
											value={isActive}
											onChange={(e) => setIsActive(e.target.value)}
										>
											<option value="">Select</option>
											<option value="active">Active</option>
											<option value="inactive">Inactive</option>
										</select>
									</div>
									<div>
										<label>Expiry Date</label>
										<input className="form-control mb-2"
											type="date"
											value={expiryDate}
											onChange={(e) => setExpiryDate(e.target.value)}
											min={minDate}
										/>
									</div>
									<div className="mt-4">
										<button className={S.clickandsave} type="button" onClick={handleSave}>
											Save
										</button>
									</div>
								</form>
							</div>


						</aside>
					</div>
				</div>


			</div>
		</div>
	);
};

export default CreatePromocode;