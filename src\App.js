import React, { useEffect } from 'react';
import Router from './customer/common-files/Router.js';
// import PaymentDetails from './customer/pricing/PaymentDetails.js';
import Mixpanel from './utils/mixpanel';
import Analytics from './utils/analyticsTracking';
import CookieConsent from './components/CookieConsent';
import { checkUserInactivity } from './utils/customerJourneyTracking';

function App() {
  useEffect(() => {
    // Only track app initialization if user has consented and tracking is available
    try {
      const hasConsent = localStorage.getItem('tracking_consent');
      if (hasConsent === 'accepted' && Mixpanel.trackingAvailable) {
        Analytics.trackFeatureUsage('App Initialization', { timestamp: new Date().toISOString() });
      }
      
      // Check for user inactivity across sessions
      const checkInactivity = () => {
        try {
          const userJson = localStorage.getItem('user');
          if (!userJson) return;
          
          const user = JSON.parse(userJson);
          if (user) {
            // Check if the user has been inactive for a period of time
            checkUserInactivity(user);
          }
        } catch (error) {
          console.warn('Error checking user inactivity:', error);
        }
      };
      
      // Check inactivity on mount
      checkInactivity();
      
      // Set up periodic checks for inactivity while app is open
      const inactivityCheckInterval = setInterval(checkInactivity, 60 * 60 * 1000); // Check every hour
      
      return () => {
        clearInterval(inactivityCheckInterval);
      };
    } catch (error) {
      console.warn('Error in App initialization:', error);
    }
  }, []);

  return(
    <div>
      <Router />
      <CookieConsent />
      {/* <PaymentDetails /> */}
    </div>
  );
}

export default App;