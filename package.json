{"name": "reach-stream", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^3.10.0", "@azure/msal-react": "^2.0.12", "@babel/plugin-proposal-private-property-in-object": "7.21.11", "@fortawesome/fontawesome-svg-core": "^6.3.0", "@fortawesome/free-solid-svg-icons": "^6.3.0", "@fortawesome/react-fontawesome": "^0.2.0", "@react-oauth/google": "^0.12.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.3.4", "chart.js": "^4.4.3", "date-fns": "^4.1.0", "glob": "^11.0.3", "http-proxy-middleware": "^2.0.6", "lz-string": "^1.5.0", "mixpanel-browser": "^2.65.0", "papaparse": "^5.5.2", "react": "^18.2.0", "react-bootstrap": "^2.8.0", "react-chartjs-2": "^5.2.0", "react-csv": "^2.2.2", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-google-recaptcha": "^3.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-linkedin-login-oauth2": "^2.0.1", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "react-select": "^5.7.4", "react-share": "^5.0.2", "react-window": "^1.8.9", "streamsaver": "^2.0.6", "styled-components": "^5.3.10", "web-vitals": "^2.1.4", "zustand": "^4.4.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}