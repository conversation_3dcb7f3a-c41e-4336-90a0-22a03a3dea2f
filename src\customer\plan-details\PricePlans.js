
import React, { useEffect, useRef, useState } from "react";
import UseTabStore from "../common-files/useGlobalState";
import ModalHeader from "./ModalHeader";
import TabToggle from "./TabToggle";
import TabContent from "./TabContent";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";

const PricePlans = () => {
    const alertRef = useRef();
    const closeAlertRef = useRef();
    const { allPlanDetails, buttonType, defaultErrorMsg, defaultAlert, setDefaultAlert, setDefaultErrorMsg, setAllPlanDetails, setButtonType } = UseTabStore();
    const [activeTab, setActiveTab] = useState("annual");
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const getAllPlanDetails = async () => {
            try {
                let params = {
                    method: "GET",
                };
                const response = await PostWithTokenNoCache(ApiName.packagePricePlan, params).then((response) => {
                    if (response.status == 200) {
                        const jsonData = response.data.data;
                        setAllPlanDetails(JSON.parse(jsonData));
                    }
                })
            } catch (error) {

            }
        };
        if (allPlanDetails.length < 1)
            getAllPlanDetails();
    }, []);

    useEffect(() => {
        const handleOutsideClick = (e) => {
            if (!e.target.closest('.modal')) {
                close();
            }
        };
        const handleEscKeyPress = (e) => {
            if (e.key === 'Escape') {
                close();
            }
        };

        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('keydown', handleEscKeyPress);

        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
            document.removeEventListener('keydown', handleEscKeyPress);
        };
    }, []);

    useEffect(() => {
        if (defaultAlert && defaultErrorMsg === "upgrade_plan" && buttonType === "upgrade") {
            alertRef.current.click();
        }
    }, []);

    const close = () => {
        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");
        if (defaultAlert && defaultErrorMsg === "upgrade_plan" && buttonType === "upgrade") {
            closeAlertRef?.current?.click();
        }
    };

    const toggleTab = () => {
        setActiveTab((prevTab) => (prevTab === "annual" ? "monthly" : "annual"));
    };

    const togglePopup = () => {
        setIsOpen(!isOpen);
    };
    useEffect(() => {
        const modalElement = document.getElementById('pricePlanModal');
        if (modalElement) {
            modalElement.addEventListener('shown.bs.modal', () => {
                modalElement.setAttribute('aria-hidden', 'false');
            });
            modalElement.addEventListener('hidden.bs.modal', () => {
                modalElement.setAttribute('aria-hidden', 'true');
            });
        }
    }, []);

    return (
        <>
            <button
                type="button"
                className="btn btn-info btn-md"
                data-toggle="modal"
                data-target="#pricePlanModal"
                ref={alertRef}
                style={{ display: 'none' }}
                data-backdrop="true"
            />

            {defaultAlert && defaultErrorMsg === "upgrade_plan" && buttonType === "upgrade" && (
                <div
                    className="modal fade bd-example-modal-lg"
                    id="pricePlanModal"
                    tabIndex="-1"
                    role="dialog"
                    aria-labelledby="myLargeModalLabel"
                    aria-hidden="true"
                >
                    <div className="modal-dialog">
                        <div className="modal-content" style={modalContentStyle}>
                            <div className="header-ice">
                                <ModalHeader close={close} />
                            </div>

                            <div className="save-up-to d-flex flex-row justify-content-center">
                                <div>
                                    <img src="../images/point.png" />
                                </div>
                                <div>
                                    <p> Save up to 20%</p>
                                </div>

                            </div>

                            <>
                                <div className="semi-ice">
                                    <div style={semiIceContainerStyle}>
                                        <div style={tabWrapperStyle}>
                                            <TabToggle
                                                activeTab={activeTab}
                                                setActiveTab={setActiveTab}
                                                toggleTab={toggleTab}
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div style={{ margin: "3rem 4rem 0 100px" }}>
                                    <TabContent
                                        activeTab={activeTab}
                                        isOpen={isOpen}
                                        togglePopup={togglePopup}
                                    />
                                </div>
                                <div className="view-container">
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Contact Views</h4>
                                            </div>
                                            <div>

                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Obtain detailed contact information, including job titles and locations, to craft highly personalized and impactful outreach strategies.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Company Views</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Unlock company data such as annual revenue and employee size to streamline your targeting efforts.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Email Credits</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Access 90%+ accurate emails; credits are applied only to valid emial addresses
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Exports</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Download 90%+ accurate email addresses-- email credits are applied only to valid emails.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Free Contact Updates</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Enjoy lifetime free updates for data stored in your Account-Based Marketing (ABM) lists created within the platform.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>ABM List Builder</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Create and manage lists to keep your prospects organized
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>ESP-Verified Emails</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Email addresses are meticulously verified using Email Service Providers (ESPs) to ensure optimal deliverability.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Advanced Filtering</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content-advance">
                                                        <p className="filter-by">Filter by</p>
                                                        <div className="dropdown-divider"></div>
                                                        <div className="d-flex flex-row">
                                                            <div>
                                                                <ul className="filter-by-list">
                                                                    <li>Job Title</li>
                                                                    <li>Job Level</li>
                                                                    <li>Job Department</li>
                                                                    <li>Job Function</li>
                                                                    <li>Industries</li>
                                                                    <li>Company Keywords</li>
                                                                </ul>
                                                            </div>
                                                            <div>
                                                                <ul className="filter-by-list">
                                                                    <li>4 Digit SIC Code</li>
                                                                    <li>Company Type</li>
                                                                    <li>Technology</li>
                                                                    <li>Company Name</li>
                                                                    <li>Company URL</li>
                                                                    <li>Employee Size</li>
                                                                </ul>
                                                            </div>
                                                            <div>
                                                                <ul className="filter-by-list">
                                                                    <li>Revenue Size</li>
                                                                    <li>Location</li>
                                                                    <li>City</li>
                                                                    <li>State</li>
                                                                    <li>Country</li>
                                                                    <li>Zip Code</li>
                                                                </ul>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Advanced ReachAPI</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Our API can be used to build custom solutions and integrations with ReachStream's data into any platform.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Social Media Profiles</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Obtain social media profile links, including LinkedIn, X, and Facebook, to better understand and engage with prospects.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr className="horizontal-hr" />
                                    <div class="contact-box">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>User Management</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Empowers users with multiple login options, enabling seamless collaboration and efficient use of the platform.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <hr className="horizontal-hr" />
                                    <div class="contact-box-Dedicated">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Dedicated Account <br /> Manager</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            Leverage a dedicated account manager to unlock the platform's full potential by providing tailored guidance and addressing any questions.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr className="horizontal-hr" />
                                    <div class="contact-box-perfect">
                                        <div className="d-flex flex-row justify-content-between">
                                            <div>
                                                <h4>Perfect Fit</h4>
                                            </div>
                                            <div>
                                                <div class="tooltip-icon">
                                                    <span class="info-icon"><img src="../images/question.png" />

                                                    </span>

                                                    <div class="tooltip-content">
                                                        <p>
                                                            This shows the number of contact views available in your plan.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>


                            </>

                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

const modalContentStyle = {
    backgroundImage: "url('/images/ice-body.png')",
    border: 0,
    backgroundSize: "cover",
    backgroundColor: "transparent",
    backgroundRepeat: "no-repeat"
};

const semiIceContainerStyle = {
    backgroundImage: "url('../images/semi-ice.png')",
    border: "0",
    backgroundRepeat: "no-repeat",
};

const tabWrapperStyle = {
    width: "328px",
    padding: "20px",
    textAlign: "center",
};

export default PricePlans;