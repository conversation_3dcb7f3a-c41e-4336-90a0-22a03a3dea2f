import AnnualTabContent from "./AnnualTabContent";
import MonthlyTabContent from "./MonthlyTabContent";

const TabContent = ({ activeTab, isOpen, togglePopup }) => {
    return (
        <>
            {activeTab === "annual" && (
                <AnnualTabContent isOpen={isOpen} togglePopup={togglePopup} />
            )}

            {activeTab === "monthly" && (
                <MonthlyTabContent isOpen={isOpen} togglePopup={togglePopup} />
            )}
        </>
    );
};

export default TabContent;