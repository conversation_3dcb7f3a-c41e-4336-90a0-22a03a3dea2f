@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap");

.form-box {
  /* max-width: -webkit-fill-available; */
  /* margin: auto; */
  padding: 0 25px 0 25px;
  background: #ffffff;
  border: 6px solid #fff;
  /* box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px; */
  border-radius: 19px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}

.form-box h3 {
  text-align: center;
  padding: 0 0 9px 0px;
  font-weight: 600;
  margin-top: 35px;
  color: #093d54;
}

.super-saver {
  border-collapse: collapse;
  overflow: hidden;
  margin: 0px 0px 20px auto;
  width: 282px;
}

.saver-plane-sign-up img {
  margin: 0 0 0 25px;
}

button.switch {
  background-color: #093d54;
  color: #fff;
  font-size: 14px;
  padding: 12px 20px 12px 20px;
  border-radius: 15px;
  margin-top: 23px;
  border: 0;
  cursor: pointer;
  outline: none;
}

/* span.email-address p {
    margin: 0px 0 10px 0px;
} */

span.password p {
  margin: 0 0 10px 0;
}

p.semi-header10 a {
  text-decoration: none;
  color: #55c2c3;
}

p.semi-header a:hover {
  color: #007bff;
}

p.password-label {
  padding: 8px 0 0 0;
}

p.password-label a {
  text-decoration: none;
}

p.password-label a:hover {
  color: #007bff;
}

input.cp-pluss1 {
  margin: 0 auto;
  display: block;
  padding: 10px 40px 10px 40px;
  outline: 0;
  background-color: #0c243c;
  color: #fff;
  border-radius: 15px;
  border: none;
  font-family: "Roboto", sans-serif;
  cursor: pointer;
  margin-bottom: 40px;
}

.bg-color {
  background-color: #e8f7f7;
  height: auto;
  /* margin-left: 6%; */
}

/* Signup */

.saver-plane {
  margin-top: 8%;
  margin-bottom: 10%;
}

.saver-plane h3 {
  text-align: center;
  margin-top: 15px;
  font-weight: 700;
  color: #0c243c;
  margin-bottom: 18px;
}

th.borderless {
  color: #fff;
  border: none;
}

.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 1rem;
  background-color: #fff;
  border-radius: 20px;
}

td.borderless {
  border: none;
}

tr.backrnd-color {
  background-color: #55c2c3;
  border-radius: 24px;
  font-size: 24px;
}

/* th.borderles span {
  background-color: #fff;
  padding: 7px 4px 7px 2px;
  border-radius: 15px;
} */

p.semi-header10 {
  text-align: center;
  font-size: 14px;
  color: #000;
}

span.dollor {
  font-size: 12px;
  font-weight: 400;
}

table.table.table-striped {
  border-collapse: collapse;
  border-radius: 1em;
  overflow: hidden;
  box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
  max-width: 338px;
  margin: auto;
}

td.text-pose {
  padding: 20px 0 15px 13px;
  font-size: 12px;
}

td.text-center {
  font-size: 13px;
  /*    font-weight: 500;*/
  padding: 17px 0 0 0px;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgb(255 255 255 / 5%);
}

tr.row-clr {
  background-color: #6b8eb126;
}

.trusted-by p {
  text-align: center;
  /* margin-top: 20px; */
  font-size: 18px;
  font-weight: 600;
  margin: 1rem 0 1rem 3rem;
}

.zeta {
  max-width: 570px;
  /* margin: auto; */
  padding: 0 25px 0 25px;
  background: #ffffff;
  border: 6px solid #fff;
}

input#fname {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#email {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#phone_number {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#password {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#cpassword {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

.spacing {
  padding: 14px 0 0 0;
}

.custom-control.custom-checkbox {
  margin-top: 0px;
}

a.Term {
  text-decoration: none;
  color: #55c2c3;
}

input#customCheck2 {
  border: 1px solid #c9c9c9;
  outline: none;
}

span.pill-button {
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
  border: none;
  background-color: #e2ebe8;
  color: rgb(0, 0, 0);
  padding: 3px 23px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 30px;
  border-radius: 33px;
  width: 39px;
  outline: none;
  margin-left: 4px;
}

.make-space {
  margin-top: 0%;
}

/* span.signup-errors p {
  font-size: 12px;
  padding: 2px 0 0 0px;
  color: #55c2c3;
  margin: 0;
} */

span.month {
  vertical-align: middle;
  font-size: 9px;
  /*    font-weight: 100;*/
  color: #000;
  margin: 3px;
}

/* .table td,
.table th {
    padding: 10px 1px 10px 20px;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
} */



/* th.borderless {
  padding: 10px 0px 10px 15px;
} */

th.borderles {
  width: 130px;
}

.invalid-input-text {
  /* border: 2px solid gray;*/
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid red;
  border-radius: 5px;
  padding: 0 0px 0px 12px;

  background-color: #fff9f9;
}

.errr {
  font-size: 10px;
  color: #7e8c9c;
}

p.semi-header a:hover {
  color: #55c2c3;
}

small.limited-offer {
  font-size: 8px;
  background-color: #f6cbbd;
  border-radius: 22px;
  padding: 2px 6px 3px 7px;
  color: #093d54;
  /* margin-right: 0px; */
  font-weight: 800;
  margin: 0 0 0 30px;
}

.super-saver-saperate {
  width: 127px;
  padding: 0 0 0 0px;
}

.prce p {
  margin: 0;
  padding: 0 0 0 0px;
}

span.minus-discount {
  font-size: 14px;
  padding: 0 0 0 2px;
  color: #55c2c3;
}



.continue {
  background-color: #F5F5F5;
  border-radius: 4px;
  padding: 10px 0 10px 0;
  width: 400px;
  margin: auto;
}

.social {
  background-color: #fff;
  border-radius: 24px;
  padding: 4px 4px 4px 4px;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 3px 0px inset;
}

.social img {
  padding: 7px 6px 6px 6px;
  cursor: pointer;
}

.with p {
  margin: 15px 0 0 0;
  font-size: 14px;
  font-weight: 600;
}


.horizontal-line {
  width: 365px;
  text-align: center;
  border-bottom: 1px solid #C9C9C9;
  line-height: 0.1em;
  margin: 20px auto 20px auto;
}

.horizontall {
  /* width: 320px; */
  text-align: center;
  border-bottom: 1px solid #C9C9C9;
  line-height: 0.1em;
  margin: 20px auto 20px auto;
}

.horback {
  background-color: #fff;
  padding: 0 6px 0 6px;
  font-size: 12px;
  color: #C9C9C9;
}






input#signupinput {
  background-color: #F1FFFF;
  border: 1px solid #D3EFF0;
}







/* .demand {
  position: absolute;
  right: 100%;
  z-index: 1000;
} */

/* .kajhdhhkj {
  position: absolute;
  top: 30%;
  z-index: 1;
} */

.button-position {
  position: absolute;
  top: 30%;
  right: 0;
  z-index: 100;
}

.content {
  border: 1px solid #55C2C3;
  margin: -2rem 0 0 0;
  width: 300px;
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  background-color: #fff;

}

p.where {
  padding: 30px 15px 10px 15px;
  font-size: 14px;
  color: #093D54;
  opacity: 102%;
  margin: 0;
}

button.toggle-button {
  background-color: #55C2C3;
  color: #fff;
  border: 0;
  outline: none;
  display: flex;
  flex-direction: row;
  display: inline-block;
  font-size: 14px;
  right: 0;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  -webkit-transform: rotate(-90deg);
  transform-origin: bottom right;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  padding: 0 20px 0 15px;
  cursor: pointer;
}


p.angry {
  margin: 0;
  font-size: 26px;
  padding: 0 0px 0 0px;
  cursor: pointer;
}

.experience p {
  font-size: 14px;
  padding: 13px 0px 0 18px;
  color: #093D54;
}

.suggestion p {
  margin: 0 0 8px 0;
  font-size: 14px;
  padding: 0 0 0 18px;
  color: #093D54;
}

textarea.feedbacksuggestion {
  margin: auto;
  display: block;
  padding: 6px 0 0px 6px;
  border: 1px solid #55C2C3;
  outline: none;
  border-radius: 4px;
  width: 260px;
  font-size: 12px;
}

textarea.feedbacksuggestion::placeholder {
  font-size: 12px;
  color: #BFBFBF;
  padding: 0 0 0 0px;
}

button.canbutton {
  background-color: #093D54;
  opacity: 0.3;
  border: 0;
  outline: none;
  cursor: pointer;
  color: #fff;
  font-size: 14px;
  padding: 4px 17px 4px 17px;
  border-radius: 10px;
}

button.sendbutton {
  background-color: #093D54;
  border: 0;
  outline: none;
  color: #fff;
  font-size: 14px;
  padding: 4px 20px 4px 20px;
  border-radius: 10px;
  cursor: pointer;
}

button.sendbuttonn {
  padding: 4px 25px 4px 25px;
  border: 0;
  color: #fff;
  border-radius: 10px;
  font-size: 14px;
  background-color: #093D54;
  cursor: pointer;
  outline: none;
}

.addascreenshot p {
  padding: 10px 0 0 18px;
  font-size: 12px;
  color: #55C2C3;
}

.star-rating {
  font-size: 24px;
}

.star {
  cursor: pointer;
}

.highlighted {
  color: gold;
}

.hidden-file-input::file-selector-button {
  display: none;
}

p.borderless {
  font-size: 24px;
  padding: 3px 0 4px 10px;
  margin: 0;
  color: #fff;
  font-weight: 600;
}