import Mixpanel from './mixpanel';
import { envConfig } from '../config'; // Import envConfig

// Common analytics events for the application
const Analytics = {
  // Track generic events (useful for custom events)
  track: (name, props = {}) => {
    try {
      // Get location data
      const locationData = getLocationData();
      
      // Use the existing Mixpanel implementation which now has queuing
      // Do not add timestamp here as it's already added in mixpanel.js
      Mixpanel.track(name, {
        ...locationData,  // Put locationData first so props can override it if needed
        ...props
        // timestamp is now added in the mixpanel.js implementation
      });
    } catch (error) {
      console.warn(`Error tracking ${name}:`, error);
    }
  },

  // Track event with deduplication to prevent duplicate events
  // This helps standardize tracking across components
  trackEventWithDeduplication: (name, props = {}, userData = {}) => {
    try {
      // Create a unique key based on event name and timestamp to prevent duplicates
      const eventKey = `${name}_${Date.now()}`;
      
      // Check if this exact event was tracked in the last second (prevents duplicates)
      if (sessionStorage.getItem(eventKey)) {
        return; // Duplicate event - don't track
      }
      
      // Set a temporary flag to prevent duplicate events (expires after 1 second)
      sessionStorage.setItem(eventKey, "true");
      setTimeout(() => {
        sessionStorage.removeItem(eventKey);
      }, 1000);
      
      // Standard properties to include in all events
      const standardProps = {
        user_email: userData.email || null,
        timestamp: new Date().toISOString(),
        page_path: window.location.pathname,
        session_id: sessionStorage.getItem('session_id') || `session_${Date.now()}`
      };
      
      // Get location data
      const locationData = getLocationData();
      
      // Track the event with all properties
      Analytics.track(name, {
        ...standardProps,
        ...props,
        ...locationData
      });
    } catch (error) {
      console.warn(`Error tracking event with deduplication (${name}):`, error);
    }
  },

  // Utility function to prevent duplicate events
  _deduplicateEvent: (eventName, uniqueId, ttlMs = 3000) => {
    try {
      // Create a unique key for this event
      const storageKey = `event_${eventName}_${uniqueId}_${Date.now()}`;
      
      // Check if this event was already fired recently
      if (sessionStorage.getItem(storageKey)) {
        return false; // Event already tracked, don't track again
      }
      
      // Store the event with expiration time
      sessionStorage.setItem(storageKey, Date.now().toString());
      
      // Remove the key after TTL to allow re-tracking if needed
      setTimeout(() => {
        sessionStorage.removeItem(storageKey);
      }, ttlMs);
      
      return true; // Event is unique, proceed with tracking
    } catch (error) {
      console.warn('Error in event deduplication:', error);
      return true; // Default to allowing the event if deduplication fails
    }
  },

  // Get current environment
  getCurrentEnvironment: () => {
    return envConfig.environment || 'unknown';
  },
  
  // Log events filtered by environment (useful for debugging)
  // Only logs when explicitly called, not on every event
  logEventsByEnvironment: (environment = null) => {
    try {
      const events = Mixpanel.getFallbackEvents();
      const targetEnv = environment || envConfig.environment || 'unknown';
      
      const filteredEvents = events.filter(e => 
        e.properties.environment === targetEnv
      );
      
      // Only log when this method is explicitly called
      if (process.env.NODE_ENV !== 'production') {
        console.log(`Events for environment: ${targetEnv}`);
      console.table(filteredEvents.map(e => ({
        event: e.event,
        timestamp: e.timestamp,
        ...e.properties
      })));
      }
      
      return filteredEvents;
    } catch (error) {
      console.warn('Error logging events by environment:', error);
      return [];
    }
  },

  // Dedicated method for tracking tour pages to ensure consistent page numbering
  trackTourPage: (pageNumber, pageName, additionalProps = {}) => {
    try {
      // Get user data
      const userData = JSON.parse(localStorage.getItem("user")) || {};
      const userEmail = userData.email;
      
      // Get device info
      const deviceInfo = {
        device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
        browser: navigator.userAgent,
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        language: navigator.language || navigator.userLanguage
      };
      
      // Track with consistent page numbering
      Mixpanel.track('Tour Page Viewed', {
        page: pageNumber,
        page_number: pageNumber,
        page_name: pageName,
        user_email: userEmail,
        current_url: window.location.href,
        ...deviceInfo,
        ...additionalProps
      });
      
      // Also identify in Mixpanel if we have user email
      if (userEmail) {
        Mixpanel.identify(userEmail);
        
        // Set user properties
        Mixpanel.people.set({
          'Tour Status': 'In Progress',
          'Last Tour Page': pageName,
          'Last Tour Page Number': pageNumber,
          'Last Tour Interaction': new Date().toISOString()
        });
      }
    } catch (error) {
      console.warn('Error tracking tour page view:', error);
    }
  },

  // User events
  trackSignUp: (method, userInfo = {}) => {
    try {
      // Create specific event name based on method and plan type
      const signupMethod = method || 'form';
      const planType = userInfo.plan_type || 'standard';
      const eventName = `Signed Up via ${signupMethod} for ${planType} Plan`;
      
      // Identify the user in Mixpanel first
      if (userInfo.email) {
        Mixpanel.identify(userInfo.email);
        
        // Set user properties
        Mixpanel.people.set({
          '$email': userInfo.email,
          '$first_name': userInfo.firstName || '',
          '$last_name': userInfo.lastName || '',
          '$phone': userInfo.phone || '',
          'Company': userInfo.company || '',
          'Package ID': userInfo.package_id || '',
          'Referral Code': userInfo.referral_code || '',
          'Signup Date': new Date().toISOString(),
          'Lifecycle Stage': 'Signup'
        });
      }
      
      Mixpanel.track(eventName, {
        method: signupMethod,
        plan_type: planType,
        email_domain: userInfo.email ? userInfo.email.split('@')[1] : null,
        referral_code: userInfo.referral_code || null,
        form_completion: userInfo.form_completion || 'complete',
        user_details: {
          firstName: userInfo.firstName || null,
          lastName: userInfo.lastName || null,
          company: userInfo.company || null
        },
        timestamp: new Date().toISOString(),
        ...userInfo
      });
    } catch (error) {
      console.warn('Error tracking sign up:', error);
    }
  },

  trackSignIn: (method, userInfo = {}) => {
    try {
      // Create specific event name based on method and role
      const loginMethod = method || 'email';
      const userRole = userInfo.role || 'user';
      const loginSource = userInfo.login_source || 'direct';
      const loginAsCustomer = userInfo.login_as_customer ? ' as Customer' : '';
      const eventName = `Signed In via ${loginMethod} (${userRole}${loginAsCustomer})`;
      
      Mixpanel.track(eventName, {
        method: loginMethod,
        role: userRole,
        email: userInfo.email || null,
        auth_method: userInfo.auth_method || loginMethod,
        login_source: loginSource,
        login_as_customer: userInfo.login_as_customer || false,
        user_segment: userInfo.user_segment || userRole,
        login_time: userInfo.login_time || new Date().toISOString(),
        device_info: userInfo.device_info || {
          device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
          browser: navigator.userAgent,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`
        },
        timestamp: new Date().toISOString(),
        ...userInfo
      });
    } catch (error) {
      console.warn('Error tracking sign in:', error);
    }
  },

  trackUserIdentify: (userId, userProps = {}) => {
    try {
      // Get attribution data if available
      const attributionData = localStorage.getItem('attribution_data') ? 
        JSON.parse(localStorage.getItem('attribution_data')) : {};
      
      // Get UTM parameters if available
      const utmParams = localStorage.getItem('utm_params') ? 
        JSON.parse(localStorage.getItem('utm_params')) : {};
      
      Mixpanel.identify(userId);
      
      // Set user properties with attribution data
      Mixpanel.people.set({
        '$email': userProps.email || '',
        '$last_login': new Date(),
        
        // Attribution properties
        'Attribution Source': attributionData.primary_source || 'direct',
        'UTM Source': attributionData.utm_source || utmParams.utm_source,
        'UTM Medium': attributionData.utm_medium || utmParams.utm_medium,
        'UTM Campaign': attributionData.utm_campaign || utmParams.utm_campaign,
        'UTM Content': attributionData.utm_content || utmParams.utm_content,
        'UTM Term': attributionData.utm_term || utmParams.utm_term,
        'Referrer Domain': attributionData.referrer_domain,
        'First Touch Source': attributionData.first_touch_source,
        'First Touch Date': attributionData.first_touch_date,
        'Landing Page': attributionData.landing_page,
        
        // Custom ReachStream parameters
        'AWC': attributionData.awc || utmParams.awc,
        'SN': attributionData.sn || utmParams.sn,
        
        // Include all other user properties
        ...userProps
      });
    } catch (error) {
      console.warn(`Error identifying user (${userId}):`, error);
    }
  },

  // Feature usage events
  trackFeatureUsage: (featureName, properties = {}) => {
    try {
      // Create more specific event name by including action and target
      const action = properties.action || 'used';
      const target = properties.target ? ` "${properties.target}"` : '';
      const specificEventName = `${action.charAt(0).toUpperCase() + action.slice(1)} ${featureName}${target}`;
      
      // Collect detailed information about the feature usage
      const enhancedProperties = {
        // Feature context
        feature_name: featureName,
        feature_category: properties.category || 'general',
        feature_action: properties.action || 'used',
        feature_value: properties.value || null,
        
        // User context
        user_id: properties.userId || null,
        user_email: properties.userEmail || null,
        user_role: properties.userRole || 'standard',
        user_segment: properties.userSegment || 'standard',
        account_type: properties.accountType || null,
        
        // Action context
        action_source: properties.source || 'ui',
        action_target: properties.target || null,
        action_result: properties.result || 'success',
        
        // Quantifiable metrics
        duration_ms: properties.duration || 0,
        count: properties.count || 1,
        
        // Location context
        page_path: properties.pagePath || window.location.pathname,
        page_title: properties.pageTitle || document.title,
        page_section: properties.pageSection || null,
        component_name: properties.componentName || null,
        
        // Device information
        device_info: {
          device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
          browser: navigator.userAgent,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`,
          language: navigator.language || navigator.userLanguage
        },
        
        // Timing information
        timestamp: new Date().toISOString(),
        
        // Include any other properties passed
        ...properties
      };
      
      Mixpanel.track(specificEventName, enhancedProperties);
    } catch (error) {
      console.warn(`Error tracking feature usage (${featureName}):`, error);
    }
  },

  // List-related events
  trackListCreated: (listInfo = {}) => {
    try {
      // Create specific event name with list name if available
      const listName = listInfo.list_name || listInfo.name || '';
      const eventName = listName ? `Created List "${listName}"` : 'Created List';
      
      Mixpanel.track(eventName, {
        list_id: listInfo.list_id || listInfo.id || null,
        list_name: listName,
        list_type: listInfo.list_type || listInfo.type || 'standard',
        list_size: listInfo.list_size || listInfo.size || listInfo.count || 0,
        filters_used: listInfo.filters_used || listInfo.filters || [],
        created_at: new Date().toISOString(),
        is_saved: listInfo.is_saved !== undefined ? listInfo.is_saved : true,
        is_advanced_filter: listInfo.is_advanced_filter || false,
        ...listInfo
      });
    } catch (error) {
      console.warn('Error tracking list creation:', error);
    }
  },

  trackListDeleted: (listInfo = {}) => {
    try {
      // Create specific event name with list name if available
      const listName = listInfo.list_name || listInfo.name || '';
      const eventName = listName ? `Deleted List "${listName}"` : 'Deleted List';
      
      Mixpanel.track(eventName, {
        list_id: listInfo.list_id || listInfo.id || null,
        list_name: listName,
        list_type: listInfo.list_type || listInfo.type || 'standard',
        list_size: listInfo.list_size || listInfo.size || listInfo.count || 0,
        created_at: listInfo.created_at || null,
        deleted_at: new Date().toISOString(),
        reason: listInfo.reason || 'user_action',
        ...listInfo
      });
    } catch (error) {
      console.warn('Error tracking list deletion:', error);
    }
  },

  trackListEdited: (listInfo = {}) => {
    try {
      Mixpanel.track('List Edited', listInfo);
    } catch (error) {
      console.warn('Error tracking list edit:', error);
    }
  },

  // Contact-related events
  trackContactViewed: (contactInfo = {}) => {
    try {
      // Create specific event name with contact name if available
      const contactName = contactInfo.contact_name || contactInfo.name || '';
      const eventName = contactName ? `Viewed Contact "${contactName}"` : 'Viewed Contact';
      
      Mixpanel.track(eventName, {
        contact_id: contactInfo.contact_id || contactInfo.id || null,
        contact_name: contactName,
        company_name: contactInfo.company_name || contactInfo.company || null,
        job_title: contactInfo.job_title || contactInfo.title || null,
        contact_location: contactInfo.location || null,
        source: contactInfo.source || 'contact_list',
        viewed_at: new Date().toISOString(),
        view_duration: contactInfo.view_duration || null,
        view_source: contactInfo.view_source || 'search_results',
        ...contactInfo
      });
    } catch (error) {
      console.warn('Error tracking contact view:', error);
    }
  },

  trackContactsDownloaded: (downloadInfo = {}) => {
    try {
      // Create specific event name with count and source
      const count = downloadInfo.count || downloadInfo.contacts_count || 0;
      const source = downloadInfo.source || downloadInfo.list_name || 'search_results';
      const eventName = `Downloaded ${count} Contacts from ${source}`;
      
      Mixpanel.track(eventName, {
        contacts_count: count,
        download_source: source,
        list_id: downloadInfo.list_id || null,
        list_name: downloadInfo.list_name || null,
        file_format: downloadInfo.file_format || 'csv',
        download_type: downloadInfo.download_type || 'full',
        filters_used: downloadInfo.filters_used || [],
        advanced_filters: downloadInfo.advanced_filters || false,
        download_date: new Date().toISOString(),
        ...downloadInfo
      });
    } catch (error) {
      console.warn('Error tracking contacts download:', error);
    }
  },

  // Company-related events
  trackCompanyViewed: (companyInfo = {}) => {
    try {
      Mixpanel.track('Company Viewed', companyInfo);
    } catch (error) {
      console.warn('Error tracking company view:', error);
    }
  },

  // Search and filter events
  trackSearch: (searchInfo = {}) => {
    try {
      // Use provided event name or construct one
      const eventName = searchInfo.event_name || 
        `Search: ${searchInfo.search_term ? `"${searchInfo.search_term}"` : 'Empty Query'}`;
      
      Mixpanel.track(eventName, {
        search_term: searchInfo.search_term || '',
        search_type: searchInfo.search_type || 'general',
        results_count: searchInfo.results_count,
        filter_count: searchInfo.filter_count || 0,
        has_results: searchInfo.has_results === false ? false : (searchInfo.results_count > 0 || null),
        page_number: searchInfo.page_number || 1,
        sort_by: searchInfo.sort_by || 'relevance',
        sort_direction: searchInfo.sort_direction || 'desc',
        source_page: searchInfo.source_page || null,
        search_category: searchInfo.search_category || 'general',
        execution_time_ms: searchInfo.execution_time_ms || null,
        device_info: searchInfo.device_info || {
          device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
          browser: navigator.userAgent,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`
        },
        timestamp: new Date().toISOString(),
        ...searchInfo
      });
    } catch (error) {
      console.warn('Error tracking search:', error);
    }
  },

  trackFilterApplied: (filterInfo = {}) => {
    try {
      // Use provided event name or construct one
      const eventName = filterInfo.event_name || 
        `Applied ${filterInfo.filterCount || 0} Filters`;
      
      Mixpanel.track(eventName, {
        filter_count: filterInfo.filterCount || 0,
        filter_types: filterInfo.filterTypes || [],
        is_advanced: filterInfo.isAdvanced || false,
        execution_time_ms: filterInfo.executionTime || null,
        user_email: filterInfo.userEmail || null,
        user_role: filterInfo.userRole || null,
        page_location: filterInfo.pageLocation || window.location.pathname,
        action: filterInfo.action || 'apply',
        active_filters: filterInfo.allActiveFilters || {},
        detailed_filters: filterInfo.filterDetails || [],
        device_info: filterInfo.device_info || {
          device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
          browser: navigator.userAgent,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`
        },
        timestamp: new Date().toISOString(),
        ...filterInfo
      });
    } catch (error) {
      console.warn('Error tracking filter application:', error);
    }
  },

  // Enhanced method specifically for tracking advanced search operations
  trackAdvancedSearch: (searchInfo = {}) => {
    try {
      // Use provided event name or construct one
      const eventName = searchInfo.event_name || 
        `Advanced Search with ${searchInfo.filterCount || 0} Filters`;
      
      Mixpanel.track(eventName, {
        search_term: searchInfo.search_term || '',
        filter_count: searchInfo.filterCount || 0,
        filters: searchInfo.filters || {},
        filter_categories: searchInfo.filter_categories || 0,
        execution_time_ms: searchInfo.executionTime || null,
        results_count: searchInfo.results_count || null,
        max_contacts: searchInfo.maxContacts || null,
        category: searchInfo.category || 'contact',
        user_email: searchInfo.userEmail || null,
        user_segment: searchInfo.userSegment || 'standard',
        page_number: searchInfo.page_number || 1,
        sort_by: searchInfo.sort_by || 'default',
        sort_direction: searchInfo.sort_direction || 'default',
        is_csv_export: searchInfo.is_csv_export || false,
        source_page: searchInfo.source_page || null,
        device_info: searchInfo.device_info || {
          device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
          browser: navigator.userAgent,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`
        },
        timestamp: new Date().toISOString(),
        ...searchInfo
      });
    } catch (error) {
      console.warn('Error tracking advanced search:', error);
    }
  },

  // Payment events
  trackPlanViewed: (planInfo = {}) => {
    try {
      Mixpanel.track('Plan Viewed', planInfo);
    } catch (error) {
      console.warn('Error tracking plan view:', error);
    }
  },

  trackPaymentStarted: (paymentInfo = {}) => {
    try {
      // Create a unique ID for deduplication based on package ID and user email
      // Add a timestamp to ensure we don't over-deduplicate
      const timestamp = new Date().getTime();
      const uniqueId = `${paymentInfo.package_id || ''}_${paymentInfo.user_email || ''}_${timestamp}`;
      
      // Check if this is a duplicate event - but we're making this less aggressive
      if (!Analytics._deduplicateEvent('payment_started', uniqueId, 1000)) {
        return; // Skip duplicate event
      }
      
      // Create specific event name based on package information
      const packageId = paymentInfo.package_id || null;
      let packageName = paymentInfo.package_name || '';
      
      // Get plan type from package name
      let planType = '';
      if (packageName.toLowerCase().includes('glacier')) {
        planType = 'Glacier';
      } else if (packageName.toLowerCase().includes('ice floe')) {
        planType = 'Ice Floe';
      } else if (packageName.toLowerCase().includes('polar peak')) {
        planType = 'Polar Peak';
      } else if (packageName.toLowerCase().includes('frozen fortune')) {
        planType = 'Frozen Fortune';
      } else if (packageId) {
        // If we can't determine plan type from name but have ID
        planType = `Plan ID: ${packageId}`;
      } else {
        planType = 'Standard Plan';
      }
      
      const eventName = `Started Payment for ${planType}`;
      
      Mixpanel.track(eventName, {
        timestamp: new Date().toISOString(),
        package_id: packageId,
        package_name: packageName,
        plan_type: planType,
        price: paymentInfo.price || 0,
        discounted_price: paymentInfo.discounted_price || paymentInfo.price || 0,
        has_discount: paymentInfo.has_discount || false,
        promo_code: paymentInfo.promo_code || null,
        checkout_step: paymentInfo.checkout_step || 'payment_initiated',
        user_email: paymentInfo.user_email || null,
        ...paymentInfo
      });
    } catch (error) {
      console.warn('Error tracking payment start:', error);
    }
  },

  trackPaymentCompleted: (paymentInfo = {}) => {
    try {
      // Create a unique ID for deduplication based on order ID
      // Add a timestamp to ensure we don't over-deduplicate
      const timestamp = new Date().getTime();
      const uniqueId = `${paymentInfo.order_id || ''}_${timestamp}`;
      
      // Check if this is a duplicate event - but we're making this less aggressive
      if (!Analytics._deduplicateEvent('payment_completed', uniqueId, 1000)) {
        return; // Skip duplicate event
      }
      
      // Create specific event name based on plan details
      const planId = paymentInfo.plan_id || '';
      let planName = paymentInfo.plan_name || '';
      
      // Get plan type from plan name
      let planType = '';
      if (planName.toLowerCase().includes('glacier')) {
        planType = 'Glacier';
      } else if (planName.toLowerCase().includes('ice floe')) {
        planType = 'Ice Floe';
      } else if (planName.toLowerCase().includes('polar peak')) {
        planType = 'Polar Peak';
      } else if (planName.toLowerCase().includes('frozen fortune')) {
        planType = 'Frozen Fortune';
      } else if (planId) {
        // If we can't determine plan type from name but have ID
        planType = `Plan ID: ${planId}`;
      } else {
        planType = 'Standard Plan';
      }
      
      const eventName = `Completed Payment for ${planType}`;
      
      Mixpanel.track(eventName, {
        timestamp: new Date().toISOString(),
        order_id: paymentInfo.order_id || null,
        plan_name: planName,
        plan_type: planType,
        plan_price: paymentInfo.plan_price || paymentInfo.sale_amount || 0,
        discount_applied: paymentInfo.discount_applied || false,
        discount_code: paymentInfo.discount_code || 'none',
        currency: paymentInfo.currency || 'USD',
        payment_source: paymentInfo.payment_source || 'credit_card',
        subscription_type: paymentInfo.subscription_type || 'standard',
        user_email: paymentInfo.user_email || null,
        ...paymentInfo
      });
    } catch (error) {
      console.warn('Error tracking payment completion:', error);
    }
  },

  trackPaymentFailed: (paymentInfo = {}) => {
    try {
      // Create specific event name with error reason
      const errorReason = paymentInfo.error || paymentInfo.reason || 'unknown error';
      const eventName = `Payment Failed: ${errorReason}`;
      
      Mixpanel.track(eventName, {
        error_code: paymentInfo.error_code || null,
        error_message: paymentInfo.error_message || errorReason,
        payment_method: paymentInfo.payment_method || 'credit_card',
        plan_name: paymentInfo.plan_name || 'unknown',
        amount: paymentInfo.amount || 0,
        currency: paymentInfo.currency || 'USD',
        timestamp: new Date().toISOString(),
        ...paymentInfo
      });
    } catch (error) {
      console.warn('Error tracking payment failure:', error);
    }
  },

  // Registration tracking
  trackIncompleteSignup: (userData = {}) => {
    try {
      Mixpanel.track('Incomplete Signup', {
        time_spent: userData.timeSpent || 0,
        form_fields_completed: userData.fieldsCompleted || [],
        last_field: userData.lastField || '',
        plan_type: userData.planType || '',
        utm_source: userData.utmSource || '',
        form_completion_percentage: userData.form_completion_percentage || 0,
        ...userData
      });
    } catch (error) {
      console.warn('Error tracking incomplete signup:', error);
    }
  },

  // User activity tracking
  trackInactivity: (userInfo = {}) => {
    try {
      Mixpanel.track('User Inactive', {
        days_inactive: userInfo.daysInactive || 0,
        last_seen: userInfo.lastSeen || '',
        ...userInfo
      });
    } catch (error) {
      console.warn('Error tracking user inactivity:', error);
    }
  },

  // Credit usage tracking
  trackCreditUsage: (usageInfo = {}) => {
    try {
      Mixpanel.track('Credit Usage', {
        credits_used: usageInfo.creditsUsed || 0,
        total_credits: usageInfo.totalCredits || 0,
        percentage_used: usageInfo.percentageUsed || 0,
        ...usageInfo
      });
    } catch (error) {
      console.warn('Error tracking credit usage:', error);
    }
  },

  // Filter usage tracking
  trackFilterUsage: (filterInfo = {}) => {
    try {
      Mixpanel.track('Filter Usage', {
        filter_count: filterInfo.filterCount || 0, 
        filter_types: filterInfo.filterTypes || [],
        search_results: filterInfo.searchResults || 0,
        ...filterInfo
      });
    } catch (error) {
      console.warn('Error tracking filter usage:', error);
    }
  },

  // Email reveal tracking
  trackEmailRevealed: (emailInfo = {}) => {
    try {
      Mixpanel.track('Email Revealed', {
        added_to_list: emailInfo.addedToList || false,
        downloaded: emailInfo.downloaded || false,
        contact_type: emailInfo.contactType || '',
        ...emailInfo
      });
    } catch (error) {
      console.warn('Error tracking email reveal:', error);
    }
  },

  // ABM list feature usage
  trackABMListUsage: (listInfo = {}) => {
    try {
      Mixpanel.track('ABM List Feature Used', {
        list_size: listInfo.listSize || 0,
        list_name: listInfo.listName || '',
        list_filters: listInfo.listFilters || [],
        ...listInfo
      });
    } catch (error) {
      console.warn('Error tracking ABM list usage:', error);
    }
  },

  // Referral tracking
  trackReferral: (referralInfo = {}) => {
    try {
      Mixpanel.track('Referral Activity', {
        referrer_email: referralInfo.referrerEmail || '',
        referred_email: referralInfo.referredEmail || '',
        referral_code: referralInfo.referralCode || '',
        referral_status: referralInfo.status || '',
        ...referralInfo
      });
    } catch (error) {
      console.warn('Error tracking referral activity:', error);
    }
  },

  // Pricing page views
  trackPricingPageView: (viewInfo = {}) => {
    try {
      Mixpanel.track('Pricing Page Viewed', {
        view_count: viewInfo.viewCount || 1,
        current_plan: viewInfo.currentPlan || '',
        time_on_page: viewInfo.timeOnPage || 0,
        ...viewInfo
      });
    } catch (error) {
      console.warn('Error tracking pricing page view:', error);
    }
  },

  // User lifecycle stage updates
  updateUserLifecycleStage: (userId, stage, additionalProps = {}) => {
    try {
      Mixpanel.identify(userId);
      Mixpanel.people.set({
        'Lifecycle Stage': stage,
        'Last Stage Update': new Date(),
        ...additionalProps
      });
    } catch (error) {
      console.warn(`Error updating lifecycle stage for user (${userId}):`, error);
    }
  },

  // User engagement tracking
  trackEngagementMilestone: (milestoneInfo = {}) => {
    try {
      Mixpanel.track('Engagement Milestone', {
        milestone_type: milestoneInfo.type || '',
        value: milestoneInfo.value || '',
        ...milestoneInfo
      });
    } catch (error) {
      console.warn('Error tracking engagement milestone:', error);
    }
  },

  // Search without results tracking
  trackNoSearchResults: (searchInfo = {}) => {
    try {
      Mixpanel.track('No Search Results', {
        search_terms: searchInfo.terms || '',
        filters_applied: searchInfo.filters || [],
        ...searchInfo
      });
    } catch (error) {
      console.warn('Error tracking no search results:', error);
    }
  },

  // Free trial subscription
  trackFreeTrialStarted: (userInfo = {}) => {
    try {
      // Create more specific event name with referral info if applicable
      const referralPrefix = userInfo.referral_code ? 'Referral ' : '';
      const eventName = `Started ${referralPrefix}Icebreaker Free Trial`;
      
      Mixpanel.track(eventName, {
        timestamp: new Date().toISOString(),
        referral_code: userInfo.referral_code || null,
        user_email: userInfo.email || null,
        user_details: {
          firstName: userInfo.firstName || null,
          lastName: userInfo.lastName || null,
          company: userInfo.company || null
        },
        source: userInfo.source || 'direct',
        ...userInfo
      });
    } catch (error) {
      console.warn('Error tracking free trial start:', error);
    }
  },
  
  trackFreeTrialCompleted: (userInfo = {}) => {
    try {
      // Identify the user in Mixpanel first
      if (userInfo.email) {
        Mixpanel.identify(userInfo.email);
        
        // Set user properties
        Mixpanel.people.set({
          '$email': userInfo.email,
          '$first_name': userInfo.firstName || '',
          '$last_name': userInfo.lastName || '',
          '$phone': userInfo.phone || '',
          'Company': userInfo.company || '',
          'Subscription Type': 'Free Trial',
          'Referral Code': userInfo.referral_code || '',
          'Signup Date': new Date().toISOString(),
          'Lifecycle Stage': 'Free Trial',
          'Auth Method': userInfo.auth_method || 'email',
          'Assigned Credits': userInfo.credits_assigned,
          'Contact Views': userInfo.contact_views,
          'Package ID': userInfo.package_id
        });
      }
      
      // Use a consistent event name for all free trial subscriptions
      // This is the ONLY event that should be fired for Icebreaker subscriptions
      const eventName = 'Icebreaker Subscription Complete';
      
      Mixpanel.track(eventName, {
        timestamp: new Date().toISOString(),
        user_email: userInfo.email || null,
        user_details: {
          firstName: userInfo.firstName || null,
          lastName: userInfo.lastName || null,
          company: userInfo.company || null
        },
        plan_type: 'free_trial',
        plan_name: 'Icebreaker',
        subscription_type: 'free',
        referral_code: userInfo.referral_code || null,
        time_to_complete: userInfo.time_spent_seconds ? 
          `${Math.round(userInfo.time_spent_seconds / 60)} minutes` : 'unknown',
        ...userInfo
      });
    } catch (error) {
      console.warn('Error tracking free trial completion:', error);
    }
  },

  // Navigation tracking
  trackPageView: (pageInfo = {}) => {
    try {
      // Convert string pageInfo to object if only path was provided
      const pageData = typeof pageInfo === 'string' ? { page_path: pageInfo } : pageInfo;
      
      // Get the current URL components
      const currentUrl = new URL(window.location.href);
      const urlParams = Object.fromEntries(currentUrl.searchParams.entries());
      
      // Extract page name for more specific event
      const pagePath = pageData.page_path || window.location.pathname;
      const pageTitle = pageData.page_title || document.title;
      const pageSection = pageData.page_section || null;
      
      // Create more descriptive page view event name
      let pageName = pageTitle;
      if (pagePath !== '/') {
        // Extract name from path if title is generic
        const pathSegments = pagePath.split('/').filter(segment => segment);
        if (pathSegments.length > 0) {
          const lastSegment = pathSegments[pathSegments.length - 1];
          pageName = lastSegment.charAt(0).toUpperCase() + 
                    lastSegment.slice(1).replace(/-/g, ' ');
        }
      }
      
      const sectionPrefix = pageSection ? `${pageSection}: ` : '';
      const eventName = `Viewed ${sectionPrefix}${pageName} Page`;
      
      // Create enhanced page view tracking
      const enhancedPageInfo = {
        // Page identification
        page_path: pagePath,
        page_title: pageTitle,
        page_url: window.location.href,
        page_query_params: urlParams,
        page_hash: window.location.hash,
        page_type: pageData.page_type || 'standard',
        
        // Page hierarchy
        page_section: pageSection,
        page_subsection: pageData.page_subsection || null,
        page_category: pageData.page_category || null,
        
        // Navigation context
        referrer: document.referrer || 'direct',
        referrer_domain: document.referrer ? new URL(document.referrer).hostname : null,
        entry_point: pageData.entry_point || (document.referrer ? 'internal' : 'direct'),
        navigation_type: pageData.navigation_type || 'navigate',
        
        // User context
        user_id: pageData.user_id || null,
        user_email: pageData.user_email || null,
        user_role: pageData.user_role || null,
        authenticated: pageData.authenticated !== undefined ? pageData.authenticated : !!localStorage.getItem('user'),
        
        // Session context
        session_id: pageData.session_id || null,
        page_number_in_session: pageData.page_number || null,
        
        // Timing
        load_time_ms: pageData.load_time || 0,
        
        // Device information
        device_info: {
          device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
          browser: navigator.userAgent,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`,
          language: navigator.language || navigator.userLanguage,
          screen_orientation: window.screen.orientation ? window.screen.orientation.type : null
        },
        
        // UTM parameters (if available)
        utm_params: localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {},
        
        timestamp: new Date().toISOString(),
        
        // Include any other properties passed
        ...pageData
      };
      
      Mixpanel.track(eventName, enhancedPageInfo);
    } catch (error) {
      console.warn('Error tracking page view:', error);
    }
  },

  // Check if tracking is available
  isTrackingAvailable: () => {
    try {
      return Mixpanel.trackingAvailable === true;
    } catch (error) {
      console.warn('Error checking tracking availability:', error);
      return false;
    }
  },

  // Check if we're using fallback tracking
  isUsingFallback: () => {
    try {
      return Mixpanel.usingFallback === true;
    } catch (error) {
      console.warn('Error checking if using fallback tracking:', error);
      return false;
    }
  },

  // Get fallback events for debugging
  getFallbackEvents: () => {
    try {
      if (Mixpanel.getFallbackEvents) {
        return Mixpanel.getFallbackEvents();
      }
      return [];
    } catch (error) {
      console.warn('Error getting fallback events:', error);
      return [];
    }
  },
  
  // Track attribution sources (where users came from)
  trackAttributionSource: (sourceInfo = {}) => {
    try {
      // Get UTM parameters from URL or localStorage
      const urlParams = new URLSearchParams(window.location.search);
      const storedUtmParams = localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {};
      
      // Standard UTM parameters
      const utmSource = sourceInfo.utm_source || urlParams.get('utm_source') || storedUtmParams.utm_source;
      const utmMedium = sourceInfo.utm_medium || urlParams.get('utm_medium') || storedUtmParams.utm_medium;
      const utmCampaign = sourceInfo.utm_campaign || urlParams.get('utm_campaign') || storedUtmParams.utm_campaign;
      const utmContent = sourceInfo.utm_content || urlParams.get('utm_content') || storedUtmParams.utm_content;
      const utmTerm = sourceInfo.utm_term || urlParams.get('utm_term') || storedUtmParams.utm_term;
      
      // Additional tracking parameters
      const referrer = document.referrer || sourceInfo.referrer;
      const referrerDomain = referrer ? new URL(referrer).hostname : null;
      const landingPage = window.location.pathname;
      
      // Social media source detection
      const socialSource = detectSocialSource();
      
      // Ad platform specific parameters
      const gclid = urlParams.get('gclid'); // Google Ads
      const fbclid = urlParams.get('fbclid'); // Facebook
      const msclkid = urlParams.get('msclkid'); // Microsoft/Bing
      const li_fat_id = urlParams.get('li_fat_id'); // LinkedIn
      
      // Custom ReachStream parameters
      const awc = urlParams.get('awc') || storedUtmParams.awc; // ReachStream custom parameter
      const sn = urlParams.get('sn') || storedUtmParams.sn; // ReachStream custom parameter
      
      // Determine primary source
      let primarySource = 'direct';
      if (utmSource) {
        primarySource = utmSource;
      } else if (socialSource) {
        primarySource = socialSource;
      } else if (referrerDomain) {
        primarySource = referrerDomain;
      } else if (gclid) {
        primarySource = 'google_ads';
      } else if (fbclid) {
        primarySource = 'facebook_ads';
      } else if (msclkid) {
        primarySource = 'microsoft_ads';
      } else if (li_fat_id) {
        primarySource = 'linkedin_ads';
      }
      
      // Compile attribution data
      const attributionData = {
        primary_source: primarySource,
        landing_page: landingPage,
        entry_timestamp: new Date().toISOString(),
        
        // UTM parameters
        utm_source: utmSource || null,
        utm_medium: utmMedium || null,
        utm_campaign: utmCampaign || null,
        utm_content: utmContent || null,
        utm_term: utmTerm || null,
        
        // Referrer information
        referrer: referrer || null,
        referrer_domain: referrerDomain || null,
        
        // Ad platform IDs
        gclid: gclid || null,
        fbclid: fbclid || null,
        msclkid: msclkid || null,
        li_fat_id: li_fat_id || null,
        
        // Custom parameters
        awc: awc || null,
        sn: sn || null,
        
        // Include any additional properties passed
        ...sourceInfo
      };
      
      // Store attribution data in localStorage for session persistence
      localStorage.setItem('attribution_data', JSON.stringify(attributionData));
      
      // Track the attribution event
      // Mixpanel.track('Attribution Source Captured', attributionData);
      
      // Update user properties with attribution data for first touch attribution
      if (Mixpanel.get_distinct_id()) {
        Mixpanel.people.set({
          'First Touch Source': attributionData.primary_source,
          'First Touch UTM Source': attributionData.utm_source,
          'First Touch UTM Medium': attributionData.utm_medium,
          'First Touch UTM Campaign': attributionData.utm_campaign,
          'First Touch Landing Page': attributionData.landing_page,
          'First Touch Date': attributionData.entry_timestamp,
        });
      }
      
      return attributionData;
    } catch (error) {
      console.warn('Error tracking attribution source:', error);
      return null;
    }
  },

  // Email verification tracking
  trackEmailVerificationInitiated: (verificationInfo = {}) => {
    try {
      // Get device info
      const deviceInfo = {
        device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
        browser: navigator.userAgent,
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        language: navigator.language || navigator.userLanguage
      };
      
      // Get attribution data if available
      const attributionData = localStorage.getItem('attribution_data') ? 
        JSON.parse(localStorage.getItem('attribution_data')) : {};
      
      // Get location data
      const locationData = getLocationData();
      
      Mixpanel.track('Initiated Email Verification', {
        email: verificationInfo.email || null,
        first_name: verificationInfo.first_name || null,
        last_name: verificationInfo.last_name || null,
        plan_name: verificationInfo.plan_name || 'standard',
        referral_code: verificationInfo.referral_code || null,
        timestamp: new Date().toISOString(),
        ...deviceInfo,
        ...attributionData,
        ...locationData,
        ...verificationInfo
      });
    } catch (error) {
      console.warn('Error tracking email verification initiation:', error);
    }
  },
  
  trackEmailVerified: (verificationInfo = {}) => {
    try {
      // Get device info
      const deviceInfo = {
        device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
        browser: navigator.userAgent,
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        language: navigator.language || navigator.userLanguage
      };
      
      // Get attribution data if available
      const attributionData = localStorage.getItem('attribution_data') ? 
        JSON.parse(localStorage.getItem('attribution_data')) : {};
      
      // Get location data
      const locationData = getLocationData();
      
      Mixpanel.track('Verified Email', {
        email: verificationInfo.email || null,
        first_name: verificationInfo.first_name || null,
        last_name: verificationInfo.last_name || null,
        plan_name: verificationInfo.plan_name || 'standard',
        verification_method: verificationInfo.verification_method || 'code',
        referral_code: verificationInfo.referral_code || null,
        timestamp: new Date().toISOString(),
        ...deviceInfo,
        ...attributionData,
        ...locationData,
        ...verificationInfo
      });
      
      // If we have an email, identify the user in Mixpanel
      if (verificationInfo.email) {
        Mixpanel.identify(verificationInfo.email);
        
        // Set user properties
        Mixpanel.people.set({
          'Email Verified': true,
          'Email Verification Date': new Date().toISOString(),
          'Email Verification Method': verificationInfo.verification_method || 'code'
        });
      }
    } catch (error) {
      console.warn('Error tracking email verification:', error);
    }
  },
  
  trackEmailVerificationFailed: (verificationInfo = {}) => {
    try {
      // Get location data
      const locationData = getLocationData();
      
      Mixpanel.track('Email Verification Failed', {
        email: verificationInfo.email || null,
        plan_name: verificationInfo.plan_name || 'standard',
        error: verificationInfo.error || 'Unknown error',
        referral_code: verificationInfo.referral_code || null,
        timestamp: new Date().toISOString(),
        ...locationData,
        ...verificationInfo
      });
    } catch (error) {
      console.warn('Error tracking email verification failure:', error);
    }
  },

  // Track user's geographical location (if permitted)
  trackUserGeoLocation: async (properties = {}) => {
    try {
      // Check if geolocation is available in the browser
      if (!navigator.geolocation) {
        return false;
      }
      
      // Get location data with timeout
      const getPosition = () => {
        return new Promise((resolve, reject) => {
          // Set timeout to 5 seconds
          const timeout = setTimeout(() => {
            reject(new Error("Geolocation request timed out"));
          }, 5000);
          
          navigator.geolocation.getCurrentPosition(
            (position) => {
              clearTimeout(timeout);
              resolve(position);
            },
            (error) => {
              clearTimeout(timeout);
              reject(error);
            },
            { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 }
          );
        });
      };
      
      // Try to get user's position
      const position = await getPosition();
      
      // Prepare location data
      const geoData = {
        geo_latitude: position.coords.latitude,
        geo_longitude: position.coords.longitude,
        geo_accuracy: position.coords.accuracy,
        geo_timestamp: new Date(position.timestamp).toISOString()
      };
      
      // Track the event with geo data
      Mixpanel.track('User Geo Location', {
        ...geoData,
        ...properties
      });
      
      // Update user properties
      if (Mixpanel.get_distinct_id()) {
        Mixpanel.people.set({
          'Last Known Latitude': position.coords.latitude,
          'Last Known Longitude': position.coords.longitude,
          'Last Location Timestamp': new Date(position.timestamp).toISOString()
        });
      }
      
      return geoData;
    } catch (error) {
      console.warn('Error tracking user geo location:', error);
      return false;
    }
  }
};

// Helper function to detect social media sources from referrer
function detectSocialSource() {
  const referrer = document.referrer;
  if (!referrer) return null;
  
  const domain = new URL(referrer).hostname.toLowerCase();
  
  if (domain.includes('facebook.com') || domain.includes('fb.com')) {
    return 'facebook';
  } else if (domain.includes('twitter.com') || domain.includes('t.co')) {
    return 'twitter';
  } else if (domain.includes('linkedin.com')) {
    return 'linkedin';
  } else if (domain.includes('instagram.com')) {
    return 'instagram';
  } else if (domain.includes('youtube.com') || domain.includes('youtu.be')) {
    return 'youtube';
  } else if (domain.includes('pinterest.com')) {
    return 'pinterest';
  } else if (domain.includes('reddit.com')) {
    return 'reddit';
  } else if (domain.includes('google.')) {
    return 'google';
  } else if (domain.includes('bing.com')) {
    return 'bing';
  } else if (domain.includes('yahoo.com')) {
    return 'yahoo';
  }
  
  return null;
}

// Update the getLocationData function to use "location" instead of "geo"
function getLocationData() {
  try {
    // Get basic URL information
    const fullUrl = window.location.href;
    const hostname = window.location.hostname;
    const path = window.location.pathname;
    const search = window.location.search;
    const hash = window.location.hash;

    // Handle referrer information
    let referrer = document.referrer || 'direct';
    let referrerDomain = 'direct';
    let referrerHost = 'direct';

    if (referrer && referrer !== 'direct') {
      try {
        const referrerUrl = new URL(referrer);
        referrerDomain = referrerUrl.hostname;
        referrerHost = referrerUrl.hostname;
      } catch (e) {
        // Keep defaults if parsing fails
      }
    }
    
    // Create base location data
    const locationData = {
      url: fullUrl,
      path: path,
      hostname: hostname,
      domain: hostname,
      hash: hash,
      search: search,
      page_title: document.title,
      referrer: referrer,
      referrer_domain: referrerDomain,
      referrer_host: referrerHost
    };
    
    // Add physical location data if available in localStorage
    try {
      const userLocation = localStorage.getItem('user_location');
      if (userLocation) {
        const parsedLocation = JSON.parse(userLocation);
        
        // Add physical location properties with "location" property name instead of "geo"
        locationData.location = {
          latitude: parsedLocation.latitude,
          longitude: parsedLocation.longitude,
          source: parsedLocation.source
        };
        
        // Add city, region, country if available (from IP-based lookup)
        if (parsedLocation.city) locationData.location.city = parsedLocation.city;
        if (parsedLocation.region) locationData.location.region = parsedLocation.region;
        if (parsedLocation.country) locationData.location.country = parsedLocation.country;
      }
    } catch (e) {
      // Silently handle location data errors
    }

    return locationData;
  } catch (error) {
    // Return minimal data if there's an error
    return { url: window.location.href };
  }
}

export default Analytics; 