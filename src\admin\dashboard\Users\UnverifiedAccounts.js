import { useEffect, useRef, useState } from "react";
import AdminHeader from "../../layouts/AdminHeader.js";
import S from "../../assets/css/layouts/admin-header.module.css";
import { useNavigate } from "react-router-dom";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import Alert from "../../../customer/common-files/alert.js";
import loadingGif from "../../../customer/assests/waiting.gif";

const UnverifiedAccounts = (props) => {
  const {
    foundCounts,
    pageNumber,
    dataPagi,
    loadingCount,
    paginationDataCount,
  } = props;
  const [foundContacts, setFoundContacts] = useState(foundCounts);
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [adminDetails, setAdminDetails] = useState(
    JSON.parse(localStorage.getItem("user"))
  );
  const itemsPerPage = 10;
  const [currentPage, setCurrentPage] = useState(1);
  const [startFrom, setStartFrom] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  useEffect(() => {
    postData();
  }, [currentPage, searchQuery]);

  const postData = async () => {
    try {
      searchQuery ? setLoading(false) : setLoading(true);
      const params = JSON.stringify({
        page: searchQuery ? 1 : currentPage,
        pageSize: searchQuery ? 100000 : itemsPerPage,
        searchParams: {
          verificationStatus: "inactive",
          email: searchQuery
        },
        sortBy: "DESC",
      });
      const res = await PostWithTokenNoCache(
        ApiName.unverifiedAccounts,
        params
      );
      if (res?.status === 200) {
        let data = JSON.parse(res.data.data);
        setTotalCount(data["all_user"]["totalItems"]);
        setTotalPages(data["all_user"]["totalPages"]);
        setData(data["all_user"]["items"]);
        setLoading(false);
      }
    } catch (errors) {
      setLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(errors?.response?.data?.message);
      setDefaultAlert(true);
    }
  };

  return (
    <div className={S.app}>
      <AdminHeader />

      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div style={{ width: "100%", padding: "0 10px 0 0" }}>
              <h5 className={S.FreemiumUsers}>Unverified Accounts</h5>
              <div className={S.content}></div>
              <aside className={S.right_sidebar}>
                <div className="d-flex flex-row justify-content-between pl-2">
                  <div>
                    <div className="d-flex flex-row">
                      <div>
                        <h5 className={S.totla_users}>Total users:</h5>
                      </div>
                      <div>
                        <p className={S.datalegnth}>{totalCount}</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="admin-search">
                      <input
                        type="text"
                        placeholder="Search by Email"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className={S.searchbyemail}
                      />
                    </div>
                  </div>
                </div>
                <table
                  className="admin-table text-center "
                  style={{ display: "table" }}
                >
                  <thead>
                    <tr className="table-active">
                      <th className={S.tableheaderspace}>ID</th>
                      <th className={S.tableheaderspaces}>Email</th>
                      <th className={S.tableheaderspace}>Status</th>
                      <th className={S.tableheaderspace}>Created At</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((item, i) => (
                      <tr key={i + 1} className={S.tablelight}>
                        <td className={S.tabledataspaceing}>
                          {item.id}
                        </td>
                        <td className={S.tabledataspaceingg}>{item.email}</td>
                        <td className={S.tabledataspaceing}>{item.status}</td>
                        <td className={S.tabledataspaceing}>
                          {new Date(item.createdAt).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </aside>
            </div>
          ) : (
            <div className="mx-auto mt-5" style={{ display: loading }}>
              <img
                src={loadingGif}
                alt="Loading"
                className="loader"
                width="400"
              />
            </div>
          )}
        </div>
      </div>

      {searchQuery ? (
        ""
      ) : (
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalPages}
          pageSize={1}
          onPageChange={(page) => {
            setCurrentPage(page);
          }}
        />
      )}

      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};
export default UnverifiedAccounts;