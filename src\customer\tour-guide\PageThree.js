import React from "react";
import "../assests/css/layouts/demo.css";
import { useNavigate, useParams } from "react-router-dom";
import Header from "../layouts/Header";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../customer/common-files/ApiCalls.js";
const PageOne = () => {
  let data_params = useParams();
  const navigate = useNavigate();

  const goNext = () => {
    navigate("/tour-guide-wishlist/" + data_params.id);
  };

  const goBack = () => {
    navigate("/tour-guide-sort-by/" + data_params.id);
  };

  const skipTour = async () => {
    const params = JSON.stringify({
      id: data_params.id,
      actionType: "SKIP",
      repeatTimeDefault: 1,
      currentRepeatedTime: 1,
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.appTourUpdate, params);
      if (res.data.status == "200") {
        navigate("/dashboard");
      }
    } catch (errors) {}
  };
  return (
    <>
      <div>
        <Header />
        <div className="examplethree">
          <img
            src="../../images/demo-image-3.png"
            class="img-fluid"
            alt="Responsive image"
            style={{ width: "100%" }}
          />
          <img
            src="../../images/demo-image-4.png"
            class="img-fluid"
            alt="Responsive image"
            style={{ width: "100%",display:"none" }}
          />
          <div>
            <p className="skiptour3" onClick={skipTour}>
              Skip Tour
            </p>
          </div>
          {/* <p className="select">Select</p>
          <p className="Youcanselect">
            You can select contacts one by one or in bulk.
          </p> */}

          <div className="sortbuttonss">
            <div className="d-flex flex-row justify-content-center">
              <div>
                <button type="button" onClick={goBack} className="sortback">
                  Back
                </button>
              </div>
              <div>
                <button type="button" onClick={goNext} className="sortnext">
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PageOne;
