import React,{createContext,useState} from 'react';

const UserData=createContext();

export function UserProvider({children}){

	const [dataCombine,setDataCombine]=useState({
		'user':JSON.parse(localStorage.getItem('user')),
		'zipImage':true, 

	});   //console.log(dataCombine.user.email);
	const addAllDataCombine=()=>{
		
	}
	return(
		<UserData.Provider value={{dataCombine,addAllDataCombine}}>
		   {children}
		 </UserData.Provider>

	);
}
	

export default UserData;