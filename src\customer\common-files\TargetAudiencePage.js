import React from "react";
import "../assests/css/layouts/target_audience_page.css";
import { useParams } from "react-router-dom";
const TargetAudiencePage = () => {

    const { referralCode } = useParams();

    // Get the current domain name
    const currentDomain = window.location.origin;


    return (
        <>
            <div className="primary">
                <div className="secondary">
                    <div className="reachaudience">
                        <img src="../images/get_special.png" width="500" />
                    </div>

                    <div className="thired-leyout">
                        <p style={{ fontSize: "14px", fontWeight: "600", padding: "0 0 0 10px", margin: "6px 0 8px 0" }}>Spreading the good word!</p>

                        <p className="prospects’">
                            Is anyone in my network looking for new business leads or rolling out a new campaign?
                        </p>

                        <p style={{ fontSize: "14px", fontWeight: "600", padding: "0 0 0 10px", margin: "6px 0 8px 0" }}>Create a list of 5000 new leads in only 3 steps. With ReachStream, you get:</p>

                        <ul>
                            <li className="bowl" style={{ fontSize: "12px" }}>Emails, phone numbers, and 20+ contact & company insights</li>
                            <li className="bowl" style={{ fontSize: "12px" }}>Opt-in contact information</li>
                            <li className="bowl" style={{ fontSize: "12px" }}>Unlimited views & free contact updates</li>
                            <li className="bowl" style={{ fontSize: "12px" }}>Advanced API access on all plans</li>
                        </ul>
                        <p className="prospects’">
                            Sign up with this link – <a href={`${currentDomain}/super-saver-sign-up?${referralCode}`} style={{ textDecoration: "none", color: "#55C2C3" }}>{`${currentDomain}/super-saver-sign-up?${referralCode}`}</a>
                        </p>


                        <p className="prospects’">
                            Or just try it for free (No credit card details needed)–  <a href={`${currentDomain}/free-trial-sign-up?${referralCode}`} style={{ textDecoration: "none", color: "#55C2C3" }}>{`${currentDomain}/free-trial-sign-up?${referralCode}`}</a>
                            </p>
                        <br />
                    </div>

                    <div className="d-flex flex-row justify-content-end">
                        <div>
                            <br />
                        </div>
                    </div>
                </div>
            </div>


        </>
    );
};

export default TargetAudiencePage;