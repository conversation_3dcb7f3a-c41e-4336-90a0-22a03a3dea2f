import React, { useState, useEffect } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import AdminHeader from "../../layouts/AdminHeader.js";
import LeftSideNav from '../../layouts/LeftSideNav.js';
import {
    postWithToken,
    PostWithTokenNoCache,
} from "../../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import loadingGif from '../../../customer/assests/waiting.gif';
import { useNavigate, useParams } from "react-router-dom";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import Alert from "../../../customer/common-files/alert.js";

const UpdateDomainRestriction = () => {

    const navigate = useNavigate();
    const { id } = useParams();

    const [data, setData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [allowedUserPerDomainMsg, setAllowedUserPerDomainMsg] = useState("");
    const [allowedUserMsg, setAllowedUserMsg] = useState("");
    const [validDomainMsg, setValidDomainMsg] = useState("");
    const [loading, setLoading] = useState(true);
    const {
        defaultAlert,
        defaultErrorMsg,
        setButtonType,
        setDefaultAlert,
        setDefaultErrorMsg,
    } = UseTabStore();

    useEffect(() => {
        const getData = async () => {
            try {
                let params = JSON.stringify({
                    "id": id
                });
                const res = await PostWithTokenNoCache(ApiName.findDomainRestriction, params);
                if (res && "status" in res) {
                    if (res.status == 200) {
                        setIsLoading(false);
                        let response = res.data.data ? JSON.parse(res.data.data) : null;
                        setData(response);
                    }
                }
            } catch (errors) {
                setLoading(false);
                setButtonType("error");
                setDefaultErrorMsg(errors?.response?.data?.message);
                setDefaultAlert(true);
            }
        };
        getData();
    }, [])

    const handleAllowedUserPerDomainInputChange = (e) => {
        const { id, value } = e.target;
        const pattern = /^\d+$/;
        if (pattern.test(value) && parseInt(value)) {
            setAllowedUserPerDomainMsg("");
        } else {
            setAllowedUserPerDomainMsg("Only Positive Integers");
        }
        setData((prevData) => ({
            ...prevData,
            [id]: value,
        }));
    }

    const handleDomainInputChange = (e) => {

        const { id, value } = e.target;

        if (value.trim()) {
            setValidDomainMsg("")
        } else {
            setValidDomainMsg("Domain could not be a empty")
        }
        setData((prevData) => ({
            ...prevData,
            [id]: value,
        }));
    }

    const handleUserCountInputChange = (e) => {

        const { id, value } = e.target;
        const pattern = /^\d+$/;

        if (pattern.test(value) && parseInt(value)) {
            setAllowedUserMsg("");
        } else {
            setAllowedUserMsg("Only Positive Integers");
        }
        setData((prevData) => ({
            ...prevData,
            [id]: value,
        }));
    };


    const submitHandler = async () => {

        //remove attribute createdAt from collection
        const { createdAt, ...params } = data;

        const res = await PostWithTokenNoCache(ApiName.updateDomainRestriction, params);
        if (res && "status" in res) {
            if (res.status == 200) {
                navigate("/admin/domain-restriction-list")
            }
        }

    }

    return (
        <div className={S.app}>
            <AdminHeader />
            <div className={S.main_container}>
                <div className="d-flex flex-row">
                    <div style={{ width: "350px" }}>
                        <LeftSideNav />
                    </div>

                    <div className={S.formlayout}>
                        {!isLoading ? (
                            <>
                                <form className="mb-3">
                                    <h3 className={S.adminsignup}>Update Domain Restriction </h3>

                                    <div className="row">
                                        <div className="col-md-12">
                                            <div className="form-group">
                                                <label>Domain</label>
                                                <input
                                                    type="text"
                                                    className="form-control"
                                                    id="domainName"
                                                    onChange={handleDomainInputChange}
                                                    value={data.domainName}
                                                    required
                                                />
                                                {validDomainMsg ?
                                                    (<p className="invalid">{validDomainMsg} </p>)
                                                    : ("")}
                                            </div>
                                        </div>

                                        <div className="col-md-12">
                                            <div className="form-group">
                                                <label>Current Users Count</label>
                                                <input
                                                    type="text"
                                                    className="form-control"
                                                    id="userCount"
                                                    onChange={handleUserCountInputChange}
                                                    value={data.userCount}
                                                    required
                                                />
                                                {allowedUserMsg ?
                                                    (<p className="invalid">{allowedUserMsg} </p>)
                                                    : ("")}
                                            </div>
                                        </div>

                                        <div className="col-md-12">
                                            <div className="form-group">
                                                <label>Allowed User per Domain</label>
                                                <input
                                                    type="text"
                                                    className="form-control"
                                                    id="allowedUserPerDomain"
                                                    onChange={handleAllowedUserPerDomainInputChange}
                                                    value={data.allowedUserPerDomain}
                                                    required
                                                />
                                                {allowedUserPerDomainMsg ?
                                                    (<p className="invalid">{allowedUserPerDomainMsg} </p>)
                                                    : ("")}
                                            </div>
                                        </div>
                                    </div>
                                </form>
                                <div className="mb-3">
                                    <button
                                        type="submit"
                                        className={`${S.adminsubmit} ${validDomainMsg || allowedUserMsg || allowedUserPerDomainMsg ? 'cust-disabled' : ''}`}
                                        onClick={submitHandler}
                                    >Update</button>

                                </div>
                            </>
                        ) : (
                            <div className="d-flex flex-row justify-content-center mt-5" style={{ display: isLoading }}>
                                <img src={loadingGif} alt="Loading" className="loader" width="400" />
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {defaultAlert && defaultErrorMsg ? (
                <Alert data={defaultErrorMsg} />
            ) : (<></>)}

        </div>
    )


}

export default UpdateDomainRestriction;