import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import lzString from 'lz-string';

const initialState = {
  noOfContact: null,
  selectedTab: 'contact',
  sortingBy: 12,
  defaultError: '',
  searchPattern: {},
  companyAddressCountry: {},
  companyAddressState: {},
  companyAddressCity: {},
  companyZipCode: {},
  contactJobTitle1: {},
  contactJobTitleLevel1: {},
  contactJobDeptName1: {},
  contactJobFunctionName1: {},
  sicCode: {},
  companyEmployeeSize: {},
  companyAnnualRevenueAmount: {},
  companyTechKeyWordsList: {},
  companyName: {},
  selectedDownloadPopup: {},
  selectedEmail: {},
  currentPage: 1,
  selectedRows: [],
  allRecordsNew: '',
  companyName1Collections: [],
  companyName1: [],
  resetPage: null,
  pageSize: null,
  checkboxesGlobal: [],
  viewSelected: false,
  sampleDownloadedIds: '',
  loadedData: [],
  viewModal: false,
  checkedBoxes: [],
  selectedValues: [],
  selectedOptions: null,
  sicCodeOption: [],
  industryData: [],
  isShowDownloadedData: true,
  defaultAlert: false,
  defaultErrorMsg: "",
  isLowViewCredits: false,
  socialMediaType: null,
  selectedContact: null,
  contactModelId: null,
  profileUpdated: null,
  buttonType: null,
  copyContent: false,
  checkedItems: {},
  currentSelectedPage: 1,
  revenueSizeCheckedBoxes: [],
  employeeSizeCheckedBoxes: [],
  companyURL: [],
  companyType: {},
  companyTypeCheckedBoxes: [],
  marketplaceToken: '',
  withDownloaded: true,
  applyFilters: false,
  selectedData: [],
  emailRevealedHistory: [],
  isFiltered: false,
  checkIsRevealedHistory: false,
  foundCounts: null,
  advancedFilterSearchPattern: {},
  advancedFilter: false,
  unSelectedRows: [],
  revealedEmail: [],
  originalFoundCounts: null,
  companyKeyword: {},
  openParentItems: {},
  allPlanDetails: [],
  selectedPlanDetails: null,
  selectedPlanType: null,
  referralCode: "",
  gtmTag: "",
  redisKey: null,
  settingsActiveTab: "profile"
};

// Storage management utility
const storageManager = {
  clearIfNeeded: () => {
    const stored = localStorage.getItem('tab-store');
    if (stored?.length > 4 * 1024 * 1024) { // 4MB threshold
      localStorage.removeItem('tab-store');
      console.warn('Cleared oversized tab-store data');
      return true;
    }
    return false;
  },
  getSize: () => {
    const stored = localStorage.getItem('tab-store');
    return stored ? stored.length : 0;
  }
};

const UseTabStore = create(
  persist(
    (set, get) => ({
      ...initialState,

      // Actions
      setIsFiltered: (val) => set({ isFiltered: val }),
      setApplyFilters: (val) => set({ applyFilters: val }),
      setSelectedCompanyAddressCountry: (val) => set({ companyAddressCountry: val }),
      setSelectedCompanyAddressState: (val) => set({ companyAddressState: val }),
      setSelectedCompanyAddressCity: (val) => set({ companyAddressCity: val }),
      setSelectedCompanyZipCode: (val) => set({ companyZipCode: val }),
      setSelectedContactJobTitle1: (val) => set({ contactJobTitle1: val }),
      setSelectedContactJobTitleLevel1: (val) => set({ contactJobTitleLevel1: val }),
      setSelectedContactJobDeptName1: (val) => set({ contactJobDeptName1: val }),
      setSelectedContactJobFunctionName1: (val) => set({ contactJobFunctionName1: val }),
      setSelectedCompanyEmployeeSize: (val) => set({ companyEmployeeSize: val }),
      setSelectedCompanyAnnualRevenueAmount: (val) => set({ companyAnnualRevenueAmount: val }),
      setSelectedCompanyTechKeyWordsList: (val) => set({ companyTechKeyWordsList: val }),
      setSelectedCompany: (val) => set({ companyName: val }),
      setSelectedTab: (tabName) => set({ selectedTab: tabName }),
      setSortingBy: (value) => set({ sortingBy: value }),
      setDefaultError: (error) => set({ defaultError: error }),
      setNumber: (value) => set({ number: value }),
      setSearchPattern: (value) => set({ searchPattern: value }),
      setSelectedDownloadPopup: (value) => set({ selectedDownloadPopup: value }),
      setSelectedEmail: (value) => set({ selectedEmail: value }),
      setCurrentPage: (value) => set({ currentPage: value }),
      setSelectedRows: (value) => set({ selectedRows: value }),
      setAllRecordsNew: (value) => set({ allRecordsNew: value }),
      setCompanyName1Collections: (value) => set({ companyName1Collections: value }),
      setCompanyName1: (value) => set({ companyName1: value }),
      setResetPage: (value) => set({ resetPage: value }),
      setPageSize: (value) => set({ pageSize: value }),
      setCheckboxesGlobal: (value) => set({ checkboxesGlobal: value }),
      setViewSelected: (value) => set({ viewSelected: value }),
      setSampleDownloadedIds: (value) => set({ sampleDownloadedIds: value }),
      setLoadedData: (value) => set({ loadedData: value }),
      setViewModal: (value) => set({ viewModal: value }),
      setCheckedBoxes: (value) => set({ checkedBoxes: value }),
      setSelectedValues: (value) => set({ selectedValues: value }),
      setSelectedOptions: (value) => set({ selectedOptions: value }),
      setIndustryData: (value) => set({ industryData: value }),
      setSicCode: (value) => set({ sicCode: value }),
      setShowDownloadedData: (value) => set({ isShowDownloadedData: value }),
      setDefaultAlert: (value) => set({ defaultAlert: value }),
      setDefaultErrorMsg: (value) => set({ defaultErrorMsg: value }),
      setIsLowViewCredits: (value) => set({ isLowViewCredits: value }),
      setSocialMediaType: (value) => set({ socialMediaType: value }),
      setSelectedContact: (value) => set({ selectedContact: value }),
      setContactModelId: (value) => set({ contactModelId: value }),
      setButtonType: (value) => set({ buttonType: value }),
      setProfileUpdated: (value) => set({ profileUpdated: value }),
      setCopyContent: (value) => set({ copyContent: value }),
      setCheckedItems: (value) => set({ checkedItems: value }),
      setCurrentSelectedPage: (value) => set({ currentSelectedPage: value }),
      setRevenueSizeCheckedBoxes: (value) => set({ revenueSizeCheckedBoxes: value }),
      setEmployeeSizeCheckedBoxes: (value) => set({ employeeSizeCheckedBoxes: value }),
      setSelectedCompanyURL: (value) => set({ companyURL: value }),
      setSelectedCompanyType: (value) => set({ companyType: value }),
      setCompanyTypeCheckedBoxes: (value) => set({ companyTypeCheckedBoxes: value }),
      setMarketplaceToken: (value) => set({ marketplaceToken: value }),
      setWithDownloaded: (value) => set({ withDownloaded: value }),
      setSelectedData: (value) => set({ selectedData: value }),
      setEmailRevealedHistory: (value) => set({ emailRevealedHistory: value }),
      setCheckIsRevealedHistory: (value) => set({ checkIsRevealedHistory: value }),
      setFoundCounts: (value) => set({ foundCounts: value }),
      setAdvancedFilterSearchPattern: (value) => set({ advancedFilterSearchPattern: value }),
      setAdvancedFilter: (value) => set({ advancedFilter: value }),
      setUnSelectedRows: (value) => set({ unSelectedRows: value }),
      setRevealedEmail: (value) => set({ revealedEmail: value }),
      setOriginalFoundCounts: (value) => set({ originalFoundCounts: value }),
      setNoOfContact: (value) => set({ noOfContact: value }),
      setCompanyKeyword: (val) => set({ companyKeyword: val }),
      setOpenParentItems: (val) => set({ openParentItems: val }),
      setRedisKey: (val) => set({ redisKey: val }),
      setSettingsActiveTab: (val) => set({ settingsActiveTab: val }),
      reset: () => {
        storageManager.clearIfNeeded();
        set(initialState);
      },
      setAllPlanDetails: (val) => set({ allPlanDetails: val }),
      setSelectedPlanDetails: (val) => set({ selectedPlanDetails: val }),
      setSelectedPlanType: (val) => set({ selectedPlanType: val }),
      setReferralCode: (val) => set({ referralCode: val }),
      setGtmTag: (val) => set({ gtmTag: val }),

      // Storage utility methods
      getStorageSize: () => storageManager.getSize(),
      clearStorage: () => {
        localStorage.removeItem('tab-store');
        set(initialState);
      }
    }),
    {
      name: 'tab-store',
      getStorage: () => localStorage,
      serialize: (state) => lzString.compress(JSON.stringify(state)),
      deserialize: (str) => {
        try {
          return JSON.parse(lzString.decompress(str)) || initialState;
        } catch (error) {
          console.error('Failed to deserialize state:', error);
          return initialState;
        }
      },
      partialize: (state) => {
        const {
          // Exclude large or temporary data
          loadedData,
          checkboxesGlobal,
          viewModal,
          checkedBoxes,
          copyContent,
          checkedItems,
          companyName1Collections,
          selectedRows,
          unSelectedRows,
          revealedEmail,
          emailRevealedHistory,
          isFiltered,
          applyFilters,
          viewSelected,
          sampleDownloadedIds,
          selectedValues,
          sicCodeOption,
          industryData,
          revenueSizeCheckedBoxes,
          employeeSizeCheckedBoxes,
          companyTypeCheckedBoxes,
          selectedData,
          allPlanDetails,
          // Keep essential state
          ...essentialState
        } = state;

        return essentialState;
      },
      onRehydrateStorage: () => (state) => {
        storageManager.clearIfNeeded();
        // console.log('Storage size:', storageManager.getSize(), 'bytes');
      }
    }
  )
);

// Initialize storage check
storageManager.clearIfNeeded();

export default UseTabStore;