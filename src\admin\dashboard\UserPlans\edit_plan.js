import React, { useEffect, useState } from "react";
import S from '../../assets/css/layouts/admin-header.module.css';
import LeftSideNav from "../../layouts/LeftSideNav";
import { useParams } from "react-router-dom";
import { ApiName } from "../../../customer/common-files/ApiNames";
import { PostWithTokenNoCache, postWithToken } from '../../../customer/common-files/ApiCalls';
import AdminHeader from "../../layouts/AdminHeader";
import loadingGif from '../../../customer/assests/waiting.gif';
import { useNavigate } from "react-router-dom";
import { currencyList } from "../../../customer/common-files/currencyList";

const EditPlan = () => {

    const navigate = useNavigate();
    const { id } = useParams();

    const [recurring, setRecurring] = useState(false);
    const [oneTimePayment, setOneTimePayment] = useState(true);
    const [paymentId, setPaymentId] = useState(false);
    const [package_name, setPackageName] = useState(null);
    const [package_price, setPackagePrice] = useState('');
    const [noOfUsers, setNoOfUsers] = useState('');
    const [freeTrialUntil, setFreeTrialUntil] = useState('');
    const [noOfCredits, setNoOfCredits] = useState('Unlimited');
    const [noOfProfileViews, setNoOfProfileViews] = useState('Unlimited');
    const [registerLink, setRegisterLink] = useState(null);
    const [currency, setCurrency] = useState('USD');
    const [billingPeriod, setBillingPeriod] = useState('daily');
    const [paymentDuration, setPaymentDuration] = useState(null);
    const [price_id, setPriceId] = useState(null);
    const [isCreated, setIsCreated] = useState(false);
    const [message, setMessage] = useState(null);
    const [customDays, setCustomDays] = useState(null);
    const [day, setDay] = useState(null);
    const [duration, setDuration] = useState(null);
    const [planNameErrorMessage, setPlanNameErrorMessage] = useState();
    const [priceErrorMessage, setPriceErrorMessage] = useState();
    const [noOfUsersErrorMessage, setNoOfUsersErrorMessage] = useState();
    const [noOfCreditErrorMessage, setNoOfCreditErrorMessage] = useState();
    const [noOfProfileErrorMessage, setNoOfProfileErrorMessage] = useState();
    const [freeTrialErrorMessage, setFreeTrialErrorMessage] = useState();
    const [dayErrorMessage, setDayErrorMessage] = useState();
    const [customCredit, setCustomCredit] = useState(false);
    const [customView, setCustomView] = useState(false);
    const [data, setData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const getData = async () => {
            try {
                let params = JSON.stringify({
                    "id": id
                });
                const res = await PostWithTokenNoCache(ApiName.findPricePlanById, params);
                if (res && "status" in res) {
                    if (res.status == 200) {
                        setIsLoading(false);
                        let response = res.data.data ? JSON.parse(res.data.data) : null;
                        setData(response);
                        if (response.export.toLowerCase() == "unlimited") {
                            setCustomCredit(false);
                        } else {
                            setCustomCredit(true);
                        }
                        if (response.no_of_contact_views.toLowerCase() == "unlimited") {
                            setCustomView(false);
                        } else {
                            setCustomView(true);
                        }
                        // Get the current domain name
                        const currentDomain = window.location.origin;
                        // Generate the link by appending the package_name with a "+" character
                        const link = `${currentDomain}/sign-up/${convertToUrlFriendlyString(response.package_name)}`;
                        setRegisterLink(link)
                    }
                }
            } catch (error) {
                // Handle any errors
            }
        };
        getData();
    }, [])

    useEffect(() => {
        if (data) {
            setPackageName(data.package_name);
            setPackagePrice(data.price);
            setNoOfUsers(data.no_of_users);
            setNoOfCredits(data.export);
            setNoOfProfileViews(data.no_of_contact_views);
            setCurrency(data.currency);
            setBillingPeriod(data.billing_period); // Set the full value (e.g., "4_month")
            setPaymentDuration(data.payment_duration);
            setPriceId(data.price_id);
            setFreeTrialUntil(data.free_trial_until);

            // Handle special case for billing_period (e.g., "4_month")
            if (data.billing_period) {
                const [dayValue, durationValue] = data.billing_period.split('_');
                setDay(dayValue); // Set the number part (e.g., 4)
                setDuration(durationValue); // Set the duration part (e.g., month)
            }

            data.payment_duration == "recurring" ? setRecurring(true) : setOneTimePayment(true);
            handleBillingPeriod(data.billing_period);
            setIsLoading(false); // Data is loaded
        }
    }, [data]); // The effect will run on component mount

    function convertToUrlFriendlyString(inputString) {
        // Convert the string to lowercase
        const lowercaseString = inputString.toLowerCase();

        // Replace spaces with hyphens
        const urlFriendlyString = lowercaseString.replace(/\s+/g, '-');

        return urlFriendlyString;
    }

    const handleNofUsersInput = (e) => {
        const inputValue = e.target.value;

        // Regular expression to allow only positive integers, floating-point numbers, or an empty string
        const pattern = /^\d*\.?\d*$/;

        if (inputValue === "" || pattern.test(inputValue)) {
            setNoOfUsersErrorMessage(''); // Clear error message
            setNoOfUsers(inputValue); // Update the state
        } else {
            setNoOfUsersErrorMessage('Please enter numbers only');
        }
    };

    const handleNoOfCreditsInput = (e) => {
        const inputValue = e.target.value;

        // Check if '@', '!', or '&' is present in the input
        if (/[^a-zA-Z0-9\s]/.test(inputValue)) {
            setNoOfCreditErrorMessage('Enter a valid credits, avoid using @$!');
        } else if (/[a-zA-Z]/.test(inputValue) && inputValue.toLowerCase() !== 'unlimited') {
            setNoOfCreditErrorMessage('Enter a valid credits, avoid using @$!');
        } else {
            setNoOfCreditErrorMessage('');
        }

        // Update the credits state
        setNoOfCredits(inputValue);
    }

    const handleNoOfProfileInput = (e) => {
        const inputValue = e.target.value;

        // Check if '@', '!', or '&' is present in the input
        if (/[^a-zA-Z0-9\s]/.test(inputValue)) {
            setNoOfProfileErrorMessage('Enter a valid profile views, avoid using @$!');
        } else if (/[a-zA-Z]/.test(inputValue) && inputValue.toLowerCase() !== 'unlimited') {
            setNoOfProfileErrorMessage('Enter a valid profile views, avoid using @$!');
        } else {
            setNoOfProfileErrorMessage('');
        }

        // Update the credits state
        setNoOfProfileViews(inputValue);
    }

    const updatePlan = async () => {
        // const postData = {
        //     id,
        //     export: noOfCredits,
        //     no_of_contact_views: noOfProfileViews
        // }
        const postData = {
            id,
            // package_name: package_name.trim(),
            // billing_period: billingPeriod,
            // paymentDuration,
            // currency,
            // priceInCents: parseFloat(package_price) * 100,
            // free_trial_until: freeTrialUntil,
            // payment_duration: oneTimePayment ? "one_time" : "recurring",
            no_of_contact_views: noOfProfileViews,
            export: noOfCredits,
            no_of_users: noOfUsers,
            // verified_email_phone: "Yes",
            // access_database: "Yes",
            // custom_filter: "Yes",
            // data_fields: "23+",
            // price_id,
            // price: package_price,
        }
        // console.log(postData);
        // return false;
        try {
            // Send the updated data to the server
            const url = `${ApiName.editPackegeDetails}`;
            const res = await postWithToken(url, postData);
            if (res && "status" in res && res.status === 200) {
                // setMessage(res.data.message);
                // setTimeout(() => {
                //     setMessage('');
                //     setIsCreated(false);
                // }, 3000)
                navigate("/admin/all-plans");
            }
        } catch (error) {
            // Handle any errors
        }
    }

    const selectOnetime = () => {
        setRecurring(false);
        setOneTimePayment(true);
        setFreeTrialUntil("");
        setFreeTrialErrorMessage("");
        setDayErrorMessage("");
    }

    const handleFreeTrialDaysChange = (e) => {

        const inputValue = e.target.value;

        // Regular expression to allow only positive integers
        const pattern = /^(\d+|\s)$/;

        if (!pattern.test(inputValue)) {
            setFreeTrialErrorMessage('Trial period must be between 1 and 730, Please enter numbers only');
            setFreeTrialUntil(inputValue);
        } else {
            const numericValue = parseInt(inputValue, 10);

            if (numericValue < 1 || numericValue > 730) {
                setFreeTrialErrorMessage('Trial period must be between 1 and 730, Please enter numbers only');
            } else {
                // Update the freeTrialUntil state
                setFreeTrialUntil(parseInt(inputValue));
                setFreeTrialErrorMessage('');
            }
        }

    };

    const handleBillingPeriod = (input) => {
        // Check if the input matches the pattern of a number followed by an underscore and a duration (e.g., 4_month)
        const match = input.match(/^(\d+)_([a-zA-Z]+)$/);
        if (match) {
            const dayValue = match[1];  // The numeric part (e.g., 4)
            const durationValue = match[2];  // The text part (e.g., month)

            // Set the day and duration values
            setDay(dayValue);
            setDuration(durationValue);  // e.g., "month"

            // Optionally, set the billing period (this may depend on how you use it)
            setBillingPeriod(input); // Store the full input, like "4_month"

            setCustomDays(true);  // Hide custom input if a valid period is selected
        } else if (!input) {
            // If input is not a valid value and not in the "day", "week", etc.
            setCustomDays(true);
            setDayErrorMessage("Please enter numbers only");
        } else {
            // Handle other valid cases (like "day", "week", etc.)
            setBillingPeriod(input);
            setCustomDays(false);
        }
    };

    const handleCustomBillingPeriod = (input) => {
        let billingPeriod = day + '_' + input;
        setDuration(input);
        setBillingPeriod(billingPeriod);
    }

    const handleDay = (e) => {
        const input = e.target.value;
        // Regular expression to allow only positive integers
        const pattern = /^\d+$/;

        if (pattern.test(input) || input === '') {
            if (input !== '0')
                setDay(input)
            setDayErrorMessage("");
        } else {
            setDayErrorMessage("Please enter numbers only")
        }
    }

    const handlePackageInput = (e) => {
        const inputValue = e.target.value;

        // Check if '@', '!', or '&' is present in the input
        if (/[^a-zA-Z0-9\s]/.test(inputValue)) {
            setPlanNameErrorMessage('Enter a valid plan name, avoid using @$!');
        } else {
            setPlanNameErrorMessage('');
        }

        // Update the package_name state
        setPackageName(inputValue);
    }

    const handlePriceInput = (e) => {
        const inputValue = e.target.value;

        // Regular expression to allow only positive integers or floating-point numbers
        const pattern = /^\d*\.?\d+$/;

        if (!pattern.test(inputValue)) {
            setPriceErrorMessage('Please enter numbers only');
        } else {
            setPriceErrorMessage('');
        }

        // Update the price state
        setPackagePrice(inputValue);
    }

    const handleFreeTrialInput = (e) => {
        const inputValue = e.target.value;

        // Check if '@', '!', or '&' is present in the input
        if (inputValue.includes('@') || inputValue.includes('!') || inputValue.includes('&')) {
            setPlanNameErrorMessage('Trial period must be between 1 and 730, Please enter numbers only');
        } else {
            setPlanNameErrorMessage('');
        }

        // Update the package_name state
        setPackageName(inputValue);
    }

    const checkCustomCredits = () => {
        setCustomCredit(!customCredit)
        if (customCredit) {
            setNoOfCredits("Unlimited");
        } else {
            setNoOfCredits("");
        }
        setNoOfCreditErrorMessage("");
    }
    const checkCustomViews = () => {
        setCustomView(!customView)
        if (customView) {
            setNoOfProfileViews("Unlimited")
        } else {
            setNoOfProfileViews("")
        }
        setNoOfProfileErrorMessage("");
    }

    const selectRecurring = () => {
        setOneTimePayment(false);
        setRecurring(true);
    }

    return (
        <>
            <div className={S.app}>
                <AdminHeader></AdminHeader>
                <div className={S.main_container}>
                    <div className="d-flex flex-row">
                        <div style={{ width: "350px" }}>
                            <LeftSideNav />

                        </div>
                        <div style={{ width: "100%", padding: "0 10px 0 0" }}>
                            <div className={S.content}>{/* Main content */}</div>
                            <aside className={S.right_sidebar}>
                                <div className="pl-3 pr-3 d-flex flex-row justify-content-between">
                                    <div className={S.createcustomerplan}>
                                        <h4>Edit Custom Plan</h4>
                                        <span className={S.underline}></span>
                                    </div>
                                    {message ? <div className="alert alert-success">{message}</div> : <></>}
                                </div>
                                {!isLoading ? (
                                    <div className="pl-3 pr-3">
                                        <div className="row mt-5">
                                            <div className="col-md-3">
                                                <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>Payment Duration</label>
                                                <div className="d-flex flex-row">

                                                    <div className="form-group mr-3">
                                                        <button type="button"
                                                            style={
                                                                !recurring
                                                                    ? {
                                                                        width: "90px",
                                                                        cursor: "pointer",
                                                                        border: "1px solid #55C2C3",
                                                                        backgroundColor: "#E8F7F7",
                                                                        padding: "3px 0px"
                                                                    }
                                                                    : {
                                                                        width: "90px",
                                                                        cursor: "pointer",
                                                                        padding: "3px 0px",
                                                                    }
                                                            }
                                                            className="form-control pure-button-active"
                                                            id="exampleInputEmail1"
                                                            aria-describedby="emailHelp"
                                                        >
                                                            One Time
                                                        </button>

                                                    </div>
                                                    <div className="mr-4">
                                                        <button type="button"
                                                            style={
                                                                recurring
                                                                    ? {
                                                                        width: "90px",
                                                                        cursor: "pointer",
                                                                        border: "1px solid #55C2C3",
                                                                        backgroundColor: "#E8F7F7",
                                                                        padding: "3px 0px"
                                                                    }
                                                                    : {
                                                                        width: "90px",
                                                                        cursor: "pointer",
                                                                        padding: "3px 0px",
                                                                    }
                                                            }
                                                            className="form-control"
                                                            id="exampleInputEmail1"
                                                            aria-describedby="emailHelp"
                                                        >
                                                            Recurring
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="d-flex flex-row ">
                                                    <div className="" style={{ display: recurring ? "block" : "none" }}>
                                                        <div className="input-group  mb-3">
                                                            <div className="form-group">
                                                                <label for="exampleInputEmail1" className={S.Billing}>Billing Period</label>
                                                                <select
                                                                    className=""
                                                                    style={{
                                                                        display: "block",
                                                                        width: "177px",
                                                                        fontSize: "14px",
                                                                        cursor: "pointer",
                                                                        padding: "5px 0px 5px 0",
                                                                        borderRadius: "4px",
                                                                        outline: "none",
                                                                        border: "1px solid #C9C9C9",
                                                                    }}
                                                                    id="inputGroupSelect02"
                                                                    value={billingPeriod}
                                                                // onChange={(e) => {
                                                                //     const selectedValue = e.target.value;
                                                                //     handleBillingPeriod(selectedValue); // Explicitly call handleBillingPeriod with the selected value
                                                                // }}
                                                                >
                                                                    <option value="day">Daily</option>
                                                                    <option value="week">Weekly</option>
                                                                    <option value="month">Monthly</option>
                                                                    <option value="3_month">Every 3 Months</option>
                                                                    <option value="6_month">Every 6 Months</option>
                                                                    <option value="year">Yearly</option>
                                                                    <option value="">Custom</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="mt-4" style={{ display: recurring && customDays ? "block" : "none", margin: "0 0 0 2rem" }}>
                                                        <div className="input-group mt-2" >
                                                            <div className="input-group-append">
                                                                <button className="" style={{ height: "30px", fontSize: "13px", backgroundColor: "#fff", cursor: "pointer", borderRadius: "3px", padding: "2px 25px 0 16px", outline: "none", border: "1px solid #C9C9C9" }} type="button">Every</button>
                                                            </div>
                                                            <div className="input-group-append">
                                                                <input type="text" className="" value={day}
                                                                    readOnly
                                                                    // onChange={handleDay} 
                                                                    style={{ height: "30px", backgroundColor: "#E8F7F7", padding: "0px 25px 0 16px", outline: "none", border: "1px solid #55C2C3", textAlign: "left", width: "70px" }} required />
                                                            </div>
                                                            <select
                                                                className="custom-select"
                                                                style={{ cursor: "pointer", fontSize: "13px", height: "30px" }}
                                                                id=""
                                                                value={duration || ""} // Bind the selected value to billing_period, fallback to ""
                                                            // onChange={(e) => {
                                                            //     const selectedValue = e.target.value;
                                                            //     handleCustomBillingPeriod(selectedValue); // Explicitly call handleBillingPeriod with the selected value
                                                            // }}
                                                            >
                                                                <option value="">Select</option>
                                                                <option value="month">Months</option>
                                                                <option value="day">Day</option>
                                                                <option value="week">Week</option>
                                                            </select>
                                                        </div>
                                                        {dayErrorMessage && dayErrorMessage !== '' ? (<p className="invalid" style={{ marginLeft: "0px" }}>{dayErrorMessage}</p>) : (<></>)}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="row mb-2">
                                            <div className="col-md-3">
                                                <div className="form-group">
                                                    <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>Plan Name</label>
                                                    <input
                                                        style={{
                                                            display: "block",
                                                            width: "240px",
                                                            border: "1px solid #C9C9C9",
                                                            borderRadius: "5px",
                                                            outline: "none",
                                                            padding: "2px 0 2px 5px"
                                                        }}
                                                        type="text"
                                                        value={package_name || ''} // Use the state variable here
                                                        // onChange={handlePackageInput}
                                                        className={S.PlanName}
                                                        id="exampleInputEmail1"
                                                        aria-describedby="emailHelp"
                                                        placeholder="Enter Plan Name"
                                                        readOnly
                                                    />
                                                    {planNameErrorMessage && planNameErrorMessage !== '' ? (<p className="invalid">{planNameErrorMessage}</p>) : (<></>)}
                                                </div>
                                            </div>
                                            <div className="col-md-4 pl-4">
                                                <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>Price</label>
                                                <div className="input-group">
                                                    <div className="d-flex flex-row">
                                                        <div>
                                                            <input type="text" value={data.price}
                                                                readOnly
                                                                // onChange={handlePriceInput} 
                                                                style={{ display: "block", width: "220px", border: "1px solid #C9C9C9", borderRadius: "5px", outline: "none", padding: "2px 0 2px 5px" }} className={S.prices} id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Enter Price" />
                                                        </div>
                                                        <div className="input-group-append">
                                                            <span className="input-group-text" style={{ backgroundColor: "#E8F7F7", padding: "0", fontSize: "14px" }}>
                                                                <div className="input-group">

                                                                    <select className={S.selectoption} id="inputGroupSelect01"
                                                                    // onChange={(e) => setCurrency(e.target.value)}
                                                                    >
                                                                        {currencyList.map((item, index) => (
                                                                            <option value={item.code} className="option" selected={item.code === 'USD'}>
                                                                                {item.code} - {item.currency}
                                                                            </option>
                                                                        ))}
                                                                    </select>
                                                                </div></span>
                                                        </div>
                                                    </div>

                                                </div>
                                                {priceErrorMessage && priceErrorMessage !== '' ? (<p className="invalid">{priceErrorMessage}</p>) : (<></>)}

                                            </div>
                                            <div className="col-md-2 pl-4">
                                                <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>No of Users</label>
                                                <div className="input-group">
                                                    <input type="number" min="0"
                                                        value={noOfUsers}
                                                        onChange={handleNofUsersInput}
                                                        style={{ display: "block", width: "220px", border: "1px solid #C9C9C9", borderRadius: "5px", outline: "none", padding: "2px 0 2px 5px" }} className={S.prices} id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Enter No Of Usres" />
                                                </div>
                                                {noOfUsersErrorMessage && noOfUsersErrorMessage !== '' ? (<p className="invalid">{noOfUsersErrorMessage}</p>) : (<></>)}
                                            </div>

                                            <div className="col-md-3">
                                                <div className={oneTimePayment ? S.setopacity : ""} >
                                                    <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>Free Trial Until</label>
                                                    <div className="input-group">
                                                        <input
                                                            type="text"
                                                            value={freeTrialUntil}
                                                            // onChange={handleFreeTrialDaysChange}
                                                            className="form-control"
                                                            id="exampleInputEmail1"
                                                            aria-describedby="emailHelp"
                                                            placeholder="Enter Free Trial Period"
                                                            readOnly
                                                            style={{ display: "block", border: "1px solid #C9C9C9", borderRadius: "5px", outline: "none", padding: "2px 0 2px 5px" }}
                                                        />
                                                        <div className="input-group-append">
                                                            <span className="input-group-text" style={{ backgroundColor: "#E8F7F7", padding: "0 0 0 20px" }}>Days&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                {freeTrialErrorMessage && freeTrialErrorMessage !== '' ? (<p className="invalid">{freeTrialErrorMessage}</p>) : (<></>)}
                                            </div>
                                        </div>
                                        <div className="d-flex flex-row mt-3">
                                            <div className="mr-4">
                                                <button
                                                    type="button"
                                                    className={package_name && package_price && freeTrialUntil && !paymentId ? "btn-block" : "btn-block cust-disabled"}
                                                    style={{ border: "0", cursor: "pointer", color: "#fff", backgroundColor: "#093D54", padding: "5px 23px 5px 23px", borderRadius: "10px" }}
                                                >Generate Payment ID</button>
                                            </div>
                                            <div className="">
                                                <input className={S.admininpts} type="text" value={data.price_id} id="exampleInputEmail1" aria-describedby="emailHelp" style={{ border: "1px solid #55C2C3", color: "#55C2C3", backgroundColor: "#FFFFFF", padding: "5px 25px 5px 25px", textAlign: "center", borderRadius: "10px", width: "335px", fontSize: "14px" }} readOnly />
                                                <span type="text"  ></span>
                                            </div>
                                        </div>
                                        <div className="d-flex flex-row mt-3">
                                            <div className="" style={{ margin: "0px 3rem 0px 0px" }}>

                                                <div className="form-group">
                                                    <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>No of Downloads Credits</label>
                                                    <div className="d-flex flex-row">
                                                        <div>
                                                            <input type="radio"
                                                                onClick={checkCustomCredits}
                                                                name="rd_no_of_credits" checked={!customCredit} aria-label="Radio button for following text input" />
                                                        </div>
                                                        <div>
                                                            <p className={S.UnlimitedCredits}>Unlimited Credits</p>
                                                        </div>
                                                    </div>

                                                    <div className="d-flex flex-row">
                                                        <div>
                                                            <input type="radio"
                                                                onClick={checkCustomCredits}
                                                                name="rd_no_of_credits" checked={customCredit} aria-label="Radio button for following text input" />
                                                        </div>
                                                        <div>
                                                            <p className={S.UnlimitedCredits}>Custom Credits</p>
                                                        </div>
                                                    </div>
                                                    <input
                                                        type="text"
                                                        value={noOfCredits}
                                                        onChange={handleNoOfCreditsInput}
                                                        className={S.nofcreds}
                                                        id="exampleInputText1"
                                                        aria-describedby="textHelp"
                                                        placeholder="Enter New Credits"
                                                        style={{ padding: "3px 0 3px 6px", width: "255px", display: "block", border: "1px solid #C9C9C9", borderRadius: "4px", outline: "none" }}
                                                        readOnly={!customCredit}
                                                    />
                                                    {noOfCreditErrorMessage && noOfCreditErrorMessage !== '' ? (<p className="invalid">{noOfCreditErrorMessage}</p>) : (<></>)}
                                                </div>

                                            </div>
                                            <div className="">
                                                <div className="form-group">
                                                    <label for="exampleInputEmail1" style={{ color: "#000 !important", fontWeight: "600" }}>No of Profile Views</label>
                                                    <div className="d-flex flex-row">
                                                        <div>
                                                            <input type="radio"
                                                                onClick={checkCustomViews}
                                                                checked={!customView} name="rd_no_of_profileview" aria-label="Radio button for following text input" />
                                                        </div>
                                                        <div>
                                                            <p className={S.UnlimitedCredits}>Unlimited Views</p>
                                                        </div>
                                                    </div>

                                                    <div className="d-flex flex-row">
                                                        <div>
                                                            <input type="radio"
                                                                onClick={checkCustomViews}
                                                                checked={customView} name="rd_no_of_profileview" aria-label="Radio button for following text input" />
                                                        </div>
                                                        <div>
                                                            <p className={S.UnlimitedCredits}>Custom Views</p>
                                                        </div>
                                                    </div>
                                                    <input
                                                        type="text"
                                                        value={noOfProfileViews}
                                                        onChange={handleNoOfProfileInput}
                                                        className={S.nofcreds} id="exampleInputText1"
                                                        aria-describedby="textHelp"
                                                        placeholder="Enter No of Profile Views"
                                                        style={{ padding: "3px 0 3px 6px", width: "255px", display: "block", border: "1px solid #C9C9C9", borderRadius: "4px", outline: "none" }}
                                                        readOnly={!customView}
                                                    />
                                                    {noOfProfileErrorMessage && noOfProfileErrorMessage !== '' ? (<p className="invalid">{noOfProfileErrorMessage}</p>) : (<></>)}
                                                </div>
                                            </div>
                                        </div>


                                        <div className="d-flex flex-row mt-3">
                                            <div className="mr-4">
                                                <button type="button" style={{ border: "0", color: "#fff", backgroundColor: "#093D54", padding: "5px 20px 5px 20px", borderRadius: "10px" }} className="cust-disabled">Generate Registration Link</button>
                                            </div>
                                            <div className="">
                                                <input type="text" value={registerLink} className={S.admininpts} id="exampleInputEmail1" aria-describedby="emailHelp" style={{ border: "1px solid #55C2C3", color: "#55C2C3", backgroundColor: "#FFFFFF", padding: "7px 25px 7px 25px", borderRadius: "10px", width: "380px", fontSize: "14px" }} readOnly />

                                                {/* <button type="button"  className="btn-block" >wrjsddu656;udkmkll/djntyfhfg9;hr</button> */}
                                                <span type="text"  ></span>
                                            </div>
                                        </div>
                                        <div className={!noOfCreditErrorMessage && !noOfProfileErrorMessage && noOfCredits && noOfProfileViews ? `${S.createcenter}` : `${S.createcenter} cust-disabled`}>
                                            <button type="button" onClick={updatePlan} style={{ outline: "none" }}>Save</button>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="d-flex flex-row justify-content-center mt-5" style={{ display: isLoading }}>
                                        <img src={loadingGif} alt="Loading" className="loader" width="400" />
                                    </div>
                                )}
                            </aside>
                        </div>
                    </div>

                </div>


            </div>
        </>
    );
}

export default EditPlan;