import React, { useState, useEffect } from 'react';
import mixpanel from '../utils/mixpanel';
import Analytics from '../utils/analyticsTracking';
import { getFromStorage, setToStorage } from '../utils/customerJourneyTracking';

const CookieConsent = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [storageAvailable, setStorageAvailable] = useState(true);
  const [usingFallback, setUsingFallback] = useState(false);

  // Check if localStorage is available
  const isLocalStorageAvailable = () => {
    try {
      const testKey = '__storage_test__';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      return true;
    } catch (e) {
      return false;
    }
  };

  useEffect(() => {
    // First check if localStorage is available
    const available = isLocalStorageAvailable();
    setStorageAvailable(available);
    
    // Check if we're using fallback tracking
    if (mixpanel && typeof mixpanel.usingFallback !== 'undefined') {
      setUsingFallback(mixpanel.usingFallback);
    }
    
    if (!available) {
      console.warn('localStorage is not available. Using fallback tracking system.');
      setShowBanner(true);
      return;
    }
    
    // Check if user has already made a choice
    try {
      const hasConsent = localStorage.getItem('tracking_consent');
      if (!hasConsent) {
        setShowBanner(true);
      } else if (hasConsent === 'accepted') {
        // If user previously accepted, check if tracking is still available
        if (!mixpanel.trackingAvailable) {
          // If tracking is no longer available but user had consented, show notification
          setShowBanner(true);
        }
      }
    } catch (e) {
      console.error('Error accessing storage:', e);
      setShowBanner(true);
    }
  }, []);

  const acceptTracking = () => {
    try {
      // Try to store in localStorage if available
      if (storageAvailable) {
        localStorage.setItem('tracking_consent', 'accepted');
      }
      
      // Always set consent in the tracking system
      // This will work with both regular and fallback tracking
      try {
        // mixpanel.opt_in_tracking();
        // Track the consent event itself
        Analytics.trackFeatureUsage('Tracking Consent', { 
          status: 'accepted', 
          usingFallback: usingFallback 
        });
      } catch (e) {
        console.warn('Error tracking consent event:', e);
      }
    } catch (e) {
      console.warn('Could not store tracking consent:', e);
    }
    setShowBanner(false);
  };

  const declineTracking = () => {
    try {
      // Try to store in localStorage if available
      if (storageAvailable) {
        localStorage.setItem('tracking_consent', 'declined');
      }
      
      // Always opt out in the tracking system
      // This will work with both regular and fallback tracking
      try {
        mixpanel.opt_out_tracking();
      } catch (e) {
        console.warn('Error opting out of tracking:', e);
      }
    } catch (e) {
      console.warn('Could not store tracking preference:', e);
    }
    setShowBanner(false);
  };

  if (!showBanner) return null;
  
  // Show an informational banner if using fallback tracking
  if (usingFallback) {
    return (
      <div className="cookie-consent" style={{
        position: 'fixed',
        bottom: '0',
        left: '0',
        right: '0',
        background: '#fff',
        padding: '15px',
        boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)',
        zIndex: '9999',
        textAlign: 'center'
      }}>
        <h3 style={{ margin: '0 0 10px 0', fontSize: '18px' }}>Privacy Notice</h3>
        <p style={{ margin: '0 0 10px 0' }}>
          Your browser's privacy settings are preventing us from storing persistent cookies.
          We're using a privacy-friendly session-only tracking system instead.
          Your preferences will only be stored for this session.
        </p>
        <div style={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>
          <button
            onClick={declineTracking}
            style={{
              padding: '8px 15px',
              border: '1px solid #ccc',
              background: 'transparent',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Decline Tracking
          </button>
          <button
            onClick={acceptTracking}
            style={{
              padding: '8px 15px',
              border: 'none',
              background: '#55C2C3',
              color: 'white',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Accept Tracking
          </button>
        </div>
      </div>
    );
  }
  
  // Show a simplified banner if storage is not available but we're not using fallback
  if (!storageAvailable) {
    return (
      <div className="cookie-consent" style={{
        position: 'fixed',
        bottom: '0',
        left: '0',
        right: '0',
        background: '#fff',
        padding: '15px',
        boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)',
        zIndex: '9999',
        textAlign: 'center'
      }}>
        <h3 style={{ margin: '0 0 10px 0', fontSize: '18px' }}>Privacy Notice</h3>
        <p style={{ margin: '0 0 10px 0' }}>
          Your browser's privacy settings are preventing us from storing cookies and tracking data.
          While we respect your privacy choice, some features of our service may not work properly.
        </p>
        <button
          onClick={() => setShowBanner(false)}
          style={{
            padding: '8px 15px',
            border: 'none',
            background: '#55C2C3',
            color: 'white',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Acknowledge
        </button>
      </div>
    );
  }

  return (
    <div className="cookie-consent" style={{
      position: 'fixed',
      bottom: '0',
      left: '0',
      right: '0',
      background: '#fff',
      padding: '15px',
      boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)',
      zIndex: '9999'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <h3 style={{ margin: '0 0 10px 0', fontSize: '18px' }}>Cookie Consent</h3>
        <p style={{ margin: '0 0 15px 0' }}>
          We use cookies and similar technologies to help personalize content, enhance your experience, and analyze our traffic.
          We use this information to improve our services and your experience.
          You can learn more about how we use your data in our Privacy Policy.
        </p>
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
          <button
            onClick={declineTracking}
            style={{
              padding: '8px 15px',
              border: '1px solid #ccc',
              background: 'transparent',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Decline All
          </button>
          <button
            onClick={acceptTracking}
            style={{
              padding: '8px 15px',
              border: 'none',
              background: '#55C2C3',
              color: 'white',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Accept All
          </button>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent; 