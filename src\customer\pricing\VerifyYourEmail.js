import React from "react";
import "../../customer/assests/css/VerifyYourEmail.css";
import { Link, useLocation } from "react-router-dom";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import { axiosPost } from "../../customer/common-files/ApiCalls.js";
import Footer from "../layouts/Footer";
import DWINTracker from "../common-files/DWINTracker.js";

const VerifyYourEmail = () => {
  const data = {
    email: localStorage.getItem('cust-email'),
  };
  const resendHandler = (event) => {
    event.preventDefault();
    const user = {
      "email": localStorage.getItem('cust-email'),
      "username": localStorage.getItem('cust-username')
    }
    let result = axiosPost(ApiName.resendVerifyEmail, user)
      .then(function (response) {
        // console.log('response',response);
        window.location.reload();
      })
      .catch(function (errors) {
        // console.log(errors);
      });
  };
  return (
    <>
      <DWINTracker />
      <div className="body">
        <div className="container">
          <div className="row">
            <div className="offset-md-1 mt-5 col-md-6">
              <div className="payment-page-logo-1">
                <img src="../images/r-logo.png" width="70" className="img-fluid" />
              </div>
            </div>
          </div>

          <div className="card">
            <img
              className="img-top"
              src="../images/group-51355.png"
              alt="Card image cap"
            />
            <h3>Verify Your Email</h3>
            <p className="semi-header">We sent a verification code to</p>
            <p className="email-link">
              <a href="#">{data.email}</a>
            </p>

            <p className="email-issue">
              Didn’t receive a code yet?{" "}
              <span onClick={resendHandler}><a href="#">
                Resend
              </a></span>
            </p>

            <hr className="width" />

            <p className="activate-account">
              Still haven’t received an email? Please check your spam folder.
            </p>
          </div>
          <br />
          <div className="receive-link">
            <p>
              Get in touch with us at <a href="mailto:<EMAIL>"><EMAIL></a> if
              you <br /> don’t receive a link within a few minutes.
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default VerifyYourEmail;
