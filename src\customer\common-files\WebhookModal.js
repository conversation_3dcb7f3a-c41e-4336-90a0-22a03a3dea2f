import React, { useEffect, useRef, useState } from "react";
import UseTabStore from "./useGlobalState";
import { useNavigate } from "react-router-dom";
import { PostWithTokenNoCache } from "./ApiCalls";
import { ApiName } from "./ApiNames";
import Alert from "./alert";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";

const WebhookModal = (props) => {
    const closeModal = props.closeModal;
    const setIsWebhookModalOpen = props.setIsWebhookModalOpen;
    const webhookKeyRef = useRef(null);

    const [webhookKey, setWebhookKey] = useState("");
    const [copiedWebhookKeyCode, setCopiedWebhookKeyCode] = useState(false);
    const [webhookUrl, setWebhookUrl] = useState("");
    const [isActive, setIsActive] = useState(true);
    const [isCreatedWebhookKey, setIsCreatedWebhookKey] = useState(false);
    const [isUpdatedWebhookKey, setIsUpdatedWebhookKey] = useState(false);
    const [isRegeneratedWebhookKey, setIsRegeneratedWebhookKey] = useState(false);
    const [isURLValid, setIsURLValid] = useState(null);
    const {
        defaultAlert,
        defaultErrorMsg,
        setButtonType,
        setDefaultAlert,
        setDefaultErrorMsg,
    } = UseTabStore();
    const [isWebhookURLEditable, setIsWebhookURLEditable] = useState(false);
    const [isViewWebhookKey, setIsViewWebhookKey] = useState(false);
    const [isActiveChanged, setIsActiveChanged] = useState(null);
    useEffect(() => {
        getUserWebhookSecreteApiKey();
    }, []);

    useEffect(() => {
        // Add event listeners when the component mounts
        document.addEventListener("mousedown", handleOutsideClick);
        document.addEventListener("keydown", handleEscKeyPress);

        // Remove event listeners when the component unmounts
        return () => {
            document.removeEventListener("mousedown", handleOutsideClick);
            document.removeEventListener("keydown", handleEscKeyPress);
        };
    }, []);

    const closeAlertRef = useRef();

    const close = () => {
        if (defaultAlert && defaultErrorMsg) closeAlertRef.current.click();
    };

    const handleOutsideClick = (e) => {
        // Check if the click is outside the modal
        if (defaultAlert && !e.target.closest(".modal")) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        // Check if the pressed key is ESC
        if (e.key === "Escape") {
            close();
        }
    };

    const getUserWebhookSecreteApiKey = async () => {
        // You can await here
        const url = ApiName.getWebhookKey;
        try {
            const res = await PostWithTokenNoCache(url);
            if (res && "status" in res) {
                if (res.data.status == 200 && "data" in res.data) {
                    let jsonObj = JSON.parse(res.data.data);
                    if (jsonObj) {
                        setWebhookKey(jsonObj.webhookKey);
                        setWebhookUrl(jsonObj.webhookUrl);
                        jsonObj.webhookUrl ? setIsURLValid("ACTIVE") : setIsURLValid("INACTIVE");
                        setIsActive(jsonObj.isActive === "ACTIVE" ? true : false);
                    }
                }
            }
        } catch (errors) { }
    };

    const generateWebhookSecreteApiKey = async () => {
        const params = {
            webhookKey: "create",
            webhookUrl,
        };
        try {
            // You can await here
            const res = await PostWithTokenNoCache(ApiName.createWebhookKey, params);
            if (res && "status" in res) {
                if (res.data.status == 200 && "data" in res.data) {
                    let jsonObj = JSON.parse(res.data.data);
                    if (jsonObj) {
                        setIsCreatedWebhookKey(true);
                        setTimeout(() => {
                            setIsCreatedWebhookKey(false);
                        }, 5000);
                        setWebhookKey(jsonObj.webhookKey);
                        setWebhookUrl(jsonObj.webhookUrl);
                    }
                } else {
                    setIsWebhookModalOpen(false);
                    setButtonType("error");
                    setDefaultErrorMsg(res.data.message);
                    setDefaultAlert(true);
                }
            }
        } catch (errors) {
            if (errors.response && errors.response.data.status === 400) {
                setIsWebhookModalOpen(false);
                setButtonType("error");
                setDefaultErrorMsg(errors.response.data.message);
                setDefaultAlert(true);
            }
        }
    };

    const updateWebhook = async (params) => {
        try {
            // You can await here
            const res = await PostWithTokenNoCache(ApiName.updateWebhookKey, params);
            if (res && "status" in res) {
                if (res.data.status == 200 && "data" in res.data) {
                    let jsonObj = JSON.parse(res.data.data);
                    if (jsonObj) {
                        setIsWebhookURLEditable(false);
                        setIsUpdatedWebhookKey(true);
                        setTimeout(() => {
                            setIsUpdatedWebhookKey(false);
                            setIsRegeneratedWebhookKey(false);
                        }, 5000);
                        setWebhookKey(jsonObj.webhookKey);
                        setWebhookUrl(jsonObj.webhookUrl);
                    }
                } else {
                    setIsWebhookModalOpen(false);
                    setButtonType("error");
                    setDefaultErrorMsg(res.data.message);
                    setDefaultAlert(true);
                }
            }
        } catch (errors) {
            if (errors.response && errors.response.data.status === 400) {
                setIsWebhookModalOpen(false);
                setButtonType("error");
                setDefaultErrorMsg(errors.response.data.message);
                setDefaultAlert(true);
            }
        }
    };

    const reGenerateWebhookSecreteApiKey = async () => {
        const params = {
            webhookKey: "update",
            webhookUrl,
            isActive: isActive ? "ACTIVE" : "INACTIVE",
        };
        setIsRegeneratedWebhookKey(true);
        updateWebhook(params);
    };

    const updateWebhookURL = async () => {
        const params = {
            webhookUrl,
            isActive: isActive ? "ACTIVE" : "INACTIVE",
        };
        updateWebhook(params);
    };

    const onChageHandler = (event) => {
        let value = event.target.value;
        const urlRegex = /^(https?:\/\/)?([a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+)(:[0-9]{1,5})?(\/.*)?$/;
        if (urlRegex.test(value)) {
            setWebhookUrl(value);
            setIsURLValid(true);
            isActive ? setIsActiveChanged("ACTIVE") : setIsActiveChanged("INACTIVE")
        } else {
            setWebhookUrl(value);
            setIsURLValid(false);
        }
    };

    const onChageStatusHandler = () => {
        setIsActive(!isActive);
        isActive ? setIsActiveChanged("ACTIVE") : setIsActiveChanged("INACTIVE")
    };

    const copywebhookKey = () => {
        const textToCopy = webhookKeyRef.current.textContent;
        // Create a temporary textarea element to copy the text to the clipboard
        const tempTextArea = document.createElement("textarea");
        tempTextArea.value = textToCopy;
        document.body.appendChild(tempTextArea);
        tempTextArea.select();
        document.execCommand("copy");
        document.body.removeChild(tempTextArea);
        setCopiedWebhookKeyCode(true);
        setTimeout(() => {
            setCopiedWebhookKeyCode(false);
        }, 5000);
    };

    const editWebhookURL = () => {
        setIsWebhookURLEditable(true);
    };

    const viewWebHookKey = () => {
        setIsViewWebhookKey(!isViewWebhookKey);
    };

    return (
        <>
            <div style={overlayStyle}>
                <div style={modalStyle}>
                    <div className="d-flex flex-row">
                        <div>
                            <p className="header-popup">Webhook</p>
                            <hr className="header-bottom-line" />
                        </div>
                        <div>
                            {" "}
                            <span onClick={closeModal} class="closebuttonimg" aria-hidden="true">
                                &times;
                            </span>
                        </div>
                    </div>

                    <div className="grey-border">
                        <div className="d-flex flex-row justify-content-between">
                            <div>
                                <p className="webbooksecretkey">Webhook Secret Key</p>
                            </div>
                            <div>
                                {!webhookKey ? (
                                    <button type="button" className="Generate-key" onClick={generateWebhookSecreteApiKey}>
                                        Generate
                                    </button>
                                ) : (
                                    <button type="button" className="Generate-key" onClick={reGenerateWebhookSecreteApiKey}>
                                        Regenerate
                                    </button>
                                )}
                            </div>
                        </div>
                        {copiedWebhookKeyCode ? (
                            <p className="successmessage4">
                                <img src="./images/promocode-success.png" />
                                Copied Successfully
                            </p>
                        ) : null}

                        {isUpdatedWebhookKey && isRegeneratedWebhookKey ? (
                            <p className="successmessage5">
                                <img src="./images/promocode-success.png" />
                                Successfully Updated
                            </p>
                        ) : null}

                        {isUpdatedWebhookKey && !isRegeneratedWebhookKey ? (
                            <p className="successmessage5">
                                <img src="./images/promocode-success.png" />
                                Successfully Submited
                            </p>
                        ) : null}

                        {isCreatedWebhookKey ? (
                            <p className="successmessage4">
                                <img src="./images/promocode-success.png" />
                                Created Successfully
                            </p>
                        ) : null}

                        <div className="white-box-api">
                            <div className="d-flex flex-row justify-content-between">
                                <div className="hidden-api">
                                    <span className="hidden-in-code" style={{ fontSize: "13px", position: "absolute", margin: "3px 0 0 0" }}>
                                        {isViewWebhookKey ? webhookKey : "*".repeat(webhookKey.length)}
                                    </span>
                                </div>
                                <div>
                                    <span className="hid-show-eye-icon">
                                        <span className="close-eye" onClick={viewWebHookKey} style={{ width: "16px" }}>
                                            <FontAwesomeIcon
                                                icon={isViewWebhookKey ? faEye : faEyeSlash}
                                                style={{ pointerEvents: "none", color: "#55C2C3" }}
                                            />
                                        </span>
                                    </span>
                                    <span style={{ display: "none" }} ref={webhookKeyRef}>
                                        {webhookKey}
                                    </span>
                                    <span
                                        className="popup-copy-api"
                                        onClick={webhookKey ? copywebhookKey : null}
                                        style={{ cursor: webhookKey ? "pointer" : "not-allowed", opacity: webhookKey ? 1 : 0.5 }}
                                    >
                                        <i className="fa fa-copy"></i>
                                        &nbsp;&nbsp;&nbsp;&nbsp; Copy
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="utl-entitys">
                        <span className="popup-url">URL</span>
                        <span className="popup-placeholder disable">
                            <div class="x-scroll">
                                <input
                                    type="text"
                                    className="input-edited"
                                    placeholder="Enter your API Endpoint URL"
                                    value={webhookUrl}
                                    name="webhook_url"
                                    onChange={onChageHandler}
                                    disabled={!isWebhookURLEditable}
                                />

                                <img src="images/Edit-pen.png" className="edit-image" width="28" onClick={editWebhookURL} />
                            </div>
                        </span>
                    </div>

                    <div className="status-entitys">
                        <span className="status-url">Status</span>
                        <span className={isActive ? "active-api-radio" : "inactive-api-radio"}>
                            <input
                                type="radio"
                                name="frequency"
                                checked={isActive}
                                onChange={() => onChageStatusHandler()} // This sets the active state
                                className="radio-button-color"
                            />
                            &nbsp;&nbsp; Active
                        </span>

                        <span className={!isActive ? "active-api-radio" : "inactive-api-radio"}>
                            <input
                                type="radio"
                                name="frequency"
                                checked={!isActive}
                                onChange={() => onChageStatusHandler()} // This sets the inactive state
                                className="radio-button-color"
                            />
                            &nbsp;&nbsp; Inactive
                        </span>

                        <div class="ui form"></div>
                    </div>
                    <div>
                        {isURLValid === false ? (
                            <span style={{ color: "red", fontSize: "14px" }}>Invalid URL</span>
                        ) : (
                            ""
                        )}
                        <button
                            type="button"
                            className="api-popup-submit"
                            style={{
                                opacity: !webhookUrl || !isURLValid || !isActiveChanged ? 0.1 : 1,
                                cursor: !webhookUrl || !isURLValid || !isActiveChanged ? "not-allowed" : "pointer",
                            }}
                            disabled={(!webhookUrl || !isURLValid) || !isActiveChanged}
                            onClick={updateWebhookURL}
                        >
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
};

// Inline Styles for Modal
const overlayStyle = {
    position: "fixed",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
};

const modalStyle = {
    backgroundColor: "#F5F5F5",
    padding: "10px 14px 5rem 14px",
    borderRadius: "5px",
    width: "380px",
    textAlign: "center",
    position: "absolute",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    top: "20%",
};

export default WebhookModal;