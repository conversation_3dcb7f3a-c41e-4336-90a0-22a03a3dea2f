import React from "react";

const DeletePopup = ({ onCloseDelete, onDeleteSuccess }) => {
    return (
        <div
            class="modal"
            style={{
                display: "block",
                important: "true",
                backgroundColor: "#0000007d",
            }}
            id="exampleModalCenter"
            tabindex="-1"
            role="dialog"
            aria-labelledby="exampleModalCenterTitle"
        >
            <div class="modal-dialog modal-dialog-centered" style={{width:"400px"}} role="document">
                <div class="modal-content">
                    <div
                        class="modal-header"
                        style={{ borderBottom: "0", padding: "6px 11px 0 0px" }}
                    >
                        <h5 class="modal-title" id="exampleModalLongTitle"></h5>
                        <button
                            type="button"
                            class="close"
                            data-dismiss="modal"
                            onClick={onCloseDelete}
                            aria-label="Close"
                            style={{fontSize:"24px", fontWeight:"600"}}
                        >
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" style={{ borderBottom: "0", padding: "0" }}>
                        <p className="areyousure">
                            Are you sure you want to <br />delete  your List?
                        </p>

                        <div className="d-flex flex-row justify-content-center">
                            <div className="mr-5">
                                <button
                                    type="submit"
                                    onClick={onDeleteSuccess}
                                    className="yesno"
                                >
                                    Yes
                                </button>
                            </div>
                            <div>
                                <button type="submit" onClick={onCloseDelete} className="no">
                                    No
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DeletePopup;