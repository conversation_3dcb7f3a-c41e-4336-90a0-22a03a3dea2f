import React from "react";
import '../assests/css/layouts/search_us.css';
import ContactUsPopup from '../filters/ContactUsPopup';
import UseTabStore from "../common-files/useGlobalState";
import Feedback from "../pagination/Feedback";


const Search_Us = () => {
    const {
        selectedTab
    } = UseTabStore();
    return (
        <div >
            <div className="search-result">
                <p>
                    Search Result Found{" "}
                    <span className="number-of"> 0 {selectedTab ? selectedTab : "Contacts"}</span>
                </p>
            </div>

            <div className="search-us-center">

                <div className="fisrt d-flex align-items-center justify-content-center">
                    <img src="./images/binaculer.png"  className="img-fluid" />
                </div>

                <div className="didnt-find">
                    <h2>Didn't find what you were looking for?</h2>
                </div>

                <div className="Bi-team">
                    <p>Our BI team has your back. To submit your custom requirements:    </p>
                </div>

                <div className="contact-button">
                    <button href="#" data-toggle="modal" data-target="#contactUsModalCenter" type="submit">Contact Support</button>
                </div>

                <hr className="hrzont" />

                <div className="reach">
                    <p>Or reach us at:</p>
                </div>


                <div className="footer-items d-flex align-items-center justify-content-center">
                    <div className="d-flex flex-row">
                        <div className="connection">
                            <span>
                                <img src="./images/email.png" />
                                <a href="mailto:<EMAIL>"><EMAIL></a>

                            </span>
                        </div>
                        <div className="connection">
                            <span>
                                <img src="./images/call.png" />
                                <a href="tel:+****************">+****************</a>
                            </span>
                        </div>
                    </div>
                </div>

            </div>




            <div className="extra-border">
                <div className="row">
                    <div className="col-6">
                        <div className="contacts-selected">
                            <p >0 Contact Selected</p>
                        </div>
                    </div>
                </div>
            </div>
            <ContactUsPopup />

            {/* <Feedback /> */}
        </div>
    )
}
export default Search_Us;