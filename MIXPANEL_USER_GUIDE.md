# Mixpanel User Guide for ReachStream

This guide walks you through using the Mixpanel dashboard for tracking and analyzing user behavior in the ReachStream application.

## Table of Contents
1. [Accessing Mixpanel](#accessing-mixpanel)
2. [Dashboard Overview](#dashboard-overview)
3. [Viewing Events](#viewing-events)
4. [Analyzing User Data](#analyzing-user-data)
5. [Creating Funnels](#creating-funnels)
6. [Setting Up Alerts](#setting-up-alerts)
7. [Customer Journey Tracking](#customer-journey-tracking)
8. [Troubleshooting](#troubleshooting)

---

## Accessing Mixpanel

### Initial Access

1. Go to [https://mixpanel.com/project/3766400/view/4263532/app/events](https://mixpanel.com/project/3766400/view/4263532/app/events)
2. If you don't have access, you'll see a request form:
   - Enter your full name
   - Enter your work email
   - Click "Request Access"
3. Wait for the project admin to approve your access
4. Once approved, you'll receive an email with login instructions

### Logging In

1. Go to [https://mixpanel.com/login](https://mixpanel.com/login)
2. Enter your email address and password
3. Click "Login"
4. Select "ReachStream" project from your dashboard

> **Note**: Mixpanel cannot grant access to projects directly. Access must be provided by the project administrator.

---

## Dashboard Overview

### Navigation Structure

The Mixpanel dashboard for ReachStream is organized into several key sections:

1. **Events** - View all tracked events
2. **Users** - Analyze individual user data
3. **Funnels** - Track user conversion paths
4. **Cohorts** - Group similar users together
5. **Insights** - Get detailed analytics and patterns

### Events Panel

The Events panel includes:

- Event selector
- Time range filter (Today, Yesterday, 7D, 30D, 3M, 6M, 12M, Custom)
- Graph visualization of event frequency
- Detailed event data table

### Common Controls

- **Date Range Selector**: Set the timeframe for your analysis
- **Filter**: Narrow down events by properties
- **Breakdown**: Split data by specific properties
- **Export**: Download data in CSV or other formats

---

## Viewing Events

### Finding Events

1. Click on "Events" in the left navigation
2. You'll see a list of all tracked events in the application
3. Events implemented in ReachStream include:
   - Sign In Attempt
   - User Sign In (success)
   - App Initialized
   - Page Viewed
   - Google Login Attempt
   - Microsoft Login Attempt
   - LinkedIn Login Attempt
   - Feature Used: Dashboard View
   - Feature Used: App Tour Started
   - Feature Used: Usage Data Fetched
   - Incomplete Signup
   - Credit Usage
   - Filter Usage
   - Email Revealed
   - Engagement Milestone
   - Limited Filter Usage
   - No Search Results
   - Referral Activity

### Analyzing Specific Events

1. Click on any event name in the list to see detailed analytics
2. Use filters to refine your view:
   - **City**: See events from specific locations
   - **Country**: Filter by country
   - **Operating System**: Filter by device OS (Android, iOS, Windows, etc.)
   - **Distinct ID**: Look up a specific user's activity

### Event Properties

Each event in ReachStream includes specific properties:

1. **Sign In Attempt**:
   - `email`: User's email address

2. **User Sign In**:
   - `method`: Authentication method (email, Google, Microsoft, LinkedIn)
   - `email`: User's email

3. **Page Viewed**:
   - `page`: Path of the viewed page

4. **Feature Used**:
   - `items_count`: Number of items in data fetch
   - `user_email`: Email of the user using the feature
   - `tour_id`: ID of the app tour (for tour events)
   
5. **Incomplete Signup**:
   - `time_spent`: Time spent on signup form before abandoning
   - `form_fields_completed`: Array of fields completed before abandonment
   - `last_field`: Last field interacted with
   - `form_completion_percentage`: Percentage of form completed

6. **Credit Usage**:
   - `credits_used`: Number of credits consumed
   - `total_credits`: Total available credits
   - `percentage_used`: Percentage of credits used

7. **Filter Usage**:
   - `filter_count`: Number of filters applied
   - `filter_types`: Types of filters used
   - `search_results`: Number of results returned

---

## Analyzing User Data

### User Profiles

1. Click "Users" in the left navigation
2. Search for specific users by email or ID
3. View complete user profiles with:
   - Basic information (email, name)
   - Last login date
   - User role
   - Activity history
   - Plan type
   - Credit usage
   - Lifecycle stage

### User Journeys

Track how users navigate through your application:

1. Select a user from the Users section
2. Click on "User Activity"
3. View a timeline of the user's interactions with your app
4. Identify patterns and potential drop-off points

### Lifecycle Stages

Users in ReachStream are categorized into lifecycle stages:

1. **Incomplete Registration**: Users who started but didn't complete signup
2. **Onboarding**: New users who have logged in but haven't used key features
3. **Active**: Users who have downloaded contacts or used search features
4. **Engaged**: Users who have downloaded 10+ contacts
5. **Credit Limit Reached**: Users who have used 90%+ of their credits
6. **At Risk**: Users who haven't logged in for 15+ days

---

## Creating Funnels

Funnels help you track user conversion paths (e.g., from sign-up to first feature use):

### Building a Funnel

1. Go to "Funnels" in the left navigation
2. Click "Create New Funnel"
3. Add steps to your funnel:
   - Step 1: "App Initialized" (first visit)
   - Step 2: "Sign In Attempt"
   - Step 3: "User Sign In" (successful login)
   - Step 4: "Feature Used: Dashboard View" (engagement)
4. Set a time window (how long users have to complete all steps)
5. Click "Create Funnel"

### Analyzing Conversion

1. View drop-offs between each step
2. See conversion rates per step and overall
3. Segment analysis by user properties
4. Identify bottlenecks in your user flow

### Pre-Built Funnels

We've created several pre-built funnels to analyze the customer journey:

1. **Signup to Activation**: Tracks users from signup to first search
2. **Search to Download**: Tracks users from search to contact download
3. **Credit Usage Path**: Tracks the progression of credit consumption
4. **Filter Usage Funnel**: Shows how users refine their searches

---

## Setting Up Alerts

Stay informed about important changes in user behavior:

### Creating Alerts

1. Go to "Alerts" in the left navigation
2. Click "Create New Alert"
3. Select metrics to monitor:
   - Event frequency (e.g., alert if sign-ins drop by 30%)
   - Funnel conversion rates (e.g., alert if checkout completion falls below 10%)
4. Set threshold values and frequency
5. Choose notification method (email, Slack)
6. Save your alert

### Managing Alerts

1. View all alerts in the Alerts dashboard
2. Edit or pause alerts as needed
3. Review alert history and triggered notifications

### Pre-Configured Alerts

Several alerts have been preconfigured for the customer journey:

1. **Incomplete Signup Alert**: Triggered when users abandon signup after spending >2 minutes
2. **Credit Limit Alert**: Triggered when users reach 90% credit usage
3. **Inactivity Alert**: Triggered when users haven't signed in for 3+ days
4. **Search Without Results**: Triggered when searches return zero results

---

## Customer Journey Tracking

### Reach Phase

Track initial user acquisition and engagement:

1. **Signup Tracking**:
   - View the "User Sign Up" event with method and plan details
   - Segment by referral source or UTM parameters

2. **Incomplete Signup**:
   - View the "Incomplete Signup" event
   - Analyze form completion percentage and time spent
   - Identify where users abandon the process

### Activation Moments

Track key activation events:

1. **First Login Tracking**:
   - View the "First Dashboard Access" event
   - Track time between signup and first login

2. **Search Activity**:
   - View the "Search Performed" event
   - Analyze filter usage patterns with the "Filter Usage" event

3. **Email Reveal and List Actions**:
   - Track when users reveal emails with the "Email Revealed" event
   - Monitor list creation and downloads with "List Created" and "Contacts Downloaded" events

### Engagement Metrics

Monitor ongoing engagement:

1. **Filter Usage Quality**:
   - Identify users with "Limited Filter Usage" events
   - See which users could benefit from search training

2. **Download Milestones**:
   - Track "Engagement Milestone" events for users who download 10+ contacts
   - Monitor patterns in downloaded contact types

3. **Credit Usage**:
   - View "Credit Usage" events to track consumption patterns
   - Identify users approaching their limits with percentage_used property

### Retention Tracking

Track user retention patterns:

1. **Inactivity Monitoring**:
   - View "User Inactive" events
   - Track patterns of user return after inactivity

2. **Pricing Page Engagement**:
   - Monitor "Pricing Page Viewed" events, especially for free users
   - Track users with multiple pricing page views as upgrade opportunities

---

## Troubleshooting

### Common Issues

1. **Not seeing events**:
   - Check if user has accepted tracking consent
   - Verify that Mixpanel initialization is working
   - Ensure events are being properly triggered in code
   - Check browser console for any Mixpanel errors

2. **Missing user properties**:
   - Ensure user identification is happening after login
   - Verify that `Mixpanel.identify()` is called before setting properties

3. **Access issues**:
   - Request access through the proper channels
   - Contact the project administrator if access is pending
   - Check for any organization-level restrictions

### Debugging Tools

1. **Mixpanel Live View**:
   - See events as they happen in real-time
   - Useful for verifying tracking implementation

2. **Browser debug mode**:
   - Add `debug: true` to Mixpanel initialization
   - Check browser console for verbose Mixpanel logs

3. **Event Inspector**:
   - View raw event data for detailed troubleshooting
   - Helpful for verifying property values

---

## Best Practices

1. **Regular Monitoring**:
   - Check key metrics at least weekly
   - Set up alerts for critical changes
   - Look for unusual patterns that might indicate issues

2. **Data Privacy**:
   - Avoid tracking sensitive personal information
   - Respect user consent choices
   - Follow GDPR and other privacy regulations

3. **Action on Insights**:
   - Use data to inform product decisions
   - Identify and fix conversion bottlenecks
   - Test hypotheses with A/B testing

---

## Support Resources

- Mixpanel Documentation: [https://developer.mixpanel.com/docs](https://developer.mixpanel.com/docs)
- Internal ReachStream Analytics Guide: See `MIXPANEL_INTEGRATION.md`
- For specific questions about the ReachStream implementation, consult the application's technical lead

---

*Note: This guide is specific to the ReachStream implementation of Mixpanel analytics. Some features may vary depending on your Mixpanel plan and project setup.* 