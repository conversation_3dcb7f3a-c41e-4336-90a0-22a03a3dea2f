import React, { useEffect, useState } from 'react';
import AsyncSelect from 'react-select/async';
import { ApiName } from '../common-files/ApiNames.js';
import { PostWithTokenNoCache, postWithToken } from '../common-files/ApiCalls.js';
import UseTabStore from '../common-files/useGlobalState.js';

const CompanyName = () => {
	// State to store selected values
	const {
		setSelectedCompany,
		companyName,
		companyURL,
		setSelectedCompanyURL,
		setCurrentPage
	} = UseTabStore();
	const [selectedValues, setSelectedValues] = useState([]);
	const [selectedCompanyURLValues, setSelectedCompanyURLValues] = useState([]);
	const [isCompanyNameAutoFocus, setIsCompanyNameAutoFocus] = useState(false); // Set one of them to autofocus initially
	const [isCompanyURLAutoFocus, setIsCompanyURLAutoFocus] = useState(false); // Set one of them to autofocus initially

	useEffect(() => {
		if (Object.keys(companyName).length < 1) {
			setSelectedValues([]);
		} else {
			const selectedValues = Object.keys(companyName).map(key => {
				return { label: companyName[key], value: companyName[key] };
			});
			setSelectedValues(selectedValues);
		}
	}, [companyName])

	useEffect(() => {
		if (Object.keys(companyURL).length < 1) {
			setSelectedCompanyURLValues([]);
		} else {
			const selectedValues = Object.keys(companyURL).map(key => {
				return { label: companyURL[key], value: companyURL[key] };
			});
			setSelectedCompanyURLValues(selectedValues);
		}
	}, [companyURL])

	const loadOptions = (inputValue, callback) => {
		if (inputValue && inputValue.length >= 2) {
			const dataPost = {
				searchPattern: inputValue // Pass the inputValue to your API request
			};
			// Make an API call here to fetch suggestions based on the inputValue
			PostWithTokenNoCache(ApiName.companyCompanyName, dataPost)
				.then((res) => {
					if (res.data.status === 200) {
						const company = JSON.parse(res.data.data);
						// Convert the array to a Set to remove duplicates and then convert it back to an array
						const uniqueCompany = [...new Set(company)];

						const options = uniqueCompany.map((option) => ({
							label: option,
							value: option
						}));
						callback(options);
					}
				})
				.catch((error) => {
					console.error('Error fetching data:', error);
					callback([]); // Clear options on error
				});
		}
	};

	const loadCompanyURLOptions = (inputValue, callback) => {
		if (inputValue && inputValue.length >= 2) {
			const dataPost = {
				searchPattern: inputValue // Pass the inputValue to your API request
			};
			// Make an API call here to fetch suggestions based on the inputValue
			PostWithTokenNoCache(ApiName.companyCompanyURL, dataPost)
				.then((res) => {
					if (res.data.status === 200) {
						const website = JSON.parse(res.data.data);
						// Convert the array to a Set to remove duplicates and then convert it back to an array
						const uniqueCompanyURL = [...new Set(website)];

						const options = uniqueCompanyURL.map((option) => ({
							label: option,
							value: option
						}));
						callback(options);
					}
				})
				.catch((error) => {
					console.error('Error fetching data:', error);
					callback([]); // Clear options on error
				});
		}
	};


	const SearchCompanyName = ({ autoFocus, onFocus, onBlur }) => {

		// Handle value change when an option is selected or deselected
		const handleValueChange = (selectedOptions) => {
			setCurrentPage(1);
			setSelectedValues(selectedOptions);
			let updatedCompanyNames = {};

			// Update the company names based on selected options
			selectedOptions.forEach((item, index) => {
				updatedCompanyNames[index] = item.value;
			});
			setSelectedCompany(updatedCompanyNames);
		};

		return (
			<div className="mb-2 mt-3 d-flex flex-column">
				<div className="">
					<AsyncSelect
						cacheOptions
						defaultOptions
						loadOptions={loadOptions}
						isMulti
						placeholder="Search by Company Name"
						onChange={handleValueChange} // Handle selected value changes
						value={selectedValues} // Pass selected values
						styles={SearchJobtitleStyles}
						autoFocus={autoFocus}
						onFocus={onFocus}
						onBlur={onBlur}
						isClearable
					/>
				</div>
				<div className="search-icon">
					<i className="fas fa-search"></i>
				</div>

			</div>
		)
	}

	const SearchCompanyURL = ({ autoFocus, onFocus, onBlur }) => {

		// Handle value change when an option is selected or deselected
		const handleCompanyURLValueChange = (selectedOptions) => {
			setCurrentPage(1);
			setSelectedCompanyURLValues(selectedOptions);
			let updatedCompanyURL = {};

			// Update the company names based on selected options
			selectedOptions.forEach((item, index) => {
				updatedCompanyURL[index] = item.value;
			});
			setSelectedCompanyURL(updatedCompanyURL);
		};

		return (
			<div className="mb-2 mt-3 d-flex flex-column">
				<div className="">

					<AsyncSelect
						cacheOptions
						defaultOptions
						loadOptions={loadCompanyURLOptions}
						isMulti
						placeholder="Search by Company URL"
						onChange={handleCompanyURLValueChange} // Handle selected value changes
						value={selectedCompanyURLValues} // Pass selected values
						styles={SearchJobtitleStyles}
						autoFocus={autoFocus}
						onFocus={onFocus}
						onBlur={onBlur}
						isClearable
					/>

				</div>
				<div className="search-icon">
					<i className="fas fa-search"></i>
				</div>

			</div>
		)
	}


	const SearchJobtitleStyles = {
		control: (provided, state) => ({
			...provided,

			marginLeft: "32px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
			cursor: "text",

			":hover": {
				borderColor: "#000",
			},
			":focus": {
				border: "1px solid #000",
			},
		}),

		placeholder: (baseStyles, state) => ({
			...baseStyles,
			gridArea: "1/1/2/3",
			color: "#A3AEBB",
			marginLeft: "6px",
			fontSize: "14px",
			width: "187px"

		}),

		singleValue: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			backgroudColor: "red",
		}),

		multiValue: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			backgroundColor: "white",
			marginTop: "8px",
			border: "1px solid",
			fontSize: "70%",

		}),


		multiValueGeneric: (provided, state) => ({
			...provided,
			paddingLeft: "8",
			paddingTop: "10px",
			padding: "1px",
			marginTop: "12px",


		}),

		IndicatorsContainer: (provided, state) => ({
			...provided,
			visibility: "hidden",
			padding:"0"
	
		}),

		indicatorsContainer: (provided, state) => ({
			...provided,
			visibility: "hidden",
			padding:"0"
	
		}),

		loadingIndicator: (provided, state) => ({
			...provided,
			visibility: "hidden",
			width: "0",
			fontSize:"0"
		}),

		menuList: (provided, state) => ({
			...provided,
			"&:hover:": {
				color: "#55C2C3",
				marginTop: "-6px",
				marginBottom: "14px",
				backgroudColor: "transperent",
				zIndex: "1000",
			},

			"&:hover": {
				backgroundColor: "inherit",
			},
		}),

		menu: (provided, state) => ({
			...provided,

			border: "1px solid #093D54",
			marginLeft: "2rem",
			marginRight: "2rem",
			fontSize: "14px",
			marginRight: "0px",
			position: "relative",
			width: "-webkit-fill-available",
			backgroudColor: "#fff",

			":hover": {
				color: "inherit",
				textDecoration: "none",
			},
		}),

		multiValueRemove: (base, state) => ({
			...base,
			backgroundColor: "#fff",
			setColor: "#000",
			borderRadius: "50%",
			borderRadius: "10px",
			paddingLeft: "-60",
			paddingRight: "0",
			innerHeight: "10px",
			border: "1px solid #08CEDC",
			marginTop: "-7px",
			marginBottom: "2px",
			font: "black !important",
			backgroudColor: "#ffffff",
			width: "11px",
			height: "11px",
			"&:hover": {
				backgroundColor: "#55C2C3",
				color: "black",
			},
		}),

		indicatorSeparator: (provided, state) => ({
			...provided,
			backgroundColor: "hsl(0deg 0% 100%)",
		}),

		MenuList: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#fff",
			outline: "none",
		}),

		valueContainer: (provided, state) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#000",
			outline: "none",
			padding: "0px 5px 0 0",
		}),
		container: (provided, state) => ({
			...provided,
			padding: "0px 11px 0 0",
		}),
		group: (provided, state) => ({
			...provided,
			width: "262px",
			// paddingLeft: '23px',
			marginLeft: "32px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
		}),
	};


	return (
		<div>
			<SearchCompanyName
				autoFocus={isCompanyNameAutoFocus}
				onFocus={() => setIsCompanyNameAutoFocus(true)}
				onBlur={() => setIsCompanyNameAutoFocus(false)}
			/>
			<SearchCompanyURL
				autoFocus={isCompanyURLAutoFocus}
				onFocus={() => setIsCompanyURLAutoFocus(false)}
				onBlur={() => setIsCompanyURLAutoFocus(false)}
			/>
		</div>
	)
}
export default CompanyName;