import Tab from "./Tab";
import ToggleSwitch from "./ToggleSwitch";

const TabToggle = ({ activeTab, setActiveTab, toggleTab }) => (
    <div style={tabToggleWrapperStyle}>
        <Tab
            label="Annual"
            isActive={activeTab === "annual"}
            onClick={() => setActiveTab("annual")}
        />
        <ToggleSwitch activeTab={activeTab} toggleTab={toggleTab} />
        <Tab
            label="Monthly"
            isActive={activeTab === "monthly"}
            onClick={() => setActiveTab("monthly")}
        />
    </div>
);

const tabToggleWrapperStyle = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: "20px",
    padding: "8px 0 0 0",
};

export default TabToggle;