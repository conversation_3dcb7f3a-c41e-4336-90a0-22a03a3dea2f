version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 16
    commands:
      - echo "Installing dependencies..."
      - npm install
      - npm install react-helmet-async  # Explicitly installing react-helmet-async
  pre_build:
    commands:
      - echo "Building React application..."
      - npm run build
  build:
    commands:
      - echo "Build completed."
artifacts:
  files:
    - '**/*'

