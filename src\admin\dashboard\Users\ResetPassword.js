import React, { useState, useEffect } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import AdminHeader from "../../layouts/AdminHeader.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { useNavigate, useParams } from "react-router-dom";
import Alert from "../../../customer/common-files/alert.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import penguineLoadingGif from "../../../customer/assests/waiting.gif";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";

const AdminForm = () => {
  const { id } = useParams();
  const [email, setEmail] = useState();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const [password, setPassword] = useState("");
  const [cpassword, setCpassword] = useState("");
  const [validLength, setValidLength] = useState(false);
  const [validCase, setValidCase] = useState(false);
  const [validWord, setValidWord] = useState(false);
  const [validConfirmPassword, setValidConfirmPassword] = useState(false);
  const [passwordColor, setPasswordColor] = useState(true);

  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  useEffect(() => {
    setemail();
  }, []);

  const setemail = () => {
    setEmail(id);
  };

  const onChangePassword = (event) => {
    let password = event.target.value.trim();

    setPassword(password);

    const minLength = 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /(?=.*[0-9]).*$/.test(password);
    const hasSymbol = /^(?=.*[~`!@#$%^&*()--+={}\[\]|\\:;"'<>,.?/_₹]).*$/.test(
      password
    );
    const isDictionaryWord =
      /password|123456|qwerty|letmein|monkey|football/.test(password);

    if (password.length < minLength) {
      setValidLength(false);
    } else {
      setValidLength(true);
    }
    if (
      password.length < minLength ||
      !hasUppercase ||
      !hasLowercase ||
      !hasNumber ||
      !hasSymbol
    ) {
      setValidCase(false);
    } else {
      setValidCase(true);
    }
    if (isDictionaryWord) {
      setValidWord(false);
    } else {
      setValidWord(true);
    }
    if (cpassword !== password) {
      setValidConfirmPassword(false);
      setPasswordColor(false);
    } else {
      setValidConfirmPassword(true);
      setPasswordColor(true);
    }
  };
  const onChangeConfirmPassword = (event) => {
    let cPassword = event.target.value;
    setCpassword(cPassword);
    if (cPassword !== password) {
      setValidConfirmPassword(false);
      setPasswordColor(false);
    } else {
      setValidConfirmPassword(true);
      setPasswordColor(true);
    }
  };
  const onSubmitHandler = (event) => {
    event.preventDefault();
    if (cpassword == password) {
      const params = {
        email: email,
        newPassword: password,
        confirmPassword: cpassword,
      };
      let result = PostWithTokenNoCache(ApiName.resetPassword, params)
        .then(function (response) {
          if (response.data.status == 200) {
            navigate(-1); // Navigate back by one step in the history stack
          }
        })
        .catch(function (errors) {
          setButtonType("error");
          setDefaultErrorMsg(
            errors?.response?.data?.message || "Could not find user details"
          );
          setDefaultAlert(true);
        });
    } else {
      setValidConfirmPassword(false);
    }
  };

  return (
    <div className={S.app}>
      <AdminHeader />
      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {id && email ? (
            <div className={S.formlayout}>
              <form className="mb-3">
                <h3 className={S.adminsignup}>Reset Password</h3>
                <div className="row">
                  <div className="col-md-12">
                    <div class="form-group">
                      <label>Email</label>
                      <input
                        type="text"
                        class="form-control"
                        id="exampleInputEmail1"
                        aria-describedby="emailHelp"
                        value={email}
                        readOnly="true"
                      />
                    </div>
                  </div>
                  <div className="col-md-12">
                    <div className="form-group">
                      <label className="n-password" label="firstname">
                        New Password
                      </label>

                      <div className="password-input">
                        <input
                          className="form-control"
                          type={showPassword ? "text" : "password"}
                          name="password"
                          onChange={onChangePassword}
                        ></input>
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          <FontAwesomeIcon
                            icon={showPassword ? faEye : faEyeSlash}
                            style={{ pointerEvents: "none" }}
                          />
                        </button>
                      </div>
                      {password && !validCase ? (
                        <span className="err">
                          <p>
                            Password must be 8 characters long with at least one
                            uppercase (A-Z), lowercase (a-z), number & special
                            character (@, #, &, $, etc).
                          </p>
                        </span>
                      ) : (
                        <></>
                      )}
                    </div>
                    <div className="Cnpwrd"></div>
                    <div className="form-group">
                      <label className="n-password" label="firstname">
                        Confirm Password
                      </label>
                      <div className="password-input">
                        {passwordColor ? (
                          <input
                            type={showConfirmPassword ? "text" : "password"}
                            className="form-control"
                            id="exampleInputPassword1"
                            name="confirmPassword"
                            onChange={onChangeConfirmPassword}
                            value={cpassword}
                          />
                        ) : (
                          <input
                            type={showConfirmPassword ? "text" : "password"}
                            className="invalid-input-text"
                            name="confirmPassword"
                            onChange={onChangeConfirmPassword}
                            value={cpassword}
                          />
                        )}
                        <button
                          type="button"
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                        >
                          <FontAwesomeIcon
                            icon={showConfirmPassword ? faEye : faEyeSlash}
                            style={{ pointerEvents: "none" }}
                          />
                        </button>
                      </div>
                      {validCase &&
                      validLength &&
                      validWord &&
                      !validConfirmPassword ? (
                        <span className="email-error-message">
                          <p>
                            {" "}
                            'Confirm password' does not match your new password
                          </p>
                        </span>
                      ) : (
                        <></>
                      )}
                    </div>
                    <span className="Shift">
                      <span>
                        {!validCase ||
                        !validLength ||
                        !validWord ||
                        !validConfirmPassword ? (
                          <input
                            type="button"
                            onClick={onSubmitHandler}
                            value="Change Password"
                            className="cp-pluss1 cust-disabled"
                          />
                        ) : (
                          <input
                            type="button"
                            onClick={onSubmitHandler}
                            value="Change Password"
                            className="cp-pluss1"
                          />
                        )}
                      </span>
                    </span>
                  </div>
                </div>
              </form>
            </div>
          ) : (
            <>
              <div className="animation">
                <img
                  src={penguineLoadingGif}
                  alt="Loading"
                  className="loader"
                  width="400"
                />
              </div>
            </>
          )}
          {defaultAlert && defaultErrorMsg ? (
            <Alert data={defaultErrorMsg} />
          ) : (
            <></>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminForm;
