import React, { useState, useContext, useEffect, useRef } from "react";
import Pagination from "./Pagination.js";
import "../assests/css/filter/contactstable.css";
import DashboardContext from "../common-files/ContextDashboard.js";
import { ApiName } from "../common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../common-files/ApiCalls.js";
import ReachUs from "../filters/ReachUs.js";
import UseTabStore from "../common-files/useGlobalState.js";
import Alert from "../common-files/alert.js";
import WarningAlert from "../common-files/warningAlert.js";
import Feedback from "./Feedback.js";
import { Link } from "react-router-dom";
import ListPopup from "../list/ListPopup.js";
import { clearStore } from "../common-files/indexedDBUtils.js";
export default function App(props) {

  const {
    noOfContact,
    originalFoundCounts,
    revealedEmail,
    buttonType,
    unSelectedRows,
    advancedFilter,
    emailRevealedHistory,
    currentSelectedPage,
    defaultErrorMsg,
    defaultAlert,
    isShowDownloadedData,
    loadedData,
    selectedData,
    viewSelected,
    sampleDownloadedIds,
    allRecordsNew,
    selectedRows,
    currentPage,
    withDownloaded,
    setWithDownloaded,
    selectedTab,
    setSelectedTab,
    sortingBy,
    searchPattern,
    setSortingBy,
    setDefaultError,
    setCurrentPage,
    setSelectedRows,
    setAllRecordsNew,
    setLoadedData,
    setViewModal,
    setShowDownloadedData,
    setDefaultErrorMsg,
    setDefaultAlert,
    isLowViewCredits,
    setContactModelId,
    setButtonType,
    setCurrentSelectedPage,
    setAdvancedFilterSearchPattern,
    setAdvancedFilter,
    setUnSelectedRows,
    setRevealedEmail,
    setEmailRevealedHistory,
    setNoOfContact
  } = UseTabStore();

  const [checkboxes, setCheckboxes] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const closeDownloadAlertRef = useRef();
  const { foundCounts, pageNumber, data, loadingCount, paginationDataCount } =
    props;
  const { dataDC } = useContext(DashboardContext);
  const [PageSize, setPageSize] = useState(
    dataDC.membership === "trail" ? 10 : dataDC.membership === "prime" ? 25 : 10
  );
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [loadingCounts, setLoadingCounts] = useState(loadingCount);
  const [foundContacts, setFoundContacts] = useState(foundCounts);
  const [isOpen, setIsOpen] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);
  const [isValidNoOfContact, setIsValidNoOfContact] = useState(noOfContact ? true : false);
  const [isValidNoOfCompany, setIsValidNoOfCompany] = useState(false);
  const [noOfCompany, setNoOfCompany] = useState(null);
  const [viewRevealedEmail, setViewRevealedEmail] = useState(null);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  let [showSortOptions, setShowSortOptions] = useState(false);
  const [showLastUpdatedOptions, setShowLastUpdatedOptions] = useState(false);
  const [showDownloadOptions, setShowDownlaodOptions] = useState(false);
  const dropdownRef = useRef(null);
  const [time, setTime] = useState(null);
  const [inputColor, setInputColor] = useState("#ffffff"); // Default color is white
  const [inputColorTwo, setInputColorTwo] = useState("#ffffff");
  const handleClickk = () => {
    const newColor = inputColorTwo === "#ffffff" ? "#E8F7F7" : "#ffffff";
    setInputColorTwo(newColor);
  }

  const handleClick = () => {
    const newColor = inputColor === "#ffffff" ? "#E8F7F7" : "#ffffff"; // Toggle between white and yellow
    setInputColor(newColor);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      emailVerifyHistory();
    }, 15000); // 15000ms = 15 seconds

    return () => clearInterval(interval); // Cleanup on component unmount
  }, []);

  useEffect(() => {
    function addSecondsToDate(date, seconds) {
      const newDate = new Date(date.getTime() + seconds * 1000);
      return newDate;
    }

    const currentTime = new Date();
    const addedSeconds = 30; // Example: add 30 seconds

    const newTime = addSecondsToDate(currentTime, addedSeconds);
    setTime(newTime);

  }, [])

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowSortOptions(false);
        setShowLastUpdatedOptions(false);
        setShowDownlaodOptions(false);
      }
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [dropdownRef]);

  useEffect(() => {
    const currentTableData = async () => {
      let sliceData;
      setCurrentPage(pageNumber);
      sliceData = data;

      // Check if allRecordsNew is an array
      if (Array.isArray(allRecordsNew)) {
        // Filter out JSON objects in sliceData that are not already present in allRecordsNew
        const updatedRecords = allRecordsNew.concat(
          sliceData.filter(
            (item) =>
              !allRecordsNew.some(
                (record) => JSON.stringify(record) === JSON.stringify(item)
              )
          )
        );
        setAllRecordsNew(updatedRecords);
        // Now, updatedRecords contains the unique JSON objects
      } else {
        setAllRecordsNew(sliceData);
      }
      if (!isShowDownloadedData) {
        const filteredSliceData = sliceData.filter(
          (item) => !sampleDownloadedIds.includes(item.id)
        );
        setLoadedData(filteredSliceData);
        checkBoxStatus(filteredSliceData);
      } else {
        setLoadedData(sliceData);
        checkBoxStatus(sliceData);
      }
      setSelectedTab("contact");
      setLoading(false);
      if (foundContacts > 0) {
        setLoadingCounts(false);
      }
    };
    setLoadedData([]);
    currentTableData();
  }, []);


  const convertToProperCase = (val) => {
    if (val) {
      // Trim the input to remove any leading or trailing whitespace
      val = val.trim();

      // Match words, keeping special characters in place
      const words = val.match(/\w+|\W+/g);

      // Capitalize the first letter of each alphanumeric word, preserving special characters
      const capitalizedWords = words.map(word =>
        /\w/.test(word) ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : word
      );

      // Join all parts back into a single string
      return capitalizedWords.join('');
    } else {
      return val;
    }
  };

  const emailVerifyHistory = async () => {
    const storedIds = JSON.parse(localStorage.getItem('revealedEmailIds') || '[]');
    const loadedIds = JSON.parse(localStorage.getItem('loadedIds') || '[]');
    if (storedIds.length > 0) {
      try {
        let data_ids = [...loadedIds];
        // Check if any storedIds exist in data_ids
        const hasMatchingIds = data_ids.some(id => storedIds.includes(id));
        if (!hasMatchingIds) {
          return; // Don't proceed if no matching IDs
        }

        const payload = data_ids.length > 0 ? { data_ids } : {};
        await PostWithTokenNoCache(ApiName.emailVerifyHistory, payload)
          .then(function (response) {
            if (response.data.status === 200) {
              const dataObj = JSON.parse(response.data.data);
              setEmailRevealedHistory(dataObj);

              const returnedDataIds = dataObj.map(item => item.dataId);
              const remainingIds = storedIds.filter(id => !returnedDataIds.includes(id));
              localStorage.setItem('revealedEmailIds', JSON.stringify(remainingIds));

              // Schedule next call if IDs remain with default 15s interval
              if (remainingIds.length > 0) {
                setTimeout(emailVerifyHistory, 5000);
              }
            } else if (response.data.status === 500) {
              // If server error, retry with longer interval
              setTimeout(emailVerifyHistory, 120000);
            }
          })
          .catch(function (error) {
            // On any error, retry with longer interval
            setTimeout(emailVerifyHistory, 120000);
          });
      } catch (error) {
        // On any error, retry with longer interval
        setTimeout(emailVerifyHistory, 120000);
      }
    }
  };

  const toggleSortOptions = () => {
    showSortOptions = showSortOptions
      ? setShowSortOptions(false)
      : setShowSortOptions(true);
    setShowDownlaodOptions(false);
    setShowLastUpdatedOptions(false);
  };
  const toggleLastUpdatedOptions = () => {
    setShowLastUpdatedOptions(true);
    setShowDownlaodOptions(false);
  };
  const toggleDownloadsOptions = () => {
    setShowLastUpdatedOptions(false);
    setShowDownlaodOptions(true);
  };
  const clearFilters = () => {
    setSortingBy(6);
    setWithDownloaded(true);
    setShowDownloadedData(true);
    setShowDownlaodOptions(false);
    setShowLastUpdatedOptions(false);
    sortBy();
  };

  const closeEmail = async (id) => {
    viewRevealedEmail == id
      ? setViewRevealedEmail("")
      : setViewRevealedEmail(id);
  };

  const viewMail = async (id) => {
    // Get the hidden input field by its id
    const hiddenInput = document.getElementById(`hiddenInput-${id}`);

    // Check if the element exists
    if (hiddenInput) {
      const textToCopy = hiddenInput.value;

      // Create a temporary textarea element to copy the text to the clipboard
      const tempTextArea = document.createElement("textarea");
      tempTextArea.value = textToCopy;
      document.body.appendChild(tempTextArea);
      tempTextArea.select();
      document.execCommand("copy");
      document.body.removeChild(tempTextArea);
    }
    viewRevealedEmail == id
      ? setViewRevealedEmail("")
      : setViewRevealedEmail(id);

    setTimeout(() => {
      setViewRevealedEmail("");
    }, 3000)
  };

  const revealMail = async (id) => {
    setShowDownlaodOptions(true);
    setShowDownloadedData(true);
    setWithDownloaded(true);

    const params = {
      dataIds: [id],
      singleDownload: true
    };

    await PostWithTokenNoCache(ApiName.revealEmail, params)
      .then((response) => {
        if (response.data.status === 200) {
          // Add the ID to localStorage
          const storedIds = JSON.parse(localStorage.getItem('revealedEmailIds') || '[]');
          const newStoredIds = Array.from(new Set([...storedIds, id]));
          localStorage.setItem('revealedEmailIds', JSON.stringify(newStoredIds));

          setRevealedEmail([...new Set([...revealedEmail, id])]);
          // emailVerifyHistory();
        }
      })
      .catch((error) => {
        if (
          error.response?.status === 400 &&
          (error.response?.data?.message == "Unable to fetch available credit." ||
            error.response?.data?.message == "No email credits available")
        ) {
          setButtonType("credit-message");

          if (error.response.data.message == "No email credits available") {
            setDefaultErrorMsg("Insufficient email credits to perform this action.");
          } else {
            setDefaultErrorMsg("Insufficient credits to perform this action.");
          }

          setDefaultAlert(true);
          return false;
        }
        setButtonType("error");
        setDefaultErrorMsg(error.response?.data?.message || "An error occurred");
        setDefaultAlert(true);
      });
  };



  // auto set checked all if all items selected in current page
  const checkBoxStatus = (currentData) => {
    let pkId = currentData.map((user) => user.id);
    let id = selectedRows.map((v) => v);
    const difference = new Set(pkId).difference(new Set(id));
    const differentElements = Array.from(difference);

    if (differentElements.length == 0) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  };

  const onChangeHandler = (event, index) => {
    const { id } = event.target;
    const isChecked = event.target.checked;

    // Create a new array based on the previous selectedRows
    const newArray = [...selectedRows];

    if (isChecked) {
      // Add the ID if it doesn't exist
      if (!newArray.includes(parseInt(id))) {
        newArray.push(parseInt(id));
      }
    } else {
      // Remove the ID if it exists
      const index2 = newArray.indexOf(parseInt(id));
      if (index2 !== -1) {
        newArray.splice(index2, 1);
      }
    }

    // Update selectedRows with the new array
    setSelectedRows(newArray);

    // Function to remove values from an array
    function removeValues(arr, valuesToRemove) {
      if (!valuesToRemove.length) {
        return arr; // No values to remove, return original array
      }
      return arr.filter((element) => !valuesToRemove.includes(element));
    }

    if (advancedFilter && isChecked) {
      const filteredArray = removeValues(unSelectedRows, parseInt(id));
      setUnSelectedRows(filteredArray);
    }

    if (advancedFilter && !isChecked) {
      // Ensure unSelectedRows is an array before spreading
      let newSelectedRows = [...(Array.isArray(unSelectedRows) ? unSelectedRows : []), parseInt(id)];
      setUnSelectedRows(newSelectedRows);
    }

    // Auto set checked all if all items are selected in the current page
    let pkId = loadedData
      .filter((user) => newArray.includes(user.id))
      .map((user) => user.id);

    if (PageSize === pkId.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }

    // Calculate the start index of the current page
    const startIndex = (currentPage - 1) * PageSize;

    // Calculate the end index of the current page (exclusive)
    const endIndex = Math.min(startIndex + PageSize, loadedData.length);

    if (pkId.length === endIndex) {
      setSelectAll(true);
    }
  };

  const handleSelectPage = () => {
    selectAllChange(!selectAll);
    setIsOpen(false);
  };

  const selectAllChange = (isChecked) => {
    setSelectAll(isChecked);

    // Get the IDs of all items that are not in sampleDownloadedIds
    const pkId = loadedData
      .filter((user) => !sampleDownloadedIds.includes(user.id))
      .map((user) => user.id);

    // Function to update checkboxes and selectedRows
    const updateCheckboxes = (prevCheckboxes, selectedRows, pkId, isChecked) => {
      const updatedCheckboxes = [...prevCheckboxes]; // Use an array instead of an object
      const updatedSelectedRows = new Set(selectedRows);

      pkId.forEach((val) => {
        const id = parseInt(val);
        const index = loadedData.findIndex((user) => user.id === id); // Find the index of the item
        if (index !== -1) {
          updatedCheckboxes[index] = isChecked; // Update the checkbox state at the correct index
          isChecked ? updatedSelectedRows.add(id) : updatedSelectedRows.delete(id);
        }
      });

      return {
        updatedCheckboxes,
        updatedSelectedRows: Array.from(updatedSelectedRows),
      };
    };

    // Update checkboxes and selectedRows
    const { updatedCheckboxes, updatedSelectedRows } = updateCheckboxes(
      checkboxes,
      selectedRows,
      pkId,
      isChecked
    );

    setCheckboxes(updatedCheckboxes);
    setSelectedRows(updatedSelectedRows);

    // Update unSelectedRows
    if (isChecked) {
      setUnSelectedRows((prevUnSelectedRows) =>
        prevUnSelectedRows.filter((id) => !pkId.includes(id))
      );
    } else {
      setUnSelectedRows(pkId);
    }
  };


  const handleModal = (id) => {
    if (isLowViewCredits) {
      setDefaultErrorMsg("You have exceeded your view credits");
      setDefaultAlert(true);
      setButtonType("upgrade");
    } else {
      setViewModal(true);
      setContactModelId(id);
    }
  };

  const mystyle = { marginBottom: "20px", marginTop: "5px" };

  const sortBy = async (sort) => {
    setLoadingCounts(true);
    if (sort === undefined) {
      sort = "";
      setSortingBy("");
    } else {
      setSortingBy(sort);
    }
    searchPattern["searchBy"] = selectedTab;
    searchPattern["sortBy"] = sort;
    searchPattern["withDownloaded"] = withDownloaded;
    const dataPost = {
      page: currentPage ? currentPage : 1,
      pageSize: PageSize,
      searchPattern: searchPattern,
    };
    const loaderElement = document.querySelector("#table-loader");
    const loaderContactTable = document.querySelector("#cust-contact-table");

    if (loaderElement) {
      loaderElement.style.display = "block"; // Set display to 'none' if the element exists in the DOM
    }
    if (loaderContactTable) {
      loaderContactTable.style.display = "none"; // Set display to 'none' if the element exists in the DOM
    }
    await PostWithTokenNoCache(ApiName.esContactFilter, dataPost)
      .then(async function (response) {
        if (response != null && "status" in response) {
          if (response.data.status === 200) {
            try {
              const dataObj = JSON.parse(response.data.data);
              if (dataObj.totalCount > 0) {
                setLoadedData(dataObj.records);
                setFoundContacts(dataObj.totalCount);
                setLoadingCounts(false);
              } else {
                setLoadedData([]);
                setFoundContacts(0);
                setLoadingCounts(false);
              }
              if (loaderElement) {
                loaderElement.style.display = "none"; // Set display to 'none' if the element exists in the DOM
              }
              if (loaderContactTable) {
                loaderContactTable.style.display = "block"; // Set display to 'none' if the element exists in the DOM
              }
            } catch (error) {
              setTimeout(() => {
                setDefaultError("");
              }, 3000);
              setDefaultError("Failed to load contact counts.");
            }
          }
        }
      })
      .catch(function () {
        setDefaultError("Failed to load contact counts.");
      });
  };

  async function callTotalCount(pattern, sort) {
    pattern["sortBy"] = sort;
    pattern["searchBy"] = selectedTab;
    const dataPostCount = {
      searchPattern: pattern,
    };
    await PostWithTokenNoCache(ApiName.totalContactCounts, dataPostCount)
      .then(function (response) {
        if (response.data.status === 200) {
          const dataObj = JSON.parse(response.data.data);
          setFoundContacts(dataObj.counts);
          setLoadingCounts(false);
        }
      })
      .catch(function () {
        setTimeout(() => {
          setDefaultError("");
        });
        setDefaultError("Failed to load counts, Try again.!");
      });
  }

  const showDownloadedContact = async () => {
    setShowDownloadedData(true);
    setLoadingCounts(true);
    setSortingBy("");
    setWithDownloaded(true);

    searchPattern["searchBy"] = selectedTab;
    searchPattern["sortBy"] = sortingBy;
    searchPattern["withDownloaded"] = true;
    const dataPost = {
      page: currentPage ? currentPage : 1,
      pageSize: PageSize,
      searchPattern: searchPattern,
    };
    const loaderElement = document.querySelector("#table-loader");
    const loaderContactTable = document.querySelector("#cust-contact-table");

    if (loaderElement) {
      loaderElement.style.display = "block"; // Set display to 'none' if the element exists in the DOM
    }
    if (loaderContactTable) {
      loaderContactTable.style.display = "none"; // Set display to 'none' if the element exists in the DOM
    }
    await PostWithTokenNoCache(ApiName.esContactFilter, dataPost)
      .then(async function (response) {
        if (response != null && "status" in response) {
          if (response.data.status === 200) {
            try {
              const dataObj = JSON.parse(response.data.data);
              if (dataObj.totalCount > 0) {
                setLoadedData(dataObj.records);
                setFoundContacts(dataObj.totalCount);
                setLoadingCounts(false);
              } else {
                setLoadedData([]);
                setFoundContacts(0);
                setLoadingCounts(false);
              }
              if (loaderElement) {
                loaderElement.style.display = "none"; // Set display to 'none' if the element exists in the DOM
              }
              if (loaderContactTable) {
                loaderContactTable.style.display = "block"; // Set display to 'none' if the element exists in the DOM
              }
              // await callTotalCount(dataPost.searchPattern, sortingBy);
            } catch (error) {
              setTimeout(() => {
                setDefaultError("");
              }, 3000);
              setDefaultError("Failed to load contact counts.");
            }
          }
        }
      })
      .catch(function () {
        setDefaultError("Failed to load contact counts.");
      });
  };

  const hideDownloadedContact = async () => {
    setShowDownloadedData(false);
    setLoadingCounts(true);
    setSortingBy("");
    setWithDownloaded(false);

    searchPattern["searchBy"] = selectedTab;
    searchPattern["sortBy"] = sortingBy;
    searchPattern["withDownloaded"] = false;
    const dataPost = {
      page: currentPage ? currentPage : 1,
      pageSize: PageSize,
      searchPattern: searchPattern,
    };
    const loaderElement = document.querySelector("#table-loader");
    const loaderContactTable = document.querySelector("#cust-contact-table");

    if (loaderElement) {
      loaderElement.style.display = "block"; // Set display to 'none' if the element exists in the DOM
    }
    if (loaderContactTable) {
      loaderContactTable.style.display = "none"; // Set display to 'none' if the element exists in the DOM
    }
    await PostWithTokenNoCache(ApiName.esContactFilter, dataPost)
      .then(async function (response) {
        if (response != null && "status" in response) {
          if (response.data.status === 200) {
            try {
              const dataObj = JSON.parse(response.data.data);
              if (dataObj.totalCount > 0) {
                setLoadedData(dataObj.records);
                setFoundContacts(dataObj.totalCount);
                setLoadingCounts(false);
              } else {
                setLoadedData([]);
                setFoundContacts(0);
                setLoadingCounts(false);
              }
              if (loaderElement) {
                loaderElement.style.display = "none"; // Set display to 'none' if the element exists in the DOM
              }
              if (loaderContactTable) {
                loaderContactTable.style.display = "block"; // Set display to 'none' if the element exists in the DOM
              }
              // await callTotalCount(dataPost.searchPattern, sortingBy);
            } catch (error) {
              setTimeout(() => {
                setDefaultError("");
              }, 3000);
              setDefaultError("Failed to load contact counts.");
            }
          }
        }
      })
      .catch(function () {
        setDefaultError("Failed to load contact counts.");
      });
  };

  const handleInputs = (e) => {
    const inputValue = e.target.value.trim(); // Trim any whitespace
    const id = e.target.id; // Identify the input field
    const pattern = /^\d*$/; // Allow only digits (non-negative integers)

    // Validate the input
    const isValid = pattern.test(inputValue);
    if (id === "no_of_contact") {
      if (isValid) {
        setNoOfContact(inputValue); // Update value only if valid
      }
      inputValue ? setIsValidNoOfContact(isValid) : setIsValidNoOfContact(false); // Update validity state
    } else if (id === "no_of_company") {
      if (isValid) {
        setNoOfCompany(inputValue); // Update value only if valid
      }
      setIsValidNoOfCompany(isValid); // Update validity state
    }
  };


  const applyFilter = async () => {

    clearStore("AdvancedFilterData", "Company")

    if (dataDC.membership === "trail" && noOfContact > 2000 && isValidNoOfContact) {
      setButtonType("warning-error");
      setDefaultAlert(true);
      setDefaultErrorMsg("Upgrade your plan to search over 2000 contacts.");
      return false;
    }
    if (!isValidNoOfContact || !noOfContact) {
      setIsValidNoOfContact(false);
      return false;
    }
    if (!isValidNoOfCompany || !noOfCompany) {
      // setIsValidNoOfCompany(false);
      // return false;
      setNoOfCompany(1);
    }
    setOpenPopup(false);
    setSelectedRows([]);
    setUnSelectedRows([]);
    setAdvancedFilter(time);
    // searchPattern["maxContactPerCompany"] = parseInt(noOfCompany);

    const dataPost = {
      page: 1,
      pageSize: parseInt(PageSize),
      searchPattern: searchPattern,
      maxContactPerCompany: noOfCompany ? parseInt(noOfCompany) : 100,
      maxContacts: parseInt(noOfContact)
    };
    setCurrentPage(1);
    setAdvancedFilterSearchPattern(dataPost);
  };

  const clearSection = () => {
    if (isValidNoOfCompany || isValidNoOfContact) {
      setNoOfCompany(null);
      setNoOfContact(null);
      setOpenPopup(false);
      setAdvancedFilter(false);
      setSelectedRows([]);
      setUnSelectedRows([]);
      setIsValidNoOfContact(false);
      setIsValidNoOfCompany(false);
    }
  };

  return (
    <>
      <div
        className="modal fade"
        id="glassAnimals"
        tabIndex="-1"
        role="dialog"
        aria-labelledby="exampleModalLabel"
        aria-hidden="true"
      >
        <div
          className="modal-dialog modal-dialog-centered modal-sm"
          role="document"
          style={{ position: "absolute", left: "40%", top: "-18rem" }}
        >
          <div
            className="modal-content"
            style={{
              borderRadius: "14px",
              border: "1px solid #55C2C3",
              height: "37px",
              padding: "4px 2px 0px 2px",
            }}
          >
            <div className="d-flex flex-row justify-content-between">
              <div className="srated">
                <p>
                  <span className="dlnld">
                    <img src="../images/downld.png" />
                  </span>{" "}
                  {showModal ? "Download Completed" : "Download Started"}
                </p>
              </div>
              <div className="cncl">
                <button
                  type="button"
                  ref={closeDownloadAlertRef}
                  className="close"
                  data-dismiss="modal"
                  aria-label="Close"
                  onClick={() => setShowModal(false)}
                >
                  <img src="../images/cancl.png" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex justify-content-between ml-2 mr-4">
        <div className="d-flex flex-row">
          <div ref={dropdownRef}>
            <button
              className="sortby dropdown-toggle"
              onClick={() => toggleSortOptions()}
            >
              <img src="../images/sortby.png" className="img-fluid" />
              Sort by
            </button>
            {showSortOptions && (
              <ul className="left">
                <li onClick={toggleLastUpdatedOptions}>
                  Last Updated{" "}
                  <span className="caret-img">
                    <img src="../images/Caret_right.png" width="14" />
                  </span>{" "}
                </li>
                <div className="dropdown-divider"></div>
                {showLastUpdatedOptions && (
                  <div className="sub-drop-1">
                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>All</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="lastUpdated"
                          checked={sortingBy === ""}
                          onClick={() => sortBy()}
                        />
                      </div>
                    </div>

                    <div className="dropdown-divider"></div>

                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>03 Months</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="lastUpdated"
                          checked={sortingBy === 3}
                          onChange={() => sortBy(3)}
                        />
                      </div>
                    </div>

                    <div className="dropdown-divider"></div>

                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>06 Months</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="lastUpdated"
                          checked={sortingBy === 6}
                          onClick={() => sortBy(6)}
                        />
                      </div>
                    </div>

                    <div className="dropdown-divider"></div>

                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>12 Months</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="lastUpdated"
                          checked={sortingBy === 12}
                          onClick={() => sortBy(12)}
                        />
                      </div>
                    </div>
                  </div>
                )}
                <li onClick={toggleDownloadsOptions}>
                  Downloads &nbsp;&nbsp;&nbsp;&nbsp;
                  <span className="caret-img">
                    <img src="../images/Caret_right.png" width="14" />
                  </span>
                </li>
                <div className="dropdown-divider"></div>
                {showDownloadOptions && (
                  <div className="sub-drop-2">
                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>Show</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="hideShowDownload"
                          checked={isShowDownloadedData ? true : false}
                          onClick={() => showDownloadedContact()}
                        />
                      </div>
                    </div>
                    <div className="dropdown-divider"></div>
                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>Hide</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="hideShowDownload"
                          checked={!isShowDownloadedData ? true : false}
                          onClick={() => hideDownloadedContact()}
                        />
                      </div>
                    </div>
                  </div>
                )}
                <li onClick={clearFilters}>Clear Filters</li>
              </ul>
            )}
          </div>
          <div className="search-result mt-1">
            Search Result Found{" "}
            <span className="number-of" id="total_contacts">
              {" "}
              {!loadingCounts
                ? originalFoundCounts == 0
                  ? "Loading..."
                  : originalFoundCounts
                : "Loading..."}{" "}
              {originalFoundCounts !== 0 || !loadingCounts ? "Contacts" : <></>}
            </span>
          </div>
        </div>
      </div>

      <div class="selected-items-bar">
        <div className="d-flex flex-row justify-content-between">
          <div>
            <div className="d-flex flex-row">
              <div>
                <input
                  checked={selectedRows.length > 0 ? true : false}
                  type="checkbox"
                  className="dropcheck"
                  onClick={toggleDropdown}
                />
              </div>
              <div></div>
              <div>
                <div
                  class="dropdown"
                  style={{ margin: "margin: 4px 0 0 0;", cursor: "pointer" }}
                >
                  <div className="dropdown">
                    <button className="caretdrop" onClick={toggleDropdown}>
                      <img
                        src={
                          isOpen
                            ? "../images/caret_up.png"
                            : "../images/caret_new_down.png"
                        }
                        alt="Caret"
                        className="caret-icon"
                        width="8"
                      />
                    </button>
                    {isOpen && (
                      <div className="dropdown-content ">
                        <a
                          class="dropdown-item Selectthis"
                          onClick={handleSelectPage}
                        >
                          {selectAll
                            ? "Unselect Current Page"
                            : "Select Current Page"}
                        </a>
                        <div className="dropdown-divider"></div>
                        <a className="dropdown-item Selectthis" style={{ cursor: "auto" }}>
                          Advanced Selection
                        </a>
                        <div className="dropdown-divider"></div>

                        <div className="d-flex flex-row justify-content-between pl-3 pr-3">
                          <div className="p-2" style={{ cursor: "auto" }}>
                            <p className="miniselect">Select No. of Contacts</p>
                          </div>
                          <div className="p-2">
                            <input
                              type="number"
                              id="no_of_contact"
                              className={`miniinput ${isValidNoOfContact ? "" : ""}`}
                              onChange={(e) => handleInputs(e, "noOfContact")}
                              value={noOfContact || ""}
                              autoComplete="off"
                              style={{ backgroundColor: inputColor }}
                              onClick={handleClick}
                              min="0"
                              onKeyDown={(e) => {
                                if (["e", "E", "+", "-", "."].includes(e.key)) {
                                  e.preventDefault();
                                }
                              }}
                            />
                            {/* <div className="desend">
                              <i className="fa fa-sort" style={{ fontSize: "12px" }}></i>
                            </div> */}
                          </div>
                        </div>

                        <div className="d-flex flex-row justify-content-start pl-3 pr-3">
                          <div className="p-2" style={{ cursor: "auto" }}>
                            <p className="miniselect">Max Contacts Per Company</p>
                          </div>
                          <div className="p-2 ml-3">
                            <input
                              type="number" // Set input type to number
                              id="no_of_company"
                              className={`miniinputt ${isValidNoOfCompany ? "" : ""}`}
                              onChange={(e) => handleInputs(e, "noOfCompany")} // Pass type to handler
                              value={noOfCompany || ""}
                              autoComplete="off"
                              onClick={handleClickk}
                              style={{ backgroundColor: inputColorTwo }}
                              min="0"
                              onKeyDown={(e) => {
                                if (["e", "E", "+", "-", "."].includes(e.key)) {
                                  e.preventDefault();
                                }
                              }}
                            />
                            {/* <div className="desend2">
                              <i className="fa fa-sort" style={{ fontSize: "12px" }}></i>
                            </div> */}
                          </div>
                        </div>


                        <div className="">
                          <button
                            type="button"
                            onClick={applyFilter}
                            className={`${isValidNoOfContact
                              ? "selectapplybutton-enable-contact"
                              : "selectapplybutton"
                              }`}
                          >
                            Apply
                          </button>
                        </div>
                        <div className="dropdown-divider"></div>

                        <div className="" onClick={clearSection}>
                          <p
                            style={{
                              color: `${isValidNoOfContact || isValidNoOfCompany
                                ? "#55C2C3"
                                : "#bbbbbb"
                                }`,
                            }}
                            className="ClearSection"
                          >
                            Clear Selection
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="droppose">
                    <div
                      class="dropdown-menu"
                      aria-labelledby="dropdownMenuButton"
                    ></div>
                  </div>
                </div>
              </div>
              <div>
                <p className="oSelected">{selectedRows.length} Selected</p>
              </div>

              <div className="ml-3">
                <button
                  type="submit"
                  className={
                    selectedRows.length > 0
                      ? "savedlist1"
                      : "addtolist"
                  }
                  onClick={
                    selectedRows.length > 0 ? () => setOpenPopup(true) : () => setOpenPopup(false)
                  }
                  style={{
                    cursor: !selectedRows.length > 0 ? "not-allowed" : "pointer",
                  }}
                >
                  <i class="fa fa-plus mr-1"></i> Add to List
                </button>
                {openPopup && selectedRows.length > 0 ? (
                  <ListPopup
                    setOpenPopup={setOpenPopup}
                    setCheckboxes={setCheckboxes}
                    setSelectAll={setSelectAll}
                  ></ListPopup>
                ) : (
                  <></>
                )}
              </div>
            </div>
          </div>

          <div className="d-flex flex-row">
            <div className="mr-2">
              <Link to="/saved-list">
                <button type="submit" className="savedlist">
                  <i className="fa fa-align-left mr-2"></i>Saved Lists
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div class="fixTableHead ">
        <table style={{ position: "sticky", top: "0", zIndex: "1" }}>
          <thead className="table-box">
            <tr>
              {/* <th className="section-1">
                {" "}
                {!viewSelected ? (
                  <input
                    type="checkbox"
                    className="check-input"
                    checked={selectAll}
                    onChange={handleSelectPage}
                  />
                ) : (
                  ""
                )}
              </th> */}
              <th className="section-2">Contact Name</th>
              <th className="companyheaders">Job Title</th>
              <th className="companyheaders">Email</th>
              <th className="companyheaders">Company Name</th>
              <th className="companyheaders">Location</th>
              <th className="companyheaders">View Details</th>
            </tr>
          </thead>

          <tbody className=" mt-3">
            {!loading && loadedData.length > 0 ? (
              loadedData.map((item, index) => {

                let isRevealIdExist = [];

                isRevealIdExist = emailRevealedHistory.filter((data) => data.dataId === item.id);
                return (
                  <tr
                    className="table-data"
                    id={index}
                    style={mystyle}
                    key={item.id}
                  >


                    <td
                      style=
                      {isRevealIdExist.length > 0 && isRevealIdExist[0].dataId === item.id && isRevealIdExist[0].verifierStatus && isRevealIdExist[0].verifierStatus.toLowerCase() == "valid"
                        // sampleDownloadedIds.includes(item.id)
                        ? { backgroundColor: "#F5F5F5", padding: "14px 0 14px 0", borderRadius: "5px" }
                        : null
                      }
                      className="section-4"
                    >

                      <div className="d-flex flex-row ml-3">
                        <div>
                          <label className="form-controller">
                            <li className="first">
                              {/* <input
                                type="checkbox"
                                class="checkboxstyle"
                                id="checkbox4"
                              /> */}



                              <label className="second" for="checkbox4">
                                {isRevealIdExist.length > 0 && isRevealIdExist[0].dataId === item.id && isRevealIdExist[0].verifierStatus && isRevealIdExist[0].verifierStatus.toLowerCase() == "valid" ? (
                                  <p className="rotate">
                                    <input
                                      className="custom-checkbox"
                                      id={item.id}
                                      type="checkbox"
                                      checked={
                                        selectedRows.includes(item.id) || false
                                      }
                                      onChange={(event) =>
                                        onChangeHandler(event, item.id)
                                      }
                                    />
                                  </p>
                                ) : (
                                  <input
                                    className="custom-checkbox"
                                    id={item.id}
                                    type="checkbox"
                                    checked={
                                      selectedRows.includes(item.id) || false
                                    }
                                    onChange={(event) =>
                                      onChangeHandler(event, item.id)
                                    }
                                  />
                                )}
                              </label>
                            </li>
                            {isRevealIdExist.length > 0 && isRevealIdExist[0].dataId === item.id && isRevealIdExist[0].verifierStatus && isRevealIdExist[0].verifierStatus.toLowerCase() == "valid" ? (
                              <span className="hideText">
                                {/* This contact has already been downloaded */}
                              </span>
                            ) : (
                              ""
                            )}
                          </label>
                        </div>
                        <div>
                          <p className="itemcontactname">
                            {item.contact_name ? convertToProperCase(item.contact_name) : ""}
                          </p>
                        </div>
                      </div>


                    </td>
                    <td
                      style={
                        isRevealIdExist.length > 0 && isRevealIdExist[0].dataId === item.id && isRevealIdExist[0].verifierStatus && isRevealIdExist[0].verifierStatus.toLowerCase() == "valid"
                          ? { backgroundColor: "#F5F5F5", padding: "14px 0 14px 0", borderRadius: "5px" }
                          : null
                      }
                      className="section-4"
                    >
                      {item.contact_job_title_1 ? convertToProperCase(item.contact_job_title_1) : ""}
                    </td>
                    <td
                      style={
                        isRevealIdExist.length > 0 && isRevealIdExist[0].dataId === item.id && isRevealIdExist[0].verifierStatus && isRevealIdExist[0].verifierStatus.toLowerCase() == "valid"
                          ? { backgroundColor: "#F5F5F5", padding: "10px 0 10px 0", borderRadius: "5px", width: "250px" }
                          : null
                      }
                      className="section-4"
                    >
                      {/* {console.log(isRevealIdExist && isRevealIdExist.length > 0 ? isRevealIdExist[0] : "No1")} */}
                      {
                        isRevealIdExist && isRevealIdExist.length < 1 && revealedEmail.includes(item?.id) ?
                          (
                            <button className="Verifyingemail">
                              <img
                                src="./images/loading-path.gif"
                                style={{
                                  padding: "0px 0px 0 0px",
                                  color: "#55C2C3 !important",
                                  width: "18px",
                                  position: "relative",
                                  top: "-1px",
                                  margin: "0 6px 0 0px",
                                }}
                              />
                              Verifying email <span className="percentage"></span>
                            </button>
                          ) : isRevealIdExist.length === 0 ||
                            isRevealIdExist[0].dataId !== item.id ? (
                            <button
                              type="button"
                              className="RevealEmail"
                              onClick={() => revealMail(item.id)}
                            >
                              Reveal Email
                            </button>
                          ) : isRevealIdExist[0].dataId === item.id &&
                            isRevealIdExist[0].verifierStatus.toLowerCase() !== "valid" ? (
                            <button
                              type="button"
                              className="dashboardEmailNotfound2"
                            >
                              Email Not found
                            </button>
                          ) : isRevealIdExist[0].dataId === item.id &&
                            (isRevealIdExist[0].verifierStatus.toLowerCase() !== "valid") ? (
                            <>
                              <input
                                type="hidden"
                                id={`hiddenInput-${item.id}`}
                                value={isRevealIdExist[0].emailId}
                              />
                              {viewRevealedEmail === item.id ? (
                                <div className="row">
                                  {/* <div className="col-5"></div> */}
                                  <div className="col-5">
                                    <p className="successmessage7">
                                      <img src="./images/promocode-success.png" />
                                      Copied Successfully
                                    </p>
                                  </div>
                                  <div className="col-3"></div>
                                </div>
                              ) : (
                                <div
                                  className="d-flex flex-row"
                                >
                                  <div>
                                    <i className="fa fa-copy" style={{ color: "#55C2C3" }}></i>&nbsp;&nbsp;&nbsp;
                                    <img style={{ cursor: "pointer" }} src="images/copytoclip.png" onClick={() => viewMail(item.id)} />
                                  </div>
                                  <div>
                                    <p className="dashboardemail">
                                      {isRevealIdExist[0]?.emailId && isRevealIdExist[0].emailId.length > 13
                                        ? `${isRevealIdExist[0].emailId.slice(0, 13)}...`
                                        : isRevealIdExist[0]?.emailId || ""}
                                      &nbsp;
                                    </p>
                                  </div>
                                </div>
                              )}
                            </>
                          ) : isRevealIdExist[0].dataId === item.id &&
                            isRevealIdExist[0].verifierStatus.toLowerCase() == "valid" ? (
                            <>
                              <input
                                type="hidden"
                                id={`hiddenInput-${item.id}`}
                                value={isRevealIdExist[0].emailId}
                              />
                              {viewRevealedEmail === item.id ? (
                                <div className="row">
                                  {/* <div className="col-5"></div> */}
                                  <div className="col-5">
                                    <p className="successmessage7">
                                      Copied Successfully
                                    </p>
                                  </div>
                                  <div className="col-3"></div>
                                </div>
                              ) : (
                                <div
                                  className="d-flex flex-row"
                                >
                                  <div>
                                    {/* <i className="fa fa-copy" style={{ color: "#55C2C3" }}></i>&nbsp;&nbsp;&nbsp; */}
                                  </div>
                                  <div>
                                    <p className="dashboardemail">
                                      <img style={{ cursor: "pointer" }} src="images/copytoclip.png" width="10" onClick={() => viewMail(item.id)} />
                                      &nbsp;&nbsp;
                                      {isRevealIdExist[0]?.emailId && isRevealIdExist[0].emailId.length > 13
                                        ? `${isRevealIdExist[0].emailId.slice(0, 13)}...`
                                        : isRevealIdExist[0]?.emailId || ""}
                                      &nbsp;
                                    </p>
                                  </div>
                                </div>
                              )}
                            </>
                          ) : (
                            <button
                              type="button"
                              className="RevealEmail"
                              onClick={() => revealMail(item.id)}
                            >
                              Reveal Email
                            </button>
                          )}
                    </td>
                    <td
                      style={
                        isRevealIdExist.length > 0 && isRevealIdExist[0].dataId === item.id && isRevealIdExist[0].verifierStatus && isRevealIdExist[0].verifierStatus.toLowerCase() == "valid"
                          ? { backgroundColor: "#F5F5F5", padding: "14px 0 14px 0", borderRadius: "5px" }
                          : null
                      }
                      className="section-4"
                    >
                      {convertToProperCase(item.company_company_name || "")}
                    </td>
                    <td
                      style={
                        isRevealIdExist.length > 0 && isRevealIdExist[0].dataId === item.id && isRevealIdExist[0].verifierStatus && isRevealIdExist[0].verifierStatus.toLowerCase() == "valid"
                          ? { backgroundColor: "#F5F5F5", padding: "14px 0 14px 0", borderRadius: "5px" }
                          : null
                      }
                      className="section-4"
                    >
                      {item.company_address_state && item.company_address_state !== "--"
                        ? `${convertToProperCase(item.company_address_state)}, ${convertToProperCase(item.company_address_country || "")}`
                        : convertToProperCase(item.company_address_country || "")}
                    </td>
                    <td
                      style={
                        isRevealIdExist.length > 0 && isRevealIdExist[0].dataId === item.id && isRevealIdExist[0].verifierStatus && isRevealIdExist[0].verifierStatus.toLowerCase() == "valid"
                          ? { backgroundColor: "#F5F5F5", padding: "14px 0 14px 0", borderRadius: "5px" }
                          : null
                      }
                      className="section-4"
                    >
                      <div
                        data-backdrop="static"
                        className="eye"
                        data-toggle="modal"
                        data-target="#exampleModalCenter"
                        onClick={() => {
                          handleModal(item.id);
                        }}
                      >
                        <img src="../images/eyee.png" />
                      </div>
                    </td>
                  </tr>
                );
              })
            ) : (
              <></>
            )}
          </tbody>
        </table>
      </div>
      <Feedback />

      {(viewSelected === true ? currentSelectedPage : currentPage) ===
        Math.ceil(
          foundContacts === 0 ? paginationDataCount : foundContacts / PageSize
        ) ? (
        <ReachUs />
      ) : (
        <></>
      )}
      <div className="extra-border">
        <div className="row">
          <div className="col-6">
            <div className="contacts-selected">
              <p className={!advancedFilter ? selectedRows.length : noOfContact > 0 ? "greenBlueShade" : ""}>
                {`${selectedRows.length} Contact${selectedRows.length > 1 ? 's' : ''} Selected`}
              </p>
            </div>
          </div>
          <div className="col-6">
            {dataDC.membership === "trail" ? (
              <Pagination
                className="pagination-bar"
                currentPage={
                  viewSelected === true ? currentSelectedPage : currentPage
                }
                totalCount={
                  viewSelected === true
                    ? selectedData.length / PageSize
                    : Math.min(
                      Math.ceil(!advancedFilter ? foundContacts / PageSize : noOfContact / PageSize),
                      paginationDataCount
                    )
                }
                pageSize={1}
                onPageChange={(page) => {

                  if (viewSelected === true) {
                    // loadCurrentPageEmailVerifyHistory();
                    setCurrentSelectedPage(page);
                    setSelectAll(false);
                  } else {
                    setCurrentPage(page);
                    // loadCurrentPageEmailVerifyHistory();
                    if (!advancedFilter) {
                      setSelectAll(false);
                    }
                  }
                }}
              />
            ) : (
              <Pagination
                className="pagination-bar"
                currentPage={
                  viewSelected === true ? currentSelectedPage : currentPage
                }
                totalCount={
                  viewSelected === true
                    ? selectedData.length / 25
                    : Math.ceil((!advancedFilter ? foundContacts : noOfContact) / 25)
                }
                pageSize={1}
                onPageChange={(page) => {

                  if (viewSelected === true) {
                    setCurrentSelectedPage(page);
                    // loadCurrentPageEmailVerifyHistory();
                    setSelectAll(false);
                  } else {
                    setCurrentPage(page);
                    // loadCurrentPageEmailVerifyHistory();
                    if (!advancedFilter) {
                      setSelectAll(false);
                    }
                  }
                }}
              />
            )}
          </div>
        </div>
      </div>

      {defaultErrorMsg && defaultAlert && (
        buttonType === "warning-error" ? (
          <WarningAlert data={defaultErrorMsg} />
        ) : buttonType === "credit-message" ? (
          <WarningAlert data={defaultErrorMsg} />
        ) : (
          <Alert data={defaultErrorMsg} />
        )
      )}

    </>
  );
}
