import React from 'react';
// import { a } from 'react-router-dom';
import '../assests/css/layouts/sidenave.css';
import { useState } from "react";
import { SearchJobTitle } from '../common-files/FiltersData.js';

import Select from "react-select";


const Sidenave = () => {

    const [searchTerm, setSearchTerm] = useState("");
    const [items, setItems] = useState([
        "Doctor",
        "Engineer",
        "Director",
        "Exicutive Director",
        "Electrical Engineer",
        "Educator",
        "Software Engineer",
    ]);

    const handleSearch = (event) => {
        setSearchTerm(event.target.value);
    };

    const filteredItems = items.filter((item) =>
        item.toLowerCase().includes(searchTerm.toLowerCase())
    );
    return (
        <div>
            <nav className="sidebar">


                <ul className="nav flex-column">
                    <div className="d-flex justify-content-between">
                        <div className="Filter">
                            <p><img src="../images/filter.png" className="img-fluid" />&nbsp;Filters</p>
                        </div>
                        <div className="d-flex align-items-end">
                            <p>Reset</p>
                        </div>
                    </div>
                    <li className="nav-item">

                    </li>


                    <li className="nav-item dropdown">
                        <a
                            className="nav-link dropdown-toggle"
                            href="#"
                            id="dropdown1"
                            role="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                        >

                            <img src="../images/briefcase.png" className="img-fluid" width="21" /> <span className="class">&nbsp;  Job Titles</span> </a>

                        <div className="dropdown-menu" aria-labelledby="dropdown1">


                            <div className="app">
                                <h2>Choose your color</h2>
                                <div className="dropdown-container">
                                    <Select
                                        options=''
                                        placeholder="Select color"
                                        value=''
                                        onChange=''
                                        isSearchable={true}
                                        isMulti
                                    />
                                </div>
                            </div>


                            {/*<div className="search-input d-flex justify-content-between">
                                <input type="text" value={searchTerm} onChange={handleSearch} placeholder="Search for a job title" className="input-field" />
                                <i className="fas fa-search"></i>
                               
                            </div>
        
                            <div className="search-list">
                                <ul className="list">
                                    {filteredItems.map((item) => (
                                        <li className="list of proffesion" key={item}>{item}</li>
                                    ))}
                                </ul>
                            </div>*/}


                            <select className="selectpicker avctive form-control data-selectbox-label=STATUS">
                                <option>
                                    <ul className="dropdown-menu checkbox-menu allow-focus">
                                        <li >
                                            <label>
                                                <input type="checkbox" /> Cheese
                                            </label>
                                        </li>
                                    </ul>
                                </option>
                            </select>

                            <select className="selectpicker avctive form-control data-selectbox-label=STATUS">
                                <option disabled selected value="0">Departments & Job Function</option>
                                <option>
                                    <input type="checkbox" id="vehicle1" name="vehicle1" value="9" />
                                </option>
                                <option value="2">
                                    <input type="checkbox" id="myCheckbox" />
                                    <label htmlFor="myCheckbox">C-Suite</label></option>
                            </select>
                        </div>
                    </li>
                    <li className="nav-item dropdown">
                        <a
                            className="nav-link dropdown-toggle"
                            href="#"
                            id="dropdown2"
                            role="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                        >
                            <img src="../images/industry.png" className="img-fluid" />&nbsp; Industry
                        </a>
                        <div className="dropdown-menu" aria-labelledby="dropdown2">
                            <div className="search-input d-flex justify-content-between">
                                <input type="text" placeholder="Search Industries" className="input-field" />
                                <i className="fas fa-search"></i>
                            </div>
                            <select className="selectpicker avctive form-control data-selectbox-label=STATUS">
                                <option disabled selected value="0">Department & Job Function</option>
                                <option>
                                    <input type="checkbox" id="vehicle1" name="vehicle1" value="9" />
                                </option>
                                <option value="2">
                                    <input type="checkbox" id="myCheckbox" />
                                    <label htmlFor="myCheckbox">C-Suite</label></option>
                            </select>

                            <div className="search-input d-flex justify-content-between">
                                <input type="text" placeholder="Search Industries" className="input-field" />
                                <i className="fas fa-search"></i>
                            </div>
                        </div>

                    </li>
                    <li className="nav-item dropdown">
                        <a
                            className="nav-link dropdown-toggle"
                            href="#"
                            id="dropdown3"
                            role="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                        >
                            <img src="../images/employee.png" className="img-fluid" />&nbsp; Employees Size
                        </a>
                        <div className="dropdown-menu" aria-labelledby="dropdown3">
                            <select className="selectpicker-1 avctive form-control data-selectbox-label=STATUS">
                                <option disabled selected value="0">Departments & Job function</option>

                                <option className="selector-dropdown" value="2">
                                    <input type="checkbox" id="myCheckbox" />
                                    <label htmlFor="myCheckbox">C-Suite</label></option>
                            </select>
                        </div>
                    </li>
                    <li className="nav-item dropdown">
                        <a
                            className="nav-link dropdown-toggle"
                            href="#"
                            id="dropdown4"
                            role="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                        >
                            <img src="../images/doller.png" className="img-fluid" />&nbsp;  Revenue Size
                        </a>
                        <div className="dropdown-menu" aria-labelledby="dropdown4">
                            <select className="selectpicker-1 avctive form-control data-selectbox-label=STATUS">
                                <option disabled selected value="0">Department & Job Function</option>

                                <option className="selector-dropdown" value="2">
                                    <input type="checkbox" id="myCheckbox" />
                                    <label htmlFor="myCheckbox">C-Suite</label></option>
                            </select>
                        </div>
                    </li>

                    <li className="nav-item dropdown">
                        <a
                            className="nav-link dropdown-toggle"
                            href="#"
                            id="dropdown5"
                            role="button"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                        >
                            <img src="../images/pin.png" className="img-fluid" />&nbsp; Location

                        </a>
                        <div className="dropdown-menu" aria-labelledby="dropdown5">
                            <label className="location-search-bar">City / State / Country / ZIP</label>

                            <div className="search-input d-flex justify-content-between">
                                <input type="text" placeholder="Search for a job title" className="input-field" />
                                <i className="fas fa-search"></i>
                            </div>





                        </div>
                    </li>
                </ul>








            </nav>




        </div>

    );
};

export default Sidenave;



