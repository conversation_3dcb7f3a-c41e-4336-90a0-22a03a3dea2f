import React from "react";
import "../assests/css/layouts/demo.css";
import { useNavigate, useParams } from "react-router-dom";
import Header from "../layouts/Header";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../customer/common-files/ApiCalls.js";

const PageFour = () => {
  let data_params = useParams();
  const navigate = useNavigate();

  const goNext = () => {
    navigate("/tour-guide-saved-list/" + data_params.id);
  };

  const goBack = () => {
    navigate("/tour-guide-contacts/" + data_params.id);
  };

  const skipTour = async () => {
    const params = JSON.stringify({
      id: data_params.id,
      actionType: "SKIP",
      repeatTimeDefault: 1,
      currentRepeatedTime: 1,
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.appTourUpdate, params);
      if (res.data.status == "200") {
        navigate("/dashboard");
      }
    } catch (errors) {}
  };
  return (
    <>
      <div>
        <Header />
        <div className="examplefour">
          <img
            src="../../images/demo-image-4.png"
            class="img-fluid"
            alt="Responsive image"
            style={{ width: "100%" }}
          />
          <img
            src="../../images/demo-image-5.png"
            class="img-fluid"
            alt="Responsive image"
            style={{ width: "100%", display: "none" }}
          />
          <div>
            <p className="skiptour4" onClick={skipTour}>
              Skip Tour
            </p>
          </div>

          {/* <p className="ADD">Add</p>
          <p className="UseAddtoList">
            Use Add to List to organize and save contacts. You can add contacts
            to already saved list or create a new list.
          </p> */}

          <div className="addtbuttonss2">
            <div className="d-flex flex-row justify-content-center">
              <div>
                <button type="button" onClick={goBack} className="sortback">
                  Back
                </button>
              </div>
              <div>
                <button type="button" onClick={goNext} className="sortnext">
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PageFour;
