const getEnvConfig = () => {
    const hostname = window.location.hostname;
    let yearlyPlanIds = [];
    let monthlyPlanIds = [];

    if (hostname === "localhost") {
        yearlyPlanIds = [374575, 374579, 374583];
        monthlyPlanIds = [329894, 330220, 330881];
        return {
            apiUrl: "https://stg-rs-fe.reachstream.com/api/",
            environment: "development",
            yearlyPlanIds,
            monthlyPlanIds
        };
    } else if (hostname === "rs-fe.reachstream.com") {
        yearlyPlanIds = [374575, 374579, 374583];
        monthlyPlanIds = [329894, 330220, 330881];
        return {
            apiUrl: "https://stg-rs-fe.reachstream.com/api/",
            environment: "staging",
            yearlyPlanIds,
            monthlyPlanIds
        };
    } else if (hostname === "app.reachstream.com") {
        yearlyPlanIds = [6688279, 6688275, 6688271, 13456554];
        monthlyPlanIds = [6688324, 6688310, 6688283];
        return {
            apiUrl: "https://api-prd.reachstream.com/api/",
            environment: "production",
            yearlyPlanIds,
            monthlyPlanIds
        };
    } else {
        yearlyPlanIds = [374575, 374579, 374583];
        monthlyPlanIds = [329894, 330220, 330881];
        return {
            apiUrl: "https://stg-rs-fe.reachstream.com/api/",
            environment: "development",
            yearlyPlanIds,
            monthlyPlanIds
        };
    }
};

export const envConfig = getEnvConfig();
