@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap");

.form-box {
  max-width: 550px;
  margin: 0px 10px 0px;
  padding: 0 25px 0 25px;
  background: #ffffff;
  border: 6px solid #fff;
  /* box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px; */
  border-radius: 19px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}

.form-box h3 {
  text-align: center;
  padding: 15px 0 9px 0px;
  font-weight: 600;
  margin-top: 23px;
  color: #093d54;
}

th.borderless {
  padding: 20px;
  border: 0;
}

span.email-address p {
  margin: 0px 0 10px 0px;
}

span.password p {
  margin: 0 0 10px 0;
}

p.semi-header2 {
  text-align: center;
  font-size: 16px;
  margin: 0px 0px 14px 0;
  font-weight: 600;
}

p.semi-header2 a {
  text-decoration: none;
  color: #55c2c3;
}

p.semi-header a:hover {
  color: #007bff;
}

p.password-label {
  padding: 8px 0 0 0;
}

p.password-label a {
  text-decoration: none;
}

p.password-label a:hover {
  color: #007bff;
}

input.cp-pluss1 {
  margin: 0 auto;
  display: block;
  padding: 10px 40px 10px 40px;
  outline: 0;
  background-color: #0c243c;
  color: #fff;
  border-radius: 15px;
  border: none;
  font-family: "Roboto", sans-serif;
  cursor: pointer;
  margin-bottom: 40px;
}

.bg-color {
  background-color: #e8f7f7;
  height: auto;
  /* margin-left: 6%; */
}

/* Signup */

.saver-plane {
  margin-top: 8%;
  margin-bottom: 10%;
}

.saver-plane h3 {
  text-align: center;
  margin-top: 15px;
  font-weight: 700;
  color: #0c243c;
  margin-bottom: 18px;
}

th.borderless {
  color: #fff;
  border: none;
  border: 0;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
  border: 0;
}

.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 1rem;
  background-color: #fff;
  border-radius: 20px;
}

td.borderless {
  border: none;
}

tr.backrnd-color {
  background-color: #55c2c3;
  border-radius: 24px;
  font-size: 24px;
}

/* th.borderles span {
  background-color: #fff;
  padding: 4px 4px 7px 1px;
  border-radius: 15px;
} */

span.dollor {
  font-size: 12px;
  font-weight: 400;
}

.dollor {
  color: rgb(0, 0, 0.1);
}

table.table.table-striped {
  border-collapse: collapse;
  border-radius: 1em;
  overflow: hidden;
  box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
  max-width: 338px;
  margin: auto 11px auto auto;
}

td.text-pose {
  padding: 16px 0 15px 13px;
  font-size: 14px;
  font-weight: 600;
  font-family: "Lato", "medium";
}

td.text-center {
  font-size: 13px;
  font-weight: 600;
  padding: 17px 0 0 0px;
  width: 100px;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgb(255 255 255 / 5%);
}

tr.row-clr {
  background-color: #6b8eb126;
}

h3.start-journey {
  text-align: center;
  font-size: 27px;
  font-weight: 600;
  margin-top: 12%;
  color: #093d54;
}

input#fname {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#email {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#phone_number {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#password {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

input#cpassword {
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid #c9c9c9;
  border-radius: 5px;
  padding: 0 0px 0px 12px;
}

.spacing {
  padding: 16px 0 0 0;
}

.custom-control.custom-checkbox {
  margin-top: 0px;
}

a.Term {
  text-decoration: none;
  color: #55c2c3;
}

input#customCheck2 {
  border: 1px solid #c9c9c9;
  outline: none;
}

span.pill-button {
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
  border: none;
  background-color: #e2ebe8;
  color: rgb(0, 0, 0);
  padding: 3px 23px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 30px;
  border-radius: 33px;
  width: 39px;
  outline: none;
  margin-left: 4px;
}

.make-space {
  margin-top: 8%;
}

span.signup-errors p {
  font-size: 12px;
  padding: 2px 0 0 0px;
  color: #de350b;
  margin: 0;
}

span.month {
  vertical-align: middle;
  font-size: 9px;
  /* font-weight: 100; */
  color: #000;
  /* position: absolute; */
  left: 64px;
  top: 25px;
  margin: 15px 0 0 1px;

}

.upgrade-doller {
  display: flex;
  background-color: #fff;
  margin: 4px;
  padding: 4px 10px 4px 10px;
  border-radius: 30px;
}

.giga {
  max-width: 570px;
  margin: 0px 17px 4px;
  padding: 0 25px 0 25px;
  background: #ffffff;
  border: 6px solid #fff;
}

/* .table td,
.table th {
  padding: 15px 1px 14px 20px;
  vertical-align: top;
  border-top: 1px solid #dee2e6;
} */

.trusted-by {
  width: 304px;
  margin: auto 47px 0px auto;
}

th.borderless {
  padding: 20px px 0 15px 15px;
  border: 0;
}

th.borderles {
  width: 130px;
}

.invalid-input-text {
  /* border: 2px solid gray;*/
  width: 100%;
  height: 40px;
  outline: none;
  border: 1px solid red;
  border-radius: 5px;
  padding: 0 0px 0px 12px;

  background-color: #fff9f9;
}

.errr {
  font-size: 10px;
  color: #7e8c9c;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #000000;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.saver-plane-sign-up {
  border-collapse: collapse;
  overflow: hidden;
  margin: auto 50px 0 auto;
  margin-top: 10%;
  width: 313px;
}

.saver-plane-sign-up h3 {
  text-align: center;
  color: #093d54;
  font-weight: 600;
  margin-bottom: 14px;
  margin: 26px 0 1rem 2rem;
}

.carousel-item {
  position: relative;
  display: none;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.6s ease;
  transition: transform 0.6s ease, -webkit-transform 0.6s ease;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
  margin-bottom: 10%;
}

.carousel-control-prev-icon {
  visibility: hidden;
}

.carousel-control-next-icon {
  visibility: hidden;
}

.carousel-inner .carousel-item {
  transition: -webkit-transform 2s ease;
  transition: transform 2s ease;
  transition: transform 2s ease, -webkit-transform 2s ease;
}

.carousel {
  position: relative;
  transition-delay: 0.01s;
  margin-bottom: 12rem;
}

#slideshow {
  width: 323px;
  margin: 5px 18px 0 auto;
  overflow: hidden;
}

.slide_container {
  width: 1200px;
  /* 300 x 4 */
  position: relative;
  -webkit-animation: slide 11s ease-in-out infinite;
  -moz-animation: slide 11s ease-in-out infinite;
  -ms-animation: slide 11s ease-in-out infinite;
  -o-animation: slide 11s ease-in-out infinite;
  animation: slide 11s ease-in-out infinite;
}

.slide_container section {
  float: left;
}

.slide_container:hover {
  animation-play-state: paused;
  -webkit-animation-play-state: paused;
}

@-o-keyframes slide {
  0% {
    right: 100;
  }

  25% {
    left: 100px;
  }

  50% {
    left: 600px;
  }

  75% {
    right: 900px;
  }

  100% {
    left: 100;
  }
}

@keyframes slide {
  0% {
    left: 0;
  }

  25% {
    left: -200px;
  }

  50% {
    left: -350px;
  }

  75% {
    left: -100px;
  }

  100% {
    left: 0;
  }
}




/* 25-08-2023 */

button.sendverificationcode {
  padding: 9px 0 9px 0;
  background-color: #093D54;
  color: #F6F6F6;
  font-size: 13px;
  border: 0;
  margin-top: 15px;
  border-radius: 11px;
  cursor: pointer;
}

.verifyemailaddress {
  padding: 9px 0 9px 0;
  background-color: #093D54;
  color: #F6F6F6;
  font-size: 13px;
  border: 0;
  margin-top: 15px;
  border-radius: 11px;
  cursor: pointer;
}



/* 28-08-2023 */

button.sendvcode {
  padding: 10px 3rem 10px 3rem;
  background-color: #093D54;
  color: #F6F6F6;
  font-size: 14px;
  border: 0;
  margin-top: 15px;
  border-radius: 12px;
}

.verifyemail {
  padding: 10px 3rem 10px 3rem;
  background-color: #093D54;
  color: #F6F6F6;
  font-size: 14px;
  border: 0;
  margin-top: 15px;
  border-radius: 12px;
}

.signup-message p {
  font-size: 12px;
  padding: 2px 0 0 0px;
  color: black;
  margin: 0;
}

input.input-field-check {
  position: absolute;
  top: 46px;
  width: 240px;
  border: 1px solid #6DE1A4;
  padding: 7px 0px 6px 2rem;
  border-radius: 4px;
}

img.check-icon {
  position: absolute;
  top: 50px;
  margin: 0px 0px 0px 0px;
  padding: 0px;
}


::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #6DE1A4;

}

@media (max-width: 915px) {
  button.sendverificationcode {
    padding: 10px 5em 10px 5em;
    margin: 0 0 0 4px;
  }

  .verifyemailaddress {
    padding: 10px 5em 10px 5em;
    margin: 0 0 0 4px;
  }
}


@media only screen and (min-width: 530px) and (max-width: 1165px) {


  .saver-plane {
    margin-left: 0;
  }

  table.table.table-striped {
    width: 295px;
  }

  .form-box {
    width: 440px;
    margin: auto;
    display: block;
  }

  /* 
.zeta {
  margin: auto;
} */

  .zeta {
    width: 440px;
    margin: auto;
  }

  button.sendverificationcode {
    padding: 10px 0 10px 0px;
    margin: 13px 0 0 0;
  }

  .verifyemailaddress {
    padding: 10px 0 10px 0px;
    margin: 13px 0 0 0;
  }
}


.continue2 {
  background-color: #F5F5F5;
  padding: 10px 0 10px 0px;
  border-radius: 5px;
}

tr.super-saver-table {
  background-color: #55C2C3;
}


p.price {
  font-size: 24px;
  color: #fff;
  font-weight: 600;
  margin: 0;
  padding: 6px 6px 3px 4px;
}

th.super-saver-header {
  width: 108px;
}

s.dolllers {
  font-size: 17px;
  color: #e9e9e9;
  display: block;
  text-align: center;
  padding: 0 0 0 8px;
}


.upgrade-doller-2 {
  background-color: #fff;
  padding: 5px 0 5px 10px;
  margin-bottom: 2px;
  border-radius: 30px;
}

span.plan-per-month {
  font-size: 10px;
  margin: 0 0 0 1px;
}