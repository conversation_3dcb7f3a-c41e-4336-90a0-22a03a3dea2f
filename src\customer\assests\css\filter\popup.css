.custom-mt-40 {
  margin-top: -85px;
}

body {
  font-family: "Lato", sans-serif;
}

.modal-header {
  margin-left: 35%;
  color: #093d54;
  opacity: 100%;
}

button.close {
  color: #55c2c3 !important;
}

.close:focus,
.close:hover {
  color: #55c2c3;
  text-decoration: none;
  /* opacity: .75; */
}

.modal-header .close {
  padding: 1rem;
  margin: -25px -19px 0 0;
}

.formcolor {
  color: #000000;
  background-color: #f1ffff;
  background: #f1ffff 0% 0% no-repeat padding-box;
  border: 1px solid #d3eff0;
  border-radius: 5px;
  opacity: 1;
}

input#FirstName {
  color: #000000;
  background-color: #f1ffff;
  text-align: left;
  font: normal normal medium 14px/44px Lato;
  letter-spacing: 0.14px;
  text-transform: capitalize;
  opacity: 1;
  padding: 2px 0px 4px 8px;
}

input#Lastname {
  color: #000000;
  background-color: #f1ffff;
  text-align: left;
  font: normal normal medium 14px/44px Lato;
  letter-spacing: 0.14px;
  text-transform: capitalize;
  opacity: 1;
  padding: 2px 0px 4px 8px;
}

input#phoneNumber {
  color: #000000;
  background-color: #f1ffff;
  text-align: left;
  font: normal normal medium 14px/44px Lato;
  letter-spacing: 0.14px;
  text-transform: capitalize;
  opacity: 1;
  padding: 2px 0px 4px 8px;
}

input#email {
  color: #000000;
  background-color: #f1ffff;
  text-align: left;
  font: normal normal medium 14px/44px Lato;
  letter-spacing: 0.14px;
  text-transform: capitalize;
  opacity: 1;
  padding: 2px 0px 4px 8px;
}

input#InputEmail1 {
  padding: 2px 0px 4px 8px;
}

.btn-primary {
  top: 466px;
  left: 671px;
  width: 98px;
  height: 38px;
  background: #093d54 0% 0% no-repeat padding-box;
  border-radius: 10px;
  opacity: 1;
  color: #fff;
  /* background-color: #007bff; */
  /* border-color: #007bff; */
}

.btn-primary:hover {
  color: #fff;
  background-color: #093d54 !important;
  border-color: #093d54 !important;
}

.btn-primary {
  color: #fff;
  background-color: #093d54;
  border-color: #093d54;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
  height: 39px;
  font-weight: 500;
  font-size: 32px;
  letter-spacing: 0px;
  color: #093d54;
  opacity: 1;
  margin-top: 0px;
}

.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0 #093d54 !important;
}

.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #093d54;
  border-color: #093d54;
}

.btn-primary.disabled,
.btn-primary:disabled {
  color: #fff;
  background-color: #093d54 !important;
  border-color: #093d54 !important;
  opacity: inherit;
}

/* .modal-dialog {
  max-width: 800px;
  margin-top: 50px;
} */

.modal-dialog.modal-dialog-centered {
  max-width: 735px;
}

.modal-dialog.modal-dialog-centered.modal-xl {
  margin-top: 3%;
}

/* center the modal horizontally */
.modal-dialog.modal-xl {
  margin: 0 auto;
}

span a {
  top: 589px;
  left: 533px;
  width: 204px;
  height: 21px;
  text-align: left;
  /* font: normal normal bold 17px/37px Lato; */
  letter-spacing: 0px;
  color: #093d54;
  opacity: 1;
}

i.fas.fa-envelope {
  margin-right: 10px;
  color: #55c2c3;
}

i.fas.fa-phone {
  margin-right: 7px;
  color: #55c2c3;
  /* margin-left: 10px; */
}

form.contact-form {
  padding: 0 13px 0 13px;
}

.modal-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 0;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}

.footer-modal {
  text-align: center;
  color: #000000;
  font-size: 17px;
}

/* footer {
    background-color: transparent;
    padding: 20px;
    text-align: center;
} */

.contact-info {
  display: inline-block;
}

.contact-info p {
  display: inline;
  margin-right: 20px;
}

.contact-info p:last-child {
  margin-right: 0;
}

p.Reach-us {
  font-size: 17px;
  color: #000;
  margin: 0;
  padding: 0 0 5px 0;
  font-weight: 500;
}

span#emailid {
  margin-left: -13px;
}

span#require_phone {
  margin-left: -13px;
}

button:focus {
  outline: 1px dotted;
  outline: 0px auto -webkit-focus-ring-color !important;
}

label.label-names {
  font-size: 14px;
  color: #000;
  -webkit-text-stroke: thin;
}

.spacer {
  padding: 12px 0 0 0px;
}

.modall-content {
  background-color: #fff;
  width: 300px;
  border-radius: 0.4rem;
}

.modal-content.modal-size {
  width: 600px;
  margin-top: 7rem;
}
.contact-info a {
  font-size: 17px;
  padding: 0 0 0 11px;
  font-weight: 500;
  text-decoration: none;
  color: #093d54;
}

button#btnSubmit {
  font-size: 17px;
  margin-top: 20px;
  margin-bottom: 20px;
}

footer {
  background-color: #f8f8f8;
  padding: 20px;
  text-align: center;
  border-bottom-left-radius: 0.4rem;
  border-bottom-right-radius: 0.4rem;
}
.modal-2-content {
  background-color: #fff;
  margin: auto;
  width: 700px;
  border-radius: 5px;
}

.modal-3-content {
  background-color: #fff;
  margin: auto;
  /* width: 700px; */
  border-radius: 5px;
}

span.cross {
  padding: 0px 6px 0px 0px;
}
.roundedTag {
  border: 1px solid;
  border-radius: 30px;
  padding: 3px 6px 3px 6px;
  position: relative;
  line-break: 18px;
  font-size: 10px;
  width: auto;
  white-space: normal;
  vertical-align: top;
  display: -webkit-inline-box;
  line-height: 11px;
  background-color: #fff;
  margin: 4px;
}