import { useContext, useEffect, useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import '../../customer/assests/css/style.css';
import { axiosPost, AxiosPostBearer } from '../../customer/common-files/ApiCalls.js';
import { ApiName } from '../../customer/common-files/ApiNames.js';

import { faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import '../../customer/assests/css/password-eye.css';

import axios from 'axios';
import { Helmet } from "react-helmet-async";
import { useSearchParams } from 'react-router-dom';
import UserData from '../../customer/common-files/ContextData.js';
import Analytics from '../../utils/analyticsTracking';
import Mixpanel from '../../utils/mixpanel';
import DashboardContext from '../common-files/ContextDashboard.js';
import DWINTracker from '../common-files/DWINTracker.js';
import { clearStore } from '../common-files/indexedDBUtils.js';
import { useGoogleLoginLogic, useLinkedInLoginLogic, useMicrosoftLoginLogic } from '../common-files/SocialMediaAuth.js';
import UseTabStore from '../common-files/useGlobalState.js';
import Footer from '../layouts/Footer';

const SignIn = (props) => {
	const { dataDC, setDataDC } = useContext(DashboardContext);
	const [searchParams] = useSearchParams();
	const user_id = searchParams.get('user_id');
	const { login, google_access_Token } = useGoogleLoginLogic();
	const { Microsoftlogout, microsoftAuth, MicrosoftAccessToken } = useMicrosoftLoginLogic();
	const { linkedInAuth, linkedinResponse } = useLinkedInLoginLogic();
	//const {addAllDataCombine}=useContext(UserData);
	const { dataCombine } = useContext(UserData);
	const [user, setUser] = useState(dataCombine.user);

	const [tokenSent, setTokenSent] = useState(null);
	const [showPassword, setShowPassword] = useState(false);

	const [passwordErr, setPasswordErr] = useState();
	const [emailErr, setEmailErr] = useState();
	const [email, setEmail] = useState('');
	const [password, setPassword] = useState('');

	const [validEmail, setValidEmail] = useState(false);
	const [validPassword, setValidPassword] = useState(false);
	const [changeColor, setChangeColor] = useState(true);
	const [passwordColor, setPasswordColor] = useState(true);

	const [isActive, setIsActive] = useState(false);
	const [message, setMessage] = useState('');
	const [tokenExpired, setTokenExpired] = useState(false);
	const [submitted, setSubmitted] = useState(false);
	const [blockedDomain, setBlockedDomain] = useState([]);
	const [extToken, setExtToken] = useState(null);
	const navigate = useNavigate();
	const location = useLocation();
	const {
		marketplaceToken,
		setButtonType,
		setDefaultAlert,
		setDefaultErrorMsg
	} = UseTabStore();
	// Helper function to mark user as having completed registration
	const markRegistrationComplete = (email) => {
		if (!email) return;
		
		try {
			// Identify the user in Mixpanel
			Mixpanel.identify(email);
			
			// Set properties to indicate this user has completed registration
			// These properties can be used in Mixpanel to filter out completed registrations
			// when viewing "Incomplete Signup" events
			Mixpanel.people.set({
				'Registration Status': 'Complete',
				'Registration Completed': true,
				'Registration Completed Date': new Date().toISOString()
			});
			
			// Track a separate event that can be used for funnel analysis
			Mixpanel.track('Registration Completed', {
				email: email,
				completion_time: new Date().toISOString(),
				completion_source: 'signin'
			});
		} catch (error) {
			// Silently handle errors - don't disrupt the login flow
		}
	};

	// Helper function to capture user location - browser geolocation + IP fallback
	const captureUserLocation = async () => {
		// Try browser geolocation first
		if (navigator.geolocation) {
			try {
				// Request user permission for location (shows browser popup)
				navigator.geolocation.getCurrentPosition(
					// Success callback
					async (position) => {
						const latitude = position.coords.latitude;
						const longitude = position.coords.longitude;
						
						// Get detailed location information using reverse geocoding
						try {
							// Use OpenStreetMap Nominatim API for reverse geocoding (free, no API key needed)
							const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1`);
							const data = await response.json();
							
							// Extract address components
							const address = data.address || {};
							
							// Store location in localStorage with detailed information
							const locationData = {
								latitude,
								longitude,
								accuracy: position.coords.accuracy,
								city: address.city || address.town || address.village || address.hamlet || '',
								region: address.state || address.county || '',
								country: address.country || '',
								country_code: address.country_code || '',
								postal_code: address.postcode || '',
								formatted_address: data.display_name || '',
								source: 'browser_geolocation',
								timestamp: new Date().toISOString()
							};
							
							localStorage.setItem('user_location', JSON.stringify(locationData));
							
							// Track the location in Mixpanel
							Mixpanel.people.set({
								'$latitude': latitude,
								'$longitude': longitude,
								'$city': locationData.city,
								'$region': locationData.region,
								'$country': locationData.country,
								'Location Source': 'browser_geolocation',
								'Location Accuracy': position.coords.accuracy,
								'Location Capture Time': new Date().toISOString(),
								'Postal Code': locationData.postal_code,
								'Formatted Address': locationData.formatted_address
							});
							
							// Also track a separate event for analytics
							Analytics.track('Location Captured', {
								latitude: latitude,
								longitude: longitude,
								city: locationData.city,
								region: locationData.region,
								country: locationData.country,
								source: 'browser_geolocation',
								accuracy: position.coords.accuracy,
								formatted_address: locationData.formatted_address,
								capture_time: new Date().toISOString()
							});
						} catch (geocodeError) {
							// If reverse geocoding fails, store basic location data
							const locationData = {
								latitude,
								longitude,
								accuracy: position.coords.accuracy,
								source: 'browser_geolocation',
								timestamp: new Date().toISOString()
							};
							
							localStorage.setItem('user_location', JSON.stringify(locationData));
							
							// Track basic location in Mixpanel
							Mixpanel.people.set({
								'$latitude': latitude,
								'$longitude': longitude,
								'Location Source': 'browser_geolocation',
								'Location Accuracy': position.coords.accuracy,
								'Location Capture Time': new Date().toISOString()
							});
							
							// Also track a separate event for analytics
							Analytics.track('Location Captured', {
								latitude: latitude,
								longitude: longitude,
								source: 'browser_geolocation',
								accuracy: position.coords.accuracy,
								geocoding_error: 'Failed to get address details',
								capture_time: new Date().toISOString()
							});
						}
					},
					// Error callback - fall back to IP geolocation
					async (error) => {
						// Silently fall back to IP-based location
						await fetchIPBasedLocation();
					},
					// Options
					{
						enableHighAccuracy: false, // Use low accuracy for faster response
						timeout: 5000,           // Wait max 5 seconds
						maximumAge: 24 * 60 * 60 * 1000 // Cache for 24 hours
					}
				);
			} catch (error) {
				// Silently fall back to IP-based location
				await fetchIPBasedLocation();
			}
		} else {
			// Browser doesn't support geolocation, use IP-based fallback
			await fetchIPBasedLocation();
		}
	};
	
	// IP-based geolocation fallback
	const fetchIPBasedLocation = async () => {
		try {
			// Try ipinfo.io first (more reliable and provides detailed information)
			const response = await fetch('https://ipinfo.io/json');
			const data = await response.json();
			
			if (data && data.loc) {
				// ipinfo returns location as "latitude,longitude"
				const [latitude, longitude] = data.loc.split(',').map(coord => parseFloat(coord));
				
				// Store location in localStorage with all available details
				const locationData = {
					latitude,
					longitude,
					city: data.city || '',
					region: data.region || '',
					country: data.country || '',
					postal_code: data.postal || '',
					timezone: data.timezone || '',
					ip_address: data.ip || '',
					isp: data.org || '',
					formatted_address: `${data.city || ''}, ${data.region || ''}, ${data.country || ''}`,
					source: 'ip_geolocation',
					provider: 'ipinfo.io',
					timestamp: new Date().toISOString()
				};
				
				localStorage.setItem('user_location', JSON.stringify(locationData));
				
				// Track the location in Mixpanel
				Mixpanel.people.set({
					'$latitude': latitude,
					'$longitude': longitude,
					'$city': data.city,
					'$region': data.region,
					'$country': data.country,
					'Location Source': 'ip_geolocation',
					'Location Provider': 'ipinfo.io',
					'Location Capture Time': new Date().toISOString(),
					'Postal Code': data.postal,
					'Timezone': data.timezone,
					'ISP': data.org
				});
				
				// Also track a separate event for analytics
				Analytics.track('Location Captured', {
					latitude: latitude,
					longitude: longitude,
					city: data.city,
					region: data.region,
					country: data.country,
					source: 'ip_geolocation',
					provider: 'ipinfo.io',
					ip_address: data.ip,
					timezone: data.timezone,
					isp: data.org,
					formatted_address: `${data.city || ''}, ${data.region || ''}, ${data.country || ''}`,
					capture_time: new Date().toISOString()
				});
				
				return;
			}
		} catch (ipinfoError) {
			// If ipinfo.io fails, try alternative service
			try {
				// Alternative: geojs.io (also free, no API key needed)
				const geoJsResponse = await fetch('https://get.geojs.io/v1/ip/geo.json');
				const geoJsData = await geoJsResponse.json();
				
				if (geoJsData && geoJsData.latitude && geoJsData.longitude) {
					const latitude = parseFloat(geoJsData.latitude);
					const longitude = parseFloat(geoJsData.longitude);
					
					// Store location in localStorage
					const locationData = {
						latitude,
						longitude,
						city: geoJsData.city || '',
						region: geoJsData.region || '',
						country: geoJsData.country || '',
						country_code: geoJsData.country_code || '',
						timezone: geoJsData.timezone || '',
						ip_address: geoJsData.ip || '',
						formatted_address: `${geoJsData.city || ''}, ${geoJsData.region || ''}, ${geoJsData.country || ''}`,
						source: 'ip_geolocation',
						provider: 'geojs.io',
						timestamp: new Date().toISOString()
					};
					
					localStorage.setItem('user_location', JSON.stringify(locationData));
					
					// Track the location in Mixpanel
					Mixpanel.people.set({
						'$latitude': latitude,
						'$longitude': longitude,
						'$city': geoJsData.city,
						'$region': geoJsData.region,
						'$country': geoJsData.country,
						'Location Source': 'ip_geolocation',
						'Location Provider': 'geojs.io',
						'Location Capture Time': new Date().toISOString(),
						'Timezone': geoJsData.timezone
					});
					
					// Also track a separate event for analytics
					Analytics.track('Location Captured', {
						latitude: latitude,
						longitude: longitude,
						city: geoJsData.city,
						region: geoJsData.region,
						country: geoJsData.country,
						source: 'ip_geolocation',
						provider: 'geojs.io',
						ip_address: geoJsData.ip,
						timezone: geoJsData.timezone,
						formatted_address: `${geoJsData.city || ''}, ${geoJsData.region || ''}, ${geoJsData.country || ''}`,
						capture_time: new Date().toISOString()
					});
					
					return;
				}
			} catch (geoJsError) {
				// If all services fail, log silently
				console.warn('All geolocation services failed');
			}
		}
	};
	
	function handleFilters(name, value) {
		// console.log(dataDC,name,value);
		setDataDC({ ...dataDC, [name]: value });
	}

	// Utility function to force update the Mixpanel profile
	const forceUpdateMixpanelProfile = () => {
		const userData = JSON.parse(localStorage.getItem('user'));
		
		if (!userData || !userData.email) {
			return false;
		}
		
		const firstName = userData.firstName || '';
		const lastName = userData.lastName || '';
		const fullName = (firstName && lastName) 
			? `${firstName} ${lastName}`
			: firstName || lastName || '';
		
		// Directly update Mixpanel profile
		Mixpanel.identify(userData.email);
		Mixpanel.people.set({
			'$email': userData.email,
			'$first_name': firstName,
			'$last_name': lastName,
			'$name': fullName,
			'Full Name': fullName,
			'Company': userData.companyName || '',
			'Manual Update': true,
			'Last Manual Update Time': new Date().toISOString(),
			'Registration Status': 'Complete',
			'Registration Completed': true
		});
		
		// Mark registration as complete
		markRegistrationComplete(userData.email);
		
		return true;
	};
	
	// Expose the function to the window object for manual debugging
	if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
		window.forceUpdateMixpanelProfile = forceUpdateMixpanelProfile;
		window.fixMixpanelProfile = Mixpanel.fixUserProfile.bind(Mixpanel);
		window.cleanupMixpanelDeviceIds = Mixpanel.cleanupDeviceIds.bind(Mixpanel);
		window.purgeMixpanelProfile = Mixpanel.purgeAndRecreateProfile.bind(Mixpanel);
		window.removeDeviceIds = Mixpanel.removeDeviceIdsFromDistinctId.bind(Mixpanel);
	}

	// Track page view when component mounts
	useEffect(() => {
		// Gather device and location info
		const deviceInfo = {
			browser: navigator.userAgent,
			device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
			viewport_size: `${window.innerWidth}x${window.innerHeight}`,
			language: navigator.language || navigator.userLanguage
		};
		
		// Get UTM parameters from localStorage
		const utmParams = localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {};
		
		// Track page view
		Analytics.track('Page Viewed', {
			page_name: 'Sign In',
			page_path: window.location.pathname,
			page_title: document.title,
			referrer: document.referrer || 'direct',
			...deviceInfo,
			...utmParams
		});
	}, []);

	useEffect(() => {
		const queryParams = new URLSearchParams(location.search);
		const extToken = queryParams.get("ext_token");

		if (extToken) {
			clearSessionStorage();
			clearLocalStorage();
		}
	}, [location, navigate]);

	useEffect(() => {
		setButtonType("");
		setDefaultAlert("");
		setDefaultErrorMsg("");

		// Prevent further execution if user data already exists in sessionStorage
		let userData = JSON.parse(localStorage.getItem('user'));

		// If user data is not in sessionStorage, try to get it from localStorage and set it to sessionStorage
		if (!userData) {
			userData = JSON.parse(localStorage.getItem('user'));
			if (userData) {
				localStorage.setItem('user', JSON.stringify(userData));
			}
		}
		// If userData exists, proceed with the logic
		if (userData) {
			const { email, token, role } = userData;
			if (email && token && role) {
				// Set up filters
				handleFilters("loggedUserEmail", email);
				handleFilters("token", token);

				// Store user data into sessionStorage
				localStorage.setItem('user', JSON.stringify({ email, token, role }));

				// Navigate based on user role or current pathname
				if (role === 'admin') {
					navigate('/admin', {
						state: { email, token },
					});
				} else if (window.location.pathname.split("/")[1] === 'download-data') {
					navigate('/saved-list', {
						state: { email, token },
					});
				} else {
					navigate('/home');
				}
			}
		}
	}, []);

	useEffect(() => {
		const params = {
			"method": "POST"
		}
		let result = axios.post(ApiName.activeDomainBlockList, params)
			.then(function (response) {
				// const token = JSON.parse(response.data.data);
				if (response.data.status == 200) {
					let data = JSON.parse(response.data.data);
					// Extract the "domain" property from each object and store in an array
					const domainArray = data.map(item => item.domain);
					setBlockedDomain(domainArray);
				}
			})

	}, [])

	useEffect(() => {
		const urlParams = new URLSearchParams(window.location.search);
		const paramValue = urlParams.get('message');
		const ext_token = urlParams.get("ext_token");
		ext_token ? setExtToken(ext_token) : setExtToken(null);
		switch (paramValue) {
			case 'token invalid':
				setTokenExpired(true);
				setMessage("Token Invalid");
				break;
			case 'Your_account_has_been_successfully_activated.':
				setIsActive(true);
				setMessage('Your account has been activated.');
				break;
			case 'Already_Activated':
				setIsActive(true);
				setMessage('Already Activated');
				break;
			case 'Invalid_token':
				setTokenExpired(true);
				setMessage("Token Invalid");
				break;
			case "This_email_isn't_registered_with_us._Sign up":
				setIsActive(true);
				setMessage("This email isn't registered with us.Sign up");
				break;
			default:
				break;
		}
		// Use the paramValue as needed
		// console.log('Param value:', paramValue);
	}, []);

	const clearSessionStorage = async () => {
		return new Promise((resolve) => {
			sessionStorage.clear();
			resolve();
		});
	};

	const clearLocalStorage = async () => {
		return new Promise((resolve) => {
			localStorage.clear();
			resolve();
		});
	};

	const disableMessage = (event) => {
		event.preventDefault();
		setIsActive(false);
		setTokenExpired(false);
		setMessage('');
		window.location.href = '/';
	}

	const onChangeEmail = (event) => {

		let email = event.target.value;
		setEmail(email.toLowerCase());

		// Construct dynamic RegExp for disallowed domains
		const disallowedDomainsRegExp = new RegExp(`^.+@((?!${blockedDomain.join('|')}).)+$`);
		let result = disallowedDomainsRegExp.test(email);

		if (result) {
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			let checkDomain = emailRegex.test(email);

			if (!checkDomain) {
				setEmailErr('Please enter a valid business email address');
				setValidEmail(false);
				setChangeColor(false);
			} else {
				setValidEmail(true);
				setEmailErr('');
				setChangeColor(true);
			}
		} else {
			setEmailErr('Please enter a valid business email address');
			setValidEmail(false);
			setChangeColor(false);
		}
	}

	const onChangePassword = (event) => {

		let password = event.target.value.trim();
		setPassword(password);

		let checkPassword = isStrongPassword(password);
		if (!checkPassword) {
			setPasswordErr('Please enter a valid password');
			setValidPassword(false);
			setPasswordColor(false);
		} else {
			setPasswordErr('');
			setValidPassword(true);
			setPasswordColor(true);
		}

	}

	function isStrongPassword(password) {
		const minLength = 8;
		const hasUppercase = /[A-Z]/.test(password);
		const hasLowercase = /[a-z]/.test(password);
		const hasNumber = /\d/.test(password);
		const hasSymbol = /[\W_]/.test(password);
		const isDictionaryWord = /password|123456|qwerty|letmein|monkey|football/.test(password);

		if (password.length < minLength) {
			return false;
		}
		if (!hasUppercase || !hasLowercase || !hasNumber || !hasSymbol) {
			return false;
		}
		if (isDictionaryWord) {
			return false;
		}
		return true;
	}

	const signinAsCustomer = (allData) => {
		let result = axiosPost(ApiName.loginAsCustomer, allData)
			.then(function (response) {
				const token = JSON.parse(response.data.data);
				setSubmitted(true);
				if (response.data.status == 200) {
					// Set loginAsCustomer flag
					localStorage.setItem('loginAsCustomer', 'true');
					
					const requestBody = {
						"method": "GET",
						// Add device_user_session_ignore flag for all customer login requests
						"device_user_session_ignore": true
					};
					
					AxiosPostBearer(ApiName.userData, requestBody, token)
						.then((async (responsedata) => {
							if (responsedata?.status === 200) {
								const userdata = JSON.parse(responsedata?.data?.data);
								
								setEmail(userdata?.email);
								handleFilters("loggedUserEmail", userdata?.email);
								handleFilters("token", token);
								localStorage.setItem('user', JSON.stringify({
									email: userdata?.email,
									firstName: userdata?.firstName || '',
									lastName: userdata?.lastName || '',
									companyName: userdata?.companyName || '',
									token: token,
									role: response.data.role
								}));

								// Force navigation to dashboard for customer login
								navigate('/home', {
									state: {
										email: userdata?.email,
										token: token,
									}
								});
								
								// Enhanced login tracking with detailed information
								Analytics.trackSignIn('email', { 
									email: userdata?.email,
									role: 'user',
									auth_method: 'standard',
									login_source: 'customer_login',
									login_as_customer: true,
									user_segment: response.data.role || 'standard',
									login_time: new Date().toISOString(),
									device_info: {
										device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
										browser: navigator.userAgent,
										viewport_size: `${window.innerWidth}x${window.innerHeight}`,
										language: navigator.language || navigator.userLanguage
									},
									utm_params: localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {}
								});
								
								// Force profile properties to update in Mixpanel by directly using Mixpanel.identify
								Mixpanel.identify(userdata?.email);
								
								// Create full name for display
								const fullName = (userdata?.firstName && userdata?.lastName) 
									? `${userdata.firstName} ${userdata.lastName}`
									: userdata?.firstName || userdata?.lastName || '';
								
								// Update user properties in Mixpanel with first name, last name, and company name
								Mixpanel.people.set({
									'$email': userdata?.email || '',
									'$first_name': userdata?.firstName || '',
									'$last_name': userdata?.lastName || '',
									'$name': fullName,
									'Full Name': fullName,
									'Company': userdata?.companyName || '',
									'Role': 'User',
									'Last Login': new Date(),
									'Login Method': 'standard',
									'Login Source': 'customer_login',
									'Login As Customer': true,
									'Registration Status': 'Complete',
									'Registration Completed': true
								});
								
								// Mark registration as complete
								markRegistrationComplete(userdata?.email);
							}
						}))
						.catch(function(error) {
							console.error("Error fetching user data:", error);
							setSubmitted(false);
							setEmailErr("Failed to login as customer. Please try again.");
							setValidEmail(false);
							setChangeColor(false);
						});
				}
			}).catch(function (errors) {
				setSubmitted(false);
				if (errors.response.data.status == 400 && errors.response.data.message == "Account is not active") {
					setEmailErr("The account is inactive");
					setValidEmail(false);
					setChangeColor(false);
				} else if (errors.response.data.status == 400 && errors.response.data.message == "Enter a valid email") {
					setEmailErr("This email isn't registered with us. Sign up");
					setValidEmail(false);
					setChangeColor(false);
				} else if (errors.response.data.status == 400 && errors.response.data.message == "Marketplace Token Is Invalid") {
					setEmailErr("Marketplace Token Is Invalid");
					setValidEmail(false);
					setChangeColor(false);
				} else {
					if (errors.response.data.status == 400 && errors.response.data.message == "Enter a valid password") {
						setPasswordErr(errors.response.data.message);
						setValidPassword(false);
						setPasswordColor(false);
					} else {
						setPasswordErr('');
						setValidPassword(true);
						setPasswordColor(true);
					}
				}
			});
	}

	const signinLogic = (allData) => {
		let result = axiosPost(ApiName.signIn, allData)
			.then(function (response) {
				const token = JSON.parse(response.data.data);
				setSubmitted(true);
				if (response.data.status == 200) {
					const requestBody = {
						"method": "GET"
					};
					AxiosPostBearer(ApiName.deviceLogin, {}, token).then((res) => {
						if (res && "status" in res) {
							if (res.data.status == 200) {
								AxiosPostBearer(ApiName.userData, requestBody, token)
									.then((async (responsedata) => {
										if (responsedata?.status === 200) {
											const userdata = JSON.parse(responsedata?.data?.data);
											
											setEmail(userdata?.email);
											handleFilters("loggedUserEmail", userdata?.email);
											handleFilters("token", token);
											// Store complete user data including firstName, lastName and companyName
											localStorage.setItem('user', JSON.stringify({ 
												email: userdata?.email, 
												firstName: userdata?.firstName || '',
												lastName: userdata?.lastName || '',
												companyName: userdata?.companyName || '',
												token: token, 
												role: response.data.role 
											}));
											
											if (response.data.role == "admin") {
												navigate('/admin', {
													state: {
														email: userdata?.email,
														token: token,
													}
												});
												Analytics.trackSignIn('email', { 
													email: userdata?.email,
													role: 'admin'
												});
												
												// Force profile properties to update in Mixpanel by directly using Mixpanel.identify
												Mixpanel.identify(userdata?.email);
												
												// Create full name for display
												const adminFullName = (userdata?.firstName && userdata?.lastName) 
													? `${userdata.firstName} ${userdata.lastName}`
													: userdata?.firstName || userdata?.lastName || '';
												
												// Update admin user properties in Mixpanel
												Mixpanel.people.set({
													'$email': userdata?.email || '',
													'$first_name': userdata?.firstName || '',
													'$last_name': userdata?.lastName || '',
													'$name': adminFullName,
													'Full Name': adminFullName,
													'Company': userdata?.companyName || '',
													'Role': 'Admin',
													'Last Login': new Date(),
													'Login Method': 'standard',
													'Registration Status': 'Complete',
													'Registration Completed': true
												});
												
												// Mark registration as complete
												markRegistrationComplete(userdata?.email);
												
											} else if (window.location.pathname.split("/")[1] == "download-data") {
												navigate('/saved-list', {
													state: {
														email: userdata?.email,
														token: token,
													}
												});
											} else {
												navigate('/home', {
													state: {
														email: userdata?.email,
														token: token,
													}
												});
												
												// Enhanced login tracking with detailed information
												Analytics.trackSignIn('email', { 
													email: userdata?.email,
													role: 'user',
													auth_method: 'standard',
													login_source: 'direct_login',
													login_as_customer: false,
													user_segment: response.data.role || 'standard',
													login_time: new Date().toISOString(),
													device_info: {
														device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
														browser: navigator.userAgent,
														viewport_size: `${window.innerWidth}x${window.innerHeight}`,
														language: navigator.language || navigator.userLanguage
													},
													utm_params: localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {}
												});
												
												// Force profile properties to update in Mixpanel by directly using Mixpanel.identify
												Mixpanel.identify(userdata?.email);
												
												// Create full name for display
												const userFullName = (userdata?.firstName && userdata?.lastName) 
													? `${userdata.firstName} ${userdata.lastName}`
													: userdata?.firstName || userdata?.lastName || '';
												
												// Update user properties in Mixpanel with first name, last name, and company name
												Mixpanel.people.set({
													'$email': userdata?.email || '',
													'$first_name': userdata?.firstName || '',
													'$last_name': userdata?.lastName || '',
													'$name': userFullName,
													'Full Name': userFullName,
													'Company': userdata?.companyName || '',
													'Role': 'User',
													'Last Login': new Date(),
													'Login Method': 'standard',
													'Login Source': 'direct_login',
													'Registration Status': 'Complete',
													'Registration Completed': true
												});
												
												// Mark registration as complete
												markRegistrationComplete(userdata?.email);
											}
										}
									}));
							}
						}
					})
				}
			}).catch(function (errors) {
				setSubmitted(false);
				//console.log(errors);
				if (errors.response.data.status == 400 && errors.response.data.message == "Account is not active") {
					setEmailErr("The account is inactive");
					setValidEmail(false);
					setChangeColor(false);

				} else if (errors.response.data.status == 400 && errors.response.data.message == "Enter a valid email") {
					setEmailErr("This email isn't registered with us. Sign up");
					setValidEmail(false);
					setChangeColor(false);
				} else if (errors.response.data.status == 400 && errors.response.data.message == "Marketplace Token Is Invalid") {
					setEmailErr("Marketplace Token Is Invalid");
					setValidEmail(false);
					setChangeColor(false);
				} else {
					if (errors.response.data.status == 400 && errors.response.data.message == "Enter a valid password") {
						setPasswordErr(errors.response.data.message);
						setValidPassword(false);
						setPasswordColor(false);

					} else {
						setPasswordErr('');
						setValidPassword(true);
						setPasswordColor(true);
					}
				}
			});
	}

	const onSubmitHandler = (event) => {
		event.preventDefault();
		
		// Track sign in attempt
		Analytics.trackFeatureUsage('Sign In Attempt', { email: email });
		clearStore("AdvancedFilterData", "Company")
		UseTabStore.getState().clearStorage();

		if (validEmail && validPassword) {
			setSubmitted(true);
			
			// Create object with login credentials
			const userData = {
				email: email.toLowerCase(),
				password: password,
				...(user_id && { user_id: user_id }),
				...(extToken && { ext_token: extToken }),
				...(marketplaceToken && { marketplaceToken: marketplaceToken })
			};

			// Remove loginAsCustomer flag if not using user_id
			if (!user_id) {
				localStorage.removeItem('loginAsCustomer');
				// Call signinLogic directly with the userData object
				signinLogic(userData);
				
				// Capture user location after login attempt
				// This runs in parallel and won't block the login process
				captureUserLocation();
			} else {
				// Set loginAsCustomer flag in localStorage
				localStorage.setItem('loginAsCustomer', 'true');
				// Call signinAsCustomer with the userData object
				signinAsCustomer(userData);
				
				// Capture user location after login attempt
				// This runs in parallel and won't block the login process
				captureUserLocation();
			}
		}
	};

	const handleGoogleLogin = () => {
		// Track Google login attempt - use only Analytics which uses Mixpanel internally
		Analytics.track('Google Login Attempt');
		// Remove duplicate tracking call
		login()
			.then(result => {
				// Google login successful
			})
			.catch(error => {
				// Google login failed
			});
	};

	useEffect(() => {
		if (google_access_Token) {
			let data = {
				"provider_name": "google",
				"provider_token": google_access_Token,
				...(extToken && { "ext_token": extToken }),
				...(marketplaceToken && { "marketplaceToken": marketplaceToken })
			};

			localStorage.setItem('signin-type', 'google');
			signinLogic(data);
		}
	}, [google_access_Token, extToken, marketplaceToken]);


	useEffect(() => {
		if (MicrosoftAccessToken) {
			let data = {
				"provider_name": "microsoft",
				"provider_token": MicrosoftAccessToken,
				...(extToken && { "ext_token": extToken }),
				...(marketplaceToken && { "marketplaceToken": marketplaceToken })
			};

			localStorage.setItem('signin-type', 'microsoft');
			signinLogic(data);
		}
	}, [MicrosoftAccessToken, extToken, marketplaceToken]);


	const microSoftAuth = () => {
		// Track Microsoft login attempt - use only Analytics which uses Mixpanel internally
		Analytics.track('Microsoft Login Attempt');
		// Remove duplicate tracking call
		microsoftAuth(); // Call the microsoft login function
	};

	const linkedInLogin = () => {
		// Track LinkedIn login attempt - use only Analytics which uses Mixpanel internally
		Analytics.track('LinkedIn Login Attempt');
		linkedInAuth.linkedInLogin();
	};
	useEffect(() => {
		if (localStorage.getItem('linkedin-code')) {
			const linkedincode = localStorage.getItem('linkedin-code');
			localStorage.removeItem('linkedin-code');

			let data = {
				"provider_name": "linkedin",
				"provider_token": linkedincode,
				...(extToken && { "ext_token": extToken }),
				...(marketplaceToken && { "marketplaceToken": marketplaceToken })
			};

			localStorage.setItem('signin-type', 'linkedin');
			signinLogic(data);
		}
	}, [linkedinResponse, extToken, marketplaceToken]);
	
	useEffect(() => {
		Analytics.trackPageView('/sign-in');
	}, []);

	// Update the debug effect that runs after sign-in
	// Effect that runs once after sign-in to ensure profile data is correctly sent to Mixpanel
	useEffect(() => {
		// Check if user data exists in localStorage
		const userData = JSON.parse(localStorage.getItem('user'));
		
		// If user is logged in, force update their profile in Mixpanel
		if (userData && userData.email) {
			// First, try to remove any device IDs from the distinct_id list
			Mixpanel.removeDeviceIdsFromDistinctId(userData.email);
			
			// Wait a bit before continuing with other profile updates
			setTimeout(() => {
				// Create fullName from firstName and lastName
				const firstName = userData.firstName || '';
				const lastName = userData.lastName || '';
				const fullName = (firstName && lastName) 
					? `${firstName} ${lastName}`
					: firstName || lastName || '';
				
				// Direct call to Mixpanel to update profile
				Mixpanel.identify(userData.email);
				Mixpanel.people.set({
					'$email': userData.email,
					'$first_name': firstName,
					'$last_name': lastName,
					'$name': fullName,
					'Full Name': fullName,
					'Company': userData.companyName || '',
					'Last Manual Update': new Date().toISOString(),
					'Registration Status': 'Complete',
					'Registration Completed': true,
					'Using Email ID Only': true
				});
				
				// Fix any issues with the user profile in Mixpanel
				setTimeout(() => {
					Mixpanel.purgeAndRecreateProfile();
				}, 1000);
				
				// Mark registration as complete
				markRegistrationComplete(userData.email);
				
				// Attempt to capture user location if not already done
				if (!localStorage.getItem('user_location')) {
					captureUserLocation();
				}
			}, 1000);
		}
	}, [submitted, email]); // This will run when sign-in submission completes

	// Add a new effect to handle social login profile updates
	// Effect to ensure social login also updates profile data properly
	useEffect(() => {
		// Check if we just completed a social login
		const signinType = localStorage.getItem('signin-type');
		if (signinType && ['google', 'microsoft', 'linkedin'].includes(signinType)) {
			// Get user data from localStorage
			const userData = JSON.parse(localStorage.getItem('user'));
			
			// If user data exists, update Mixpanel profile
			if (userData && userData.email) {
				// Create fullName from firstName and lastName
				const firstName = userData.firstName || '';
				const lastName = userData.lastName || '';
				const fullName = (firstName && lastName) 
					? `${firstName} ${lastName}`
					: firstName || lastName || '';
				
				// Direct call to Mixpanel to update profile
				Mixpanel.identify(userData.email);
				Mixpanel.people.set({
					'$email': userData.email,
					'$first_name': firstName,
					'$last_name': lastName,
					'$name': fullName,
					'Full Name': fullName,
					'Company': userData.companyName || '',
					'Login Method': signinType,
					'Last Login': new Date().toISOString(),
					'Registration Status': 'Complete',
					'Registration Completed': true,
					'Social Login': true
				});
				
				// Mark registration as complete
				markRegistrationComplete(userData.email);
				
				// Track social login event with detailed user info
				Analytics.trackSignIn(signinType, {
					email: userData.email,
					firstName: firstName,
					lastName: lastName,
					fullName: fullName,
					company: userData.companyName || '',
					role: userData.role || 'user',
					login_method: signinType
				});
				
				// Clear the signin-type flag to avoid duplicate processing
				localStorage.removeItem('signin-type');
			}
		}
	}, [google_access_Token, MicrosoftAccessToken, linkedinResponse]);

	return (
		<>
			<Helmet>
				<title>Sign in to ReachStream</title>
				<meta name="description" content="Create an account or sign into ReachStream to start building custom B2B prospecting lists today." />
			</Helmet>
			<DWINTracker />
			<div className="container-fluid pl-2 pr-2">
				<div className="row">
					<div className="col-lg-6 col-md-12 image-container">
						<div className="rs-logo-img">
							<img src="./images/r-logo.png" width="60" className="img-fluid" alt="" />
						</div>
						<div className="make-img-center">
							<img src="./images/group-50970.png" width="370" className="img-fluid" alt="" />
						</div>
					</div>

					<div className="col-lg-6 col-md-12">
						{isActive
							? (<div className="main_cont">
								<div className="d-flex  justify-content-between">

									<div className="d-flex flex-column">
										<div className="out">
											<img src="./images/promocode.png" width="20px" /><span className="Congratulations">Congratulations!</span><span className="my-account">{message}</span>
										</div>
									</div>

									<div className="d-flex  flex-column ">
										<div className="multiply_by mouse-pointer" onClick={disableMessage}>
											<img src="./images/cancel.png" width="7px" />
										</div>
									</div>
								</div>

							</div>
							) : <div className="margin-top-14"></div>}
						{tokenExpired
							? (<div className="main_cont">
								<div className="d-flex  justify-content-between">

									<div className="d-flex flex-column">
										<div className="out">
											<img src="./images/promocode.png" width="20px" /><span className="Congratulations">Sorry!</span><span className="my-account">{message}</span>
										</div>
									</div>

									<div className="d-flex  flex-column ">
										<div className="multiply_by mouse-pointer" onClick={disableMessage}>
											<img src="./images/cancel.png" width="7px" />
										</div>
									</div>
								</div>

							</div>
							) : <></>}
						<div className='form-box2'>
							<form className="form-content" onSubmit={onSubmitHandler}>
								<h3>Sign In</h3>
								<p className="semi-header1">Don't have an account?<span className="manage"></span><Link to="sign-up/ice-breaker-plan">Sign Up</Link></p><br />

								<div className="continue2">
									<div className="d-flex flex-row justify-content-around">
										<div className="with">
											<p>Continue with :</p>
										</div>
										<div className="social" onClick={handleGoogleLogin}>
											<img src="../images/googgle.png" className="img-fluid" width="40" />
										</div>
										<div className="social" onClick={microSoftAuth}>
											<img src="../images/microsoft.png" className="img-fluid" width="40" />
										</div>
										<div className="social" onClick={linkedInLogin}>
											<img src="../images/linkkkedin.png" className="img-fluid" width="40" />
										</div>
									</div>
								</div>

								<h6 className="horizontall"><span className="horback">Or</span></h6>

								<span className="email-address"><p>Email Address</p></span>
								{changeColor
									? (<input type="text" className="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" onChange={onChangeEmail} value={email} name="email" autoFocus autoComplete="off" />)
									: <input type="text" className="invalid-input-text" aria-describedby="emailHelp" onChange={onChangeEmail} value={email} name="email" autoComplete="off" />}

								{ }
								{!validEmail ? (
									<span className="email-error-message">{emailErr && <p>{emailErr}</p>}</span>
								) : (
									<></>
								)}
								<div className="space-0">
								</div>
								<span className="password"><p>Password</p></span>

								<div className="password-input">
									{passwordColor
										? (<input type={showPassword ? 'text' : 'password'} className="form-control" id="exampleInputPassword1" name="password" onChange={onChangePassword} value={password} autoComplete="off" />)
										: (<input type={showPassword ? 'text' : 'password'} className="invalid-input-text" name="password" onChange={onChangePassword} value={password} autoComplete="off" />)
									}
									<button type="button" onClick={() => setShowPassword(!showPassword)}>
										<FontAwesomeIcon icon={showPassword ? faEye : faEyeSlash}
											style={{ pointerEvents: "none", color: "#55C2C3" }}
										/>
									</button>
								</div>


								{!validPassword ? (
									<span className="email-error-message">{passwordErr && <p>{passwordErr}</p>}</span>
								) : (
									<></>
								)}
								<p className="lgp"><Link to="forgot-password" className="password-label">Forgot Password?</Link></p><br />
								<span>
									{!validEmail || !validPassword || submitted ? (
										<input
											type="button"
											value={!submitted ? "Sign In" : "Processing"}
											className="cp-pluss cust-disabled"
										/>
									) : (
										<input type="submit" value="Sign In" className="cp-pluss" />
									)}
								</span>
								<div className="dummy-class">
								</div>

							</form>
						</div>
					</div>

					<div className="col-md-1"></div>
				</div>



			</div>
			<Footer />
		</>

	)
}
export default SignIn;
