
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap');
.body{
    background-color: #E8F7F7;
    height: auto;
}

.card {
    max-width: 30rem;
    border-radius: 32px;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    border: 0;
    margin: auto;
}


.card h3 {
    text-align: center;
    margin-top: 30px;
    color: #093D54;
    margin-bottom: 15px;
    font-size: 24px;
}
/* img.img-top {
    max-width: 440px;
    margin-top: 0px;
} */
.card-1 {
    max-width: 28rem;
    border-radius: 22px;
    box-shadow: rgb(0 0 0 / 10%) 0px 4px 12px;
    border: 0;
    margin: auto;
    height: 27rem;
    margin-top: 3%;
    background-color: #FFF;
}

.card-1 h3 {
    text-align: center;
     font-weight: 600; color: #093D54; padding:14px 0 14px 0;
    }

.receive-link a {
    text-decoration: none;
    color: #55C2C3;
}

.shifted {
    margin-top: 3%;
}


p.semi-header {
    text-align: center;
    font-size: 18px;
    margin-bottom: 8px;
    color: #000;
    padding: 0 28px 0 28px;
    font-weight: 600;
}

p.email-link a {
    font-size: 16px;
    text-decoration: none;
    color: #55C2C3;
}