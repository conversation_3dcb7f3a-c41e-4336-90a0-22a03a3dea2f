@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    /* Regular */
}

code {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
}

p.enrich-company {
    text-align: center;
    color: #093D54;
    margin: 19px 0 14px 0;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
}


.main-body {
    /* min-height: 100vh; */
    /* background: #EDF0F4;
    background: linear-gradient(241deg, rgba(237, 240, 244, 1) 0%, rgba(244, 243, 244, 1) 50%); */
    /* margin: 0 0 0 0; */
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}

.data-enrichment p {
    font-size: 18px;
    font-weight: 600;
    margin: 0px 0 0 18px;
    padding: 10px 0 0 0px;
    font-family: 'Poppins', sans-serif;

}

span.enrich-back img {
    margin: 16px 4px 0 0;
}

p.data-enrich-ment {
    font-size: 16px;
    margin: 0;
    padding: 15px 0 0 15px;
    font-weight: 700;
    color: #093d54;
}

.csv-tabs {
    padding: 0;
    /* border-bottom: 2px solid #DDDDDD; */
    margin: 0px 20px 0px 0px;
}

.enrcih-tab-button {
    cursor: pointer;
    border-bottom: 3px solid transparent;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 14px;
    color: #000;
}

.enrich-tab-button {
    font-size: 14px;
    padding: 0 20px 0px 20px;
    font-weight: 600;
    cursor: pointer;
}

.active-tab {
    border-bottom: 2px solid #0d6efd;
    color: #000;
    cursor: pointer;
    padding: 0 20px 0px 20px;
    font-size: 12px;
    font-weight: 600;
}

.white-boxx {
    background-color: #ffffff;
    width: 100%;
    max-width: 420px;
    margin: auto;
    padding: 20px;
    border-radius: 10px;
    box-sizing: border-box;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}


p.enrich-contacts {
    text-align: center;
    color: #093D54;
    margin: 14px 0 14px 0;
    font-weight: 600;

    font-family: 'Poppins', sans-serif;
}

p.information {
    text-align: center;
    font-size: 12px;
    color: #000;
    font-family: 'Poppins', sans-serif;

}

button.select-csv {
    margin: 30px auto 10px auto;
    display: block;
    width: 180px;
    font-size: 12px;
    background-color: #fff;
    border: 1px solid #DDDDDD;
    color: #151417;
    padding: 4px 0 4px 0;
    cursor: pointer;
    outline: none;
    border-radius: 4px;
    font-family: 'Poppins', sans-serif;

}


button.select-csv:hover {
    background-color: #c6dafa;
    border: 1px solid #146EF6;
}



/* Animation: overlay */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.4);
    transition: opacity 0.3s ease;
}

.fade-in {
    opacity: 1;
}

.fade-out {
    opacity: 0;
}

/* Animation: modal scale */
.custom-modal {
    background: #fff;
    /* background: linear-gradient(234deg, rgba(237, 240, 244, 1) 0%, rgba(244, 243, 244, 1) 50%); */
    width: 600px;
    max-width: 95%;
    border-radius: 8px;
    padding: 20px 20px 0px 20px;
    position: relative;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    transform: scale(1);
    transition: transform 0.3s ease;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}

.scale-in {
    transform: scale(1);
}

.scale-out {
    transform: scale(0.9);
}

/* Remaining styling unchanged... */

span.enrich-close-button {
    border: 0;
    border-radius: 26px;
    cursor: pointer;
    font-size: 14px;
    outline: none;
    padding: 0;
    position: absolute;
    right: 5px;
    top: 10px;
}

p.enrich-popup-header {
    margin: 0 0 6px 0;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
}

.enrich-border {
    border-bottom: 2px solid #115EA3;
    position: relative;
    bottom: 6px;
}

.enrich-custom-modal-body-1 {
    background-color: #fff;
    padding: 20px 20px 20px 20px;
    border-radius: 5px;
    border: 1px solid #EEEEEE;
    height: 350px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}


/* Hide default file input */
.invisible-file-input {
    display: none;
}

.hidden-file {
    display: none;
}

/* .choose-border {
    border: 2px dashed #d9dfe7;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    background-color: #f9fbfd;
} */

/* .custom-file-trigger {
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
    font-weight: 500;
} */

.file-name {
    font-size: 12px;
    margin: 10px 0 0 0;
    color: #093D54;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
}

.replace-file {
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
    font-size: 12px;
    font-weight: 400;
    font-family: 'Poppins', sans-serif;
}

.replace-file:hover {
    text-decoration: none;
}

button.next-button {
    background-color: #115EA3;
    color: white;
    padding: 0px 25px;
    border: 1px solid #115EA3;
    border-radius: 18px;
    font-weight: 400;
    cursor: pointer;
    outline: none;
    font-size: 14px;
    position: relative;
    margin: 0 0 0px 0;
    font-family: 'Poppins', sans-serif;
}

.next-button:disabled {
    /* background-color: #ccc; */
    cursor: not-allowed;
    opacity: 0.3;
}


/* Make label look like plain text or a link */
.custom-file-trigger {
    cursor: pointer;
    color: #007bff;
    text-decoration: underline;
    font-size: 12px;
    font-weight: 400;
    width: 140px;
    margin: auto;
    display: block;
    text-decoration: none;
    font-family: 'Poppins', sans-serif;

}

.custom-file-trigger:hover {
    text-decoration: none;
}

.choose-border {
    /* border: 1px dotted #007bff; */
    border: 2px dashed #C2C8CE;
    border-radius: 10px;
    padding: 45px 24px;
    width: 100%;
    max-width: 300px;
    background: #EDF0F4;
    background: linear-gradient(238deg, rgba(237, 240, 244, 1) 0%, rgba(244, 243, 244, 1) 50%);
    text-align: center;
    margin: 10px auto;
}

p.drag-and-drop {
    font-size: 14px;
    margin: 18px 0 0 0;
    color: #093D54;
    font-family: 'Poppins', sans-serif;
}

span.the-or-plan {
    color: #093D54;
}

p.uolpade-your-csv-here {
    text-align: center;
    margin: 20px 0 5px 0;
    font-weight: 500;
    font-family: 'Poppins', sans-serif;
}

p.select-csv {
    text-align: center;
    font-size: 12px;
    margin: 0 0 6px 0;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
}

.display-csv-file {
    background-color: #fff;
    padding: 10px 20px 10px 20px;
    border-radius: 5px;
    box-shadow: rgba(0, 0, 0, 0.20) 0px 1px 4px;
}

p.referred-csv {
    margin: 3px 0 0px 6px;
    font-size: 14px;
    color: #000;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
}

p.reupload-csv {
    margin: 2px 0px 0 5px;
    font-size: 14px;
    color: #093D54;
    font-family: 'Poppins', sans-serif;
}


.scrollable-table-wrapper {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 5px;
    margin: 12px 0 0 0;
    padding: 0px 10px 10px 10px;
    background-color: #fff;
    /* box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px; */
}

/* Fix table layout so headers and body align */
.custom-scroll-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.custom-scroll-table thead {
    background-color: #ebf6ff;
    position: sticky;
    top: 0;
    z-index: 10000;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
}

.custom-scroll-table tbody tr {
    background-color: #ffffff;
    /* white background */
}

.custom-scroll-table thead th:first-child {
    border-top-left-radius: 5px;
}

.custom-scroll-table thead th:last-child {
    border-top-right-radius: 5px;
}

.custom-scroll-table th,
.custom-scroll-table td {
    padding: 8px 10px;
    border-bottom: 1px solid #ddd;
    text-align: left;
    min-width: 300px;
    font-size: 14px;
    font-weight: 400;
    font-family: 'Poppins', sans-serif;

}

.custom-scroll-table tbody tr:last-child td:first-child {
    border-bottom-left-radius: 5px;
}

.custom-scroll-table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 5px;
}

td.table-last-column {
    font-size: 12px;
    color: #C2C8CE;
}


.dropdown-container {
    /* width: 250px; */
    position: relative;
    font-family: "Poppins", sans-serif;
}

.dropdown-selected {
    padding: 5px 10px;
    border: 1px solid #ccc;
    border-radius: 6px;
    background-color: #fff;
    cursor: pointer;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
    font-family: "Poppins", sans-serif;

}

.arrow {
    font-size: 12px;
}

.dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 115px;
    overflow-y: auto;
    background-color: #fffefe;
    border: 1px solid #ccc;
    border-radius: 6px 6px 6px 6px;
    z-index: 10;
    margin-top: 4px;
    padding: 0;
}

.dropdown-option {
    padding: 5px 10px;
    cursor: pointer;
    font-size: 10px;
}

.dropdown-option:hover {
    background-color: #f0f8ff;
}



.dotted-line {
    border-top: 1px dotted #999;
    height: 1px;
    width: 100%;
    margin: 8px 0;
}

.dotted-row {
    border: none !important;
}

.dotted-row td {
    border: none !important;
    padding: 10px 0 0px 0;
}

button.csv-back-button {
    background-color: #fff;
    color: #000;
    padding: 2px 16px;
    border: 1px solid #115EA3;
    border-radius: 18px;
    font-weight: 500;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.5s ease, color 0.5s ease;
    font-family: 'Poppins', sans-serif;
}



.option-card {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px 0 0 0;
    text-align: center;
    width: 115px;
    height: 124px;
    position: relative;
    transition: border-color 0.3s, color 0.3s;
    cursor: pointer;
    background-color: #fff;
    margin: 3rem auto 3rem;
}

.option-card img {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
    filter: grayscale(100%);
    opacity: 0.5;
    transition: filter 0.3s, opacity 0.3s;
}

.option-card.active {
    border: 1px solid #146EF6;
}

.option-card.active img {
    filter: none;
    opacity: 1;
}

.option-card .form-check-input {
    position: absolute;
    top: 10px;
    right: 10px;
    pointer-events: none;
}

.option-card .label-text {
    font-size: 14px;
    color: #C2C8CE;
}

.option-card.active .label-text {
    color: #093D54;
    font-weight: 500;
}

p.what-whould-you-like {
    margin: 0;
    text-align: center;
    font-size: 14px;
    color: #151417;
    font-family: 'Poppins', sans-serif;

}

p.charged {
    text-align: center;
    font-size: 12px;
    margin: 7px 0 2rem 0;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;

}

p.enriching-contacts {
    text-align: center;
    color: #151417;
    font-weight: 500;
    margin: 0 0 8px 0;
    font-family: 'Poppins', sans-serif;

}

p.notify-you {
    text-align: center;
    font-size: 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
}

span.make-it-bold {
    font-weight: 600;
}


button.upload-another-csv {
    margin: 30px auto 30px auto;
    display: block;
    width: 180px;
    font-size: 12px;
    background-color: #fff;
    border: 1px solid #DDDDDD;
    color: #151417;
    padding: 4px 0 4px 0;
    cursor: pointer;
    outline: none;
    border-radius: 4px;
    font-family: 'Poppins', sans-serif;
font-weight: 400;
}

p.csv-companies-enrich {
    text-align: center;
    font-weight: 400;
    margin: 1rem 0 8px 0;
    color: #151417;
    font-family: 'Poppins', sans-serif;

}

p.reachstream-database {
    text-align: center;
    font-size: 10px;
    font-family: 'Poppins', sans-serif;
}

.enrich-img {
    width: 250px;
}

img.img-fluid.custom-width {
    width: 14px;
    height: 12px;
    margin: 6px 0 0 0;
}

.scrollable-table-wrapper-box {
    padding: 1px 0 0 0px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    margin: 10px 0 0 0;
    border-radius: 8px;
}



.smooth-tab-container {
    display: flex;
    border-bottom: 1px solid #dddddd;
    gap: 8px;
  }
  
  .smooth-tab-button {
    position: relative;
    padding: 10px 20px;
    font-size: 12px;
    background: transparent;
    border: 1px solid transparent;
    border-radius: 6px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    font-family: 'Poppins', sans-serif;
    overflow: hidden;
  }
  
  .smooth-tab-button::before {
    content: "";
    position: absolute;
    inset: 4px;
    background-color: #f0f0f0;
    border-radius: 4px;
    z-index: 0;
    opacity: 0;
    transition: opacity 0.3s ease;
    box-shadow: inset 0px 0px 6px rgba(0, 0, 0, 0.06);
  }
  
  .smooth-tab-button:hover::before {
    opacity: 1;
  }
  
  .smooth-tab-button:hover {
    color: #000;
    border-color: #c1c1c1;
  }
  
  .smooth-tab-button span,
  .smooth-tab-button img {
    position: relative;
    z-index: 1;
  }
  
  .tab-active {
    color: #007bff;
    border-color: #007bff;
    background-color: #f5f9ff;
  }
  
  .tab-active::before {
    opacity: 1;
  }
  