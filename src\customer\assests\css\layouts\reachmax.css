p.ReachMax {
    font-size: 18px;
    color: #093D54;
    font-weight: 600;
    margin: 8px 0 6px 4px;
}

p.ReachMax img {  
    cursor: pointer;
}

span.successmessage2 img {
    margin: 4px 0 0 0;
}

p.Back {
    font-size: 18px;
    color: #093D54;
    font-weight: 600;
    cursor: pointer;
}

.fisrt-layer-1 {
    background-color: #F9F9F9;
    border-radius: 16px;
    width: 444px;
    margin: auto;
    padding: 0 0px 0 25px;
}

.second-layer-2 {
    background-color: #F9F9F9;
    border-radius: 16px;
    margin: 40px auto;
    width: 590px;
    padding: 8px 0 10px 0;
}

.thired-layer-3 {
    /* background-color: #F9F9F9; */
    border-radius: 16px;
    padding: 5px 0 5px 0;
    margin: 5px 0 0 0;
}

.fourth-layer-4{
    background-color: #F9F9F9;
    border-radius: 16px;
    margin: 8px auto;
    /* height: 444px; */
}


.share-your-link-share p {
    padding: 20px 0 0px 0px;
    color: #093D54;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
}


.share-your-link p {
    margin: 0;
    padding: 6px 0 0px 20px;
    color: #093D54;
    font-size: 15px;
    font-weight: 600;
}

.YourReferrals p {
    margin: 0;
    padding: 23px 0 15px 18px;
    color: #093D54;
    font-size: 15px;
    font-weight: 600;
}



.share-your-link-earnings {
    margin: 0;
    padding: 2px 0 0 33px;
    color: #093D54;
    font-size: 17px;
    font-weight: 600;
}



.copy-button {
    border-left: 0;
    font-size: 14px;
    padding: 0 6px 0px 10px;
    border-top: 1px solid #55C2C3;
    border-bottom: 1px solid #55C2C3;
    border-right: 1px solid #55C2C3;

}

input.apis2::placeholder {
    color: #000;
    font-size: 14px;
}

input.apis::placeholder {
    color: #55C2C3;
    font-size: 14px;

}

input.apis {
    padding: 3px;
    margin: 0;
    border: 1px solid #55C2C3;
    /* color: #55C2C3; */
    font-size: 14px;
 
}

input.apis2 {
    padding: 3px;
    margin: 0;
    border: 1px solid #55C2C3;
    color: #55C2C3;
    font-size: 14px;
    /* border-right: 0; */
}

p.invalid {
    font-size: 10px;
    color: #DE350B;
    margin: -2px 0 1px 0;
    padding: 0;
}

.refralemail {
    border: 1px solid #6DE1A4;
    background-color: #EEFFF6;
    width: 216px;
    padding: 0 0 0 0;
    margin: 0px;
    border-radius: 6px;
}

.refralemail p {
    font-size: 10px;
    padding: 0px;
    margin: 0;
}

span.grey-back {
    border-radius: 27px;
    background-color: #EEEEEE;
    font-size: 12px;
    padding: 1px 15px 1px 15px;
    cursor: copy;
}


.Vl-3 {
    border-left: 1px solid #939CA1;
    margin-left: 13px;
    padding: 0 0px 0px 15px;
    margin: 4px 0 4px -8px;
}


.share-your-link-2 p {
    margin: 10px 0 12px 0;
    padding: 25px 0 0px 0px;
    color: #093D54;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
}


.apis::placeholder {
    color: rgb(165, 165, 165);
    font-size: 12px;
    padding: 0 0 0px 5px;
}

button.send-to-account {
    border: 0;
    background-color: #093D54;
    color: #fff;
    font-size: 14px;
    margin-top: 0px;
    padding: 3px 26px 4px 26px;
    border-radius: 8px;
    cursor: pointer;
    outline: none;
}

p.Separate {
    font-size: 10px;
    color: #000;
    padding: 0px 0 0px 0px;
}


p.include {
    font-size: 10px;
    color: #55C2C3;
    padding: 1px 0px 0px 0rem;
    text-align: end;
    cursor: pointer;
    margin: 0 16px 0 8px;
    font-weight: 600;
}

.Referred {
    border: 1px solid #55C2C3;
    border-radius: 10px;
}

.Referred p {
    font-size: 14px;
    padding: 0px 10px 0 10px;
    margin: 8px 0 8px 0;
    font-weight: 600;
}

span.accesible {
    padding: 0 6px 0 6px;
    color: #55C2C3;
    font-weight: 600;
}





#customers td,
#customers th {
    /* border: 1px solid #ddd; */
    padding: 8px;
}




.table-headers {
    background-color: #EBF6FF;
    padding: 9px;
}


.customers tr {
    background: #fff;
    padding: 15px;
    border-radius: -5px;
    box-shadow: inset 0 0 6px #afafaf38, 3px 3px 5px 0 #0c20350d;
    border-left: 0;
    border-right: 0;
}

.table-headers th {
    background-color: #EBF6FF;
    margin-top: 12px !important;
    padding: 10px;
    font-size: 14px;
}

/* tr.table-data td {
    font-size: 14px;
    font-weight: 400;
    color: #000;
    font-variant: traditional;
} */

p.view-all {
    padding: 0;
    margin: 4px 0 20px 17px;
    font-size: 14px;
    color: #55C2C3;
    cursor: pointer;
}

.outerlayer {
    border: 1px solid #bdbdbd;
    border-radius: 8px;
    background-color: #fff;
}

.credits-earned p {
    text-align: center;
    margin: 13px 0 2px 0;
    color: #000000;
    font-weight: 600;
}


p.number {
    font-size: 22px;
    margin: 0 0 6px 0px;
    color: #55C2C3;
}

a.mouse-pointer {
    color: #55c2c3;
    text-decoration: underline;
}

button.redeem {
    border: 0;
    background-color: #093D54;
    color: #fff;
    padding: 5px 17px 5px 17px;
    border-radius: 8px;
    cursor: pointer;
    margin: 0 8px 0 0px;
    outline: none;
}

.share-your-link-3 p {
    margin: 0;
    padding: 10px 0 6px 20px;
    color: #093D54;
    font-size: 17px;
    font-weight: 600;
}

.para p {
    font-size: 12px;
    padding: 11px 20px 6px 20px;
    margin: 0px 0 0px 0px;
}

.the-terms {
    padding: 8px 0 8px 20px;
    font-size: 14px;
    border: 1px solid #BFBFBF;
    margin: 0 30px 0px 20px;
    border-radius: 6px;
}

.the-termss {
    padding: 0px 0 8px 20px;
    font-size: 14px;
    /* border: 1px solid #BFBFBF; */
    margin: 0 30px 0px 20px;
    border-radius: 6px;
}

.the-terms p {
    padding: 0;
    margin: 0;
    font-size: 12px;
}

a.the-condition {
    text-decoration: underline;
    color: #55C2C3;
}




span.horizontal1 {
    border-bottom: 2px solid #093d54;
    width: 34px;
    margin: auto;
    display: inline-table;
    margin-bottom: 4px;
    color: #093d54;
    margin-left: 18px;
    position: absolute;
    margin-top: 4px;
}

span.horizontal2 {
    border-bottom: 2px solid #093d54;
    width: 34px;
    margin: auto;
    display: inline-table;
    margin-bottom: 4px;
    color: #093d54;
    margin-left: 20px;
    position: absolute;
    margin-top: 4px;
}

span.horizontal4 {
    border-bottom: 2px solid #093d54;
    width: 34px;
    margin: auto;
    display: inline-table;
    margin-bottom: 4px;
    color: #093d54;
    margin-left: 2px;
    position: absolute;
    margin-top: 4px;
}

span.horizontal5 {
    border-bottom: 2px solid #093d54;
    width: 34px;
    margin: auto;
    display: inline-table;
    margin-bottom: 4px;
    color: #093d54;
    margin-left: 20px;
    position: absolute;
    margin-top: 4px;
}


td.data-goes-here {
    padding: 10px 0 10px 10px;
    font-size: 13px;
}



.table-data-small {
    /* padding: 11px 0 11px 10px; */
    box-shadow: inset 0px 0px 2px 2px #afafaf38, 0px 0px 0px 0 #0c20350d;
    margin-top: 11px;
    margin-bottom: 30px;
    border-radius: 4px;
    background-color: #fff;
    /* box-shadow: inset 0px 0px 3px 6px #afafaf38, 0px 0px 0px 0 #0c20350d; */
}

table.your-refrel-list {
    border-collapse: separate;
    border-spacing: 0 7px;
    width: 100%;
    padding: 0 22px 0 22px;
}

.YourReferrals {
    /* Add any specific styling for YourReferrals if needed */
}

button.openable {
    font-size: 10px;
    border: 0;
    margin: 0;
    /* padding: 4px; */
    background-color: transparent;
    outline: none;
}

.included {
    background-color: #fff;
    border: 1px solid #55C2C3;
    border-radius: 5px;
    position: absolute;
    z-index: 1000;
    width: 450px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    top: -74px;
    left: 34rem;
    padding: 5px 0 5px 0px;
}

.included2 {
    background-color: #fff;
    border: 1px solid #55C2C3;
    border-radius: 5px;
    position: absolute;
    top: 14rem;
    left: 8rem;
    width: initial;
    box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
    margin: 0 1rem 0 0;
}

p.included-data {
    margin: 0;
    padding: 8px 0 0 20px;
    font-size: 12px;
    font-weight: 600;
    color: #093D54;
}

p.letter {
    font-size: 12px;
    padding: 0px 20px 0 20px;
    margin: 10px 0 10px 0;
}

p.letter2 {
    font-size: 12px;
    padding: 0px 20px 0 20px;
    margin: 10px 0 10px 0;
    font-weight: 600;
}

a.freemiums {
    color: #55C2C3;
    text-decoration: underline;
}

ul.participate li {
    font-size: 12px;
    margin: 0 30px 8px 4px;
}


a.myAccount {
    color: #55C2C3;
    text-decoration: none;
}


span.the-condition {
    color: #55C2C3;
    text-decoration: underline;
    cursor: pointer;
}


/* error popup */

p.grey-avetar {
    margin: 0;
    padding: 0 0px 0 7px;
    font-size: 34px;
    font-weight: 600;
    color: red;
}

p.anerror {
    margin: 10px 0 0 0px;
    padding: 0;
    font-size: 20px;
    color: #093D54;
    font-weight: 600;
}

p.anerror-red {
    margin: 9px;
    padding: 0;
    font-size: 20px;
    color: red;
    font-weight: 600;
}

p.anerror-green {
    margin: 9px;
    padding: 0;
    font-size: 20px;
    color: #4CAF50;
    font-weight: 600;
}

.lorem p {
    font-size: 14px;
    color: #939CA1;
    text-align: center;
    margin: 10px 0 6px 1rem;
    padding: 0px 20px 0 20px;
}

.upgrdbutton {
    text-align: center;
    padding: 10px 0 16px 0;
}

.upgrdbutton button {
    border: 0;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    background-color: #093D54;
    border-radius: 10px;
    cursor: pointer;
    margin: 0px 0 0 13px;
    padding: 10px 20px;
}

span.successmessage2 {
    border: 1px solid #6DE1A4;
    font-size: 10px;
    padding: 0px 18px 0px 3px;
    border-radius: 8px;
    margin: auto;
    display: block;
    background-color: #EEFFF6;
    width: fit-content;
}


.cust-error-input {
    border: 1px solid red !important;
    color: red;
}

.goBack {
    padding: 0px 8px 0 0px;
    cursor: pointer;
}

.pagination-bar {
    margin: 22px 2rem 7px 0;
}

p.Updnginprs {
    padding: 0 0 0 24px;
    font-size: 14px;
}

/* new update */

p.copyandsharethelink {
    text-align: center;
    font-size: 16px;
    color: #093D54;
    font-weight: 600;
    padding: 0;
    margin: 0px 0 4px 0;
}

.checksocialmedia {
    border: 1px solid #DEDEDE;
    border-radius: 5px;
    text-align: center;
    padding: 10px 15px 10px 15px;
    margin: 0px 14px 0 14px;
}

.checksocialmediaa {
    border: 1px solid #DEDEDE;
    border-radius: 5px;
    text-align: center;
    padding: 10px 25px 10px 25px;
    margin: 0 10px 0 10px;
}

p.postvia {
    font-size: 9px;
    margin: 7px 0 -4px 0;
    color: #093D54;
    font-weight: 600;
}

.learnmore p {
    font-size: 14px;
    color: #55C2C3;
    padding: 0 0 15px 20px;
    margin: 0;
    cursor: pointer;
}

.Period1 p {
    font-size: 14px;
    margin: 10px 0 0 0;
    font-weight: 600;
}

.Period2 p {
    font-size: 14px;
    color: #939CA1;
    margin: 0 0 10px 0;
}

.nofcredss {
    border: 1px solid #55C2C3;
    border-radius: 10px;
    padding: 2px 30px 1px 30px;
    margin: -7px 30px 0 0px;
    background-color: #fff;
    box-shadow: rgba(37, 36, 93, 0.25) 0px 2px 4px 0px inset, rgba(0, 0, 0, 0.3) 6px 0px 52px -71px inset;
}

p.credits {
    margin: 0;
    font-size: 12px;
    font-weight: 600;
}

p.credsno {
    padding: 0;
    margin: 0;
    color: #6DE1A4;
    font-weight: 600;
    font-size: 18px;
    text-align: center;
}

p.credsname{
    padding: 0;
    margin: 2px 0 0 2px;
    color: #000000;
    font-weight: 600;
    font-size: 14px;
}

.bowl::marker {
    font-size: 17px;
    padding: 0;
    margin: 0;
    color: #093D54;
}

p.insights {
    font-size: 12px;
}

ul.insightsunits {
    padding: 0 0 0 4rem;
    margin: 0;
}



p.insights {
    padding: 0 0 0 0;
    margin: 0 0 0 0;
}

p.following-email {
    padding: 5px 0 8px 0;
    font-size: 16px;
    text-align: center;
    font-weight: 600;
    color: #093D54;
    margin: 0px 0 0px 0;
}

p.jeffgennette\@gmail {
    text-align: center;
    font-size: 14px;
    color: #55C2C3;
    margin: 0 0 25px 0;
    padding: 0 0 4px 0;
}

p.jeffgennette\@gmail-1 {
    text-align: center;
    font-size: 14px;
    color: #55C2C3;
    margin: 0 0 0px 0;
}

p.douglas45\@gmail{
    text-align: center;
    font-size: 14px;
    color: #55C2C3;
    margin: 0 0 20px 0;
}


.refer-spacing {
    padding: 0 3rem 0 3rem;
}