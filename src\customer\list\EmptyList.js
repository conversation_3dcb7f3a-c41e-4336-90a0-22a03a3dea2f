import React from "react";
import "../assests/css/filter/addtolist.css";
import { Link, useNavigate } from "react-router-dom";

const EmptyList = (props) => {

  const navigate = useNavigate();
  const createList = () => {
    if (props.selectedWishList == "CONTACT") {
      navigate("/dashboard");
    } else {
      navigate("/company-filters");
    }
  }

  return (
    <>
      <div className="maindivetobecenter">
        <div className="d-flex flex-row justify-content-between">
          {/* <div>
            <img src="../../images/filter-img.png" width="300" height="auto" />
          </div> */}

          <div className="w-100" style={{ margin: "2rem 0 0 0" }}>
            <img src="../../images/add-to-list.png" width="57%" className="m-auto d-flex" />
            <p className="noflist">No Lists Created</p>

            <p className="filtercontacts">Filter contacts and company profiles based on your ICP criteria. Select preferred profiles <br /> and click on Add to List. Once you have created and saved a list, you can view them here.</p>
            {/* <div className="m-auto d-flex justify-content-center">
              <button
                type="button"
                className="createnewListclone "
                onClick={createList}>
                Create New List
              </button>
            </div> */}

          </div>
        </div>
      </div>
    </>
  );
};

export default EmptyList;
