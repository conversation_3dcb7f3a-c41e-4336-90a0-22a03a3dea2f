@import url('https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

body{
  font-family: 'Lato', 'medium';
}


p.savedcontactlist {
    padding: 0 0 0 10px;
    color: #093D54;
    font-weight: 600;
    margin: 0;
}

.contactlistfirstlayer {
    background-color: #F5F5F5;
    padding: 1rem 2rem 2rem 2rem;
    margin: 1rem 0 0 0;
    border-radius: 10px;
}

p.ContactList3 {
    color: #093D54;
    font-weight: 600;
    padding: 0 20px 0 0px;
}

p.contactlistSearchResultFound {
    font-size: 12px;
    margin: 4px 0 0 0;
    color: #7E8C9C;
}

span.fivehundradcontact {
    font-weight: 600;
    color: #000000;
}

thead.contactlistheader {
    background-color: #EBF6FF;
}

th.contactlistth {
    padding: 14px 16px 14px 16px;
    margin: 0 0 0 0px;
}



tbody.contactlistbody {
    background-color: #fff;
    /* box-shadow: rgba(50, 50, 93, 0.25) 0px 50px 100px -20px, rgba(0, 0, 0, 0.3) 0px 30px 60px -30px, rgba(1, 17, -70, 0.35) 0px -1px 3px 0px inset; */
    border-radius: 4px;
}

td.contactlisttd {
    padding: 10px 16px 10px 16px;
    font-size: 14px;
    font-weight: 600;
}

td.contactlisttd img {
    margin: 0 0 0 1rem;
    cursor: pointer;
}

button.contactslistreveal {
    background-color: #F5F5F5;
    border: 1px solid #F5F5F5;
    font-size: 14px;
    padding: 2px 5px 2px 5px;
    cursor: pointer;
    outline-style: none;
}