import React from "react";
import "../customer/assests/css/servererrorcss/pagenotfound.css";

const PageNotFound = () => {

    const goBack = () => {
        // Go back to the previous page in the browser's history
        window.history.back();
    }

    return (
        <>
            <nav className="navbar navbar-expand-lg navbar-light d-flex justify-content-between">
                <a className="navbar-brand" href={window.location.href}><img src="../images/black-logo.png" className="img-fluid" width="150" />
                </a>
            </nav>

            <div className="container">
                <div className="glob">
                    <img src="../images/icon-page-not-found.png" className="img-fluid mx-auto d-block mt-5" width="300" />
                </div>

                <div className="fdown text-center">
                    <h1>Page Not Found</h1>

                    <img src="../images/swicth.png" className="img-fluid mx-auto d-block" width="200" />

                </div>

                <div className="information text-center">
                    <h4>Uh oh, we can't seem to find the page you'r looking for.
                        Try going back to the <br /><a href="#" onClick={goBack} className="go-back">previous page</a> or <a href="" className="go-back">contact us</a> for more information</h4>
                </div>

            </div>
        </>
    )
}

export default PageNotFound;