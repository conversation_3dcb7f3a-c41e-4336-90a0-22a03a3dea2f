import React, { useContext, useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import "../assests/css/filter/contactstable.css";
import Alert from "../common-files/alert.js";
import {
  PostWithTokenNoCache
} from "../common-files/ApiCalls.js";
import { ApiName } from "../common-files/ApiNames.js";
import DashboardContext from "../common-files/ContextDashboard.js";
import { clearStore } from "../common-files/indexedDBUtils.js";
import UseTabStore from "../common-files/useGlobalState.js";
import ReachUs from "../filters/ReachUs.js";
import ListPopup from "../list/ListPopup.js";
import Feedback from "./Feedback.js";
import Pagination from "./Pagination.js";
import WarningAlert from "../common-files/warningAlert.js";
export default function App(props) {
  const [checkboxes, setCheckboxes] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const closeDownloadAlertRef = useRef();
  const { foundCounts, pageNumber, data, loadingCount, paginationDataCount } =
    props;
  const { dataDC, setDataDC } = useContext(DashboardContext);
  const [PageSize, setPageSize] = useState(
    dataDC.membership === "trail" ? 10 : dataDC.membership === "prime" ? 25 : 10
  );
  const [showModal, setShowModal] = useState(false);

  const [loading, setLoading] = useState(true);
  const [loadingCounts, setLoadingCounts] = useState(loadingCount);
  const [foundContacts, setFoundContacts] = useState(foundCounts);
  const [isOpen, setIsOpen] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);
  const {
    unSelectedRows,
    advancedFilter,
    currentSelectedPage,
    defaultErrorMsg,
    defaultAlert,
    isShowDownloadedData,
    setViewModal,
    loadedData,
    buttonType,
    viewSelected,
    sampleDownloadedIds,
    allRecordsNew,
    selectedRows,
    currentPage,
    selectedTab,
    setSelectedTab,
    sortingBy,
    searchPattern,
    withDownloaded,
    setWithDownloaded,
    setSortingBy,
    setDefaultError,
    setCurrentPage,
    setSelectedRows,
    setAllRecordsNew,
    setLoadedData,
    setShowDownloadedData,
    setContactModelId,
    setUnSelectedRows,
    setAdvancedFilter,
    setAdvancedFilterSearchPattern
  } = UseTabStore();

  let [showSortOptions, setShowSortOptions] = useState(false);
  const [showLastUpdatedOptions, setShowLastUpdatedOptions] = useState(false);
  const [showDownloadOptions, setShowDownlaodOptions] = useState(false);
  const [isValidNoOfCompany, setIsValidNoOfCompany] = useState(false);
  const [noOfCompany, setNoOfCompany] = useState(null);
  const [inputColorTwo, setInputColorTwo] = useState("#ffffff");
  const dropdownRef = useRef(null);
  const [time, setTime] = useState(null);

  useEffect(() => {
    function addSecondsToDate(date, seconds) {
      const newDate = new Date(date.getTime() + seconds * 1000);
      return newDate;
    }

    const currentTime = new Date();
    const addedSeconds = 30; // Example: add 30 seconds

    const newTime = addSecondsToDate(currentTime, addedSeconds);
    setTime(newTime);

  }, [])
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowSortOptions(false);
        setShowLastUpdatedOptions(false);
        setShowDownlaodOptions(false);
      }
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [dropdownRef]);
  const handleClickk = () => {
    const newColor = inputColorTwo === "#ffffff" ? "#E8F7F7" : "#ffffff";
    setInputColorTwo(newColor);
  }

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const selectAllChange = (isChecked) => {
    setSelectAll(isChecked);
    const pkId = loadedData
      .filter((user) => !sampleDownloadedIds.includes(user.id))
      .map((user) => user.id);

    const updateCheckboxes = (prevCheckboxes, selectedRows, pkId, isChecked) => {
      const updatedCheckboxes = { ...prevCheckboxes };
      const updatedSelectedRows = new Set(selectedRows);

      pkId.forEach((val) => {
        const id = parseInt(val);
        updatedCheckboxes[val] = isChecked;
        isChecked ? updatedSelectedRows.add(id) : updatedSelectedRows.delete(id);
      });

      return {
        updatedCheckboxes,
        updatedSelectedRows: Array.from(updatedSelectedRows),
      };
    };

    const { updatedCheckboxes, updatedSelectedRows } = updateCheckboxes(
      checkboxes,
      selectedRows,
      pkId,
      isChecked
    );

    setCheckboxes(updatedCheckboxes);
    setSelectedRows(updatedSelectedRows);

    if (isChecked) {
      setUnSelectedRows((prevUnSelectedRows) =>
        prevUnSelectedRows.filter((id) => !pkId.includes(id))
      );
    } else {
      setUnSelectedRows(pkId);
    }
  };
  const handleInputs = (e) => {
    const { id, value } = e.target;
    const inputValue = value.trim();
    const isValid = /^\d+$/.test(inputValue) && Number(inputValue) > 0;

    if (id === "no_of_company" && inputValue <= 5000) {
      setNoOfCompany(inputValue);
      setIsValidNoOfCompany(isValid);
    }
  };

  const toggleSortOptions = () => {
    showSortOptions = showSortOptions
      ? setShowSortOptions(false)
      : setShowSortOptions(true);
    setShowDownlaodOptions(false);
    setShowLastUpdatedOptions(false);
  };

  const toggleLastUpdatedOptions = () => {
    setShowLastUpdatedOptions(true);
    setShowDownlaodOptions(false);
  };
  const toggleDownloadsOptions = () => {
    setShowLastUpdatedOptions(false);
    setShowDownlaodOptions(true);
  };
  const clearFilters = () => {
    setSortingBy(6);
    setWithDownloaded(true);
    // setShowDownloadedData(true);
    setShowDownlaodOptions(false);
    setShowLastUpdatedOptions(false);
    sortBy();
  };
  useEffect(() => {
    const currentTableData = async () => {
      let sliceData;
      setCurrentPage(pageNumber);
      sliceData = data;
      // Check if allRecordsNew is an array
      if (Array.isArray(allRecordsNew)) {
        // Filter out JSON objects in sliceData that are not already present in allRecordsNew
        const updatedRecords = allRecordsNew.concat(
          sliceData.filter(
            (item) =>
              !allRecordsNew.some(
                (record) => JSON.stringify(record) === JSON.stringify(item)
              )
          )
        );
        setAllRecordsNew(updatedRecords);
        // Now, updatedRecords contains the unique JSON objects
      } else {
        setAllRecordsNew(sliceData);
      }
      if (!isShowDownloadedData) {
        const filteredSliceData = sliceData.filter(
          (item) => !sampleDownloadedIds.includes(item.id)
        );
        setLoadedData(filteredSliceData);
        checkBoxStatus(filteredSliceData);
      } else {
        setLoadedData(sliceData);
        checkBoxStatus(sliceData);
      }
      setSelectedTab("company");
      setLoading(false);
      if (foundContacts > 0) {
        setLoadingCounts(false);
      }
    };
    currentTableData();
  }, []);

  // auto set checked all if all items selected in current page
  const checkBoxStatus = (currentData) => {
    let pkId = currentData
      .filter((user) => selectedRows.includes(user.id))
      .map((user) => user.id);

    if (PageSize === pkId.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  };

  const onChangeHandler = (event, index) => {
    const { id } = event.target;
    const isChecked = event.target.checked;

    // Update the checkboxes
    // setCheckboxes((prevCheckboxes) => {
    //   if (!Array.isArray(prevCheckboxes)) {
    //     console.error("prevCheckboxes is not an array:", prevCheckboxes);
    //     return []; // Fallback to an empty array
    //   }
    //   const updatedCheckboxes = [...prevCheckboxes];
    //   updatedCheckboxes[index] = isChecked;
    //   return updatedCheckboxes;
    // });

    // Create a new array based on the previous selectedRows
    const newArray = [...selectedRows];

    if (isChecked) {
      // Add the ID if it doesn't exist
      if (!newArray.includes(parseInt(id))) {
        newArray.push(parseInt(id));
      }
    } else {
      // Remove the ID if it exists
      const index2 = newArray.indexOf(parseInt(id));
      if (index2 !== -1) {
        newArray.splice(index2, 1);
      }
    }

    // Update selectedRows with the new array
    setSelectedRows(newArray);

    // Function to remove values from an array
    function removeValues(arr, valuesToRemove) {
      if (!valuesToRemove.length) {
        return arr; // No values to remove, return original array
      }
      return arr.filter((element) => !valuesToRemove.includes(element));
    }

    if (advancedFilter && isChecked) {
      const filteredArray = removeValues(unSelectedRows, parseInt(id));
      setUnSelectedRows(filteredArray);
    }

    if (advancedFilter && !isChecked) {
      // Ensure unSelectedRows is an array before spreading
      let newSelectedRows = [...(Array.isArray(unSelectedRows) ? unSelectedRows : []), parseInt(id)];
      setUnSelectedRows(newSelectedRows);
    }

    // Auto set checked all if all items are selected in the current page
    let pkId = loadedData
      .filter((user) => newArray.includes(user.id))
      .map((user) => user.id);

    if (PageSize === pkId.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }

    // Calculate the start index of the current page
    const startIndex = (currentPage - 1) * PageSize;

    // Calculate the end index of the current page (exclusive)
    const endIndex = Math.min(startIndex + PageSize, loadedData.length);

    if (pkId.length === endIndex) {
      setSelectAll(true);
    }
  };


  const handleSelectPage = () => {
    selectAllChange(!selectAll);
    setIsOpen(false);
  };

  const handleModal = (id) => {
    setViewModal(true);
    setContactModelId(id);
  };

  const convertToProperCase = (val) => {
    if (val) {
      // Trim the input to remove any leading or trailing whitespace
      val = val.trim();

      // Match words, keeping special characters in place
      const words = val.match(/\w+|\W+/g);

      // Capitalize the first letter of each alphanumeric word, preserving special characters
      const capitalizedWords = words.map(word =>
        /\w/.test(word) ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : word
      );

      // Join all parts back into a single string
      return capitalizedWords.join('');
    } else {
      return val;
    }
  };

  const sortBy = async (sort) => {
    setLoadingCounts(true);
    if (sort === undefined) {
      sort = "";
      setSortingBy("");
    } else {
      setSortingBy(sort);
    }
    searchPattern["sortBy"] = sort;
    searchPattern["withDownloaded"] = withDownloaded;
    searchPattern["searchBy"] = selectedTab;
    const dataPost = {
      page: currentPage ? currentPage : 1,
      pageSize: PageSize,
      searchPattern: searchPattern,
    };
    const loaderElement = document.querySelector("#table-loader");
    const loaderContactTable = document.querySelector("#cust-contact-table");

    if (loaderElement) {
      loaderElement.style.display = "block"; // Set display to 'none' if the element exists in the DOM
    }
    if (loaderContactTable) {
      loaderContactTable.style.display = "none"; // Set display to 'none' if the element exists in the DOM
    }
    await PostWithTokenNoCache(ApiName.esCompanyFilter, dataPost)
      .then(async function (response) {
        if (response != null && "status" in response) {
          if (response.data.status === 200) {
            try {
              const dataObj = JSON.parse(response.data.data);
              if (dataObj.totalCount > 0) {
                setLoadedData(dataObj.records);
                setFoundContacts(dataObj.totalCount);
                setLoadingCounts(false);
              } else {
                setLoadedData([]);
                setFoundContacts(0);
                setLoadingCounts(false);
              }
              if (loaderElement) {
                loaderElement.style.display = "none"; // Set display to 'none' if the element exists in the DOM
              }
              if (loaderContactTable) {
                loaderContactTable.style.display = "block"; // Set display to 'none' if the element exists in the DOM
              }
              // await callTotalCount(dataPost.searchPattern, sortingBy);
            } catch (error) {
              setTimeout(() => {
                setDefaultError("");
              }, 3000);
              setDefaultError("Failed to load contact counts.");
            }
          }
        }
      })
      .catch(function (errors) {
        setDefaultError("Failed to load contact counts.");
      });
  };

  const mystyle = { marginBottom: "20px", marginTop: "5px" };

  const showDownloadedContact = async () => {
    setShowDownloadedData(true);
    setLoadingCounts(true);
    setSortingBy("");
    setWithDownloaded(true);

    searchPattern["searchBy"] = selectedTab;
    searchPattern["sortBy"] = sortingBy;
    searchPattern["withDownloaded"] = true;
    const dataPost = {
      page: currentPage ? currentPage : 1,
      pageSize: PageSize,
      searchPattern: searchPattern,
    };
    const loaderElement = document.querySelector("#table-loader");
    const loaderContactTable = document.querySelector("#cust-contact-table");

    if (loaderElement) {
      loaderElement.style.display = "block"; // Set display to 'none' if the element exists in the DOM
    }
    if (loaderContactTable) {
      loaderContactTable.style.display = "none"; // Set display to 'none' if the element exists in the DOM
    }
    await PostWithTokenNoCache(ApiName.esCompanyFilter, dataPost)
      .then(async function (response) {
        if (response != null && "status" in response) {
          if (response.data.status === 200) {
            try {
              const dataObj = JSON.parse(response.data.data);
              if (dataObj.totalCount > 0) {
                setLoadedData(dataObj.records);
                setFoundContacts(dataObj.totalCount);
                setLoadingCounts(false);
              } else {
                setLoadedData([]);
                setFoundContacts(0);
                setLoadingCounts(false);
              }
              if (loaderElement) {
                loaderElement.style.display = "none"; // Set display to 'none' if the element exists in the DOM
              }
              if (loaderContactTable) {
                loaderContactTable.style.display = "block"; // Set display to 'none' if the element exists in the DOM
              }
              // await callTotalCount(dataPost.searchPattern, sortingBy);
            } catch (error) {
              setTimeout(() => {
                setDefaultError("");
              }, 3000);
              setDefaultError("Failed to load contact counts.");
            }
          }
        }
      })
      .catch(function (errors) {
        setDefaultError("Failed to load contact counts.");
      });
  };

  const hideDownloadedContact = async () => {
    setShowDownloadedData(false);
    setLoadingCounts(true);
    setSortingBy("");
    setWithDownloaded(false);

    searchPattern["searchBy"] = selectedTab;
    searchPattern["sortBy"] = sortingBy;
    searchPattern["withDownloaded"] = false;
    const dataPost = {
      page: currentPage ? currentPage : 1,
      pageSize: PageSize,
      searchPattern: searchPattern,
    };
    const loaderElement = document.querySelector("#table-loader");
    const loaderContactTable = document.querySelector("#cust-contact-table");

    if (loaderElement) {
      loaderElement.style.display = "block"; // Set display to 'none' if the element exists in the DOM
    }
    if (loaderContactTable) {
      loaderContactTable.style.display = "none"; // Set display to 'none' if the element exists in the DOM
    }
    await PostWithTokenNoCache(ApiName.esCompanyFilter, dataPost)
      .then(async function (response) {
        if (response != null && "status" in response) {
          if (response.data.status === 200) {
            try {
              const dataObj = JSON.parse(response.data.data);
              if (dataObj.totalCount > 0) {
                setLoadedData(dataObj.records);
                setFoundContacts(dataObj.totalCount);
                setLoadingCounts(false);
              } else {
                setLoadedData([]);
                setFoundContacts(0);
                setLoadingCounts(false);
              }
              if (loaderElement) {
                loaderElement.style.display = "none"; // Set display to 'none' if the element exists in the DOM
              }
              if (loaderContactTable) {
                loaderContactTable.style.display = "block"; // Set display to 'none' if the element exists in the DOM
              }
              // await callTotalCount(dataPost.searchPattern, sortingBy);
            } catch (error) {
              setTimeout(() => {
                setDefaultError("");
              }, 3000);
              setDefaultError("Failed to load contact counts.");
            }
          }
        }
      })
      .catch(function (errors) {
        setDefaultError("Failed to load contact counts.");
      });
  };

  const addProtocol = (url) => {
    // Check if the URL already contains a protocol
    if (!/^https?:\/\//i.test(url)) {
      // If not, prepend 'http://'
      return `http://${url}`;
    }
    return url;
  };

  const applyFilter = async () => {

    if (!isValidNoOfCompany || !noOfCompany) {
      setIsValidNoOfCompany(false);
      return false;
    }
    setOpenPopup(false);
    setSelectedRows([]);
    setUnSelectedRows([]);
    setAdvancedFilter(time);
    clearStore("AdvancedFilterData", "Company")

    const dataPost = {
      page: 1,
      pageSize: parseInt(PageSize),
      searchPattern: searchPattern,
      maxCompanies: noOfCompany ? parseInt(noOfCompany) : 100
    };
    setCurrentPage(1);
    setAdvancedFilterSearchPattern(dataPost);
  };

  const clearSection = () => {
    if (isValidNoOfCompany) {
      setNoOfCompany(null);
      setOpenPopup(false);
      setAdvancedFilter(false);
      setSelectedRows([]);
      setUnSelectedRows([]);
      setIsValidNoOfCompany(false);
    }
  };
  return (
    <>
      {defaultErrorMsg && defaultAlert ? (
        <>
          <Alert data={defaultErrorMsg} />
        </>
      ) : (
        <></>
      )}
      <div
        className="modal fade"
        id="glassAnimals"
        tabIndex="-1"
        role="dialog"
        aria-labelledby="exampleModalLabel"
        aria-hidden="true"
      >
        <div
          className="modal-dialog modal-dialog-centered modal-sm"
          role="document"
          style={{ position: "absolute", left: "40%", top: "-18rem" }}
        >
          <div
            className="modal-content"
            style={{
              borderRadius: "14px",
              border: "1px solid #55C2C3",
              height: "37px",
              padding: "4px 2px 0px 2px",
            }}
          >
            <div className="d-flex flex-row justify-content-between">
              <div className="srated">
                <p>
                  <span className="dlnld">
                    <img src="../images/downld.png" />
                  </span>{" "}
                  {showModal ? "Download Completed" : "Download Started"}
                </p>
              </div>
              <div className="cncl">
                <button
                  type="button"
                  ref={closeDownloadAlertRef}
                  className="close"
                  data-dismiss="modal"
                  aria-label="Close"
                  onClick={() => setShowModal(false)}
                >
                  <img src="../images/cancl.png" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="d-flex justify-content-between ml-2 mr-4">
        <div className="d-flex flex-row">
          <div ref={dropdownRef}>
            <button
              className="sortby dropdown-toggle"
              onClick={() => toggleSortOptions()}
            >
              <img src="../images/sortby.png" className="img-fluid" />
              Sort by
            </button>
            {showSortOptions && (
              <ul className="left">
                <li onClick={toggleLastUpdatedOptions}>
                  Last Updated{" "}
                  <span className="caret-img">
                    <img src="../images/Caret_right.png" width="14" />
                  </span>{" "}
                </li>
                <div className="dropdown-divider"></div>
                {showLastUpdatedOptions && (
                  <div className="sub-drop-1">
                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>All</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="lastUpdated"
                          checked={sortingBy === ""}
                          onClick={() => sortBy()}
                        />
                      </div>
                    </div>

                    <div className="dropdown-divider"></div>

                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>03 Months</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="lastUpdated"
                          checked={sortingBy === 3}
                          onChange={() => sortBy(3)}
                        />
                      </div>
                    </div>

                    <div className="dropdown-divider"></div>

                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>06 Months</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="lastUpdated"
                          checked={sortingBy === 6}
                          onClick={() => sortBy(6)}
                        />
                      </div>
                    </div>

                    <div className="dropdown-divider"></div>

                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>12 Months</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="lastUpdated"
                          checked={sortingBy === 12}
                          onClick={() => sortBy(12)}
                        />
                      </div>
                    </div>
                  </div>
                )}
                <li onClick={toggleDownloadsOptions}>
                  Downloads &nbsp;&nbsp;&nbsp;&nbsp;
                  <span className="caret-img">
                    <img src="../images/Caret_right.png" width="14" />
                  </span>
                </li>
                <div className="dropdown-divider"></div>
                {showDownloadOptions && (
                  <div className="sub-drop-2">
                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>Show</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="hideShowDownload"
                          checked={isShowDownloadedData ? true : false}
                          onClick={() => showDownloadedContact()}
                        />
                      </div>
                    </div>
                    <div className="dropdown-divider"></div>
                    <div className="d-flex flex-row justify-content-between">
                      <div className="months">
                        <p>Hide</p>
                      </div>
                      <div className="radio">
                        <input
                          type="radio"
                          name="hideShowDownload"
                          checked={!isShowDownloadedData ? true : false}
                          onClick={() => hideDownloadedContact()}
                        />
                      </div>
                    </div>
                  </div>
                )}
                <li onClick={clearFilters}>Clear Filters</li>
              </ul>
            )}
          </div>
          <div className="search-result mt-1">
            Search Result Found{" "}
            <span className="number-of" id="total_contacts">
              {" "}
              {!loadingCounts
                ? foundContacts == 0
                  ? "Loading..."
                  : foundContacts
                : "Loading..."}{" "}
              {foundContacts !== 0 || !loadingCounts ? "Companies" : <></>}
            </span>
          </div>
        </div>
      </div>

      <div class="selected-items-bar">
        <div className="d-flex flex-row justify-content-between">
          <div>
            <div className="d-flex flex-row">
              <div>
                <input
                  checked={selectedRows.length > 0 ? true : false}
                  type="checkbox"
                  className="dropcheck"
                  onClick={toggleDropdown}
                />
              </div>
              <div></div>
              <div>
                <div
                  class="dropdown"
                  style={{ margin: "margin: 4px 0 0 0;", cursor: "pointer" }}
                >
                  <div className="dropdown">
                    <button className="caretdrop" onClick={toggleDropdown}>
                      <img
                        src={
                          isOpen
                            ? "../images/caret_up.png"
                            : "../images/caret_new_down.png"
                        }
                        alt="Caret"
                        className="caret-icon"
                        width="8"
                      />
                    </button>
                    {isOpen && (
                      <div className="dropdown-content ">
                        <a
                          class="dropdown-item Selectthis"
                          onClick={handleSelectPage}
                        >
                          {selectAll
                            ? "Unselect Current Page"
                            : "Select Current Page"}
                        </a>
                        <div className="dropdown-divider"></div>
                        <a className="dropdown-item Selectthis" style={{ cursor: "auto" }}>
                          Advanced Selection
                        </a>
                        <div className="dropdown-divider"></div>
                        <div className="d-flex flex-row justify-content-start pl-3 pr-3">
                          <div className="p-2" style={{ cursor: "auto" }}>
                            <p className="miniselect">Select No. of Companies</p>
                            <button
                              type="button"
                              onClick={applyFilter}
                              value={noOfCompany}
                              className='selectapplybutton-enable'
                              style={{
                                opacity: !isValidNoOfCompany ? 0.5 : 1,
                                cursor: !isValidNoOfCompany ? "not-allowed" : "pointer"
                              }}
                            >
                              Apply
                            </button>
                          </div>
                          <div className="p-2 ml-3">
                            <input
                              type="number" // Set input type to number
                              id="no_of_company"
                              className={`miniinputt ${isValidNoOfCompany ? "" : ""}`}
                              onChange={(e) => handleInputs(e, "noOfCompany")} // Pass type to handler
                              value={noOfCompany || ""}
                              autoComplete="off"
                              onClick={handleClickk}
                              style={{ backgroundColor: inputColorTwo }}
                              min="0"
                              onKeyDown={(e) => {
                                if (["e", "E", "+", "-", "."].includes(e.key)) {
                                  e.preventDefault();
                                }
                              }}
                            />
                            <p className="max-contacts">(Max. 5000)</p>
                          </div>
                        </div>


                        <div className="">

                        </div>
                        <div className="dropdown-divider"></div>

                        <div className="" onClick={clearSection}>
                          <p
                            style={{
                              color: `${isValidNoOfCompany
                                ? "#55C2C3"
                                : "#bbbbbb"
                                }`,
                            }}
                            className="ClearSection"
                          >
                            Clear Selection
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="droppose">
                    <div
                      class="dropdown-menu"
                      aria-labelledby="dropdownMenuButton"
                    ></div>
                  </div>
                </div>
              </div>
              <div>
                <p className="oSelected">{selectedRows.length} Selected</p>
              </div>

              <div className="ml-3">
                <button
                  type="submit"
                  className={
                    selectedRows.length > 0
                      ? "savedlist1"
                      : "addtolist"
                  }
                  onClick={
                    selectedRows.length > 0 ? () => setOpenPopup(true) : () => setOpenPopup(false)
                  }
                  style={{
                    cursor: !selectedRows.length > 0 ? "not-allowed" : "pointer",
                  }}
                >
                  <i class="fa fa-plus mr-1"></i> Add to List
                </button>
                {openPopup && selectedRows.length > 0 ? (
                  <ListPopup
                    setOpenPopup={setOpenPopup}
                    setCheckboxes={setCheckboxes}
                    setSelectAll={setSelectAll}
                  ></ListPopup>
                ) : (
                  <></>
                )}
              </div>
            </div>
          </div>

          <div className="d-flex flex-row">
            <div className="mr-2">
              <Link to="/saved-list">
                <button type="submit" className="savedlist">
                  <i className="fa fa-align-left mr-2"></i>Saved Lists
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>


      <div class="fixTableHeadcompany">
        <table className="responsive-table" style={{ position: "sticky", top: "0", zIndex: "1" }}>
          <thead className="table-box">
            <tr>
              <th className="company-data-header">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Company Name
              </th>
              <th className="companyheaders">
                Company URL
              </th>
              <th className="companyheaders">Employee Size</th>
              <th className="companyheaders" colspan="0">
                Revenue
              </th>
              <th className="companyheaders">Industry</th>
              <th className="companyheaders">Location</th>
              <th className="companyheaders">View Details</th>
            </tr>
          </thead>
          <tbody className=" mt-3">
            {!loading && loadedData.length > 0 &&
              loadedData.map((item, index) => (
                <tr
                  className="table-data"
                  id={index}
                  style={mystyle}
                  key={item.id}
                >
                  <td
                    style={
                      sampleDownloadedIds.includes(item.id)
                        ? { backgroundColor: null }
                        : null
                    }
                    className="section-3"
                    id={index}
                  >
                    <label className="form-controller">
                      <li className="first">
                        <label className="second" for="checkbox4">
                          {sampleDownloadedIds.includes(item.id) ? (
                            <p className="rotate"></p>
                          ) : (
                            <input
                              className="custom-checkbox"
                              id={item.id}
                              type="checkbox"
                              checked={
                                selectedRows.includes(item.id) || false
                              }
                              onChange={(event) =>
                                onChangeHandler(event, item.id)
                              }
                            />
                          )}
                        </label>
                      </li>
                      <p style={{ backgroundColor: null, fontWeight: "600", fontSize: "14px", margin: "0 5px 0 10px" }} className="company-data-filled">{convertToProperCase(item.company_company_name || "")}</p>
                    </label>

                  </td>

                  <td
                    style={{
                      cursor: item?.company_website ? "pointer" : "",
                      backgroundColor: sampleDownloadedIds.includes(item.id)
                        ? null
                        : "transparent",
                    }}
                    className="section-5"
                  >
                    <a
                      href={
                        item.company_website === "--"
                          ? ""
                          : addProtocol(item.company_website)
                      }
                      target={item?.company_website ? "_blank" : ""}
                    >
                      {item.company_website === "--"
                        ? ""
                        : item.company_website}
                    </a>
                  </td>
                  <td
                    style={
                      sampleDownloadedIds.includes(item.id)
                        ? { backgroundColor: null }
                        : null
                    }
                    className="company-data-filled"
                  >
                    <span>
                      <img src="./images/employee-size.png" width="15" />
                    </span>{" "}
                    {item.company_employee_size}
                  </td>
                  <td
                    style={
                      sampleDownloadedIds.includes(item.id)
                        ? { backgroundColor: null }
                        : null
                    }
                    className="company-data-filled"
                  >
                    {item.company_annual_revenue_amount}
                  </td>
                  <td
                    style={
                      sampleDownloadedIds.includes(item.id)
                        ? { backgroundColor: null }
                        : null
                    }
                    className="industry-1"

                  >
                    {item.company_industries
                      ? convertToProperCase(item.company_industries)
                      : ""}
                  </td>
                  <td
                    style={
                      sampleDownloadedIds.includes(item.id)
                        ? { backgroundColor: null }
                        : null
                    }
                    className="industry-1"
                  >
                    {item.company_address_state && item.company_address_state !== "--"
                      ? `${convertToProperCase(item.company_address_state)}, ${convertToProperCase(item.company_address_country || "")}`
                      : convertToProperCase(item.company_address_country || "")}
                  </td>
                  <td
                    style={
                      sampleDownloadedIds.includes(item.id)
                        ? { backgroundColor: null, fontWeight: "100" }
                        : null
                    }
                    className="company-data-filled"
                  >
                    <div
                      className="eye"
                      data-toggle="modal"
                      data-target="#exampleModalLabel"
                      onClick={() => {
                        handleModal(item.id);
                      }}
                    >
                      <img src="../images/eyee.png" />
                    </div>
                  </td>
                </tr>
              ))
            }
          </tbody>
        </table>
      </div>
      <Feedback />
      {(currentSelectedPage === currentPage) ===
        Math.ceil(
          foundContacts === 0 ? paginationDataCount : foundContacts / PageSize
        ) ? (
        <ReachUs />
      ) : (
        <></>
      )}
      {/* reach us popup  end */}
      <div className="extra-border">
        <div className="row">
          <div className="col-6">
            <div className="contacts-selected">
              <p className={selectedRows.length > 0 ? "greenBlueShade" : ""}>
                {`${selectedRows.length} Compan${selectedRows.length > 1 ? 'ies' : 'y'} Selected`}
              </p>
            </div>
          </div>

          <div className="col-6">
            {dataDC.membership === "trail" ? (
              <Pagination
                className="pagination-bar"
                currentPage={
                  currentPage
                }
                totalCount={
                  Math.min(
                    Math.ceil(foundContacts / PageSize),
                    paginationDataCount
                  )
                }
                pageSize={1}
                onPageChange={(page) => {
                  setCurrentPage(page);
                  setSelectAll(false);
                }}
              />
            ) : (
              <Pagination
                className="pagination-bar"
                currentPage={
                  currentPage
                }
                totalCount={
                  foundContacts / 25
                }
                pageSize={1}
                onPageChange={(page) => {
                  setCurrentPage(page);
                  setSelectAll(false);
                }}
              />
            )}
          </div>
        </div>
      </div>
      {defaultErrorMsg && defaultAlert && (
        buttonType === "warning-error" ? (
          <WarningAlert data={defaultErrorMsg} />
        ) : buttonType === "credit-message" ? (
          <WarningAlert data={defaultErrorMsg} />
        ) : (
          <Alert data={defaultErrorMsg} />
        )
      )}
    </>
  );
}