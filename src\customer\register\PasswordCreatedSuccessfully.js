import React, { useEffect } from 'react';
import '../../customer/assests/css/PasswordCreatedSuccessfully.css';
import { Link, useLocation } from 'react-router-dom';
import Footer from '../layouts/Footer';
import Analytics from '../../utils/analyticsTracking';
import Mixpanel from '../../utils/mixpanel';

const PasswordCreatedSuccessfully = () => {
    const location = useLocation();
    
    // Track page view when component mounts
    useEffect(() => {
        try {
            // Gather device and location info
            const deviceInfo = {
                browser: navigator.userAgent,
                device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
                viewport_size: `${window.innerWidth}x${window.innerHeight}`,
                language: navigator.language || navigator.userLanguage
            };
            
            // Track password reset completion
            Analytics.track('Password Reset Completion Page Viewed', {
                email: location.state?.email || '',
                flow_type: 'password_reset',
                page_path: window.location.pathname,
                referrer: document.referrer || 'direct',
                ...deviceInfo
            });
            
            // Identify user if email is available
            if (location.state?.email) {
                Mixpanel.identify(location.state.email);
                
                // Update user properties
                Mixpanel.people.set({
                    'Password Reset Flow Completed': true,
                    'Password Reset Completion Time': new Date().toISOString()
                });
            }
        } catch (error) {
            console.warn('Error tracking password reset completion:', error);
        }
    }, [location.state?.email]);
    
    // Track when user clicks on Sign In button
    const handleSignInClick = () => {
        try {
            Analytics.track('Password Reset Sign In Clicked', {
                email: location.state?.email || '',
                flow_type: 'password_reset',
                time_to_click: new Date().getTime() - (window.performance.timing.navigationStart || 0)
            });
        } catch (error) {
            console.warn('Error tracking sign in click:', error);
        }
    };
    
    return (
        <>
        <div className="container-fluid">
            <div className="row">
                <div className="col-md-6 bg-color">
                    {/* <div className="offset-md-2 saver-plane2">
                        <img src="../images/r-logo.png" width="50" alt='logo' />
                    </div>

                    <div className="banner-2">
                        <img src="../images/group-50970.png" className="img-fluid" alt='banner' />
                    </div> */}

                    <div className="offset-md-3 saver-plane-password-change">
                        <img src="../images/r-logo.png" width="50" alt='logo' />
                    </div>

                    <div className="offset-md-3 password-changed-successfully-banner">
                        <img src="../images/group-50970.png" className="img-fluid" alt='banner' />
                    </div>
                </div>
                <div className="col-md-0 ml-5"></div>
                <div className="col-md-5 mt-5">

                    <div className="card-body">
                        <img className="img-fluid" src="../images/group-51319d.png" alt="Card image cap" />
                        <h3>Password Changed <br /> Successfully!</h3>
                        <p className="semi-header8">Sign in with your new password <br /><br />
                        </p>
                        <span className="Shift">

                            <Link to="/" onClick={handleSignInClick}> <button>Sign In</button></Link>

                        </span>
                    </div>

                </div>
            </div>
        </div>
        <Footer />
        </>
    );
}

export default PasswordCreatedSuccessfully;