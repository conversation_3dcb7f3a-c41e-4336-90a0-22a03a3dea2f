import { useEffect, useState } from "react";
import '../../customer/assests/css/PaymentSuccessfull.css';
import { getSessionItem } from '../common-files/LocalStorage';
import { ApiName } from '../common-files/ApiNames';
import { PostWithTokenNoCache } from '../common-files/ApiCalls.js';
import Footer from "../layouts/Footer";
import UseTabStore from "../common-files/useGlobalState.js";
import GTM from '../common-files/GTM.js';
import AWINTracker from '../common-files/AWINTracker.js';
import DWINTracker from "../common-files/DWINTracker.js";
import Mixpanel from '../../utils/mixpanel';
import Analytics from '../../utils/analyticsTracking';

// Cookie helper function
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}

const PaymentSuccessfull = () => {
    const gtmId = process.env.REACT_APP_GTM_ID;
    const [isLoading, setIsLoading] = useState(true);
    const [orderData, setOrderData] = useState(null);

    let loggedUserEmail = null;
    let token = null;

    let user = getSessionItem("user");
    user = user ? JSON.parse(user) : null;
    if (user && "token" in user) token = user.token;
    if (user && "email" in user) loggedUserEmail = user.email;

    const { referralCode, selectedPlanType, selectedPlanDetails, setSelectedPlanDetails, setReferralCode } = UseTabStore();

    useEffect(() => {
        const prepareOrderData = () => {
            // Get orderId from URL or localStorage
            const urlParams = new URLSearchParams(window.location.search);
            const orderId = urlParams.get('orderId') || localStorage.getItem('orderId');

            // Get AWIN cookies
            const awinCookie = getCookie('_aw_sn_116059');
            const awinJCookie = getCookie('_aw_j_116059');

            // Get discount-related values from localStorage
            const discountedPrice = localStorage.getItem("discounted_price");
            const appliedPromocode = localStorage.getItem("applied_promocode");

            if (orderId) {
                const data = {
                    order_subtotal: parseFloat(discountedPrice) || parseFloat(selectedPlanDetails?.price) || 0,
                    currency_code: 'USD',
                    order_ref: orderId,
                    commission_group: 'DEFAULT',
                    sale_amount: parseFloat(discountedPrice) || parseFloat(selectedPlanDetails?.price) || 0,
                    voucher_code: appliedPromocode || referralCode || '',
                    customer_acquisition: 'NEW',
                    ...(awinCookie && { _aw_sn_116059: awinCookie }),
                    ...(awinJCookie && { _aw_j_116059: awinJCookie })
                };
                
                // Track successful payment in Mixpanel
                try {
                    // Gather device and location info
                    const deviceInfo = {
                        browser: navigator.userAgent,
                        device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
                        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
                        language: navigator.language || navigator.userLanguage
                    };
                    
                    // Get UTM parameters from localStorage
                    const utmParams = localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {};
                    
                    // Ensure we have a valid plan name and determine plan type
                    let planName = selectedPlanDetails?.package_name || '';
                    let planType = '';
                    
                    if (planName.toLowerCase().includes('glacier')) {
                        planType = 'Glacier';
                    } else if (planName.toLowerCase().includes('ice floe')) {
                        planType = 'Ice Floe';
                    } else if (planName.toLowerCase().includes('polar peak')) {
                        planType = 'Polar Peak';
                    } else if (planName.toLowerCase().includes('frozen fortune')) {
                        planType = 'Frozen Fortune';
                    } else if (selectedPlanDetails?.id) {
                        planType = `Plan ID: ${selectedPlanDetails.id}`;
                        planName = planType;
                    } else {
                        planType = 'Standard Plan';
                        planName = planType;
                    }
                    
                    // Track payment success in Mixpanel directly without deduplication
                    Mixpanel.track('Payment Successful', {
                        order_id: orderId,
                        plan_name: planName,
                        plan_type: planType,
                        plan_price: data.sale_amount,
                        original_price: parseFloat(selectedPlanDetails?.price) || 0,
                        discount_applied: !!appliedPromocode || !!referralCode,
                        discount_code: appliedPromocode || referralCode || 'none',
                        currency: 'USD',
                        payment_method: 'credit_card',
                        subscription_type: selectedPlanType || 'standard',
                        timestamp: new Date().toISOString(),
                        ...deviceInfo,
                        ...utmParams
                    });
                    
                    // Track revenue in Mixpanel
                    Mixpanel.track('Purchase', {
                        distinct_id: loggedUserEmail,
                        order_id: orderId,
                        product_name: planName,
                        plan_type: planType,
                        price: data.sale_amount,
                        currency: 'USD',
                        quantity: 1,
                        timestamp: new Date().toISOString()
                    });
                    
                    // Update user lifecycle stage in Mixpanel
                    if (loggedUserEmail) {
                        Mixpanel.people.set({
                            'Lifecycle Stage': 'Paying Customer',
                            'Latest Purchase Date': new Date().toISOString(),
                            'Latest Purchase Amount': data.sale_amount,
                            'Latest Plan': planName,
                            'Plan Type': planType
                        });
                    }
                } catch (error) {
                    console.warn('Error tracking payment success in Mixpanel:', error);
                }
                
                setOrderData(data);
                return data;
            }
            return null;
        };

        const makeOrdersRegistrationCall = async () => {
            try {
                const orderData = prepareOrderData();
                if (orderData) {
                    await PostWithTokenNoCache(ApiName.ordersRegistration, orderData);
                    
                    // Track subscription success in Mixpanel
                    const utmParams = localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {};
                    
                    // Gather device and location info
                    const deviceInfo = {
                        browser: navigator.userAgent,
                        device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
                        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
                        language: navigator.language || navigator.userLanguage
                    };
                    
                    // Retrieve the applied promo code from localStorage
                    const appliedPromocode = localStorage.getItem("applied_promocode");
                    
                    // Determine plan type for analytics
                    let planName = selectedPlanDetails?.package_name || '';
                    let planType = '';
                    
                    if (planName.toLowerCase().includes('glacier')) {
                        planType = 'Glacier';
                    } else if (planName.toLowerCase().includes('ice floe')) {
                        planType = 'Ice Floe';
                    } else if (planName.toLowerCase().includes('polar peak')) {
                        planType = 'Polar Peak';
                    } else if (planName.toLowerCase().includes('frozen fortune')) {
                        planType = 'Frozen Fortune';
                    } else if (selectedPlanDetails?.id) {
                        planType = `Plan ID: ${selectedPlanDetails.id}`;
                    } else {
                        planType = 'Standard Plan';
                    }
                    
                    // Track the subscription success event with unique timestamp
                    Analytics.trackPaymentCompleted({
                        order_id: orderData.order_ref,
                        plan_name: selectedPlanDetails?.package_name || 'unknown',
                        plan_type: planType,
                        plan_id: selectedPlanDetails?.id,
                        plan_price: orderData.sale_amount,
                        discount_applied: !!appliedPromocode || !!referralCode,
                        discount_code: appliedPromocode || referralCode || 'none',
                        currency: 'USD',
                        user_email: loggedUserEmail,
                        payment_source: 'credit_card',
                        subscription_type: selectedPlanType || 'unknown',
                        referral_code: referralCode || 'none',
                        timestamp: new Date().toISOString(),
                        ...deviceInfo,
                        ...utmParams
                    });
                    
                    // Identify user in Mixpanel
                    if (loggedUserEmail) {
                        Mixpanel.identify(loggedUserEmail);
                        
                        // Update user properties
                        Mixpanel.people.set({
                            '$email': loggedUserEmail,
                            'Plan': selectedPlanDetails?.package_name || 'unknown',
                            'Plan Price': orderData.sale_amount,
                            'Subscription Date': new Date().toISOString(),
                            'Lifecycle Stage': 'Customer',
                            'Payment Status': 'Paid'
                        });
                    }
                }
            } catch (error) {
                console.error('Error in ordersRegistration:', error);
            } finally {
                setIsLoading(false);
            }
        };

        makeOrdersRegistrationCall();

        // Then proceed with other operations
        if (selectedPlanDetails?.package_name && selectedPlanType === "upgrade") {
            sendPlanActivatedEmail();
        }
        if (selectedPlanDetails?.package_name && referralCode) {
            handleReferralCode();
        }
    }, []);

    const handleReferralCode = async () => {
        if (!referralCode) return;

        try {
            const updateReferralRes = await PostWithTokenNoCache(
                ApiName.updateReferral,
                {
                    referral_code: referralCode,
                    recipient_email_reference: user?.email,
                    recipient_name_reference: localStorage.getItem('cust-username'),
                    email_send_status: 1,
                    subscription_status: "active",
                    subscription_package: selectedPlanDetails.package_name,
                    redeem_status: 0
                }
            );

            if (updateReferralRes?.data?.status === 200) {
                setReferralCode("");
                return true;
            }
        } catch (error) {
            setReferralCode("");
            console.error('Referral update failed:', error);
        }
        return false;
    };

    const sendPlanActivatedEmail = async () => {
        const param = JSON.stringify({
            package_name: selectedPlanDetails.package_name,
            username: (user?.firstName && user?.lastName)
                ? `${user.firstName} ${user.lastName}`
                : "user",
            email: user?.email,
            export: selectedPlanDetails?.export,
            price: selectedPlanDetails?.price,
            path: "api/email/upgrade/plan",
        });
        try {
            const res = await PostWithTokenNoCache(ApiName.upgradeEmail, param);
            if (res && "status" in res && res.status === 200) {
                setSelectedPlanDetails(null);
            }
        } catch (error) {
            console.log(error)
        }
    }

    return (
        <>
            {!isLoading && (
                <>
                    <GTM gtmId={gtmId} />
                    {orderData && <AWINTracker orderData={orderData} />}
                    <DWINTracker />
                    <div className="body">
                        <div className="container">
                            <div className="row">
                                <div className="offset-md-1 mt-3 col-md-6">
                                    <div className="payment-page-logo">
                                        <img src="../images/r-logo.png" className="img-fluid" width="70" />
                                    </div>
                                </div>
                            </div>

                            <div className="card">
                                <img className="img-top" src="../images/group-51319d.png" alt="Card image cap" />
                                <h3>Payment Successful!</h3>
                                <hr className="width" />

                                <div className="shifted">
                                    <input
                                        type="submit"
                                        value="Get Started"
                                        className="cp-pluss"
                                        onClick={() => {
                                            const currentDomain = window.location.origin;
                                            window.location.href = `${currentDomain}/dashboard`;
                                        }}
                                    />
                                </div>
                                <br />
                            </div>
                            <br />
                            <div className="receive-link">
                                <p>Get in touch with us at <a href="mailto:<EMAIL>"><EMAIL></a> if you <br /> don't receive a link within a few minutes.</p>
                            </div>
                        </div>
                    </div>
                    <Footer />
                </>
            )}
        </>
    );
}

export default PaymentSuccessfull;