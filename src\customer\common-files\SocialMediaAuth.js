import { useState, useEffect } from 'react';
import { useGoogleLogin } from '@react-oauth/google';
import { useLinkedIn } from 'react-linkedin-login-oauth2';
import { PublicClientApplication } from '@azure/msal-browser';

// Google login logic
export const useGoogleLoginLogic = () => {
    const [google_access_Token, set_google_access_Token] = useState(null);
    const login = useGoogleLogin({
        onSuccess: (tokenResponse) => {
            set_google_access_Token(tokenResponse.access_token);
        },
        onFailure: (error) => {
            console.error('Authentication failed:', error);
            // Additional error handling logic
        }, clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID,
    });

    return { login, google_access_Token };
};

// LinkedIn login logic
export const useLinkedInLoginLogic = () => {
    const [linkedinResponse, setLinkedinResponse] = useState(null);
    const linkedInAuth = useLinkedIn({  // Destructure linkedInAuth from the useLinkedIn hook
        clientId: process.env.REACT_APP_LINKEDIN_CLEINT_ID,
        redirectUri: process.env.REACT_APP_LINKEDIN_REDIRECT_URL,
        onSuccess: (code) => {
            setLinkedinResponse(code);
        },
        onError: (error) => {
            setLinkedinResponse(error);
        },
        scope: 'email profile openid'
    });

    return { linkedInAuth, linkedinResponse };
};

// Microsoft login logic
export const useMicrosoftLoginLogic = () => {
    const [MicrosoftAccessToken, setMicrosoftAccessToken] = useState(null);
    const [pca, setPca] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        // logout();
        const initializeMsal = async () => {
            try {
                const msalConfig = {
                    auth: {
                        clientId: process.env.REACT_APP_MICROSOFT_CLIENT_ID,
                        authority: process.env.REACT_APP_MICROSOFT_AUTHORITY_URL_AND_TENENT,
                        redirectUri: process.env.REACT_APP_MICROSOFT_REDIRECT_URL
                    }
                };
                const pcaInstance = new PublicClientApplication(msalConfig);
                await pcaInstance.initialize(); // Initialize MSAL instance
                setPca(pcaInstance);
            } catch (error) {
                console.error('Failed to initialize MSAL:', error);
            }
        };

        initializeMsal();
    }, []);

    const Microsoftlogout = async () => {
        try {
            if (!pca || isLoading) {
                console.error('MSAL instance not initialized or authentication in progress.');
                return;
            }
            setIsLoading(true);
            await pca.logoutPopup();
            window.location.href = window.location.origin;
            setMicrosoftAccessToken(null);
            setPca(null);
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const microsoftAuth = async () => {
        try {
            if (!pca || isLoading) {
                console.error('MSAL instance not initialized or authentication in progress.');
                return;
            }

            setIsLoading(true);

            const loginResponse = await pca.loginPopup({
                scopes: ['openid', 'profile', 'User.Read']
            });

            const tokenResponse = await pca.acquireTokenSilent({
                scopes: ['openid', 'profile', 'User.Read'],
                account: loginResponse.account
            });

            setMicrosoftAccessToken(tokenResponse.accessToken);
        } catch (error) {
            console.error('Authentication error:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return { Microsoftlogout, microsoftAuth, MicrosoftAccessToken };
};