import React, { useEffect } from 'react';
import Analytics from '../utils/analyticsTracking';

/**
 * Component to track user attribution sources
 * This is a "headless" component that doesn't render anything
 * but captures and tracks attribution data on mount
 */
const AttributionTracker = () => {
  useEffect(() => {
    // Check if this is a new session
    const lastSessionTimestamp = sessionStorage.getItem('session_start');
    const currentTime = new Date().getTime();
    
    // If no session exists or it's been more than 30 minutes (session timeout)
    if (!lastSessionTimestamp || (currentTime - parseInt(lastSessionTimestamp)) > 30 * 60 * 1000) {
      // This is a new session, track the attribution source
      const attributionData = Analytics.trackAttributionSource();
      
      // Mark this as the start of a new session
      sessionStorage.setItem('session_start', currentTime.toString());
      sessionStorage.setItem('is_new_session', 'true');
      
      // If this is the user's first visit ever, track it specially
      const isFirstVisit = !localStorage.getItem('first_visit_date');
      if (isFirstVisit) {
        localStorage.setItem('first_visit_date', new Date().toISOString());
        // Analytics.track('First Website Visit', {
        //   ...attributionData,
        //   landing_page: window.location.pathname,
        //   landing_url: window.location.href,
        //   is_first_visit: true
        // });
      }
    } else {
      // This is a continuation of an existing session
      sessionStorage.setItem('is_new_session', 'false');
    }
    
    // Track page view with attribution data
    const storedAttributionData = localStorage.getItem('attribution_data') ? 
      JSON.parse(localStorage.getItem('attribution_data')) : {};
    
    // Add attribution data to all page views for better analysis
    Analytics.trackPageView({
      ...storedAttributionData,
      is_new_session: sessionStorage.getItem('is_new_session') === 'true'
    });
    
  }, []);
  
  // This component doesn't render anything
  return null;
};

export default AttributionTracker; 