import React, { useState, useEffect } from "react";
import { ApiName } from "../common-files/ApiNames";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";

const Popup = ({ togglePopup }) => {
    const [message, setMessage] = useState("");
    const [submitted, setSubmitted] = useState(false);
    const [buttonType, setButtonType] = useState("");
    const [errorMsg, setErrorMsg] = useState("");
    const [isButtonDisabled, setIsButtonDisabled] = useState(true);
    const [user, setUser] = useState(JSON.parse(localStorage.getItem('user')));

    // Disable button if message is empty or submitting
    useEffect(() => {
        setIsButtonDisabled(message.trim().length === 0 || submitted);
    }, [message, submitted]);

    // Function to handle form submission
    const handleSubmit = async () => {
        if (message.trim().length > 0) {
            setSubmitted(true);
            const param = JSON.stringify({
                description: message,
                username: (user?.firstName && user?.lastName)
                    ? `${user.firstName} ${user.lastName}`
                    : "user",
                email: user.email,
                path: "api/email/pricing/custom-request",
                title: "Custom plan request"
            });
            try {
                const res = await PostWithTokenNoCache(ApiName.upgradeEmail, param);
                if (res && "status" in res && res.status === 200) {
                    setButtonType("success");
                    setMessage("");
                    setTimeout(() => {
                        setSubmitted(false);
                    }, 5000);
                } else {
                    setButtonType("error");
                    setErrorMsg(res?.response?.data?.message || "Error occurred");
                    setTimeout(() => {
                        setSubmitted(false);
                        setErrorMsg("");
                    }, 5000);
                }
            } catch (error) {
                setButtonType("error");
                setErrorMsg(error?.response?.data?.message || "An error occurred while submitting.");
                setTimeout(() => {
                    setSubmitted(false);
                    setErrorMsg("");
                }, 3000);
            }
        } else {
            setButtonType("error");
            setErrorMsg("Please provide a description for your custom plan.");
            setTimeout(() => {
                setSubmitted(false);
                setErrorMsg("");
            }, 3000);
        }
    };

    return (
        <div className="first-output">
            <div className="second-output">
                <span
                    onClick={togglePopup}
                    style={{ padding: '10px 10px', fontSize: '16px', display: "flex", justifyContent: "end", cursor: "pointer" }}
                >
                    <img src="../images/popup-cancel-btn.png" width="15" alt="close" />
                </span>
                <h2 className="get-custom">Get A Custom Plan</h2>
                <>
                    <div>
                        <textarea
                            id="message"
                            name="message"
                            rows="5"
                            cols="40"
                            placeholder="Describe your custom plan needs*"
                            className="popup-text-area"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                        />
                    </div>

                    <div>
                        <button
                            type="button"
                            className="popup-request"
                            onClick={handleSubmit}
                            disabled={isButtonDisabled}
                            style={{
                                opacity: isButtonDisabled ? 0.5 : 1,
                                cursor: isButtonDisabled ? 'not-allowed' : 'pointer'
                            }}
                        >
                            Send Request
                        </button>
                    </div>
                </>
                {buttonType === "success" && submitted && (
                    <div>
                        <p className="popup-success-meessage">
                            Thank you for submitting your custom plan request. <br /> One of our Growth Experts will reach out to you shortly.
                        </p>
                    </div>
                )}
                {buttonType === "error" && errorMsg && (
                    <div>
                        <p className="popup-error-meessage">
                            {errorMsg}
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Popup;
