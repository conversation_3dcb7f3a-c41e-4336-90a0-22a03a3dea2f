import React, { useRef } from "react";
import "../../assests/css/filter/thank_you.css";

const Thank_you = ({ thankYouModalBtn }) => {
  return (
    <div>
      <a
        ref={thankYouModalBtn}
        href="#"
        className="ancher"
        data-toggle="modal"
        data-target="#thankYouModalLong"
      ></a>

      <div
        className="modal fade"
        id="thankYouModalLong"
        tabIndex="-1"
        role="dialog"
        aria-labelledby="thankYouModalLongTitle"
        aria-hidden="true"
      >
        <div className="modal-dialog" role="document" style={{margin:"12rem auto 0"}}>
          <div className="modall-content" style={{margin:"auto"}}>
            <div className="modal-header" style={{borderBottom: "0"}}>
              <h5 className="modal-title" id="thankYouModalLongTitle"></h5>
              <button
                type="button"
                className="close"
                data-dismiss="modal"
                aria-label="Close"
              >
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <h5 className="thank_you">Thank you for contacting us!</h5>
            <div className="modal-body">
              <p className="development">
                Our business development manager will get in <br /> touch with
                you shortly. 
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Thank_you;
