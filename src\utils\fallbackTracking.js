/**
 * Fallback tracking mechanism for browsers with tracking prevention
 * 
 * This provides in-memory tracking for the current session only,
 * with no persistence between page loads.
 */

import { envConfig } from '../config'; // Import envConfig

// In-memory storage for tracking events
const memoryStore = {
  events: [],
  userId: null,
  userProperties: {},
  consentGiven: false
};

// Simple fallback tracking implementation
const FallbackTracking = {
  // Flag to indicate if this is a fallback
  isFallback: true,
  
  // Track an event
  track: function(eventName, properties = {}) {
    if (!this.consentGiven) return;
    
    try {
      // Add environment to properties
      const enhancedProps = {
        ...properties,
        environment: envConfig.environment || 'unknown'
      };
      
      // Add event to memory store
      memoryStore.events.push({
        event: eventName,
        properties: enhancedProps,
        timestamp: new Date().toISOString()
      });
      
      // Remove debug logging in production and success paths
      
      // If we have more than 100 events, remove the oldest ones
      if (memoryStore.events.length > 100) {
        memoryStore.events = memoryStore.events.slice(-100);
      }
    } catch (error) {
      console.warn('Error in fallback tracking:', error);
    }
  },
  
  // Identify a user
  identify: function(userId) {
    if (!this.consentGiven) return;
    memoryStore.userId = userId;
  },
  
  // Set user properties
  setUserProperties: function(properties) {
    if (!this.consentGiven) return;
    memoryStore.userProperties = {
      ...memoryStore.userProperties,
      ...properties
    };
  },
  
  // Get tracking events (for debugging)
  getEvents: function() {
    return [...memoryStore.events];
  },
  
  // Set consent status
  setConsent: function(consentGiven) {
    this.consentGiven = consentGiven;
    memoryStore.consentGiven = consentGiven;
    
    // If consent is withdrawn, clear all data
    if (!consentGiven) {
      memoryStore.events = [];
      memoryStore.userProperties = {};
    }
  },
  
  // Check if consent is given
  hasConsent: function() {
    return this.consentGiven;
  },
  
  // Clear all tracking data
  clear: function() {
    memoryStore.events = [];
    memoryStore.userProperties = {};
  },
  
  // Stub for compatibility with Mixpanel API
  people: {
    set: function(properties) {
      if (!FallbackTracking.consentGiven) return;
      FallbackTracking.setUserProperties(properties);
    },
    set_once: function(properties) {
      if (!FallbackTracking.consentGiven) return;
      // Only set properties that don't already exist
      Object.entries(properties).forEach(([key, value]) => {
        if (!(key in memoryStore.userProperties)) {
          memoryStore.userProperties[key] = value;
        }
      });
    }
  },
  
  // Additional stubs for compatibility
  register: function() {},
  opt_in_tracking: function() {
    this.setConsent(true);
    return true;
  },
  opt_out_tracking: function() {
    this.setConsent(false);
    return true;
  },
  
  // Flag for tracking availability
  trackingAvailable: true
};

export default FallbackTracking; 