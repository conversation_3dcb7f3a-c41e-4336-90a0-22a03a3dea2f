import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';

const LinkedinCallback = () => {
    const location = useLocation();
    const params = new URLSearchParams(location.search);
    const code = params.get('code');

    useEffect(() => {
        localStorage.setItem('linkedin-code', code);
        window.close();

    }, []);

    return <div>Loding...</div>
};

export default LinkedinCallback;


