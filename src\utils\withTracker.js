import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Analytics from './analyticsTracking';

// Higher-order component for tracking page views
const withTracker = (WrappedComponent) => {
  const WithTrackerComponent = (props) => {
    const location = useLocation();
    
    useEffect(() => {
      // Track page view when location changes
      try {
        const page = location.pathname;
        Analytics.trackPageView(page);
      } catch (error) {
        console.warn('Error tracking page view:', error);
      }
    }, [location]);

    return <WrappedComponent {...props} />;
  };

  return WithTrackerComponent;
};

export default withTracker; 