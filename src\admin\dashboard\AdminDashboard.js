import React, { useEffect } from 'react';
import AdminHeader from '../layouts/AdminHeader.js';
import LeftSideNav from '../layouts/LeftSideNav.js';
import { PostWithTokenNoCache } from "../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import Mixpanel from '../../utils/mixpanel';

const AdminDashboard=()=>{
	// Track total active users count and revenue data when the dashboard loads
	useEffect(() => {
		getTotalActiveUser();
		getMonthlyRevenueData();
	}, []);

	// Function to get total active users count and track it in Mixpanel
	const getTotalActiveUser = async () => {
		try {
			const params = JSON.stringify({
				method: "POST",
				page: 1,
				pageSize: 100000,
				searchParams: {
					userRole: "customer",
					planStatus: "active",
				},
				sortBy: "DESC",
			});
	
			const res = await PostWithTokenNoCache(
				ApiName.getAllUserPlansPagination,
				JSON.parse(params)
			);
	
			if (res?.status === 200) {
				let data = JSON.parse(res.data.data);
				let totalActiveUsersCount = data["all_user"]["totalItems"];
				
				// Track the total active users count in Mixpanel
				Mixpanel.track('Total Active Users Count', {
					count: totalActiveUsersCount,
					timestamp: new Date().toISOString(),
					tracked_by: 'admin_dashboard',
					environment: process.env.NODE_ENV || 'development'
				});
			}
		} catch (errors) {
			console.error("Error fetching total active users:", errors);
		}
	};

	// Function to get monthly revenue data and track it in Mixpanel
	const getMonthlyRevenueData = async () => {
		try {
			// Get revenue data for the last 1 months
			const params = {
				months: 1 // Number of months to fetch data for
			};

			const res = await PostWithTokenNoCache(
				ApiName.transactionAmount,
				params
			);

			if (res?.status === 200 && res.data?.data) {
				const revenueData = JSON.parse(res.data.data);
				
				// Track the revenue data in Mixpanel
				Mixpanel.track('Monthly Revenue Data', {
					revenue_data: revenueData,
					months_analyzed: params.months,
					timestamp: new Date().toISOString(),
					tracked_by: 'admin_dashboard',
					environment: process.env.NODE_ENV || 'development'
				});

				// If the data has monthly breakdown, track each month separately for better analytics
				if (Array.isArray(revenueData)) {
					revenueData.forEach(monthData => {
						Mixpanel.track('Monthly Revenue', {
							month: monthData.month,
							year: monthData.year,
							total_amount: monthData.total_amount,
							currency: monthData.currency || 'USD',
							transaction_count: monthData.transaction_count,
							average_transaction: monthData.average_transaction,
							timestamp: new Date().toISOString(),
							tracked_by: 'admin_dashboard',
							environment: process.env.NODE_ENV || 'development'
						});
					});
				}
			}
		} catch (errors) {
			console.error("Error fetching monthly revenue data:", errors);
		}
	};

	return(
		<div>
			<AdminHeader />
			<LeftSideNav />
		</div>
	)
}
export default AdminDashboard;