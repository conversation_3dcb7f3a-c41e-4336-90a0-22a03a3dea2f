import React, { useEffect, useState } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import loadingGif from "../../../customer/assests/waiting.gif";
import Alert from "../../../customer/common-files/alert.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import { useNavigate, useParams } from "react-router-dom";
import AdminHeader from "../../layouts/AdminHeader.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";

const ErrorNotification = () => {
  const navigate = useNavigate();
  let data_params = useParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [data, setData] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 10; // You can adjust the number of items per page here
  const [currentPage, setCurrentPage] = useState(1);
  const [formattedDate, setFormattedDate] = useState("--");
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  useEffect(() => {
    getData();
  }, [currentPage, searchQuery]);

  const getData = async () => {
    let dataParam = JSON.parse(data_params.id);
    try {
      let params = JSON.stringify({
        page: currentPage,
        pageSize: itemsPerPage,
        sortBy: "createdAt",
        searchParams: {
          user_id: dataParam.userid,
          response_status: ["500", "400", "401", "403"],
          latest_by_days: dataParam.days
        },
      });
      const res = await PostWithTokenNoCache(ApiName.fetchUserHistory, params);
      if (res && "status" in res) {
        if (res?.data?.status == 200 && "data" in res.data) {
          let data = res.data.data.user_filter_usage_history;
          setData(data.items);
          setLoading(false);
          setTotalPages(data.totalPages);
          setTotalItems(data.totalItems);
        }
      }
    } catch (errors) {
      setLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(errors?.response?.data?.message);
      setDefaultAlert(true);
    }
  };
  const back = () => {
    let data_param = JSON.parse(data_params.id);
    let data = JSON.stringify({
      id: data_param.userid,
      userPlans: data_param.userPlans,
    });
    navigate("/admin/user-insights/" + data);
  };
  useEffect(() => {
    const currentDate = new Date();
    let date = format_Date(currentDate);
    setFormattedDate(date);
  }, []);

  const format_Date = (date) => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };
  const searchKeyValue = (key) => {
    switch (key) {
      case "contact_name":
        return "Contact Name";
      case "contact_first_name":
        return "First Name";
      case "contact_middle_name":
        return "Middle Name";
      case "contact_last_name":
        return "Last Name";
      case "contact_job_title_1":
        return "Job Title";
      case "contact_job_title_level_1":
        return "Job Title Level";
      case "contact_job_dept_name_1":
        return "Job Department Name";
      case "contact_job_function_name_1":
        return "Job Function Name";
      case "contact_email_1":
        return "email";
      case "contact_phone_1":
        return "Phone Number";
      case "company_company_name":
        return "Company Name";
      case "company_website":
        return "Website";
      case "company_address_street":
        return "Address";
      case "company_address_city":
        return "City";
      case "company_address_state":
        return "State";
      case "company_address_zipcode":
        return "Zipcode";
      case "company_address_country":
        return "Country";
      case "company_employee_size":
        return "Company Employee Size";
      case "company_annual_revenue_amount":
        return "Company Annual Revenue Amount";
      case "sic_code":
        return "Siccode";
      case "company_industry_categories_list":
        return "Industry Category";
      case "contact_social_linkedin":
        return "Contact Social Linkedin";
      case "npi_number":
        return "NPI Number";
      default:
        return key;
    }
  };
  const filterApplied = (string) => {
    let searchstring = "";
    let array = JSON.parse(string);
    let array1 = array.searchPattern;
    if (array1 !== undefined) {
      Object.keys(array1).forEach((key) => {
        const value = array1[key];
        if (Object.keys(value).length !== 0 && key !== "searchBy") {
          if (searchstring === "") {
            searchstring = searchKeyValue(key);
          } else {
            searchstring += "|" + searchKeyValue(key);
          }
        }
      });
      return searchstring;
    } else {
      return "--";
    }
  };
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const strTime = hours + ":" + minutes + ampm;

    return `${day}/${month}/${year} - ${strTime}`;
  };
  const getResponseMessage = (responseStatus) => {
    switch (responseStatus) {
      case "200":
        return "Success";
        break;
      case "400":
        return "Invalid token";
        break;
      case "500":
        return "Internal Server Error";
        break;
      case "401":
        return "Unauthorized";
        break;
      case "403":
        return "Bad Request";
        break;
    }
  };

  return (
    <>
      <div className={S.app}>
        <AdminHeader />
        <div className={S.main_container}>
          <div className="d-flex flex-row">
            <div style={{ width: "300px" }}>
              <LeftSideNav />
            </div>
            {!loading ? (
              <div className="p-3" style={{ width: "100%" }}>
                <div className="d-flex flex-row  justify-content-between">
                  <div>
                    <p className={S.UserInsightsparagraph}>
                      No. of Error Notifications
                    </p>
                    <div className={S.underlinisights}></div>
                  </div>
                  <div>
                    <button
                      type="button"
                      onClick={() => back()}
                      className={S.DetailsOfUserback}
                    >
                      Back
                    </button>
                  </div>
                </div>
                {/* <div className="">
                  <p className={S.ErrorNotificationdate}>{formattedDate}</p>
                </div> */}

                <div className="detailsofusertablepadding">
                  <div>
                    <table className="your-refrel-lists">
                      <tbody>
                        <tr className="table-headers-saved-list mt-2">
                          <th style={{ padding: "0px 0px 0 18px" }}>
                            Timestamp
                          </th>
                          <th>Filter Type</th>
                          <th>Filter Applied</th>
                          <th>Status</th>
                        </tr>
                        {data.map((item, i) => (
                          <tr className="table-data-small">
                            <td className={S.datagoesheresavedlist}>
                              {formatDate(item.requestTime)}
                            </td>
                            <td style={{ fontSize: "13px" }} className="">
                              {item.filterType}
                            </td>
                            <td style={{ fontSize: "13px" }} className="">
                              {filterApplied(item.filterApplied)}
                            </td>
                            <td className="" style={{ fontSize: "13px" }}>
                              {getResponseMessage(item.responseStatus)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  <div className="d-flex flex-row justify-content-end mt-3">
                    {/* <div>
                            <p className={S.insightsSearchResultFound}>Search Result Found<span className={S.insightscontacts}> 200 Contacts</span></p>
                        </div> */}
                    <div>
                      {searchQuery ? (
                        ""
                      ) : (
                        <Pagination
                          className="pagination-bar"
                          currentPage={currentPage}
                          totalCount={totalPages}
                          pageSize={1}
                          onPageChange={(page) => {
                            setCurrentPage(page);
                          }}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div
                className="d-flex flex-row justify-content-center mt-5"
                style={{ display: loading, width: "100%" }}
              >
                <img
                  src={loadingGif}
                  alt="Loading"
                  className="loader"
                  width="400"
                  height="400"
                />
              </div>
            )}
            {defaultAlert && defaultErrorMsg ? (
              <Alert data={defaultErrorMsg} />
            ) : (
              <></>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ErrorNotification;
