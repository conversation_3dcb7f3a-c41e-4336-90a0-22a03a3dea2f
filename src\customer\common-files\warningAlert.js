import React, { useEffect, useRef } from "react";
import UseTabStore from "./useGlobalState";
import { useNavigate } from "react-router-dom";

const WarningAlert = (props) => {
    const currentDomain = window.location.origin;
    const navigate = useNavigate();
    const alertRef = useRef();
    const closeAlertRef = useRef();
    const {
        setButtonType,
        buttonType,
        defaultErrorMsg,
        defaultAlert,
        setDefaultAlert,
        setDefaultErrorMsg,
        setViewModal,
        viewModal,
    } = UseTabStore();

    useEffect(() => {
        document.addEventListener("mousedown", handleOutsideClick);
        document.addEventListener("keydown", handleEscKeyPress);

        return () => {
            document.removeEventListener("mousedown", handleOutsideClick);
            document.removeEventListener("keydown", handleEscKeyPress);
        };
    }, []);

    const handleOutsideClick = (e) => {
        if (!e.target.closest(".modal-content")) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        if (e.key === "Escape") {
            close();
        }
    };

    const close = () => {
        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");
        setViewModal(false);
        if (defaultAlert && defaultErrorMsg && buttonType !== "error") closeAlertRef?.current?.click();
    };

    const upgradeNow = () => {
        // close();
        setButtonType("upgrade");
        setDefaultAlert(true);
        setDefaultErrorMsg("upgrade_plan");
    }

    useEffect(() => {
        alertRef.current.click();
    }, []);

    return (
        <>
            <button
                type="button"
                className="btn btn-info btn-md"
                data-toggle="modal"
                data-target="#alertModal"
                ref={alertRef}
                style={{ display: "none" }}
                data-backdrop="false"
            ></button>
            {defaultAlert && defaultErrorMsg ? (
                <div
                    className="modal fade show"
                    tabIndex="-1"
                    role="dialog"
                    aria-labelledby="mySmallModalLabel"
                    aria-hidden="true"
                    id="alertModal"
                    style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        height: "100vh",
                        width: "100vw",
                        position: "fixed",
                        top: 0,
                        left: 0,
                        zIndex: 1050,
                        backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent background
                    }}
                >
                    <div
                        className="modal-dialog modal-sm"
                        style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            margin: 0,
                            height: "100%",
                            transform: "translateX(10%)", // Shift slightly to the right
                        }}
                    >
                        <div
                            className="modal-content"
                            style={{
                                width: "376px",
                                borderRadius: "16px",
                                backgroundColor: "#fff",
                                padding: "5px",
                                position: "relative",
                                boxShadow: "0 4px 10px rgba(0, 0, 0, 0.3)", // Add shadow for better visibility
                            }}
                        >
                            <div className="d-flex flex-row justify-content-end pt-2 pr-3 pb-2">
                                <button
                                    ref={closeAlertRef}
                                    type="button"
                                    className="close"
                                    data-dismiss="modal"
                                    aria-label="Close"
                                    onClick={close}
                                    style={{ display: "none" }}
                                />
                                <div onClick={close}>
                                    <img
                                        src={`${currentDomain}/images/cancel.png`}
                                        style={{ cursor: "pointer" }}
                                    />
                                </div>
                            </div>
                            <div className="d-flex flex-row justify-content-center">
                                <div>
                                    <img src={`${currentDomain}/images/grey-avetar.png`} width="50" />
                                </div>
                                <div>
                                    <p className="anerror" style={{ color: "#FFCC00" }}>
                                        ! WARNING
                                    </p>
                                </div>
                            </div>

                            <div className="lorem">
                                <p>{props.data}</p>
                            </div>

                            <div className="upgrdbutton">
                                {buttonType === "upgrade" ||
                                    defaultErrorMsg === "Upgrade your plan to search over 2000 contacts." ||
                                    defaultErrorMsg === "Insufficient email credits to perform this action." ||
                                    defaultErrorMsg === "Insufficient Credits to perform this action." ? (
                                    <button type="button" className="btn btn-sm" onClick={upgradeNow}>
                                        Upgrade
                                    </button>
                                ) : (
                                    <button type="button" onClick={close}>
                                        Close
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            ) : null}
        </>
    );
};

export default WarningAlert;
