import React from "react";
import "../assests/css/layouts/demo.css";
import { useNavigate, useParams } from "react-router-dom";
import Header from "../layouts/Header";
import { ApiName } from "../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../customer/common-files/ApiCalls.js";
const PageOne = () => {
  let data_params = useParams();
  const navigate = useNavigate();

  const goNext = () => {
    navigate("/tour-guide-contacts/" + data_params.id);
  };

  const goBack = () => {
    navigate("/tour-guide-filter/" + data_params.id);
  };

  const skipTour = async () => {
    const params = JSON.stringify({
      id: data_params.id,
      actionType: "SKIP",
      repeatTimeDefault: 1,
      currentRepeatedTime: 1,
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.appTourUpdate, params);
      if (res.data.status == "200") {
        navigate("/dashboard");
      }
    } catch (errors) {}
  };

  return (
    <>
      <div>
        <Header />
        <div className="exampletwo">
          <img
            src="../../images/demo-image-2.png"
            class="img-fluid"
            alt="Responsive image"
            style={{ width: "100%" }}
          />
          <img
            src="../../images/demo-image-3.png"
            class="img-fluid"
            alt="Responsive image"
            style={{ width: "100%",display:"none" }}
          />
          <div>
            <p className="skiptour2" onClick={skipTour}>
              Skip Tour
            </p>
          </div>
          {/* <p className="sort">Sort</p>
          <p className="Sortcontactdetails">
            Sort contact details by last updated in 3, 6, or 12 months. Show or
            hide contacts that you have downloaded before.
          </p> */}

          <div className="sortbuttons">
            <div className="d-flex flex-row justify-content-center">
              <div>
                <button type="button" onClick={goBack} className="sortback">
                  Back
                </button>
              </div>
              <div>
                <button type="button" onClick={goNext} className="sortnext">
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PageOne;
