import React, { useState, useEffect } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import AdminHeader from "../../layouts/AdminHeader.js";
import LeftSideNav from '../../layouts/LeftSideNav.js';
import {
    postWithToken,
    PostWithTokenNoCache,
} from "../../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import loadingGif from '../../../customer/assests/waiting.gif';
import { useNavigate, useParams } from "react-router-dom";
import Alert from "../../../customer/common-files/alert.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";

const UpdateDomainBlock = () => {

    const navigate = useNavigate();
    const { id } = useParams();

    const [data, setData] = useState(null);
    const [validDomainMsg, setValidDomainMsg] = useState("");
    const [validBlockStatusReason, setValidBlockStatusReason] = useState("");
    const [loading, setLoading] = useState(true);
    const {
        defaultAlert,
        defaultErrorMsg,
        setButtonType,
        setDefaultAlert,
        setDefaultErrorMsg,
    } = UseTabStore();

    useEffect(() => {
        const getData = async () => {
            try {
                let params = JSON.stringify({
                    "id": id
                });
                const res = await PostWithTokenNoCache(ApiName.findUserDomainBlock, params);
                if (res && "status" in res) {
                    if (res.status == 200) {
                        setLoading(false);
                        let response = res.data.data ? JSON.parse(res.data.data) : null;
                        setData(response);
                    }
                }
            } catch (errors) {
                setLoading(false);
                setButtonType("error");
                setDefaultErrorMsg(errors?.response?.data?.message);
                setDefaultAlert(true);
            }
        };
        getData();
    }, [])

    const handleInputDomainChange = (e) => {
        var { id, value, type } = e.target;

        if (value) {
            setValidDomainMsg("")
        } else {
            setValidDomainMsg("Domain could not be a empty")
        }
        setData((prevData) => ({
            ...prevData,
            [id]: type === 'checkbox' ? e.target.checked : value,
        }));
    };

    const handleInputBlockStatusChange = (e) => {
        var { id, value, type } = e.target;

        // check value and convert to boolean only if value is string TRUE or FALSE
        value = value === "True" ? true : value === "False" ? false : value;

        setData((prevData) => ({
            ...prevData,
            [id]: type === 'checkbox' ? e.target.checked : value,
        }));
    };

    const handleInputBlockStatusReasonChange = (e) => {
        var { id, value } = e.target;

        if (value) {
            setValidBlockStatusReason("")
        } else {
            setValidBlockStatusReason("Reason could not be a empty")
        }
        setData((prevData) => ({
            ...prevData,
            [id]: value,
        }));
    };


    const submitHandler = async () => {

        //remove attribute createdAt and ipAddress from collection
        const { createdAt, ipAddress, ...params } = data;

        const res = await PostWithTokenNoCache(ApiName.updateUserDomainBlock, params);
        if (res && "status" in res) {
            if (res.status == 200) {
                navigate("/admin/users-domain-block-list")
            }
        }

    }

    return (
        <div className={S.app}>
            <AdminHeader />
            <div className={S.main_container}>
                <div className="d-flex flex-row">
                    <div style={{ width: "350px" }}>
                        <LeftSideNav />
                    </div>

                    <div className={S.formlayout}>
                        {!loading ? (
                            <>
                                <form className="mb-3">
                                    <h3 className={S.adminsignup}>Update Users Block</h3>

                                    <div className="row">
                                        <div className="col-md-12">
                                            <div className="form-group">
                                                <label>Domain</label>
                                                <input
                                                    type="text"
                                                    className="form-control"
                                                    id="domain"
                                                    value={data.domain}
                                                    onChange={handleInputDomainChange}
                                                />
                                                {validDomainMsg ?
                                                    (<p className="invalid">{validDomainMsg} </p>)
                                                    : ("")}
                                            </div>
                                        </div>

                                        <div className="col-md-12" style={{ display: 'none' }}>
                                            <div className="form-group">
                                                <label>Block Status</label>
                                                <select
                                                    id="blockStatus"
                                                    className="form-control"
                                                    value={data.blockStatus ? "True" : "False"}
                                                    onChange={handleInputBlockStatusChange}
                                                >
                                                    <option value="True">True</option>
                                                    <option value="False">False</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div className="col-md-12">
                                            <div className="form-group">
                                                <label>Block Reason</label>
                                                <textarea
                                                    type="text"
                                                    className="form-control"
                                                    id="blockReason"
                                                    onChange={handleInputBlockStatusReasonChange}
                                                >
                                                    {data.blockReason}
                                                </textarea>
                                                {validBlockStatusReason ?
                                                    (<p className="invalid">{validBlockStatusReason} </p>)
                                                    : ("")}
                                            </div>
                                        </div>

                                    </div>
                                </form>
                                <div className="mb-3">
                                    <button
                                        type="submit"
                                        className={`${S.adminsubmit} ${validDomainMsg !== "" || validBlockStatusReason !== "" ? 'cust-disabled' : ''}`}
                                        onClick={submitHandler}
                                    >Update</button>

                                </div>
                            </>
                        ) : (
                            <div className="d-flex flex-row justify-content-center mt-5" style={{ display: loading }}>
                                <img src={loadingGif} alt="Loading" className="loader" width="400" />
                            </div>
                        )}
                    </div>
                </div>
            </div>
            {defaultAlert && defaultErrorMsg ? (
                <Alert data={defaultErrorMsg} />
            ) : (<></>)}
        </div>
    )


}

export default UpdateDomainBlock;