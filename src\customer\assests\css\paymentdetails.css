@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap");

.form-box5 {
  max-width: 550px;
  margin: 0px 28px 4px;
  padding: 0 25px 0 25px;
  background: #ffffff;
  border: 6px solid #fff;
  box-shadow: rgb(0 0 0 / 10%) 0px 4px 12px;
  border-radius: 19px;
  /* margin-top: 14%; */
}

.input-field::placeholder {
  color: #6de1a4;
  /*background-color: #EEFFF6 0% 0%;*/
  /*background-color: #F1FFFF;*/
}

.input-color {
  background-color: #eefff6;
}

input#exampleInputPass {
  background: #f1ffff;
}

/*input#exampleInputPassword {
    background: #F1FFFF;
}*/

p.start-journeyy {
  font-size: 27px;
  text-align: center;
  color: #093d54;
  font-weight: 600;
  max-width: 550px;
  margin: 0px 28px 4px;
  padding: 0 25px 0 25px;
  background: #ffffff;
  border: 6px solid #fff;
  margin-top: 9%;
}

/* .rs-logo img {
  margin: 0 0 0 0px;
} */

.fisrt-layer {
  margin: 0 0 0 4px;
  padding: 0 2rem 0 2rem;
}

input.cp-pluss {
  background-color: #093d54;
  border: none;
  border-radius: 15px;
  color: #fff;
  cursor: pointer;
  display: block;
  font-family: Roboto, sans-serif;
  margin: 0 auto 10px;
  outline: 0;
  padding: 10px 40px;
  font-size: 14px;
}

.dark-blue p {
  background-color: #55c2c3;
  padding: 6px 14px 6px 14px;
  border-radius: 19px;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

input.cp-plusss {
  background-color: #093d54;
  border: none;
  border-radius: 15px;
  color: #fff;
  cursor: pointer;
  display: block;
  font-family: Roboto, sans-serif;
  /* margin: 0 auto 10px; */
  outline: 0;
  padding: 10px 38px;
  font-size: 14px;
}

.saver-plane1 {
  margin-top: 8%;
  margin-bottom: 10%;
}

.rs-logo {
  border-collapse: collapse;
  border-radius: 1em;
  overflow: hidden;
  max-width: 338px;
  margin: auto 11px auto auto;
}

.saver-plane1 h3 {
  text-align: center;
  padding: 20px 0 16px 0px;
  color: #093d54;
  font-weight: 600;
  font-size: 27px;
  border-collapse: collapse;
  border-radius: 1em;
  overflow: hidden;
  max-width: 338px;
  margin: auto 11px auto auto;
}


hr.horizontal {
  width: 95%;
  color: #e0e3e6;
}

.second-layer {
  padding-left: 4%;
  margin: 20px 0 18px 0;
  margin-top: 7%;
}

.total-discount {
  color: #000;
  font-weight: 500;
}

button.ConfirmandPay {
  text-align: center;
  width: 100px;
  font-size: 14px;
  padding: 8px 0 8px 0px;
  outline: none;
  border-radius: 12px;
  border: 1px solid #093d54;
  background-color: #093d54;
  color: #fff;
  cursor: pointer;
  border: none;
}

.second-layer p {
  font-size: 14px;
  padding: 0 0 0 16px;
  margin: 0px 0 8px 0;
  color: #000;
  font-weight: 600;
  opacity: 0.8;
}

.adjust {
  text-align: center;
  margin-top: 10%;
}

button.Confirman {
  text-align: center;
  background-color: #093d54;
  padding: 9px 25px 9px 25px;
  color: #fff;
  border-radius: 15px;
  margin-bottom: 8%;
  cursor: pointer;
  outline: none;
  border: 0;
}

.dark {
  color: #000;
  font-weight: 600;
}

.dark-wall {
  padding: 5px 0 0 0;
  color: #000;
}

.Pnlty {
  padding: 0;
}

span.month1 {
  font-size: 9px;
  position: relative;
  bottom: 5px;
}



/*
input.cp-pluss {
    margin: 0 auto;
    display: block;
    padding: 10px 40px 10px 40px;
    outline: 0;
    background-color: #093D54;
    color: #fff;
    border-radius: 15px;
    border: none;
    font-family: 'Roboto', sans-serif;
    cursor: pointer;
    margin-bottom: 0px;
}*/

td.text-pose {
  font-size: 12px;
}

input#promocode-input {
  padding: 10px 0px 10px 11px;
}

@media only screen and (min-width: 280px) and (max-width: 767px) {
  input.cp-pluss.cust-disabled {
    padding: 9px 23px 10px 23px;
  }
}

/* #slideshow {
  width: 340px;
  margin: 0 auto;
  overflow: hidden;
} */

.slide_container {
  width: 1200px;
  /* 300 x 4 */
  position: relative;
  -webkit-animation: slide 11s ease-in-out infinite;
  -moz-animation: slide 11s ease-in-out infinite;
  -ms-animation: slide 11s ease-in-out infinite;
  -o-animation: slide 11s ease-in-out infinite;
  animation: slide 11s ease-in-out infinite;
}

.slide_container section {
  float: left;
}

.slide_container:hover {
  animation-play-state: paused;
  -webkit-animation-play-state: paused;
}

@-o-keyframes slide {
  0% {
    right: 100;
  }

  25% {
    left: 100px;
  }

  50% {
    left: 600px;
  }

  75% {
    right: 900px;
  }

  100% {
    left: 100;
  }
}

@keyframes slide {
  0% {
    left: 0;
  }

  25% {
    left: -200px;
  }

  50% {
    left: -350px;
  }

  75% {
    left: -100px;
  }

  100% {
    left: 0;
  }
}


@media only screen and (min-width: 530px) and (max-width: 1165px) {


  table.table.table-striped {
    width: 340px;
    margin: 0 0px 0 -4rem;
  }

  .extraa {
    margin: -20px 1px -16px 1px;
  }

  .saver-plane1 {
    margin-left: 0;
  }
}


.updgrade-plan-header h4 {
  text-align: center;
  padding: 20px 0 16px 0px;
  color: #093d54;
  font-weight: 600;
  font-size: 27px;
  border-collapse: collapse;
  border-radius: 1em;
  overflow: hidden;
  max-width: 338px;
  margin: auto 11px auto auto;
}