import React, { useState, useContext, useEffect } from "react";
import "../../customer/assests/css/paymentdetails.css";
import { useNavigate, useLocation } from "react-router-dom";
//import { FaUser } from '../images/promocode-success.png';

import "../../customer/assests/css/password-eye.css";

import {
  AxiosPostBearer, PostWithTokenNoCache, postWithToken
} from "../common-files/ApiCalls.js";
import { ApiName } from "../common-files/ApiNames.js";
import DashboardContext from "../common-files/ContextDashboard.js";
import { getSessionItem } from "../common-files/LocalStorage";
import Footer from "../layouts/Footer";

const AppSumoPaymentDetails = () => {
  const { dataDC, setDataDC } = useContext(DashboardContext);
  function handleFilters(name, value) {
    setDataDC({ ...dataDC, [name]: value });
  }
  const [promocode, setPromocode] = useState();
  const [error, setError] = useState(false);
  const [errorDisplay, setErrorDisplay] = useState(false);
  const [afterDiscount, setAfterDiscount] = useState(0);
  const [calculatedPrice, setCalculatedPrice] = useState();
  const location = useLocation();
  const navigate = useNavigate();
  const [promocodeInvalid, setPromocodeInvalid] = useState(true);

  const [btnVisible, setBtnVisible] = useState(false);
  const [promocodeMsg, setPromocodeMsg] = useState(false);
  const [inputDisable, setInputDisable] = useState(false);
  const [couponId, setCouponId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [packageId, setPackageId] = useState(null);
  const [validPrmocode, setValidPrmocode] = useState(false);

  const priceData = {
    access_database: "",
    custom_filter: "",
    data_fields: "",
    export: "",
    no_of_contact_views: "",
    no_of_users: "",
    price: "",
    verified_email_phone: "",
  };
  const [dollarData, setDollorData] = useState(priceData);

  let loggedUserEmail = null;
  let token = null;
  if ("loggedUserEmail" in dataDC) loggedUserEmail = dataDC.loggedUserEmail;
  if ("token" in dataDC) token = dataDC.token;
  let user = getSessionItem("user");
  user = user ? JSON.parse(user) : null;
  if (user && "token" in user) token = user.token;
  if (user && "email" in user) loggedUserEmail = user.email;
  if (!token) {
    window.location.href = "/";
  }
  useEffect(() => {
    const postData = async () => {
      try {
        const response = await fetch(ApiName.packagePricePlan, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ method: "GET" }), // Replace with your payload
        });
        const data = await response.json();
        if (data.status == 200) {
          const jsonData = JSON.parse(data.data);
          let filterPlan = jsonData.filter((value) => value?.package_name == "APP_SUMO_59" ? value : null);
          if (filterPlan[0] !== undefined || null) {
            setPackageId(filterPlan[0].id)
            setCalculatedPrice('$' + filterPlan[0].price)
            setDollorData((prevData) => ({
              ...prevData,
              ...filterPlan[0],
            }));
          }
        }
      } catch (error) {
      }
    };

    postData();

  }, [])

  const promocodeAppied = (event) => {
    event.preventDefault();
    if (promocode) {
      const params = {
        coupon_id: promocode,
        user_plan_name: dollarData.package_name,
      };

      let result = PostWithTokenNoCache(ApiName.oneTimeCoupon, params)
        .then(function (response) {

          if (
            response.data.status == 200 &&
            response.data.message == "Coupon Not Found"
          ) {
            setError("Invalid code");
            setPromocodeInvalid(false);
            setBtnVisible(false);
          } else if (
            response.data.status == 200 &&
            response.data.message == "Valid Coupon"
          ) {
            setValidPrmocode(true);
            setError(true);
            setBtnVisible(false);
            setPromocode("");
            setPromocodeMsg("Code successfully applied");
            setCalculatedPrice('$0');
            dollarData.price = 0;
            setPromocodeInvalid(true);
            setInputDisable(true);
          }
        })
        .catch(function (errors) {
          if (
            errors.response.data.status == 400
          ) {
            setError(errors.response.data.message);
            setPromocodeInvalid(false);
            setBtnVisible(false);
          }
          if (
            errors.response.data.status == 404
          ) {
            setError(errors.response.data.message);
            setPromocodeInvalid(false);
            setBtnVisible(false);
          }
        });
    }
  };

  const onChangeHandler = (event) => {
    let promocode = event.target.value;
    setPromocode(promocode);

    const minLength = 2;
    if (promocode.length == 0) {
      setPromocodeInvalid(true);
    } else if (minLength <= promocode.length) {
      setError(true);
      setBtnVisible(true);
      setPromocodeInvalid(true);
    } else {
      setError(false);
      setBtnVisible(false);
      setPromocodeInvalid(false);
    }
  };
  const onSubmitHandler = (event) => {
    event.preventDefault();

    let total_assigned_credit = null;
    let total_assigned_contact_view = null;
    if ("export" in dollarData) total_assigned_credit = dollarData.export;
    if ("no_of_contact_views" in dollarData) total_assigned_contact_view = dollarData.no_of_contact_views;

    setLoading(true);
    const url = ApiName.createOrder;
    const dataPost = { email_id: loggedUserEmail, package_id: packageId, package_name: dollarData.package_name };

    // return true;
    async function createOrder(data) {
      // You can await here
      const res = await PostWithTokenNoCache(url, { ...data, token });
      if (res && "status" in res) {
        if (res.data.status == 200) {
          let orderObj = JSON.parse(res.data.data);
          let orderId = orderObj.orderId;
          const dataPostUserPlan = {
            user_plan_name: dollarData.package_name,
            order_id: orderId,
            status: "active",
            total_assigned_credit: total_assigned_credit,
            total_balance_credit: total_assigned_credit,
            total_assigned_contact_view: total_assigned_contact_view,
            total_balance_contact_view: total_assigned_contact_view
          };
          const urlCreateUserPlan = ApiName.createUserPlan;
          const userPlanRes = await PostWithTokenNoCache(
            urlCreateUserPlan,
            { ...dataPostUserPlan, token }
          );
          if (userPlanRes && "status" in userPlanRes) {
            if (userPlanRes.data.status == 200) {
              setLoading(false);
              navigate('/dashboard', {
                state: {
                  email: loggedUserEmail,
                  token: token,
                }
              });

            }
          }
        }
      }
    }
    setTimeout(() => { createOrder(dataPost) }, 500);

  };

  return (
    <>
      <div className="container-fluid">
        <div className="row">
          {/* <div className="col-md-1"></div> */}
          <div className="col-md-5 bg-color">
            <div className="offset-md-3 saver-plane1">
              <div className="rs-logo">
                <img src="../images/r-logo.png" width="50" className="" />
              </div>
              <h3>Super Saver Plan</h3>
              <div className="pricing-plane">
                <table className="table table-striped">
                  <thead>
                    <tr className="backrnd-color">
                      <th className="borderless">
                        <div className="d-flex flex-row justify-content-between">
                          <div className="prce"><p>Pricing</p></div>
                          <div>
                            <div className="">
                              <s className="dolllers">$499</s>
                            </div>
                            <div className="extraa"><small className="limited-offer">Limited time offer</small></div></div>
                        </div>
                      </th>
                      <th className="borderles">
                        <div className="doller-$">
                          <span className="">
                            {dollarData && dollarData.price !== null ? `$${dollarData.price}` : null} <span className="month">/ Month</span>
                          </span>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="borderless">
                      <td className="text-pose">Number of Contact Views</td>
                      <td className="text-center">
                        {dollarData.no_of_contact_views}
                      </td>
                    </tr>
                    <tr className="row-clr">
                      <td className="text-pose">Export to CSV</td>
                      <td className="text-center">{dollarData.export}</td>
                    </tr>
                    <tr>
                      <td className="text-pose">Number of Users</td>
                      <td className="text-center">{dollarData.no_of_users}</td>
                    </tr>
                    <tr className="row-clr">
                      <td className="text-pose">
                        Verified Emails and Phone Numbers
                      </td>
                      <td className="text-center">
                        {dollarData.verified_email_phone}
                      </td>
                    </tr>
                    <tr>
                      <td className="text-pose">Access to Global Database</td>
                      <td className="text-center">
                        {dollarData.access_database}
                      </td>
                    </tr>
                    <tr className="row-clr">
                      <td className="text-pose">Custom Filters</td>
                      <td className="text-center">{dollarData.custom_filter}</td>
                    </tr>

                    <tr className="borderless">
                      <td className="text-pose">Data Fields</td>
                      <td className="text-center">{dollarData.data_fields}</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div className="trusted-by">
                <p>Trusted by Businesses Globally</p>
              </div>

              <div
                id="carouselExampleControls"
                class="carousel slide"
                data-ride="carousel"
              >
                <div id="slideshow">
                  <div class="slide_container">
                    <section className="slide-item">
                      <div className="d-flex mx-auto">
                        <img
                          src="../images/mask-group-42567.png"
                          className="img-fluid mx-auto d-block"
                        />
                        <img
                          src="../images/redhat.png"
                          className="img-fluid mx-auto d-block"
                        />
                        <img
                          src="../images/cannon.png"
                          className="img-fluid mx-auto d-block"
                        />

                        <img
                          src="../images/moreneau.png"
                          className="img-fluid mx-auto d-block"
                        />
                        <img
                          src="../images/freshwork.png"
                          className="img-fluid mx-auto d-block"
                        />
                        <img
                          src="../images/saint-gobain.png"
                          className="img-fluid mx-auto d-block"
                        />
                      </div>
                    </section>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="col-md-11">
              <p className="start-journeyy">Payment Details</p>
            </div>
            <div className="form-box5">
              <div className="fisrt-layer">
                <br />
                <br />
                <div class=" d-flex justify-content-between">
                  <div className="total-discount">Total</div>
                  <div className="dark">
                    {calculatedPrice}
                  </div>
                </div>

                <div class=" d-flex justify-content-between">
                  {/* <div lassName="total-discount">Discount<span className="minus-discount">{afterDiscount ? "" : "-80%"}</span></div> */}
                  <div className="dark">
                    {/* {afterDiscount && <p>{afterDiscount}%</p>} */}
                    {/* {afterDiscount ? <p>{afterDiscount}%</p> : <p>-$400.00</p>} */}
                    {/* <p>-$400.00</p> */}
                  </div>
                </div>
              </div>
              <hr className="horizontal" />
              <div className="fisrt-layer">
                <div class=" d-flex justify-content-between">
                  <div className="dark-wall">Total (Including Tax)</div>
                  <div className="dark-blue">
                    {calculatedPrice && <p>{calculatedPrice}{Number.isInteger(+calculatedPrice.replace('$', "")) ? ".00" : ""}</p>}
                  </div>
                </div>
              </div>
              <form onSubmit={promocodeAppied}>
                <div className="second-layer">
                  <p>Have a promo code?</p>
                  <div className="d-flex">
                    <div className="col-md-7 mt-1">
                      {/* <input type="text" className="form-control"  name="promocode" onChange={onChangeHandler} id="promocode-input"/>*/}

                      <div
                        className={
                          !promocodeMsg
                            ? "input-with-icon-text"
                            : "input-with-icon"
                        }
                      >
                        {promocodeInvalid ? (
                          <input
                            type="text"
                            className="form-control input-field"
                            id="exampleInputPass"
                            name="promocode"
                            onChange={onChangeHandler}
                            value={promocode}
                            placeholder={promocodeMsg}
                            disabled={inputDisable ? "disabled" : ""}
                            autoFocus
                          />
                        ) : (
                          <></>
                        )}

                        {promocodeMsg ? (
                          <img
                            src="../images/promocode-success.png"
                            className="icon"
                          />
                        ) : (
                          <></>
                        )}
                      </div>

                      {!promocodeInvalid ? (
                        <input
                          type="text"
                          className="invalid-input-text"
                          name="promocode"
                          onChange={onChangeHandler}
                          value={promocode}
                          placeholder={promocodeMsg}
                          disabled={inputDisable ? "disabled" : ""}
                          autoFocus
                        />
                      ) : (
                        <></>
                      )}

                      <span className="email-error-message">
                        {error && <p>{error}</p>}
                      </span>
                    </div>

                    <div className="col-md-4 Pnlty">
                      {!btnVisible ? (
                        <input
                          type="submit"
                          value="Apply"
                          className="cp-pluss cust-disabled"
                        />
                      ) : (
                        <input type="submit" value="Apply" className="cp-pluss" />
                      )}
                    </div>
                  </div>
                </div>
              </form>
              <div>
                <div className="adjust">
                  {!validPrmocode ? (
                    <button type="submit" onClick={onSubmitHandler} className="Confirman cp-pluss cust-disabled">
                      {loading ? "Loading..." : "Register and Sign-in "}
                    </button>
                  ) : (
                    <button type="submit" onClick={onSubmitHandler} className="Confirman cp-pluss">
                      {loading ? "Loading..." : "Register and Sign-in "}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default AppSumoPaymentDetails;