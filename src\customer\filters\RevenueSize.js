import React, { useState, useEffect, useContext } from "react";
import { postWithToken } from "../common-files/ApiCalls.js";
import { ApiName } from "../common-files/ApiNames";
import UseTabStore from '../common-files/useGlobalState.js';
import DashboardContext from '../common-files/ContextDashboard.js';

const RevenueSize = () => {
  // State to store selected values
  const { dataDC, setDataDC } = useContext(DashboardContext);

  const {
    revenueSizeCheckedBoxes,
    resetPage,
    companyAnnualRevenueAmount,
    setSelectedCompanyAnnualRevenueAmount,
    setCurrentPage,
    setRevenueSizeCheckedBoxes,
    setIsFiltered
  } = UseTabStore();
  const [annualRevenueAmountData, setAnnualRevenueAmountData] = useState([]);

  function handleFilters(value) {
    let updatedCompanyAnnualRevenueAmount = {};
    // Update the company names based on selected options
    value.forEach((item, index) => {
      updatedCompanyAnnualRevenueAmount[index] = item;
    });
    setDataDC({ ...dataDC, ["company_annual_revenue_amount"]: value });
    setSelectedCompanyAnnualRevenueAmount(updatedCompanyAnnualRevenueAmount);
  }

  useEffect(() => {
    //check for selected data and make checkbox true
    const arrCompanyAnnualRevenueAmount = Object.values(companyAnnualRevenueAmount);
    if (arrCompanyAnnualRevenueAmount.length > 0) setRevenueSizeCheckedBoxes(arrCompanyAnnualRevenueAmount);
  }, []);

  useEffect(() => {
    if ("company_annual_revenue_amount" in dataDC) setRevenueSizeCheckedBoxes(dataDC.company_annual_revenue_amount);
  }, [dataDC, resetPage]);


  useEffect(() => {
    //set global state and update filter
    handleFilters(revenueSizeCheckedBoxes);
  }, [revenueSizeCheckedBoxes]);

  useEffect(() => {
    const url = ApiName.predefinedValuesFilter;
    const dataAmtSize = { url: "api/get-revenue-amount" };
    const fetchData = async (data) => {
      // You can await here
      const res = await postWithToken(url, data);
      if (res && "status" in res) {
        if (res.data.status == 200) {
          let titlesObj = JSON.parse(JSON.parse(res.data.data));
          if (data.url == "api/get-revenue-amount") {
            let mapped = titlesObj.map((v) =>
              JSON.parse(
                `{ "value": "${v.amount_range}" ,"label": "${v.amount_range}" }`
              )
            );
            setAnnualRevenueAmountData(mapped);
          }
        }
      }
    }
    fetchData(dataAmtSize);
  }, []);

  const handleCheckboxChange = (event) => {
    setCurrentPage(1);
    setIsFiltered(true);
    
    const checkboxValue = event.target.value;
    if (checkboxValue) {
      // const loaderElement = document.querySelector('#table-loader');
      // const loaderContactTable = document.querySelector('#cust-contact-table');
      // if (loaderElement) {
      //   loaderElement.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
      // }
      // if (loaderContactTable) {
      //   loaderContactTable.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
      // }
    }
    if (revenueSizeCheckedBoxes.includes(checkboxValue)) {
      setRevenueSizeCheckedBoxes(
        revenueSizeCheckedBoxes.filter((checkbox) => checkbox !== checkboxValue)
      );
    } else {
      setRevenueSizeCheckedBoxes([...revenueSizeCheckedBoxes, checkboxValue]);
    }
  };

  const SearchCompanyAnnualRevenueAmount = () => {
    return (
      <div className="d-flex flex-column">
        {annualRevenueAmountData && annualRevenueAmountData.map((checkbox, i) => (
          <label key={i} className="job-checkbox">&nbsp;
            <input
              key={"in_" + checkbox}
              type="checkbox"
              value={checkbox.label}
              checked={revenueSizeCheckedBoxes.includes(checkbox.label)}
              onChange={handleCheckboxChange}
            />
            {checkbox.label}
          </label>
        ))}
      </div>
    );
  };


  return (
    <div key={"revenueSize"}>
      <div>
        <div className="paragraph">
          <button
            className="Job-Titles-1"
            type="button"
            data-toggle="collapse"
            data-target="#collapseExample1Rev1"
            aria-expanded="false"
            aria-controls="collapseExample"
          >
            Select Revenue Size<i className="fa fa-caret-down"></i>

          </button>

          <div className="collapse" id="collapseExample1Rev1">
            <div className=" card-body-containerr">
              <SearchCompanyAnnualRevenueAmount />
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};

export default RevenueSize;