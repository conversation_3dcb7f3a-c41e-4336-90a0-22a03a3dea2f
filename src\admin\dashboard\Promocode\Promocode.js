import React, { useEffect, useState } from "react";
import AdminHeader from "../../layouts/AdminHeader.js";
import S from "../../assets/css/layouts/admin-header.module.css";
import { useNavigate } from "react-router-dom";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import ReactSelect from "react-select";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Pagination from "../../../customer/pagination/Pagination.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import Alert from "../../../customer/common-files/alert.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import loadingGif from "../../../customer/assests/waiting.gif";

const Promocode = (props) => {
  const {
    foundCounts,
    pageNumber,
    dataPagi,
    loadingCount,
    paginationDataCount,
  } = props;
  const [foundContacts, setFoundContacts] = useState(foundCounts);
  const [currentPage, setCurrentPage] = useState(1);
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [isEdited, setEdited] = useState(false);
  const [editedItem, setEditedItem] = useState(null);

  const itemsPerPage = 10; // You can adjust the number of items per page here
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [totalPage, setTotalPage] = useState(0);

  useEffect(() => {
    const postData = async () => {
      try {
        let params = JSON.stringify({
          page: searchQuery ? 1 : currentPage,
          pageSize: searchQuery ? 100000 : itemsPerPage,
          searchParams: {
            name: searchQuery,
          },
          sortBy: "createdAt DESC",
        });
        searchQuery ? setLoading(false) : setLoading(true);
        const res = await PostWithTokenNoCache(
          ApiName.getAllPromoWithPagination,
          params
        );
        if (res && "status" in res) {
          if (res.status == 200) {
            let jsonArray = res.data.data ? JSON.parse(res.data.data) : [];
            setTotalPage(jsonArray["totalPage"]);
            setData(jsonArray["records"] ? jsonArray["records"] : []);
            setLoading(false);
          }
        }
        setLoading(false);
      } catch (error) {
        // Handle any errors
        setLoading(false);
      }
    };
    postData();
  }, [currentPage, searchQuery]);

  const handleEdit = (id) => {
    const itemToEdit = data.find((item) => item.id === id);
    setEditedItem(itemToEdit);
    setEdited(true);
  };
  const handleDelete = async (id) => {
    try {
      // Display a confirmation dialog before proceeding with the delete operation
      const confirmed = window.confirm(
        "Are you sure you want to delete this item?"
      );

      if (!confirmed) {
        // User canceled the delete operation
        return;
      }

      let params = JSON.stringify({
        method: "POST",
      });
      const url = `${ApiName.deletePromocode}/delete/${id}`;
      const res = await PostWithTokenNoCache(url, params);

      if (res && "status" in res) {
        if (res.status == 200) {
          // Remove the deleted item from the data array
          setData((prevData) => prevData.filter((item) => item.id !== id));
        }
      }
    } catch (error) {
      // Handle any errors
    }
  };
  const handleUpdate = async (id) => {
    setEdited(false);
    // Convert the date to "YYYY-mm-dd" format
    const formattedExpiryDate = new Date(editedItem.expiry_date)
      .toISOString()
      .slice(0, 10);

    // Update the date in the item to the formatted date
    editedItem.expiry_date = formattedExpiryDate;
    // console.log(editedItem);
    try {
      // Send the updated data to the server
      const url = `${ApiName.updatePromocode}`;
      const res = await PostWithTokenNoCache(url, editedItem);

      if (res && "status" in res && res.status === 200) {
        const newData = data.map((item) =>
          item.id === id ? { ...item, ...editedItem } : item
        );
        setData(newData);

        // Reset the editedItem state and exit edit mode
        setEditedItem(null);
        setEdited(false);
      }
    } catch (error) {
      // Handle any errors
    }
  };

  const signoutHandler = async () => {
    return new Promise((resolve) => {
      sessionStorage.clear();
      resolve();
      navigate("/");
    });
  };

  const handleCreate = async () => {
    navigate("/admin/create-promocode");
  };


  return (
    <div className={S.app}>
      <AdminHeader></AdminHeader>
      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div style={{ width: "100%", padding: "0 10px 0 0" }}>
              <div className={S.content}>{/* Main content */}</div>
              <aside className={S.right_sidebar}>
                <div className={S.table_responsive}>
                  <div className="d-flex flex-row justify-content-end">
                    <div className="mr-2">
                      <button
                        onClick={() => handleCreate()}
                        className={S.createpromocode}
                      >
                        CREATE
                      </button>
                    </div>
                    <div className="admin-search">
                      <input
                        type="text"
                        placeholder="Search by promocode"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className={S.searchbyemail}
                      />
                    </div>
                  </div>
                  <table>
                    <thead className="table-active text-center">
                      <tr>
                        <th className={S.packagename}>SL No</th>
                        <th className={S.packagename}>Coupon ID</th>
                        <th className={S.packagename}>Name</th>
                        <th className={S.packagename}>Percentage</th>
                        <th className={S.packagename}>Status</th>
                        <th className={S.packagename}>Expiry At</th>
                        <th className={S.packagename}>Action</th>
                        <th></th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.map((item, i) => (
                        <tr className={S.tablelight} key={i + 1}>
                          <td className={S.centeralign}>
                            {(currentPage - 1) * 10 + i + 1}
                          </td>
                          <td className={S.centeralign}>
                            {isEdited && editedItem?.id === item.id ? (
                              <input
                                type="text"
                                value={editedItem.coupon_id}
                                onChange={(e) =>
                                  setEditedItem({
                                    ...editedItem,
                                    coupon_id: e.target.value,
                                  })
                                }
                              />
                            ) : (
                              item.coupon_id
                            )}
                          </td>
                          <td className={S.centeralign}>
                            {isEdited && editedItem?.id === item.id ? (
                              <input
                                type="text"
                                value={editedItem.name}
                                onChange={(e) =>
                                  setEditedItem({
                                    ...editedItem,
                                    name: e.target.value,
                                  })
                                }
                              />
                            ) : (
                              item.name
                            )}
                          </td>
                          <td className={S.centeralign}>
                            {isEdited && editedItem?.id === item.id ? (
                              <input
                                type="number"
                                value={editedItem.percentage}
                                onChange={(e) =>
                                  setEditedItem({
                                    ...editedItem,
                                    percentage: e.target.value,
                                  })
                                }
                              />
                            ) : (
                              item.percentage
                            )}
                          </td>
                          <td className={S.centeralign}>
                            {isEdited && editedItem?.id === item.id ? (
                              <ReactSelect
                                className={S.activedrop}
                                options={[
                                  { value: "active", label: "Active" },
                                  { value: "inactive", label: "Inactive" },
                                ]}
                                value={
                                  // (!hasDateExpired(new Date(item.expiry_date).toISOString().slice(0, 10)) && item.is_active === 'active' )
                                  editedItem.is_active === "active"
                                    ? { value: "active", label: "Active" }
                                    : {
                                      value: "inactive",
                                      label: "Inactive",
                                    }
                                }
                                onChange={(selectedOption) =>
                                  setEditedItem({
                                    ...editedItem,
                                    is_active: selectedOption.value,
                                  })
                                }
                              />
                            ) : // (!hasDateExpired(new Date(item.expiry_date).toISOString().slice(0, 10)) && item.is_active === 'active' ) ? 'Active' : 'Inactive'
                              item.is_active === "active" ? (
                                "Active"
                              ) : (
                                "Inactive"
                              )}
                          </td>
                          <td className={S.centeralign}>
                            {isEdited && editedItem?.id === item.id ? (
                              <DatePicker
                                selected={new Date(editedItem.expiry_date)}
                                onChange={(date) =>
                                  setEditedItem({
                                    ...editedItem,
                                    expiry_date: date,
                                  })
                                }
                                dateFormat="dd-MM-yyyy"
                                minDate={new Date()}
                              />
                            ) : (
                              new Date(item.expiry_date)
                                .toISOString()
                                .slice(0, 10)
                            )}
                          </td>
                          <td className="d-flex flex-row justify-content-center text-center">
                            {!isEdited && (
                              <button
                                onClick={() => handleEdit(item.id)}
                                className="btn btn-sm btn-secondary mr-2"
                              >
                                Edit
                              </button>
                            )}
                            {isEdited && editedItem?.id === item.id ? (
                              <>
                                <button
                                  onClick={() => handleUpdate(item.id)}
                                  className="btn btn-sm btn-primary mr-2"
                                >
                                  Update
                                </button>
                                <button
                                  onClick={() => setEdited(false)}
                                  className="btn btn-sm btn-secondary mr-2"
                                >
                                  Cancel
                                </button>
                              </>
                            ) : (
                              <button
                                onClick={() => handleDelete(item.id)}
                                className="btn btn-sm btn-danger mr-2"
                              >
                                Delete
                              </button>
                            )}
                          </td>
                          <td></td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </aside>
            </div>
          ) : (
            <div className="mx-auto mt-5" style={{ display: loading }}>
              <img
                src={loadingGif}
                alt="Loading"
                className="loader"
                width="400"
              />
            </div>
          )}
        </div>
      </div>
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={totalPage}
        pageSize={1}
        onPageChange={(page) => {
          setCurrentPage(page);
        }}
      />

      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};
export default Promocode;
