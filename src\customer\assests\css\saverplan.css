
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap');

.form-box h3 {
    text-align: center;
    padding: 0 0 9px 0px;
    font-weight: 600;
    margin-top: 23px;
}


span.email-address p {
    margin: 0px 0 10px 0px;
}

span.password p {
    margin: 0 0 10px 0;
}

p.semi-header {
    text-align: center;
    font-size: 14px;
}

p.semi-header a {
    text-decoration: none;
	color: #007bff;
}


p.semi-header a:hover {
    color: #007bff;
}

p.password-label {
    padding: 8px 0 0 0;
}

p.password-label a{
	text-decoration: none;
}

p.password-label a:hover{
	color: #007bff;
}

input.cp-pluss {
    margin: 0 auto;
    display: block;
    padding: 10px 40px 10px 40px;
    outline: 0;
    background-color: #0C243C;
	color: #fff;
    border-radius: 15px;
    border: none;
	cursor: pointer;
	margin-bottom: 40px;
}

/* .banner-1 img {
    width: 500px;
    margin: 0 auto;
    display: block;
    padding-top: 13%;
} */

.bg-color {
    background-color: #E8F7F7;
    height: auto;
    /* margin-left: 6%; */
}


/* Signup */

.saver-plane {
    margin-top: 10%;
    margin-bottom: 10%;
}

.saver-plane h3 {
    text-align: center;
    margin-top: 10px;
    font-weight: 700;
    color: #0C243C;
    margin-bottom: 18px;
}

th.borderless {
    color: #fff;
}

.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 1rem;
    background-color: #fff;
    border-radius: 20px;
}

th.borderless {
    border: none;
}

td.borderless{
    border: none;
}

.table td, .table th {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 0px solid #dee2e6;
}

tr.backrnd-color {
    background-color: #55C2C3;
    border-radius: 24px;
    font-size: 24px;
}

th.borderles span {
    background-color: #fff;
    padding:7px 23px 7px 23px;
    border-radius: 15px;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    padding: 20px 30px 20px 16px;
}

table.table.table-striped {
    border-collapse: collapse;
    border-radius: 1em;
    overflow: hidden;
    box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
}


td.text-pose {
    font-size: 14px;
    padding: 15px;
    padding: 12px 0 12px 20px;
    font-weight: 600;
}

td.text-center {
    font-size: 14px;
    font-weight: 600;
    padding: 17px 0 0 0px;

}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgb(255 255 255 / 5%);
}

tr.row-clr {
    background-color: #6b8eb126;
}

.trusted-by p {
    text-align: center;
    margin-top: 3rem;
    font-size: 18px;
    font-weight: 600;
}


p.start-journey {
    text-align: center;
    font-size: 27px;
    font-weight: 600;
    margin-top: 13%;
    color: #093D54;
}




.spacing {
    padding: 16px 0 0 0;
}

.custom-control.custom-checkbox {
    margin-top: 30px;
}

a.Term {
    text-decoration: none;
}

input#customCheck2 {
    border: 1px solid #C9C9C9;
    outline: none;
}

span.pill-button {
    font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande", "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
    border: none;
    background-color: #e2ebe8;
    color: rgb(0, 0, 0);
    padding: 3px 23px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 30px;
    border-radius: 33px;
    width: 39px;
    outline: none;
    margin-left: 4px;
}

.make-space {
    margin-top: 10%;
}

.pricing-plane {
    padding: 0 10px 0 10px;
}

.pin-box {
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
    border-radius: 15px;
    max-width: 600px;
    margin: 0 auto;
}

.internal-box {
    background-color: #FFF7F5;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    padding: 4px 0 0 0;
}

.total-doller p {
    padding: 0px 0 0 20px;
}

hr.line {
    margin-top: 0;
    color: #f7b1a0;
}

span.starlabel {
    color: red;
}

.including-tax p {
    padding: 0 0 0 17px;
}

.\$\$\$\$ {
    background-color: rgb(255, 255, 255);
    width: fit-content;
    padding: 0 0 0 10px;
    border-radius: 15px;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    margin-left: 25px;
}


.\$\$\$\$ p {
    padding: 6px 16px 6px 6px;
    color: #55C2C3;
}


span.input-group-btn1 {
    padding: 0 0px 0 19px;
}
form.subscribe_form {
    margin-left: 5%;
    margin-top: 5%;
}

button.btn.btn-default1 {
    padding: 8px 35px 8px 35px;
    border-radius: 17px;
    color: #fff;
    background-color: #093D54;
}

input#promocode {
    border-radius: 5px;
    border: 1px solid #C9C9C9;
}

.billing-info p {
    text-align: center;
    margin-top: 5%;
    font-size: 28px;
    font-weight: 600;
    color: #093D54;
}

.ensure {
    padding: 0 21px 0 21px;
}


button#pay-it {
    background-color: #093D54;
    color: #fff;
    padding: 10px 33px 10px 33px;
    border-radius: 16px;
    cursor: pointer;
}

.confirm {
    text-align: center;
    margin-top: 6%;
}

.secure p {
    text-align: center;
    margin-top: 5%;
    color: #7E8C9C;
}

p.class {
    font-weight: 600;
}


.secure p {
    margin: 0;
    padding: 15px 0 15px 0px;
}