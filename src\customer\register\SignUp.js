import React, { useState, useContext, useEffect } from "react";
import "../../customer/assests/css/signup.css";
import { Link, useNavigate } from "react-router-dom";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";
import "../../customer/assests/css/password-eye.css";

import { ApiName } from "../common-files/ApiNames.js";
import {
  PostWithTokenNoCache,
  axiosPost,
  AxiosPostBearer,
} from "../common-files/ApiCalls.js";
import DashboardContext from "../common-files/ContextDashboard.js";
import Footer from "../layouts/Footer.js";
import { Helmet } from "react-helmet-async";
import { useParams } from "react-router-dom";
import axios from "axios";
import {
  useGoogleLoginLogic,
  useLinkedInLoginLogic,
  useMicrosoftLoginLogic,
} from "../common-files/SocialMediaAuth.js";
import ReCAPTCHA from "react-google-recaptcha";
import { envConfig } from "../../config.js";
import UseTabStore from "../common-files/useGlobalState.js";
import GTM from '../common-files/GTM.js';
import DWINTracker from "../common-files/DWINTracker.js";
import Analytics from '../../utils/analyticsTracking';
import { trackSignupAbandonment } from '../../utils/customerJourneyTracking';
import Mixpanel from 'mixpanel-browser';

const SignUp = () => {
  // Store the last field focused for abandonment tracking
  const [lastFocusedField, setLastFocusedField] = useState('');

  const gtmId = process.env.REACT_APP_GTM_ID;

  const { packageName } = useParams();

  const { dataDC, setDataDC } = useContext(DashboardContext);
  function handleFilters(name, value) {
    setDataDC({ ...dataDC, [name]: value });
  }
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const navigate = useNavigate();
  const [minLength, setMinLength] = useState(3);

  const [fname, setFname] = useState("");
  const [fnameErr, setFnameErr] = useState("");
  const [validFname, setValidFname] = useState(false);

  const [lname, setLname] = useState("");
  const [lnameErr, setLnameErr] = useState("");
  const [validLname, setValidLname] = useState(false);

  const [email, setEmail] = useState("");
  const [emailErr, setEmailErr] = useState("");
  const [validEmail, setValidEmail] = useState(false);

  const [phone, setPhone] = useState("");
  const [phoneErr, setPhoneErr] = useState("");
  const [validPhone, setValidPhone] = useState(false);

  const [verificationCode, setVerificationCode] = useState("");
  const [verificationCodeErr, setVerificationCodeErr] = useState("");
  const [validVerificationCode, setValidVerificationCode] = useState(false);

  const [verificationCodeMsg, setVerificationCodeMsg] = useState("");

  const [password, setPassword] = useState("");
  const [passwordErr, setPasswordErr] = useState("");
  const [validPassword, setValidPassword] = useState(false);

  const [cpassword, setCpassword] = useState("");
  const [cpasswordErr, setCpasswordErr] = useState("");
  const [validConfirmPassword, setValidConfirmPassword] = useState(false);

  const [validLength, setValidLength] = useState(false);
  const [validCase, setValidCase] = useState(false);
  const [validWord, setValidWord] = useState(false);

  const [isChecked, setIsChecked] = useState(false);
  const [validCheckBox, setValidCheckBox] = useState(false);

  const [fNameInvalidText, setFNameInvalidText] = useState(true);
  const [lNameInvalidText, setLNameInvalidText] = useState(true);
  const [emailInvalidText, setEmailInvalidText] = useState(true);
  const [phoneInvalidText, setPhoneInvalidText] = useState(true);
  const [verificationCodeInvalidText, setVerificationCodeInvalidText] =
    useState(true);
  const [passwordInvalidText, setPasswordInvalidText] = useState(true);
  const [confirmpasswordInvalidText, setConfirmPasswordInvalidText] =
    useState(true);
  const [submitted, setSubmitted] = useState(false);
  const [loadingVEmail, setLoadingVEmail] = useState(false);
  const [loadingVCode, setLoadingVCode] = useState(false);
  const [isVerifiedEmail, setIsVerifiedEmail] = useState(false);
  const [sendVerificationCode, setSendVerificationCode] = useState(false);
  const [verifyEmail, setVerifyEmail] = useState(false);
  const priceData = {
    access_database: "",
    custom_filter: "",
    data_fields: "",
    export: "",
    no_of_contact_views: "",
    no_of_users: "",
    price: "",
    verified_email_phone: "",
  };
  const [dollarData, setDollorData] = useState(priceData);
  const [customPackageId, setCustomPackageId] = useState();
  const [seconds, setSeconds] = useState(600); // 10 minutes in seconds
  const [isActive, setIsActive] = useState(false);
  const [blockedDomain, setBlockedDomain] = useState([]);

  const [captchValue, setCaptchValue] = useState("");
  const [companyName, setCompanyName] = useState("");
  const yearlyPlanIds = envConfig.yearlyPlanIds;

  const [passwordValidation, setPasswordValidation] = useState({
    uppercase: false,
    lowercase: false,
    number: false,
    specialChar: false,
  });
  const url = new URL(window.location.href);
  const params = new URLSearchParams(url.search);
  // Get the referralcode from the URL
  const referralcode = params.get("referralcode");
  const { setSelectedPlanType, setReferralCode, setSelectedPlanDetails } = UseTabStore();

  // Track field focus for abandonment tracking
  const trackFieldFocus = (fieldName) => {
    setLastFocusedField(fieldName);
  };

  // Setup abandonment tracking
  useEffect(() => {
    // Save timestamp when form is first loaded
    const signupStartTime = new Date().getTime();
    sessionStorage.setItem('signup_start_time', signupStartTime);

    // Gather device and location info
    const deviceInfo = {
      browser: navigator.userAgent,
      device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
      viewport_size: `${window.innerWidth}x${window.innerHeight}`,
      language: navigator.language || navigator.userLanguage
    };

    // Get UTM parameters from localStorage
    const utmParams = localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {};

    // Check if we've already tracked this event for this session
    if (!sessionStorage.getItem('paid_signup_started_tracked')) {
      // Track initial form view
      Analytics.track('Paid Sign Up Started', {
        form_type: packageName || 'standard',
        referral_source: referralcode || '',
        location: window.location.href,
        page_title: document.title,
        referrer: document.referrer || 'direct',
        ...deviceInfo,
        ...utmParams
      });

      // Set flag to prevent duplicate tracking
      sessionStorage.setItem('paid_signup_started_tracked', 'true');
    }

    // Add beforeunload event listener to track abandonment
    const handleBeforeUnload = () => {
      // Get completed fields
      const formData = {
        firstName: fname,
        lastName: lname,
        email: email,
        phone: phone,
        password: password ? '(password provided)' : '',
        confirmPassword: cpassword ? '(confirmed)' : '',
        lastFocusedField,
        planType: packageName || '',
        totalFields: 6, // Count of total fields in the form
        completedFields: [
          fname ? 'firstName' : null,
          lname ? 'lastName' : null,
          email ? 'email' : null,
          phone ? 'phone' : null,
          password ? 'password' : null,
          cpassword ? 'confirmPassword' : null
        ].filter(Boolean).length,
        ...deviceInfo
      };

      // Track the abandonment
      trackSignupAbandonment(formData);

      // Also track as standard event
      Analytics.track('Incomplete Signup', {
        form_completion_percentage: (formData.completedFields / formData.totalFields) * 100,
        last_field: lastFocusedField,
        time_spent: (new Date().getTime() - signupStartTime) / 1000, // in seconds
        form_fields_completed: formData.completedFields,
        ...deviceInfo
      });
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [fname, lname, email, phone, password, cpassword, lastFocusedField, packageName, referralcode]);



  useEffect(() => {
    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const utmParams = {};

    // List of UTM parameters we want to track
    const utmKeys = [
      'utm_source',
      'utm_id',
      'utm_campaign',
      'awc',
      'sn'
    ];

    // Check if any UTM parameters exist in URL
    let hasUtmParams = false;

    utmKeys.forEach(key => {
      if (urlParams.has(key)) {
        utmParams[key] = urlParams.get(key);
        hasUtmParams = true;
      }
    });

    if (hasUtmParams) {
      // Store all UTM parameters in localStorage
      localStorage.setItem('utm_params', JSON.stringify(utmParams));
    } else {
      // Remove UTM parameters from localStorage if they exist
      if (localStorage.getItem('utm_params')) {
        localStorage.removeItem('utm_params');
      }
    }
  }, []);

  useEffect(() => {
    if (referralcode) {
      setReferralCode(referralcode)
    }
  }, [])

  useEffect(() => {
    const params = {
      method: "POST",
    };
    let result = axios
      .post(ApiName.activeDomainBlockList, params)
      .then(function (response) {
        // const token = JSON.parse(response.data.data);
        if (response.data.status == 200) {
          let data = JSON.parse(response.data.data);
          // Extract the "domain" property from each object and store in an array
          const domainArray = data.map((item) => item.domain);
          setBlockedDomain(domainArray);
        }
      });
  }, []);

  useEffect(() => {
    let interval;

    if (isActive && seconds > 0) {
      interval = setInterval(() => {
        setSeconds((prevSeconds) => prevSeconds - 1);
      }, 1000);
    } else if (seconds === 0) {
      setIsActive(false);
      // You can perform an action when the timer reaches 0 here
    }

    return () => clearInterval(interval);
  }, [isActive, seconds]);

  // Format the seconds into minutes and seconds
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  useEffect(() => {
    setCustomPackageId(dollarData.id);
  }, [dollarData]);
  const handleCheckboxChange = (event) => {
    setIsChecked(event.target.checked);
    if (isChecked) {
      setValidCheckBox(false);
    } else {
      setValidCheckBox(true);
    }
  };

  function convertToOriginalString(urlFriendlyString) {
    // Split the URL-friendly string by hyphens
    const words = urlFriendlyString.split("-");

    // Capitalize the first letter of each word and join them with spaces
    const originalString = words
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");

    return originalString;
  }

  const onChangeFname = (event) => {
    const specialChars = /[`!@#$%^&*()_+\=\[\]{};:"\\|,.<>\/?~*********]/;
    let fname = event.target.value;
    setFname(fname);
    if (specialChars.test(fname)) {
      setValidFname(false);
      setFNameInvalidText(false);
      setFnameErr("Enter a valid first name, avoid using 123@$!");
    } else {
      setValidFname(true);
      setFnameErr("");
      setFNameInvalidText(true);
    }
  };
  const onChangeCompanyName = (event) => {
    let cname = event.target.value;
    setCompanyName(cname);
  };
  const onChangeLname = (event) => {
    let lname = event.target.value;
    setLname(lname);
    const specialChars = /[`!@#$%^&*()_+\=\[\]{};:"\\|,.<>\/?~*********]/;
    if (specialChars.test(lname)) {
      setValidLname(false);
      setLNameInvalidText(false);
      setLnameErr("Enter a valid last name, avoid using 123@$!");
    } else {
      setValidLname(true);
      setLnameErr("");
      setLNameInvalidText(true);
    }
  };
  const onChangeEmail = (event) => {
    let email = event.target.value;

    setEmail(email.toLowerCase());

    // Construct dynamic RegExp for disallowed domains
    const disallowedDomainsRegExp = new RegExp(
      `^.+@((?!${blockedDomain.join("|")}).)+$`
    );
    let result = disallowedDomainsRegExp.test(email);

    if (result) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      let checkDomain = emailRegex.test(email);

      if (!checkDomain) {
        setEmailErr("Please enter a valid business email address");
        setValidEmail(false);
        setEmailInvalidText(false);
      } else {
        setValidEmail(true);
        setEmailErr("");
        setEmailInvalidText(true);
      }
    } else {
      setEmailErr("Please enter a valid business email address");
      setValidEmail(false);
      setEmailInvalidText(false);
    }
  };
  function validatePhoneNumber(phoneNumber) {
    // Regular expression for a phone number with + sign and country code
    const phoneRegex = /^\+\d{1,}$/;

    // Test the phone number against the regular expression
    return phoneRegex.test(phoneNumber);
  }
  const onChangePhone = (event) => {
    let phone = event.target.value;
    // Remove non-numeric characters (except '+')
    phone = phone.replace(/[^0-9+]/g, "");
    setPhone(phone);
    const isValid = validatePhoneNumber(phone);
    if (isValid) {
      setValidPhone(true);
      setPhoneErr("");
      setPhoneInvalidText(true);
    } else {
      setValidPhone(false);
      setPhoneInvalidText(false);
      setPhoneErr("Enter a valid phone number with a country code");
    }
  };
  const onChangePassword = (event) => {
    let password = event.target.value.trim();
    setPassword(password);
    const minLength = 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /(?=.*[0-9]).*$/.test(password);
    const hasSymbol = /^(?=.*[~`!@#$%^&*()--+={}\[\]|\\:;"'<>,.?/_₹]).*$/.test(
      password
    );
    const isDictionaryWord =
      /password|123456|qwerty|letmein|monkey|football/.test(password);

    if (password.length < minLength) {
      setValidLength(false);
      setPasswordInvalidText(false);
    } else {
      setValidLength(true);
      setPasswordInvalidText(true);
    }
    setPasswordValidation({
      uppercase: hasUppercase,
      lowercase: hasLowercase,
      number: hasNumber,
      specialChar: hasSymbol,
    });
    if (
      password.length < minLength ||
      !hasUppercase ||
      !hasLowercase ||
      !hasNumber ||
      !hasSymbol
    ) {
      setValidCase(false);
      setPasswordInvalidText(false);
    } else {
      setValidCase(true);
      setPasswordInvalidText(true);
    }

    if (isDictionaryWord) {
      setValidWord(false);
      setPasswordInvalidText(false);
    } else {
      setValidWord(true);
      setPasswordInvalidText(true);
    }

    if (validLength && validCase && validWord) {
      setValidPassword(true);
      setPasswordInvalidText(true);
    }

    if (cpassword !== password) {
      setValidConfirmPassword(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidConfirmPassword(true);
      setConfirmPasswordInvalidText(true);
    }
  };
  const onChangeConfirmPassword = (event) => {
    let cPassword = event.target.value;
    setCpassword(cPassword);

    const minLength = 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSymbol = /[\W_]/.test(password);
    const isDictionaryWord =
      /password|123456|qwerty|letmein|monkey|football/.test(password);

    if (password.length < minLength) {
      setValidLength(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidLength(true);
      setConfirmPasswordInvalidText(true);
    }

    if (!hasUppercase || !hasLowercase || !hasNumber || !hasSymbol) {
      setValidCase(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidCase(true);
      setConfirmPasswordInvalidText(true);
    }

    if (isDictionaryWord) {
      setValidWord(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidWord(true);
      setConfirmPasswordInvalidText(true);
    }
    if (validLength && validCase && validWord) {
      setValidPassword(true);
      setConfirmPasswordInvalidText(true);
    }

    if (cPassword !== password) {
      setValidConfirmPassword(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidConfirmPassword(true);
      setConfirmPasswordInvalidText(true);
    }
  };

  const onChangeVerificationCode = (event) => {
    let verificationCode = event.target.value;
    setLoadingVEmail(true);
    setVerificationCode(verificationCode);
    setValidVerificationCode(true);
    setVerificationCodeErr("");
    setVerificationCodeInvalidText(true);
    setLoadingVCode(true);
  };

  const onSubmitVerificationCode = (event) => {
    let customPackageId = null;
    event.preventDefault();
    if (validFname && validLname && validEmail) {
      setVerificationCodeMsg("");
      const allData = {
        firstName: fname.trim(),
        lastName: lname.trim(),
        email: email.trim(),
        userRole: "customer",
        phoneNumber: phone ? phone.trim() : null,
        packageId: customPackageId,
      };
      setLoadingVCode(true);

      // Use dedicated method for tracking email verification initiation
      Analytics.trackEmailVerificationInitiated({
        plan_name: dollarData.package_name || packageName || 'standard',
        email: email.trim(),
        first_name: fname.trim(),
        last_name: lname.trim()
      });

      let result = axiosPost(ApiName.tempRegister, allData)
        .then(function (response) {
          if (response.data.status == 200) {
            setIsActive(true);
            setVerificationCodeMsg(
              "Check your inbox or spam folder for a verififcation code."
            );
            setSendVerificationCode(true);
            setLoadingVCode(false);
            setLoadingVEmail(true);
          } else if (response.data.status == 400) {
            setValidEmail(false);
            setEmailErr(response.data.message);
            setLoadingVCode(false);
            setLoadingVEmail(false);
          }
        })
        .catch(function (errors) {
          setLoadingVCode(false);
          if (errors.response.data.status == 400) {
            setValidEmail(false);
            setEmailErr(errors.response.data.message);
            setLoadingVCode(false);
            setLoadingVEmail(false);
          }
        });
    }
  };

  const onSubmitVerifyEmailAddress = (event) => {
    setLoadingVEmail(false);
    event.preventDefault();
    if (validEmail && verificationCode) {
      const allData = {
        code: verificationCode,
        email: email.trim(),
      };
      let result = axiosPost(ApiName.activeTempRegister, allData)
        .then(function (response) {
          if (response.data.status == 200) {
            setIsVerifiedEmail(true);
            setIsActive(false);

            // Use dedicated method for tracking successful email verification
            Analytics.trackEmailVerified({
              plan_name: dollarData.package_name || packageName || 'standard',
              email: email.trim(),
              first_name: fname.trim(),
              last_name: lname.trim(),
              verification_method: 'code'
            });
          } else if (response.data.status == 400) {
            setLoadingVEmail(true);
            setValidVerificationCode(false);
            setVerificationCodeErr(response.data.message);

            // Use dedicated method for tracking failed email verification
            Analytics.trackEmailVerificationFailed({
              plan_name: dollarData.package_name || packageName || 'standard',
              email: email.trim(),
              error: response.data.message
            });
          }
        })
        .catch(function (errors) {
          if (errors.response.data.status == 400) {
            setLoadingVEmail(true);
            setValidVerificationCode(false);
            setVerificationCodeErr(errors.response.data.message);

            // Use dedicated method for tracking failed email verification
            Analytics.trackEmailVerificationFailed({
              plan_name: dollarData.package_name || packageName || 'standard',
              email: email.trim(),
              error: errors.response.data.message
            });
          }
        });
    }
  };

  const onSubmitHandler = (event) => {
    event.preventDefault();
    if (
      validFname &&
      validLname &&
      validEmail &&
      // validPhone &&
      validPassword &&
      validPassword &&
      validConfirmPassword &&
      captchValue
    ) {
      // Track successful signup attempt
      try {
        // Gather device and location info
        const deviceInfo = {
          browser: navigator.userAgent,
          device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
          viewport_size: `${window.innerWidth}x${window.innerHeight}`,
          language: navigator.language || navigator.userLanguage
        };

        // Get UTM parameters from localStorage
        const utmParams = localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {};

        // Get attribution data
        const attributionData = localStorage.getItem('attribution_data') ?
          JSON.parse(localStorage.getItem('attribution_data')) : {};

        // Track signup - this will handle identifying the user and setting properties internally
        Analytics.trackSignUp('form', {
          plan_type: packageName || 'standard',
          email_domain: email.split('@')[1],
          form_completion: 'complete',
          firstName: fname,
          lastName: lname,
          email: email,
          phone: phone || '',
          company: companyName || '',
          package_id: customPackageId ? customPackageId : packageName ? packageName : "3",
          referral_code: referralcode || '',
          location: window.location.href,
          page_title: document.title,
          referrer: document.referrer || 'direct',
          form_submission_time: new Date().toISOString(),
          time_spent_seconds: sessionStorage.getItem('signup_start_time') ?
            (new Date().getTime() - parseInt(sessionStorage.getItem('signup_start_time'))) / 1000 : 0,
          // Include attribution data
          primary_source: attributionData.primary_source || 'direct',
          utm_source: attributionData.utm_source || utmParams.utm_source,
          utm_medium: attributionData.utm_medium || utmParams.utm_medium,
          utm_campaign: attributionData.utm_campaign || utmParams.utm_campaign,
          utm_content: attributionData.utm_content || utmParams.utm_content,
          utm_term: attributionData.utm_term || utmParams.utm_term,
          referrer_domain: attributionData.referrer_domain,
          first_touch_source: attributionData.first_touch_source,
          first_touch_date: attributionData.first_touch_date,
          ...deviceInfo,
          ...utmParams,
          ...attributionData
        });
      } catch (error) {
        console.warn('Error tracking signup:', error);
      }

      // Store signup date for later tracking
      localStorage.setItem('signup_date', new Date().toISOString());

      const allData = {
        firstName: fname.trim(),
        lastName: lname.trim(),
        email: email.trim(),
        password: password.trim(),
        confirmPassword: cpassword.trim(),
        userRole: "customer",
        phoneNumber: phone ? phone.trim() : null,
        packageId: customPackageId,
        agreement: "true",
        recaptchaToken: captchValue,
        companyName,
      };
      const fullName = `${fname} ${lname}`;
      localStorage.setItem('contact_name', fullName);
      setSubmitted(true);
      let result = PostWithTokenNoCache(ApiName.superSignUp, allData)
        .then(function (response) {
          const token = JSON.parse(response.data.data);
          if (response.data.status == 200) {
            AxiosPostBearer(ApiName.deviceLogin, {}, token)
              .then(async (res) => {
                if (email && fname && token) {
                  setSessionAndNavigate(email, fname, token);
                } else {
                  setValidEmail(true);
                  setEmailErr("");
                }
              })
              .catch(function (errors) {
                setValidEmail(true);
                setEmailErr("");
              });
          }
        })
        .catch(function (errors) {
          setSubmitted(false);
          setError(errors);
        });
    }
  };

  useEffect(() => {
    const postData = async () => {
      try {
        const response = await fetch(ApiName.superSaverDollar, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ method: "GET" }), // Replace with your payload
        });
        const data = await response.json();
        if (data.status == 200) {
          const jsonData = JSON.parse(data.data);
          let filterPlan = jsonData.filter((value) => {
            const packageNameLowerCase = value?.package_name?.toLowerCase();
            const targetPackageNameLowerCase =
              convertToOriginalString(packageName)?.toLowerCase();

            return packageNameLowerCase === targetPackageNameLowerCase;
          });
          if (filterPlan[0] !== undefined || null) {
            setDollorData((prevData) => ({
              ...prevData,
              ...filterPlan[0],
            }));
          }
        }
      } catch (error) { }
    };

    postData();
  }, []);

  const { login, google_access_Token } = useGoogleLoginLogic();
  const { Microsoftlogout, microsoftAuth, MicrosoftAccessToken } =
    useMicrosoftLoginLogic();
  const { linkedInAuth, linkedinResponse } = useLinkedInLoginLogic();
  const handleGoogleLogin = () => {
    login();
  };

  useEffect(() => {
    if (google_access_Token) {
      // call the backend

      socialLogic(google_access_Token, "google");
    }
  }, [google_access_Token]);

  const microSoftAuth = () => {
    microsoftAuth(); // Call the microsoft login function
  };

  useEffect(() => {
    if (MicrosoftAccessToken) {
      socialLogic(MicrosoftAccessToken, "microsoft");
    }
  }, [MicrosoftAccessToken]);

  const linkedInLogin = () => {
    linkedInAuth.linkedInLogin();
  };

  useEffect(() => {
    if (localStorage.getItem("linkedin-code")) {
      const linkedincode = localStorage.getItem("linkedin-code");
      localStorage.removeItem("linkedin-code");
      socialLogic(linkedincode, "linkedin");
    }
  }, [linkedinResponse]);

  const socialLogic = (access_token, type) => {
    // Use Analytics instead of direct Mixpanel tracking
    Analytics.track('Social Auth Initiated', {
      provider: type,
      email: email || null
    });

    const allData = {
      userRole: "customer",
      packageId: customPackageId,
      provider_name: type,
      provider_token: access_token,
    };

    let result = axiosPost(ApiName.socialmediaregister, allData)
      .then(async function (res) {
        if (res?.status === 200) {
          const token = JSON.parse(res?.data?.data);
          AxiosPostBearer(ApiName.deviceLogin, {}, token)
            .then(async (resp) => {
              const requestBody = {
                method: "GET",
              };
              const response = AxiosPostBearer(
                ApiName.userData,
                requestBody,
                token
              )
                .then(async (response) => {
                  if (response?.status === 200) {
                    const userdata = JSON.parse(response?.data?.data);
                    if (userdata?.email && userdata?.firstName && token) {
                      localStorage.setItem("signin-type", type);
                      setSessionAndNavigate(
                        userdata?.email,
                        userdata?.firstName,
                        token,
                        type // Pass the authentication method type
                      );
                    } else {
                      setValidEmail(true);
                      setEmailErr("");
                    }
                  }
                })
                .catch(function (errors) {
                  // Use Analytics instead of direct Mixpanel tracking
                  Analytics.track('Social Auth Failed', {
                    auth_method: type,
                    error: errors?.message || 'Unknown error'
                  });
                });
            })
            .catch(function (errors) {
              setValidEmail(true);
              setEmailErr("");
              // Use Analytics instead of direct Mixpanel tracking
              Analytics.track('Social Auth Failed', {
                auth_method: type,
                error: errors?.message || 'Unknown error'
              });
            });
        }
      })
      .catch(function (errors) {
        setError(errors);
        // Use Analytics instead of direct Mixpanel tracking
        Analytics.track('Social Auth Failed', {
          auth_method: type,
          error: errors?.response?.data?.message || errors?.message || 'Unknown error'
        });
      });
  };


  const setSessionAndNavigate = async (email, firstName, token, authMethod = "email") => {
    setEmail(email);
    setFname(firstName);
    localStorage.setItem("cust-username", firstName);
    localStorage.setItem("cust-email", email);
    handleFilters("loggedUserEmail", email);
    handleFilters("token", token);
    handleFilters("packageId", customPackageId);
    localStorage.setItem(
      "user",
      JSON.stringify({ email: email, token: token, packageId: customPackageId })
    );
    setSelectedPlanDetails(dollarData)
    setSelectedPlanType(null);

    // Use Analytics instead of direct Mixpanel tracking
    Analytics.track('Signup Completed', {
      email,
      first_name: firstName,
      plan_type: packageName || 'standard',
      auth_method: authMethod,
      timestamp: new Date().toISOString(),
      referrer: document.referrer || 'direct'
    });

    // createAppTour(email);
    // navigate("/proceed-to-pay", {
    //   state: {
    //     email,
    //     token,
    //   },
    // });
    const currentDomain = window.location.origin;
    window.location.href = `${currentDomain}/proceed-to-pay`
  };

  const createAppTour = async (email) => {
    const date = Math.floor(Date.now() / 1000);
    const params = JSON.stringify({
      name: "Welcome-Tour-" + email + date,
      actionType: "OPEN",
      repeatTimeDefault: 1,
      currentRepeatedTime: 1,
    });
    try {
      const res = await PostWithTokenNoCache(ApiName.appTourCreate, params);
    } catch (errors) { }
  };

  const setError = (errors) => {
    if (
      errors.response.data.status == 400 &&
      errors.response.data.message == "Email is already registered"
    ) {
      setEmailErr(errors.response.data.message);
      setValidEmail(false);
    } else if (
      errors?.response?.data?.status === 400 &&
      errors?.response?.data?.message ===
      "The registration limit for this domain has been exceeded."
    ) {
      setEmailErr(errors.response.data.message);
      setValidEmail(false);
    } else {
      setValidEmail(true);
      setEmailErr("");
    }
  };

  const capitalizeFirstLetters = (str) => {
    if (!str || typeof str !== 'string') {
      return '';
    }
    return str
      .replace(/Yearly/gi, '') // Remove the word "Yearly" (case-insensitive)
      .trim()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };


  return (
    <>
      <Helmet>
        <title>
          {dollarData.package_name
            ? `Sign Up to ${capitalizeFirstLetters(dollarData.package_name)} | ReachStream`
            : 'Sign Up | ReachStream'}
        </title>
        <meta
          name="description"
          content={`Sign up to ReachStream's ${capitalizeFirstLetters(dollarData.package_name || 'custom')} plan to start your prospecting journey today. View and build custom B2B lists.`}
        />
      </Helmet>
      <GTM gtmId={gtmId} />
      <DWINTracker />
      <div className="container-fluid">
        <div className="row">
          <div className="col-md-5 bg-color">
            <div className="offset-md-3 saver-plane1">
              <div className="rs-logo">
                <img src="../images/r-logo.png" width="50" className="" />
              </div>
              <h3>{capitalizeFirstLetters(dollarData.package_name)}</h3>
              <div className="pricing-plane">
                <table class="table table-striped">
                  <thead>
                    <tr className="backrnd-color">
                      <th className="borderless">
                        <div className="d-flex flex-row justify-content-between">
                          <div className="prce"><p>Pricing</p></div>
                        </div>
                      </th>
                      <th className="borderles">
                        <div className="upgrade-doller-2">
                          ${dollarData.price}
                          <span className="month">
                            / {dollarData.billing_period
                              ? dollarData.billing_period
                                .split('_')
                                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                                .join(' ')
                              : ''}
                          </span>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="borderless">
                      <td className="text-pose">Unlimited Contact Views</td>
                      <td className="text-center">
                        {dollarData?.no_of_contact_views === "Unlimited" ? "Yes" : dollarData?.no_of_contact_views}
                      </td>
                    </tr>
                    <tr className="borderless">
                      <td className="text-pose">Email Credits</td>
                      <td className="text-center">
                        Unlimited
                      </td>
                    </tr>
                    <tr className="row-clr">
                      <td className="text-pose">Exports/Month</td>
                      <td className="text-center">
                        {dollarData?.export ? (Number(dollarData.export)).toLocaleString() : ""}
                      </td>
                    </tr>
                    <tr>
                      <td className="text-pose">20+ Data Insights</td>
                      <td className="text-center">Yes</td>
                    </tr>
                    <tr className="row-clr">
                      <td className="text-pose">
                        Verified Emails and Phone Numbers
                      </td>
                      <td className="text-center">Yes</td>
                    </tr>
                    <tr>
                      <td className="text-pose">Access to ReachAPI</td>
                      <td className="text-center">Yes</td>
                    </tr>
                    <tr className="row-clr">
                      <td className="text-pose">Free Monthly Updates</td>
                      <td className="text-center">Yes</td>
                    </tr>
                    <tr className="borderless">
                      <td className="text-pose">Dedicated Account Manager</td>
                      <td className="text-center">
                        {dollarData.package_name === "polar peak plan yearly" || dollarData.package_name === "polar peak" ? "Yes" : "No"}
                      </td>
                    </tr>

                    <tr className="row-clr">
                      <td className="text-pose">24/7 Customer Support</td>
                      <td className="text-center">
                        {dollarData.package_name === "polar peak plan yearly" || dollarData.package_name === "polar peak" ? "Yes" : "No"}
                      </td>
                    </tr>

                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div className="col-md-6">
            <div className="giga">
              <h3 className=" start-journey">Start Your Journey With Us!</h3>
            </div>
            <div className="form-box">
              <form>
                <h3>Sign Up</h3>
                <p className="semi-header2">
                  Already have an account?<Link to="/"> &nbsp;Sign In</Link>
                </p>

                <div className="continue">
                  <div className="d-flex flex-row justify-content-around">
                    <div className="with">
                      <p>Continue with :</p>
                    </div>
                    <div className="social" onClick={handleGoogleLogin}>
                      <img
                        src="../images/googgle.png"
                        className="img-fluid"
                        width="40"
                      />
                    </div>
                    <div className="social" onClick={microSoftAuth}>
                      <img
                        src="../images/microsoft.png"
                        className="img-fluid"
                        width="40"
                      />
                    </div>
                    <div className="social" onClick={linkedInLogin}>
                      <img
                        src="../images/linkkkedin.png"
                        className="img-fluid"
                        width="40"
                      />
                    </div>
                  </div>
                </div>

                <h6 className="horizontal-line">
                  <span className="horback">Or</span>
                </h6>
                <div className="row">
                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label for="usr" className="n-password">
                      First Name<span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    {fNameInvalidText ? (
                      <input
                        type="text"
                        className="form-control"
                        id={fname.length >= 3 ? "signupinput" : ""}
                        name="firstName"
                        onChange={onChangeFname}
                        value={fname}
                        autoFocus
                        onFocus={() => trackFieldFocus('firstName')}
                      />
                    ) : (
                      <input

                        type="text"
                        className="invalid-input-text"
                        name="firstName"
                        onChange={onChangeFname}
                        value={fname}
                        autoFocus
                        onFocus={() => trackFieldFocus('firstName')}
                      />
                    )}
                    {!validFname ? (
                      <span className="signup-errors">
                        {fnameErr && <p>{fnameErr}</p>}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label for="usr" className="n-password">
                      Last Name<span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    {lNameInvalidText ? (
                      <input
                        type="text"
                        className="form-control"
                        id={lname.length >= 1 ? "signupinput" : ""}
                        name="firstName"
                        onChange={onChangeLname}
                        value={lname}
                        onFocus={() => trackFieldFocus('lastName')}
                      />
                    ) : (
                      <input
                        type="text"
                        className="invalid-input-text"
                        name="firstName"
                        onChange={onChangeLname}
                        value={lname}
                        onFocus={() => trackFieldFocus('lastName')}
                      />
                    )}
                    {!validLname ? (
                      <span className="signup-errors">
                        {lnameErr && <p>{lnameErr}</p>}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>
                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label className="n-password">Company Name</label>
                    <input
                      type="text"
                      id={companyName.length >= 1 ? "signupinput" : ""}
                      onChange={onChangeCompanyName}
                      className="form-control"
                      onFocus={() => trackFieldFocus('companyName')}
                    />
                  </div>
                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label className="n-password">Phone Number</label>
                    <br />
                    {phoneInvalidText ? (
                      <input
                        type="text"
                        className="form-control"
                        id={validPhone ? "signupinput" : ""}
                        name="phoneNumber"
                        onChange={onChangePhone}
                        value={phone}
                        onFocus={() => trackFieldFocus('phoneNumber')}
                      />
                    ) : (
                      <input
                        type="text"

                        className="invalid-input-text"
                        name="phoneNumber"
                        onChange={onChangePhone}
                        value={phone}
                        onFocus={() => trackFieldFocus('phoneNumber')}
                      />
                    )}

                    {!validPhone ? (
                      <span className="signup-errors">
                        {phoneErr && <p>{phoneErr}</p>}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label className="n-password">
                      Business Email<span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    {emailInvalidText ? (
                      <input
                        type="text"
                        className="form-control"
                        id={validEmail ? "signupinput" : ""}
                        name="firstName"
                        onChange={onChangeEmail}
                        value={email}
                        onFocus={() => trackFieldFocus('email')}
                      />
                    ) : (
                      <input
                        type="text"
                        className="invalid-input-text"
                        name="firstName"
                        onChange={onChangeEmail}
                        value={email}
                        onFocus={() => trackFieldFocus('email')}
                      />
                    )}
                    {!validEmail ? (
                      <span className="signup-errors">
                        {emailErr && <p>{emailErr}</p>}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <div className="spacing"></div>
                    <button
                      type="button"
                      onClick={onSubmitVerificationCode}
                      className={`sendverificationcode btn-block ${loadingVCode ||
                        loadingVEmail ||
                        !validEmail ||
                        !validFname ||
                        !validLname
                        ? "cust-disabled"
                        : ""
                        }`}
                    >
                      Send Verification Code
                    </button>
                    {isActive ? (
                      <span className="signup-message">
                        <p>
                          Verify in : {String(minutes).padStart(2, "0")}:
                          {String(remainingSeconds).padStart(2, "0")}
                        </p>
                      </span>
                    ) : null}
                    <span className="signup-message">
                      {verificationCodeMsg && !isVerifiedEmail && (
                        <p>
                          {verificationCodeMsg}
                          <span
                            className="signup-message mouse-pointer"
                            onClick={onSubmitVerificationCode}
                          >
                            {verificationCodeMsg && !isVerifiedEmail && (
                              <span className="resend">Resend</span>
                            )}
                          </span>
                        </p>
                      )}
                    </span>
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>

                    <div class="form-group">
                      <label for="exampleInputEmail1">
                        Verification Code
                        <span style={{ color: "#DE350B" }}>*</span>
                      </label>

                      {verificationCodeInvalidText ? (
                        <input
                          type="text"
                          class="form-control"
                          disabled={!sendVerificationCode}
                          id={validVerificationCode ? "signupinput" : ""}
                          name="verificationCode"
                          onChange={onChangeVerificationCode}
                          value={verificationCode}
                          readOnly={isVerifiedEmail}
                          onFocus={() => trackFieldFocus('verificationCode')}
                        />
                      ) : (
                        <input
                          type="text"

                          className="invalid-input-text"
                          name="verificationCode"
                          onChange={onChangeVerificationCode}
                          value={verificationCode}
                          autoFocus
                          readOnly={isVerifiedEmail}
                          onFocus={() => trackFieldFocus('verificationCode')}
                        />
                      )}

                      {!validVerificationCode ? (
                        <span className="signup-errors">
                          {verificationCodeErr && <p>{verificationCodeErr}</p>}
                        </span>
                      ) : (
                        <></>
                      )}
                    </div>
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <div className="spacing"></div>
                    {!isVerifiedEmail ? (
                      <button
                        type="button"
                        onClick={onSubmitVerifyEmailAddress}
                        className={`verifyemailaddress btn-block ${!loadingVEmail ? "cust-disabled" : ""
                          }`}
                      >
                        &nbsp; &nbsp;Verify Email Address &nbsp;
                      </button>
                    ) : (
                      <div className="input-with-check-icon">
                        <input
                          className=" input-field-check"
                          // id="exampleInputPass"
                          name="promocode"
                          placeholder="Verification successful"
                          readOnly
                        />
                        <img
                          src="../images/promocode-success.png"
                          className="check-icon"
                        />
                      </div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <div className="spacing"></div>
                    <label className="n-password">
                      Password<span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    <div className="password-input">
                      {passwordInvalidText ? (
                        <input
                          type={showPassword ? "text" : "password"}
                          className="form-control"
                          id={password ? "signupinput" : ""}
                          name="password" //{showPassword ? "text" : "password"}
                          onChange={onChangePassword}
                          value={password}
                          onFocus={() => trackFieldFocus('password')}
                        />
                      ) : (
                        <input

                          type={showPassword ? "text" : "password"}
                          className="invalid-input-text"
                          name="password"
                          onChange={onChangePassword}
                          value={password}
                          autoFocus
                          onFocus={() => trackFieldFocus('password')}
                        />
                      )}
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        <FontAwesomeIcon
                          icon={showPassword ? faEye : faEyeSlash}
                          style={{ pointerEvents: "none", color: "#55C2C3" }}
                        />
                      </button>
                    </div>
                    {password ? (
                      <span className="signup-message">
                        {!validLength ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;Password
                            must be 8 characters
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;Password must
                            be 8 characters
                          </p>
                        )}
                        {!passwordValidation.uppercase ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one uppercase (A-Z)
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            uppercase (A-Z)
                          </p>
                        )}
                        {!passwordValidation.lowercase ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one lowercase (a-z)
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            lowercase (a-z)
                          </p>
                        )}
                        {!passwordValidation.number ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one number
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            number
                          </p>
                        )}
                        {!passwordValidation.specialChar ? (
                          <p style={{ color: "red" }}>
                            <i className="fa fa-times"></i>&nbsp;&nbsp;At least
                            one Special character (@, #, &, $, etc.)
                          </p>
                        ) : (
                          <p style={{ color: "green" }}>
                            <i className="fa fa-check"></i>&nbsp;At least one
                            Special character (@, #, &, $, etc.)
                          </p>
                        )}
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>

                  <div className="col-md-6">
                    <div className="spacing"></div>

                    <label className="n-password">
                      Confirm Password
                      <span style={{ color: "#DE350B" }}>*</span>
                    </label>
                    <div className="password-input">
                      {confirmpasswordInvalidText ? (
                        <input
                          type={showConfirmPassword ? "text" : "password"}
                          className="form-control"
                          id={validConfirmPassword ? "signupinput" : ""}
                          name="confirmPassword"
                          onChange={onChangeConfirmPassword}
                          value={cpassword}
                          onFocus={() => trackFieldFocus('confirmPassword')}
                        />
                      ) : (
                        <input

                          type={showConfirmPassword ? "text" : "password"}
                          className="invalid-input-text"
                          name="confirmPassword"
                          onChange={onChangeConfirmPassword}
                          value={cpassword}
                          onFocus={() => trackFieldFocus('confirmPassword')}
                        />
                      )}
                      <button
                        type="button"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                      >
                        <FontAwesomeIcon
                          icon={showConfirmPassword ? faEye : faEyeSlash}
                          style={{ pointerEvents: "none", color: "#55C2C3" }}
                        />
                      </button>
                    </div>
                    {validCase &&
                      validLength &&
                      validWord &&
                      !validConfirmPassword ? (
                      <span className="signup-errors">
                        <p>Passwords do not match</p>
                      </span>
                    ) : (
                      <></>
                    )}
                  </div>
                </div>

                <div className="custom-control custom-checkbox  mt-2">
                  <input
                    type="checkbox"
                    className="custom-control-input"
                    id="customCheck2"
                    defaultChecked={isChecked}
                    checked={isChecked}
                    onChange={handleCheckboxChange}
                  />
                  <label className="custom-control-label" for="customCheck2">
                    I agree to ReachStream's{" "}
                    <a
                      href="https://www.reachstream.com/terms-conditions/"
                      className="Term"
                      target="_blank"
                    >
                      Terms
                    </a>{" "}
                    and{" "}
                    <a
                      href="https://www.reachstream.com/privacy-center/"
                      className="Term"
                      target="_blank"
                    >
                      Privacy Policy.
                    </a>
                  </label>

                  {validFname &&
                    validLname &&
                    // validPhone &&
                    validPassword &&
                    validEmail &&
                    validConfirmPassword &&
                    !validCheckBox ? (
                    <span className="signup-errors">
                      <p></p>
                    </span>
                  ) : (
                    <></>
                  )}
                </div>
                <div style={{ margin: "20px 0 0 0" }}>
                  <ReCAPTCHA
                    sitekey={process.env.REACT_APP_GOOGLE_RECAPTCHA_SITE_KEY}
                    onChange={(token) => setCaptchValue(token)}
                    onExpired={() => setCaptchValue("")}
                  />
                </div>
                <div className="make-space" style={{ marginTop: "4%" }}></div>
                <span>
                  {!validFname ||
                    !validLname ||
                    // !validPhone ||
                    !validPassword ||
                    !validEmail ||
                    !validConfirmPassword ||
                    !validCheckBox ||
                    !captchValue ||
                    submitted ? (
                    <input
                      type="button"
                      onClick={onSubmitHandler}
                      value={!submitted ? "Proceed to Pay" : "Processing"}
                      className="cp-pluss1 cust-disabled"
                    />
                  ) : (
                    <input
                      type="button"
                      onClick={onSubmitHandler}
                      value="Proceed to Pay"
                      className="cp-pluss"
                    />
                  )}
                </span>
              </form>
            </div>
          </div>
          <div className="col-md-1"></div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default SignUp;
