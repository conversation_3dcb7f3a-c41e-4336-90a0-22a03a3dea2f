import React, { useEffect, useState } from "react";
import AdminHeader from "../../layouts/AdminHeader.js";
import S from "../../assets/css/layouts/admin-header.module.css";
import { useNavigate } from "react-router-dom";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import {
  postWithToken,
  PostWithTokenNoCache,
} from "../../../customer/common-files/ApiCalls.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import {
  axiosPost,
  PostReqWithTokenNoCache,
  AxiosPostBearer,
} from "../../../customer/common-files/ApiCalls.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import Alert from "../../../customer/common-files/alert.js";
import loadingGif from "../../../customer/assests/waiting.gif";

const DomainRestrictionList = (props) => {
  const {
    foundCounts,
    pageNumber,
    dataPagi,
    loadingCount,
    paginationDataCount,
  } = props;
  const [foundContacts, setFoundContacts] = useState(foundCounts);
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const itemsPerPage = 10; // You can adjust the number of items per page here
  const [currentPage, setCurrentPage] = useState(1);
  const [startFrom, setStartFrom] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [totalPage, setTotalPage] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  useEffect(() => {
    postData();
  }, [currentPage, searchQuery]);

  const postData = async () => {
    try {
      let param = JSON.stringify({
        page: searchQuery ? 1 : currentPage,
        pageSize: searchQuery ? 100000 : itemsPerPage,
        searchParams: {
          domainName: searchQuery,
        },
        sortBy: "createdAt",
      });
      searchQuery ? setLoading(false) : setLoading(true);
      const res = await PostWithTokenNoCache(
        ApiName.fetchDomainRestrictionPagination,
        param
      );
      // console.log('res',res);
      if (res?.status === 200) {
        let data = JSON.parse(res?.data?.data);
        setData(data["items"]);
        setTotalPage(data["totalPages"]);
        setTotalCount(data["totalItems"]);
        setLoading(false);
      }
    } catch (errors) {
      setLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(errors?.response?.data?.message);
      setDefaultAlert(true);
    }
  };

  const handleDelete = async (id) => {
    try {
      // Display a confirmation dialog before proceeding with the delete operation
      const confirmed = window.confirm(
        "Are you sure you want to delete this item?"
      );

      if (!confirmed) {
        // User canceled the delete operation
        return;
      }

      let params = JSON.stringify({
        id: id,
      });
      const res = await PostWithTokenNoCache(
        ApiName.deleteDomainRestriction,
        params
      );

      if (res?.status === 200) {
        // Remove the deleted item from the data array
        setData((prevData) => prevData.filter((item) => item.id !== id));
      } else {
        alert("Sorry, Please try again");
      }
    } catch (errors) {
      setLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(errors?.response?.data?.message);
      setDefaultAlert(true);
    }
  };

  const handleEdit = async (id) => {
    navigate("/admin/update-domain-restriction/" + id);
  };

  const handleCreate = async () => {
    navigate("/admin/create-domain-restriction");
  };

  return (
    <div className={S.app}>
      <AdminHeader />

      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div style={{ width: "100%", padding: "0 10px 0 0" }}>
              <h5 className={S.FreemiumUsers}>Restrict Domain</h5>
              <div className={S.content}>{/* Main content */}</div>
              <aside className={S.right_sidebar}>
                <div className="d-flex flex-row justify-content-between pl-2">
                  <div>
                    <div className="d-flex flex-row">
                      <div>
                        <h5 className={S.totla_users}>
                          Total restricted domain:
                        </h5>
                      </div>
                      <div>
                        <p className={S.datalegnth}>{totalCount}</p>
                      </div>
                    </div>
                  </div>
                  <div className="d-flex flex-row justify-content-end">
                    <div className="mr-2">
                      <button
                        onClick={() => handleCreate()}
                        className={S.createpromocode}
                      >
                        CREATE
                      </button>
                    </div>

                    <div className="admin-search">
                      <input
                        type="text"
                        placeholder="Search by Domain"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className={S.searchbyemail}
                      />
                    </div>
                  </div>
                </div>
                <table className="admin-table text-center">
                  <thead>
                    <tr className="table-active">
                      <th className={S.tableheaderspace}>Sl No</th>
                      <th className={S.tableheaderspace}>Domain</th>
                      <th className={S.tableheaderspace}>
                        Allowed User Per Domain
                      </th>
                      <th className={S.tableheaderspaces}>User Count</th>
                      <th className={S.tableheaderspace}>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((item, i) => (
                      <tr key={i + 1} className={S.tablelight}>
                        <td className={S.tabledataspaceing}>
                          {(currentPage - 1) * 10 + i + 1}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {item.domainName}
                        </td>
                        <td className={S.tabledataspaceing}>
                          {item.allowedUserPerDomain}
                        </td>
                        <td className={S.tabledataspaceingg}>
                          {item.userCount}
                        </td>
                        <td className={S.tabledataspaceing}>
                          <button
                            onClick={() => handleEdit(item.id)}
                            className="btn btn-sm btn-secondary mr-2"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDelete(item.id)}
                            className="btn btn-sm btn-danger mr-2"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </aside>
            </div>
          ) : (
            <div className="mx-auto mt-5" style={{ display: loading }}>
              <img
                src={loadingGif}
                alt="Loading"
                className="loader"
                width="400"
              />
            </div>
          )}
        </div>
      </div>

      {searchQuery ? (
        ""
      ) : (
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalPage}
          pageSize={1}
          onPageChange={(page) => {
            setCurrentPage(page);
          }}
        />
      )}

      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};
export default DomainRestrictionList;
