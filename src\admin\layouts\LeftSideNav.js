import React from "react";
import { useNavigate } from "react-router-dom";
import S from '../assets/css/layouts/admin-header.module.css';

const LeftSideNav = () => {
    const navigate = useNavigate();
    return (
        <nav className={S.left_navbar} style={{ width: "300px" }}>
            <button className="freemium" onClick={(e) => navigate('/admin/free-trial-users')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/freetrial.png" className="mr-3" /><p className={S.prop}>Freemium Users</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button className="freemium" onClick={(e) => navigate('/admin/paid-users')}> <div className="d-flex flex-row justify-content-between"><div><img src="../../images/paidusers.png" className="mr-3" /><p className={S.prop}>Paid Users</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/app-sumo')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/doller.png" className="mr-3" /><p className={S.prop}>App Sumo</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/usage-details')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/usericon.png" className="mr-3" /><p className={S.prop}>Usage Details</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/promocode')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/promocodeicon.png" className="mr-3" /><p className={S.prop}>Promocode</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/inapp-prompt')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/doller.png" className="mr-3" /><p className={S.prop}>In-App Prompt</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/all-plans')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/allplans.png" className="mr-3" /><p className={S.prop}>All Plans</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/all-inactive-users')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/doller.png" className="mr-3" /><p className={S.prop}>Inactive Plan Users</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/create-user')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/doller.png" className="mr-3" /><p className={S.prop}>Create Users</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/users-domain-block-list')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/usericon.png" className="mr-3" /><p className={S.prop}>Block Domain</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/domain-restriction-list')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/usericon.png" className="mr-3" /><p className={S.prop}>Restrict Domain</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/logs')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/usericon.png" className="mr-3" /><p className={S.prop}>In-App Logs</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/promocode-history')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/usericon.png" className="mr-3" /><p className={S.prop}>Promocode History</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            {/* <button onClick={(e) => navigate('/admin/contact-us-history')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/usericon.png" className="mr-3" /><p className={S.prop}>Contact-Us History</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button> */}
            <button onClick={(e) => navigate('/admin/incomplete-registration')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/usericon.png" className="mr-3" /><p className={S.prop}>Incomplete Registration</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
            <button onClick={(e) => navigate('/admin/unverified-accounts')}><div className="d-flex flex-row justify-content-between"><div><img src="../../images/usericon.png" className="mr-3" /><p className={S.prop}>Unverified Accounts</p></div><div><img src="../../images/Caret_right.png" width="15" className={S.align_right} /></div></div></button>
        </nav>
    )
}


export default LeftSideNav;