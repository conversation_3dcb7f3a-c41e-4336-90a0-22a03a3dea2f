
import React, { useEffect, useRef } from "react";

const ReferralError = (props) => {

    const referralAlertRef = useRef();

    useEffect(() => {
        referralAlertRef.current.click();
    }, [])

    useEffect(() => {
        // Add event listeners when the component mounts
        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('keydown', handleEscKeyPress);

        // Remove event listeners when the component unmounts
        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
            document.removeEventListener('keydown', handleEscKeyPress);
        };
    }, []);

    const handleOutsideClick = (e) => {
        // Check if the click is outside the modal
        if (!e.target.closest('.modal')) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        // Check if the pressed key is ESC
        if (e.key === 'Escape') {
            close();
        }
    };


    const close = () => {
        let setReferralErr = props.setReferralErr;
        setReferralErr(null);
    }

    return (
        <>
            <button
                type="button"
                className="btn btn-info btn-md"
                data-toggle="modal"
                data-target="#referralErr"
                ref={referralAlertRef}
                style={{ display: 'none' }}
                data-backdrop="true"
            ></button>
            <div class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" id="referralErr">
                <div class="modal-dialog modal-sm">

                    <div class="modal-content" style={{ width: "350px", borderRadius: "8px", margin: "14rem auto" }}>
                        <div className="d-flex flex-row justify-content-end">
                            <div style={{ padding: "0px 8px 0px 1px" }}>
                                <button
                                    style={{ cursor: "pointer" }}
                                    type="button"
                                    className="close"
                                    data-dismiss="modal"
                                    aria-label="Close"

                                >
                                    <span aria-hidden="true">
                                        <img src="./images/cancel.png" />
                                    </span>
                                </button>
                            </div>
                        </div>

                        <p className="following-email">The following email {props.data.length > 1 ? 'addresses' : 'address'} {props.data.length > 1 ? 'have' : 'is'} already subscribed with ReachStream</p>
                        {props.data.map((val, i) => (
                            <p className={i === props.data.length - 1 ? "jeffgennette@gmail" : "jeffgennette@gmail-1"}>{val}</p>
                        ))}

                    </div>
                </div>
            </div>
        </>
    )
}

export default ReferralError;