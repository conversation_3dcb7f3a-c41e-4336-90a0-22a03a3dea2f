import React, { useEffect, useMemo, useRef, useState, useContext } from "react";
import Header from "../../customer/layouts/Header.js";
import LeftNavbar from "../../customer/filters/LeftNavbar.js";
import penguineLoadingGif from "../../customer/assests/waiting.gif";
import "../assests/css/layouts/enrich.css";
import EnrichCustomDropdown from "./EnrichCustomDropdown.js";
import { useNavigate } from "react-router-dom";

const fieldOptions = ["Fisrt Name", "Last Name", "Job title", "Company", "ZipCode", "Phone", "Email Address", "Business Type"];

const optionsData = [
    {
        id: 'emails',
        label: 'Emails',
        icon: 'images/open-envi.png',
    },
    {
        id: 'phones',
        label: 'Phones',
        icon: 'images/phone-envi.png',
    },
    {
        id: 'company',
        label: 'Company Attributes',
        icon: 'images/company-env.png',
    },
    {
        id: 'technology',
        label: 'Technology',
        icon: 'images/technology-env.png',
    },
];



const Enrich = ({ label, options }) => {
    const [isAuth, setIsAuth] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState("CSV");
    const [showModal, setShowModal] = useState(false);
    const [isClosing, setIsClosing] = useState(false);

    const [selectedFile, setSelectedFile] = useState(null);
    const fileInputRef = useRef(null);
    // const [selectedFile, setSelectedFile] = useState(null);
    const [isDragging, setIsDragging] = useState(false);
    const [step, setStep] = useState(1);
    const [selected, setSelected] = useState(label);
    const [isOpen, setIsOpen] = useState(false);
    const [selectedOptions, setSelectedOptions] = useState(['emails']);
    const [showNewContainerAfterDone, setShowNewContainerAfterDone] = useState(false);
    const [showCrmTab, setShowCrmTab] = useState(false); // false hides CRM tab

  

    const navigate = useNavigate();

    const handleRedirect = () => {
        navigate("/data-enrich"); // <-- your route path here
      };


    useEffect(() => {
        if (!showCrmTab && activeTab === "CRM") {
            setActiveTab("CSV");
        }
    }, [showCrmTab, activeTab]);


    const toggleOption = (id) => {
        setSelectedOptions((prevOptions) =>
            prevOptions.includes(id)
                ? prevOptions.filter((option) => option !== id)
                : [...prevOptions, id]
        );
    };

    const handleSelect = (option) => {
        setSelected(option);
        setIsOpen(false);
    };


    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (file && file.name.endsWith(".csv")) {
            setSelectedFile(file);
        }
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragging(false);
        const file = e.dataTransfer.files[0];
        if (file && file.name.endsWith(".csv")) {
            setSelectedFile(file);
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = () => {
        setIsDragging(false);
    };


    const openFileDialog = () => {
        fileInputRef.current.click();
    };


    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            setShowModal(false);
            setIsClosing(false);
        }, 300); // match CSS transition duration
    };



    useMemo(() => {
        const user = JSON.parse(localStorage.getItem("user"));
        if (!user) {
            window.location.href = "/";
        } else {
            setIsAuth(true);
        }
    }, []);



    const submitEnrichment = () => {
        setShowModal(false);                  // close popup
        setShowNewContainerAfterDone(true);
        navigate("/list-enrichment");
    };

    return (
        <>
            {isAuth ? (
                <div>
                    <Header />

                    <div style={{ display: "none" }}>
                        <LeftNavbar />
                    </div>

                    {!isLoading ? (
                        <div className="main-body">
                            <div className="data-enrichment">
                                <p>Data Enrichment</p>
                            </div>

                            <div className="csv-tabs p-3">
                                {/* Tabs */}
                                <div className="d-flex flex-row border-bottom justify-content-between">
                                    {/* Tabs container */}
                                    <div className="d-flex flex-row">
                                        <div
                                            className={`enrich-tab-button py-2 ${activeTab === "CSV" ? "active-tab" : ""}`}
                                            onClick={() => setActiveTab("CSV")}
                                        >
                                            CSV
                                        </div>

                                        <div
                                            className={`enrich-tab-button py-2 ms-3 ${activeTab === "CRM" ? "active-tab" : ""} ${!showCrmTab ? "disabled-tab" : ""}`}
                                            onClick={() => {
                                                if (showCrmTab) setActiveTab("CRM");
                                            }}
                                            style={{ opacity: "0.3", cursor: "text" }}
                                        >
                                            CRM
                                        </div>
                                    </div>


                                    <div className="d-flex flex-row">
                                        <div>

                                        </div>
                                        <div>
                                            <p className="download-sample"><img src="images/down.png"  /> Download sample &nbsp; <img src="images/enrich-arrow.png" /></p>
                                        </div>

                                        <div onClick={handleRedirect} style={{ cursor: "pointer" }}>
                                            <p className="download-sample">
                                                <img src="images/down.png" alt="down icon" /> &nbsp;
                                                Enriched List &nbsp;
                                                <img src="images/enrich-arrow.png" alt="arrow icon" />
                                            </p>
                                        </div>
                                        <div>

                                        </div>
                                    </div>
                                </div>


                                {/* Tab Content */}
                                <div className="tab-content-container h-100 p-3 mt-2 rounded">
                                    {activeTab === "CSV" &&
                                        <div className="container">
                                            <div className="row mx-auto justify-content-center">
                                                <div className="col-md-5">
                                                    <div className="white-boxx container my-4 rounded shadow-sm bg-white">
                                                        <div className="">
                                                            <img src="images/enrich-companies.png" className="d-block mx-auto" width="246" alt="" />
                                                        </div>

                                                        <div>
                                                            <p className="enrich-contacts">Enrich Contacts</p>

                                                            <p className="information">Select a CSV of contacts and enrich their information.</p>


                                                        </div>

                                                        <button type="button" className="select-csv" onClick={() => setShowModal(true)}>Select CSV File</button>



                                                    </div>
                                                </div>

                                                <div className="col-md-5">
                                                    <div className="white-boxx container my-4 rounded shadow-sm bg-white">
                                                        <div className="">
                                                            <img src="images/enrich-companies.png" className="d-block mx-auto" width="238" alt="" />
                                                        </div>

                                                        <div>
                                                            <p className="enrich-contacts">Enrich Contacts</p>

                                                            <p className="information">Select a CSV of contacts and enrich their information.</p>


                                                        </div>
                                                        <button type="select" className="select-csv">Select CSV File</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>}


                                    {showCrmTab && activeTab === "CRM" && (
                                        <div>
                                            <p>This is CRM content.</p>
                                        </div>
                                    )}


                                    <div>
                                        <p className="csv-companies-enrich">Enrich contact or companies in a CSV</p>

                                        <p className="reachstream-database">Select a CSV file of contacts or companies to enrich using ReachStream's <br /> database.</p>
                                    </div>
                                </div>


                            </div>




                            {showModal && (
                                <div className={`custom-modal-overlay ${isClosing ? "fade-out" : "fade-in"}`}>
                                    <div className={`custom-modal ${isClosing ? "scale-out" : "scale-in"}`}>
                                        <div className="d-flex flex-row justify-content-between">
                                            <div className="enrich-border">
                                                <p className="enrich-popup-header">Enrich Contact</p>
                                            </div>
                                            <div>
                                                <span className="enrich-close-button" onClick={handleClose}>
                                                    <img src="images/enrichpopup.png" className="img-fluid" width="28" alt="enrichpopup-img" />
                                                </span>
                                            </div>
                                        </div>



                                        {step === 1 && (
                                            <div className="enrich-custom-modal-body">
                                                <div className="enrich-custom-modal-body-1">
                                                    <div className="choose-border">

                                                        <div>
                                                            <img src="images/csv-image.png" className="img-fluid" alt="CSV Icon" />
                                                        </div>

                                                        {!selectedFile ? (
                                                            <>
                                                                <p className="drag-and-drop">Drag & drop your CSV file</p>
                                                                <label onClick={openFileDialog} className="custom-file-trigger">
                                                                  <span className="the-or-span">or</span>  Upload CSV File
                                                                </label>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <p className="file-name">{selectedFile.name}</p>
                                                                <span className="replace-file" onClick={openFileDialog}>
                                                                    Replace file
                                                                </span>
                                                            </>
                                                        )}

                                                        <input
                                                            type="file"
                                                            accept=".csv"
                                                            ref={fileInputRef}
                                                            className="hidden-file"
                                                            onChange={handleFileChange}
                                                        />
                                                    </div>

                                                    <div>
                                                        <p className="uolpade-your-csv-here">Upload Your CSV contact list</p>
                                                        <p className="select-csv">
                                                            Select a CSV of contacts to enrich it with information from <br />
                                                            ReachStream.
                                                        </p>
                                                    </div>
                                                </div>


                                                <div className="modal-footer">
                                                    <button className="next-button" type="button" disabled={!selectedFile} onClick={() => setStep(2)}>
                                                        Next
                                                    </button>
                                                </div>
                                            </div>
                                        )}

                                        {step === 2 && (
                                            <div className="next-step-container">
                                                <div className="display-csv-file">
                                                    <div className="d-flex flex-row justify-content-between">
                                                        <div className="d-flex flex-row">
                                                            <div><img src="images/noun-csv.png" className="img-fluid" alt="" /></div>
                                                            <div><p className="referred-csv">Sampledata.csv</p></div>
                                                        </div>
                                                        <div className="d-flex flex-row">
                                                            <div>
                                                                <img src="images/reupload-csv.png" className="img-fluid" alt="" />
                                                            </div>
                                                            <div>
                                                                <p className="reupload-csv">Reupload CSV</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="scrollable-table-wrapper">
                                                    <table className="custom-scroll-table">
                                                        <thead>
                                                            <tr>
                                                                <th>ReachStream field</th>
                                                                <th>CSV column header</th>
                                                                <th>Preview</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>First name*</td>
                                                                <td>

                                                                    <EnrichCustomDropdown label="First Name" options={fieldOptions} />
                                                                </td>
                                                                <td className="table-last-column">Martin, Adrienne, Lena</td>
                                                            </tr>
                                                            <tr>
                                                                <td>Last name*</td>
                                                                <td>


                                                                    <EnrichCustomDropdown label="Last Name" options={fieldOptions} />
                                                                </td>
                                                                <td className="table-last-column">Smith, Thoms, Magil</td>
                                                            </tr>
                                                            <tr>
                                                                <td>Job title*</td>
                                                                <td>
                                                                    <EnrichCustomDropdown label="Job Title" options={fieldOptions} />
                                                                </td>
                                                                <td className="table-last-column">CEO, Owner, Director</td>
                                                            </tr>
                                                            <tr>
                                                                <td>Company*</td>
                                                                <td>
                                                                    <EnrichCustomDropdown label="Company" options={fieldOptions} />
                                                                </td>
                                                                <td className="table-last-column">SynthPoint Music, Sound Mordern, Cyberphonoc Music</td>
                                                            </tr>
                                                            <tr className="dotted-row">
                                                                <td colSpan="3">
                                                                    <div className="dotted-line"></div>
                                                                </td>
                                                            </tr>
                                                            <tr>

                                                                <td>Personal Linkedin URL</td>
                                                                <td>
                                                                    <EnrichCustomDropdown label="select" options={fieldOptions} />
                                                                </td>
                                                                <td className="table-last-column">SynthPoint Music, Sound Mordern, Cyberphonoc Music</td>
                                                            </tr>

                                                            <tr>
                                                                <td>Email</td>
                                                                <td>
                                                                    <EnrichCustomDropdown label="Email" options={fieldOptions} />
                                                                </td>
                                                                <td className="table-last-column"></td>
                                                            </tr>

                                                            <tr>
                                                                <td>Website</td>
                                                                <td>
                                                                    <EnrichCustomDropdown label="Website" options={fieldOptions} />
                                                                </td>
                                                                <td className="table-last-column">https://app.reachstream.com/</td>
                                                            </tr>

                                                            {/* Add more rows as needed */}
                                                        </tbody>
                                                    </table>
                                                </div>


                                                <div className="d-flex flex-row justify-content-between mt-3">
                                                    <div>
                                                        <button className="csv-back-button" onClick={() => setStep(1)}>
                                                            &lt; Back
                                                        </button>
                                                    </div>
                                                    <div>
                                                        <button className="next-button" type="button" disabled={!selectedFile} onClick={() => setStep(3)}>Next</button>

                                                    </div>

                                                </div>
                                            </div>
                                        )}


                                        {step === 3 && (
                                            <>
                                                <div className="enrich-custom-modal-body-1">
                                                    <div className="d-flex gap-3 flex-wrap">
                                                        {optionsData.map((option) => (
                                                            <div
                                                                key={option.id}
                                                                className={`option-card ${selectedOptions.includes(option.id) ? 'active' : ''
                                                                    }`}
                                                                onClick={() => toggleOption(option.id)}
                                                            >
                                                                <input
                                                                    className="form-check-input"
                                                                    type="checkbox"
                                                                    checked={selectedOptions.includes(option.id)}
                                                                    readOnly
                                                                />
                                                                <img src={option.icon} alt={option.label} />
                                                                <div className="label-text">{option.label}</div>
                                                            </div>
                                                        ))}
                                                    </div>

                                                    <div>
                                                        <p className="what-whould-you-like">What would you like to enrich?</p>

                                                        <p className="charged">You will only be for contacts matched in your file.</p>
                                                    </div>
                                                </div>
                                                <div className="d-flex flex-row justify-content-between mt-3">
                                                    <div>
                                                        <button className="csv-back-button" onClick={() => setStep(1)}>
                                                            &lt; Back
                                                        </button>
                                                    </div>
                                                    <div>
                                                        <button className="next-button" type="button" disabled={!selectedFile} onClick={() => setStep(4)}>Next</button>
                                                    </div>

                                                </div>
                                            </>
                                        )}

                                        {step === 4 && (
                                            <>
                                                <div className="enrich-custom-modal-body-1">

                                                    <div className="mt-4 mb-4">
                                                        <img src="images/final-csv.png" className="img-fluid mx-auto d-block" width="300" />
                                                    </div>

                                                    <p className="enriching-contacts">We are enriching your contacts</p>

                                                    <p className="notify-you">Your enriched contacts list is being prepared. We'll notify you at <br /> <span className="make-it-bold"><EMAIL></span> when it's complete.</p>

                                                    <button className="upload-another-csv">Upload another CSV</button>

                                                </div>

                                                <div className="d-flex flex-row justify-content-end mt-3">
                                                    <button
                                                        type="button"
                                                        className="next-button"
                                                        onClick={() => {
                                                            submitEnrichment();
                                                            // show final container
                                                        }}
                                                    >
                                                        Done
                                                    </button>

                                                </div>

                                            </>

                                        )}





                                    </div>


                                </div>
                            )}

                            {/* {showNextPage && (
                                <div className="mt-5 p-4 border rounded text-center bg-light">
                                    <h4>You're on the next page!</h4>
                                    <p>This replaces the tab content after clicking Done.</p>
                                </div>
                            )} */}
                        </div>
                    ) : (
                        <div className="d-flex flex-row justify-content-center mt-5">
                            <img
                                src={penguineLoadingGif}
                                alt="Loading"
                                className="loader"
                                width="400"
                            />
                        </div>
                    )}
                </div>
            ) : (
                <p>User is not authenticated.</p> // or redirect or login prompt
            )}
        </>
    );
};

export default Enrich;
