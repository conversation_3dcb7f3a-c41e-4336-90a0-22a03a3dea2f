@media (min-width: 576px) {
    .modal-dialog {
        max-width: 78rem;
        margin: 1.75rem auto;
    }
}

span.cancel-btn {
    position: relative;
    display: flex;
    justify-content: end;
    padding: 1rem 3rem 0px 0px;
}

h5.modal-title.choose-plan {
    margin: 14px 0 10px 0;
    font-size: 25px;
    color: #000;
    font-weight: 600;
    text-align: center;
}


.save-up-to p {
    text-align: center;
    font-size: 18px;
    padding: 8px 0 0px 0;
    margin: 0;
    color: #093D54;
}

.semi-ice {
    width: 326px;
    margin: auto;
    display: block;
    /* justify-content: center; */
}

.tab-container-one {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0 2rem 0;
    border-radius: 10px;
}


.roll-tab {
    width: 24px;
    height: 24px;
    /* background-color: rgb(255, 255, 255); */
    border-radius: 50%;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px;
    transition: 0.7s;
    background: rgb(8, 192, 203);
    background: linear-gradient(270deg, rgba(8, 192, 203, 1) 0%, rgba(28, 141, 156, 1) 100%);
}



.secondary-column {
    background-color: #F5F5F5;
    margin: 15px 24px 5px -28px;
    width: 55rem;
    position: absolute;
}


.pricing-header {
    text-align: center;
    margin-bottom: 20px;
}

.toggle-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
}

.toggle-label {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    color: #555;
}

.toggle-label.active {
    color: #007bff;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 25px;
    background: #ddd;
    border-radius: 50px;
    margin: 0 10px;
    cursor: pointer;
}

.slider-circle {
    position: absolute;
    top: 3px;
    left: 3px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.slider-circle.monthly {
    transform: translateX(25px);
}

.pricing-card-1 {
    border: 1px solid #8ADEDF;
    transition: box-shadow 0.3s ease;
    width: 210px;
    border-radius: 20px;
    background: rgb(232, 247, 247);
    background: linear-gradient(180deg, rgba(232, 247, 247, 1) 0%, rgba(255, 255, 255, 1) 55%, rgba(255, 255, 255, 1) 100%);
    z-index: 1000;

}

.pricing-card-2 {
    border: 1px solid #F2C3B5;
    transition: box-shadow 0.3s ease;
    width: 210px;
    border-radius: 20px;
    background: rgb(255, 248, 248);
    background: linear-gradient(180deg, rgba(255, 248, 248, 1) 0%, rgba(255, 255, 255, 1) 54%);
    z-index: 1000;
    position: absolute;
    top: -20px;
}

.pricing-card-3 {
    border: 1px solid #B8C9E4;
    transition: box-shadow 0.3s ease;
    width: 210px;
    border-radius: 20px;
    background: rgb(233, 237, 254);
    background: linear-gradient(180deg, rgba(233, 237, 254, 1) 0%, rgba(255, 255, 255, 1) 51%, rgba(255, 255, 255, 1) 98%);
    z-index: 1000;

}

.pricing-card-4 {
    border: 1px solid #B8C9E4;
    transition: box-shadow 0.3s ease;
    width: 210px;
    border-radius: 20px;
    background: rgb(255, 255, 255);
    background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(248, 248, 248, 1) 35%);
    z-index: 1000;
    border-left: 0;

}

.pricing-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.recommended-badge {
    position: absolute;
    top: -13px;
    left: 34%;
    transform: translateX(-50%);
    background: #007bff;
    color: #fff;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 5px;
    background: rgb(8, 192, 203);
    background: linear-gradient(100deg, rgba(8, 192, 203, 1) 0%, rgba(28, 141, 156, 1) 50%);
}

.pricing-card .price {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
}

.pricing-card-1 .btn-block {
    background: #093D54;
    color: white;
    border-radius: 20px;
    padding: 7px 0px;
    margin: 2rem auto 25px;
    width: 172px;
    font-size: 14px;
    border: 1px solid #093D54;
    outline: none;
    cursor: pointer;
}

.pricing-card-2 .btn-block {
    background: #093D54;
    color: white;
    border-radius: 20px;
    padding: 7px 0px;
    margin: 2rem auto 25px;
    width: 172px;
    font-size: 14px;
    border: 1px solid #093D54;
    outline: none;
    cursor: pointer;


}

.pricing-card-3 .btn-block {
    background: #093D54;
    color: white;
    border-radius: 20px;
    padding: 7px 0px;
    margin: 2rem auto 25px;
    width: 172px;
    font-size: 14px;
    border: 1px solid #093D54;
    outline: none;
    cursor: pointer;


}

.pricing-card-4 .btn-block {
    background: #093D54;
    color: white;
    border-radius: 20px;
    padding: 7px 0px;
    margin: 15px auto 25px;
    width: 172px;
    font-size: 14px;
    border: 1px solid #093D54;
    outline: none;
    cursor: pointer;
}

.features img {
    margin: auto;
    display: block;
    padding: 32px 0 30px 0;
}

.features-cross img {
    margin: auto;
    display: block;
    padding: 29px 0 30px 0;
}

.features {
    font-size: 14px;
    /* margin-top: 10px; */
    margin: 0;
}

.features p {
    padding: 31px 0 27px 0;
    margin: 0;
    text-align: center;
    font-size: 16px;
    color: #093D54;
}

.features-Enterprises p {
    padding: 30px 0px 28px 0;
    margin: 0;
    text-align: center;
    font-size: 16px;
    color: #093D54;
}

.features-Enterprises-annually p{
    padding: 25px 0px 25px 0;
    margin: 0;
    text-align: center;
    font-size: 16px;
    color: #093D54;
}

h4.Glacier-of-month {
    font-size: 25px;
    color: #08C0CB;
    font-weight: 600;
    margin: 40px 0 40px 15px;
}

h4.ice-floe {
    font-size: 25px;
    color: #08C0CB;
    font-weight: 600;
    margin: 40px 0 40px 15px;
}

h4.ice-floe-top {
    font-size: 25px;
    color: #08C0CB;
    font-weight: 600;
    margin: 60px 0 40px 15px;
}


.original-price {
    font-size: 16px;
    text-decoration: line-through;
    color: #aaa;
    margin: 0 0 0 15px;
    position: absolute;
}

.current-price {
    font-size: 28px;
    font-weight: bold;
    color: #111833;
    margin: 0 0 0 3rem;
}

.current-price-2 {
    font-size: 28px;
    font-weight: bold;
    color: #111833;
    margin: 0 0 0 1rem;
}

.per-month {
    font-size: 14px;
    color: #000;
    margin-left: 5px;
}

.text-muted-italic {
    color: #000;
    padding: 5px 0 0 15px;
    font-style: italic;
}

.text-success {
    color: #000 !important;
    background-color: #8adedf69;
    font-size: 12px;
    text-align: center;
    padding: 10px 10px;
    font-style: italic;
    margin: 0;
}

.unlock-success {
    color: #000 !important;
    background-color: #b8c9e46e;
    font-size: 12px;
    text-align: center;
    padding: 10px 10px;
    font-style: italic;
    margin: 0;
}

.unlock-success-blank {
    color: #000 !important;
    background-color: transparent;
    font-size: 12px;
    text-align: center;
    padding: 19px 10px;
    font-style: italic;
    margin: 0;
}

p.Unlock-annual {
    color: #000 !important;
    background-color: #f2c3b580;
    font-size: 12px;
    text-align: center;
    padding: 10px 10px;
    font-style: italic;
    margin: 0;
}


.glaciar-parts {
    position: absolute;
    right: 6px;
    top: -30px;
}

.ice-flow-img {
    position: absolute;
    right: 0px;
    top: -16px;
}

.polar-peak-img {
    position: absolute;
    right: 0px;
    top: -50px;
}


p.Playing-it {
    font-size: 15px;
    color: #093D54;
    font-weight: 600;
    margin: 0;
    padding: 0px 6px 3px 15px;
}

p.text-Fully {
    font-size: 14px;
    padding: 10px 15px 10px 15px;
    color: #000;
    font-weight: 600;
}

.top-penguin {
    margin: 6rem 0 0 0;
    position: absolute;
    left: 8px;
    /* right: 19px; */
    top: 15px;
}

.view-container {
    left: 80px;
    position: absolute;
    background-color: #fff;
    width: 64rem;
    border-radius: 23px;
    border: 1px solid #E0E3E6;
    box-shadow: 0 0 20px #0000001A;
    right: 14px;
    top: 36rem;
    margin: 7px 0 0 0;
    /* z-index: 1000; */
}

p.Contact--views {
    padding: 20px 0 20px 16px;
    margin: 0;
}

.contact-box {
    border-radius: 10px;
    width: 200px;
    padding: 30px 0 30px 0;
    text-align: center;
}

.contact-box-Dedicated {
    border-radius: 10px;
    width: 200px;
    padding: 20px 0 20px 0;
    text-align: center;
}

.contact-box-perfect {
    border-radius: 10px;
    width: 200px;
    padding: 30px 0 30px 0;
    text-align: center;
}

.contact-box h4 {
    font-size: 16px;
    margin: 0 0 0 24px;
    color: #333;
    display: inline-block;
    font-weight: 600;
}

.contact-box-perfect h4 {
    font-size: 16px;
    margin: 0 0 0 24px;
    color: #333;
    display: inline-block;
    font-weight: 600;
    text-align: left;
}

.contact-box-Dedicated h4 {
    font-size: 16px;
    margin: 0 0 0 24px;
    color: #333;
    display: inline-block;
    font-weight: 600;
    text-align: left;
}

.info-icon {
    display: inline-block;
    width: 18px;
    height: 18px;
    /* margin-left: 25px; */
    /* border: 1.5px solid #00bcd4; */
    border-radius: 50%;
    text-align: center;
    font-size: 14px;
    line-height: 16px;
    color: #00bcd4;
    font-weight: bold;
    cursor: pointer;
}




/* .tooltip-container {
    display: flex;
    align-items: center;
    font-family: Arial, sans-serif;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    width: 250px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
} */

/* Text for Contact Views */
.tooltip-text {
    font-size: 16px;
    font-weight: bold;
    color: #000;
    margin-right: 8px;
}

/* Question Mark Icon */
.tooltip-icon {
    position: relative;
    display: inline-block;
    /* background-color: #00bcd4; */
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 50%;
    cursor: pointer;
    padding: 0 0 0 15px;
}

/* Hidden tooltip */
.tooltip-icon:hover .tooltip-content {
    visibility: visible;
    opacity: 1;
}

/* Tooltip Content */
.tooltip-content {
    visibility: hidden;
    opacity: 0;
    width: 330px;
    background-color: #fff;
    color: #000;
    text-align: center;
    padding: 8px 11px 8px 11px;
    border-radius: 6px;
    font-size: 13px;
    position: absolute;
    /* bottom: 0px; */
    left: 37px;
    /* transform: translateX(-50%); */
    z-index: 1000;
    transition: opacity 0.3s ease;
    top: -27px;
    right: 0;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    text-align: justify;
    line-height: 17px;
    font-weight: 500;
    font-family: 'Lato';
}

/* Hidden tooltip */
.tooltip-icon:hover .tooltip-content-advance {
    visibility: visible;
    opacity: 1;
}

/* Tooltip Content */
.tooltip-content-advance {
    visibility: hidden;
    opacity: 0;
    width: 480px;
    background-color: #fff;
    color: #000;
    text-align: center;
    padding: 8px 11px 8px 11px;
    border-radius: 6px;
    font-size: 13px;
    position: absolute;
    left: 26px;
    z-index: 1000;
    transition: opacity 0.3s ease;
    top: -27px;
    right: 0;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    text-align: justify;
    line-height: 17px;
}

.tooltip-content p {
    margin: 0;
    text-align: left;
}

hr.horizontal-hr {
    padding: 0;
    margin: 0;
    color: #E0E3E6;
}

hr.horizontal-hr-2 {
    margin: 2px 0 0 0;
    padding: 0px;
    color: #E0E3E6;
}




.first-output {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.second-output {
    background-color: #fff;
    margin: 10rem auto;
    width: 420px;
    height: fit-content;
    /* position: absolute; */
    /* left: 0; */
    border-radius: 12px;
    border: 1px solid #F5F5F5;
    box-shadow: 0 1px 6px #00000029;
}

h2.get-custom {
    text-align: center;
    font-size: 25px;
    font-weight: 600;
    color: #093D54;
}

textarea.popup-text-area {
    overflow: auto;
    resize: vertical;
    margin: 2rem auto;
    display: block;
    border: 0;
    outline: none;
    background-color: #F5F5F5;
    border-radius: 12px;
    padding: 17px 0 17px 30px;
}

textarea.popup-text-area::placeholder {
    color: #727272;
    padding: 0 0 0 0px;
}

button.popup-request {
    margin: 30px auto;
    display: block;
    padding: 12px 40px 12px 40px;
    outline: none;
    border: 0;
    background-color: #093D54;
    color: #fff;
    border-radius: 22px;
    cursor: pointer;
    opacity: 0.5;
}

p.popup-success-meessage {
    color: #08C0CB;
    font-size: 14px;
    text-align: center;
    padding: 0px 0 20px 0;
}

p.popup-error-meessage {
    color: red;
    font-size: 14px;
    text-align: center;
    padding: 20px 0 20px 0;
}

hr.horizontal-hr-3 {
    margin: -1px;
}


.features-Freelancers p {
    padding: 24px 0px 24px 0;
    text-align: center;
    margin: 0 0 0 0;
    color: #093D54;
    font-size: 16px;
}

.save-up-to img {
    position: absolute;
    margin: 16px 11px 0 -42px;
}

.frozen-fortune-img {
    position: absolute;
    top: -63px;
    width: 171px;
    /* left: 0px; */
    right: 39px;
}

button.btn-block-current-plan {
    margin: 2rem auto 25px;
    display: block;
    border: 1px solid #5CD5FF;
    font-size: 14px;
    padding: 5px 35px 5px 35px;
    border-radius: 20px;
    background-color: #fff;
    box-shadow: inset 1px 3px 3px 1px #0000001A;
    outline: none;
    cursor: pointer;
}

i.fa.fa-check.checked-plan {
    font-weight: 300;
    color: #57d9c8;
    padding: 0 4px 0 0px;
}

p.filter-by {
    text-align: center;
    font-size: 18px;
    padding: 8px 0 8px 0;
    margin: 0;
}

ul.filter-by-list {
    font-size: 14px;
    line-height: 23px;
    list-style-type: none;
    padding: 10px 0px 0 24px;
    font-weight: 500;
}

.filter-by-list li::before {
    content: '•';
    /* Unicode character for bullet */
    color: #08c0cb;
    /* Change to desired color */
    font-size: 16px;
    /* Customize bullet size */
    padding: 0 10px 0 0;
}