import React, { useEffect } from "react";
import '../../customer/assests/css/PaymentFailed.css';
import Footer from "../layouts/Footer";
import { useNavigate } from 'react-router-dom';
import UseTabStore from "../common-files/useGlobalState";
import { getSessionItem } from "../common-files/LocalStorage";
import Mixpanel from '../../utils/mixpanel';
import Analytics from '../../utils/analyticsTracking';


const PaymentFailed = () => {

    let loggedUserEmail = null;
    let token = null;

    let user = getSessionItem("user");
    user = user ? JSON.parse(user) : null;
    if (user && "token" in user) token = user.token;
    if (user && "email" in user) loggedUserEmail = user.email;

    const navigate = useNavigate();
    const { selectedPlanType, selectedPlanDetails } = UseTabStore();

    useEffect(() => {
        // Track payment failure in Mixpanel when component mounts
        try {
            // Get orderId from localStorage if available
            const orderId = localStorage.getItem('orderId');
            
            // Gather device and location info
            const deviceInfo = {
                browser: navigator.userAgent,
                device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
                viewport_size: `${window.innerWidth}x${window.innerHeight}`,
                language: navigator.language || navigator.userLanguage
            };
            
            // Get UTM parameters from localStorage
            const utmParams = localStorage.getItem('utm_params') ? JSON.parse(localStorage.getItem('utm_params')) : {};
            
            // Track in Mixpanel
            Mixpanel.track('Payment Failed', {
                order_id: orderId || 'unknown',
                plan_name: selectedPlanDetails?.package_name || 'unknown',
                plan_price: selectedPlanDetails?.price || 0,
                user_email: loggedUserEmail || 'unknown',
                subscription_type: selectedPlanType || 'standard',
                ...deviceInfo,
                ...utmParams
            });
            
            // Also track in Analytics
            Analytics.track('Payment Failed', {
                order_id: orderId || 'unknown',
                plan_name: selectedPlanDetails?.package_name || 'unknown',
                plan_price: selectedPlanDetails?.price || 0,
                user_email: loggedUserEmail || 'unknown',
                subscription_type: selectedPlanType || 'standard',
                location: window.location.href,
                page_title: document.title,
                referrer: document.referrer || 'direct',
                ...deviceInfo,
                ...utmParams
            });
        } catch (error) {
            console.warn('Error tracking payment failure:', error);
        }
    }, []);

    const reTryPayment = async () => {
        // Track payment retry attempt in Mixpanel
        try {
            Mixpanel.track('Payment Retry Initiated', {
                plan_name: selectedPlanDetails?.package_name || 'unknown',
                plan_price: selectedPlanDetails?.price || 0,
                user_email: loggedUserEmail || 'unknown',
                subscription_type: selectedPlanType || 'standard'
            });
        } catch (error) {
            console.warn('Error tracking payment retry:', error);
        }
        
        if (selectedPlanDetails?.package_name && selectedPlanType === "upgrade") {
            navigate('/upgrade-plan', {
                state: {
                    email: user.email,
                    token: user.token,
                    planDetails: selectedPlanDetails
                }
            });
        } else {
            navigate("/proceed-to-pay", {
                state: {
                    email: user.email,
                    token: user.token,
                },
            });
        }
    }

    return (
        <>
            <div className="body">
                <div className="container">
                    <div className="row">
                        <div className="offset-md-1 mt-5 col-md-6">
                            <div className="payment-page-logo">
                                <img src="../images/r-logo.png" width="70" className="img-fluid" />
                            </div>
                        </div>
                    </div>

                    <div className="card-1">
                        <img className="img-fluid" src="../images/group-51319.png" alt="Card image cap" />
                        <h3>Payment Failed</h3>
                        <p className="semi-header">Oops! Something went wrong. Your payment wasn't completed
                        </p>
                        <div className="shifted">
                            <input type="submit" value="Try Again" className="cp-pluss" onClick={() => reTryPayment()} />
                        </div>
                    </div>
                    <br />
                    <div className="receive-link">
                        <p>Get in touch with us at <a href="mailto:<EMAIL>"><EMAIL></a> if you <br /> don't receive a link within a few minutes.</p>
                    </div>
                </div>
            </div>
            <Footer />
        </>
    )
}

export default PaymentFailed;