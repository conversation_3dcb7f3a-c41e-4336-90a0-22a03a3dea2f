import React, { useEffect, useRef, useState } from "react";
import "../assests/css/layouts/target_audience.css";
import UseTabStore from "./useGlobalState";
import {
    FacebookShareButton,
    LinkedinShareButton,
    PinterestShareButton,
    TwitterShareButton,
} from "react-share";

const Target_Audience = ({ data }) => {

    const textAreaRef = useRef(null);
    const [copy, setCopy] = useState(false);
    // Get the current domain name
    const currentDomain = window.location.origin;
    const description = "ReachStream offers a 7-tier AI and manual-verified opt-in database and a DIY custom list-building platform to empower organisations of all sizes to scale their businesses with powerful business-to-business intelligence."
    // Access the URLs from the data prop
    const [premium, freemium, target_auidence] = data;
    const [viewModal, setViewModal] = useState(true);
    const { copyContent, socialMediaType, setCopyContent } = UseTabStore();
    const closeModal = () => {
        setViewModal(false);
    }
    let referralCode = `${currentDomain}/free-trial-sign-up`;
    let url = referralCode;

    useEffect(() => {
        // Use a regular expression to extract the referralcode
        const match = target_auidence.match(/referralcode=([^&]+)/);

        // Check if the match is found
        if (match && match[1]) {
            referralCode = match[1];
            url = `${currentDomain}/target-audience/referralcode=${referralCode}`;
        }
    }, [])

    const copyContents = () => {
        var r = document.createRange();
        r.selectNode(document.getElementById("copy_content"));
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(r);
        try {
            document.execCommand('copy');
            window.getSelection().removeAllRanges();
            setCopyContent(true);
        } catch (err) {
            console.log('Unable to copy!');
        }
    };

    return (
        <>
            {viewModal ? (
                <>
                    <div className="modal fade" id="targetAudience" role="dialog" tabindex="1">
                        <div className="modal-dialog" style={{ width: "600px", margin: "auto", }}>
                            <div className="modal-content" style={{ width: "600px", margin: "3rem 0 0 0" }}>
                                <div className="modal-header" style={{ borderBottom: "0", padding: "1px 5px 0px" }}>
                                    <button type="button" className="close" data-dismiss="modal"><img src="../images/rouncancel.png" /></button>

                                </div>
                                <h5 className="modal-title-include" id="exampleModalLabel">Need a caption, Copy the description below:</h5>
                                <hr className="you-line" />

                                <div className="modal-body">
                                    <div className="d-flex flex-row justify-content-center">
                                        <img src="../images/get_special.png" width="545" />
                                    </div>

                                    <div className="modal-conetnt" ref={textAreaRef} id="copy_content">
                                        <p style={{ fontSize: "14px", fontWeight: "600", padding: "0 0 0 10px", margin: "6px 0 8px 0" }}>Spreading the good word!</p>

                                        <p className="prospects’">
                                            Is anyone in my network looking for new business leads or rolling out a new campaign?
                                        </p>

                                        <p style={{ fontSize: "14px", fontWeight: "600", padding: "0 0 0 10px", margin: "6px 0 8px 0" }}>Create a list of 5000 new leads in only 3 steps. With ReachStream, you get:</p>

                                        <ul>
                                            <li className="bowl" style={{ fontSize: "12px",color:"#000" }}>Emails, phone numbers, and 20+ contact & company insights</li>
                                            <li className="bowl" style={{ fontSize: "12px",color:"#000" }}>Opt-in contact information</li>
                                            <li className="bowl" style={{ fontSize: "12px",color:"#000" }}>Unlimited views & free contact updates</li>
                                            <li className="bowl" style={{ fontSize: "12px",color:"#000" }}>Advanced API access on all plans</li>
                                        </ul>
                                        <p className="prospects’">
                                            Sign up with this link – <a href={premium} style={{ textDecoration: "none", color: "#55C2C3" }}>{premium}</a>
                                        </p>


                                        <p className="prospects’">
                                            Or just try it for free (No credit card details needed) –  <a href={freemium} style={{ textDecoration: "none", color: "#55C2C3" }}>{freemium}</a>
                                            </p>

                                    </div>


                                    <div className="d-flex flex-row justify-content-end">
                                        <div>
                                            {copyContent ? (
                                                <span className="successmessages"><img src="./images/promocode-success.png" width="28" />Copied Successfully</span>
                                            ) : (null)}

                                        </div>
                                        <div className="mr-2">
                                            <button type="button" onClick={copyContents} className="copypastcontent"><img src="./images/copy-icon.png" width="15" />&nbsp;&nbsp;Copy</button>
                                        </div>
                                        <div>
                                            {socialMediaType == "facebook" ? (
                                                <FacebookShareButton
                                                    url={target_auidence}
                                                    quote="Sign up and get 50% off"
                                                    hashtag="#reachstream, #contactinsights, #companyinsights, #referandearn, #B2Bsalesintelligence"
                                                    className={!copyContent ? "cust-disabled" : ""}
                                                >
                                                    <button type="button" className={!copyContent ? "sharenow cust-disabled" : "sharenow"}>Share now</button>
                                                </FacebookShareButton>
                                            ) : socialMediaType == "twitter" ? (
                                                <TwitterShareButton
                                                    url={target_auidence}
                                                    title="Create a list of 5000 leads today!"
                                                    className={!copyContent ? "cust-disabled" : ""}
                                                >
                                                    <button type="button" className={!copyContent ? "sharenow cust-disabled" : "sharenow"}>Share now</button>
                                                </TwitterShareButton>
                                            ) : socialMediaType == "linkedIn" ? (
                                                <LinkedinShareButton
                                                    summary={"ReachStream offers a 7-tier AI and manual-verified opt-in database and a DIY custom list-building platform to empower organisations of all sizes to scale their businesses with powerful business-to-business intelligence."}
                                                    source={currentDomain}
                                                    url={target_auidence}
                                                    className={!copyContent ? "cust-disabled" : ""}
                                                >
                                                    <button type="button" className={!copyContent ? "sharenow cust-disabled" : "sharenow"}>Share now</button>
                                                </LinkedinShareButton>
                                            ) : socialMediaType == "pintrest" ? (
                                                // <PinterestShareButton
                                                //     media={`${currentDomain}/get_special.png`}
                                                //     url={target_auidence}
                                                //     pinId={"reachstream"}
                                                //     description={"This is test summary"}
                                                // >
                                                //     <button type="button" className={!copyContent ? "sharenow cust-disabled" : "sharenow"}>Share now</button>
                                                // </PinterestShareButton>
                                                <a className={!copyContent ? "cust-disabled" : ""} href={`https://pinterest.com/pin/create/button/?url=${url}&media=${currentDomain}/images/get_special.png&description=${encodeURI(description)}`} target="_blank">
                                                    <button type="button" className={!copyContent ? "sharenow cust-disabled" : "sharenow"}>Share now</button>
                                                </a>
                                            ) : (<></>)}

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            ) : (
                <></>
            )
            }

        </>
    )
};

export default Target_Audience;