import React, { useState } from 'react';
import Header from "../layouts/Header";
import "../assests/css/filter/saved_list.css";

const SavedList = () => {


    return (
        <>
            <Header />
            <div className="ml-5 mt-2 mr-5">
                <div className="d-flex flex-row">
                    <div className="back-image">
                        <img src="http://localhost:3000/images/arrow-left.png" />
                    </div>
                    <div>
                        <p className="Slist">Saved List</p>
                    </div>
                </div>

                <div className="d-flex flex-row justify-content-end">
                    <div>
                        <button type="button" className="createnewList">Create New List</button>
                    </div>
                </div>

                <div className="savedlistbackgroundcolor mt-3">
                    <table className="your-refrel-lists">
                        <tbody>
                            <tr className="table-headers-saved-list mt-2">
                                <th style={{ textAlign: "center" }}>Sl No</th>
                                <th>List Name</th>
                                <th>Data and Time</th>
                                <th style={{}}>Total Records</th>
                                <th style={{}}>Total Valid Emails</th>
                                <th style={{}}>Actions</th>
                            </tr>

                            <tr className="table-data-small" >
                                <td style={{ textAlign: "center" }} className="data-goes-here-saved-list">1</td>
                                <td className="data-goes-here-saved-list">Contact List 3</td>
                                <td style={{ fontSize: "13px" }} className="">2024/01/20 - 6:00 pm</td>
                                <td style={{ fontSize: "13px" }} className="">500</td>
                                <td style={{ fontSize: "13px" }} className="">....</td>
                                <td style={{ fontSize: "13px" }} className="">
                                    <div className="d-flex flex-row justify-content-start">
                                        <div>
                                            <button type="button" className="savedlistDownloadbutton" data-toggle="modal" data-target="#exampleModalCenter" >  Verify and Download  </button>

                                          
                                           

                                        </div>
                                        <div className="savedlistverticalline ml-2"></div>
                                        <div className="mr-2 ml-2">
                                            <img src="../images/eyee.png" />
                                        </div>
                                        <div className="savedlistverticalline"></div>
                                        <div className="ml-2">
                                            <img src="../images/delete.png" />
                                        </div>
                                    </div>
                                </td>
                                <td style={{ textAlign: "center", fontSize: "13px" }} className=""></td>
                            </tr>
                        </tbody>
                    </table>                   
                </div>
            </div>
        </>
    )
}

export default SavedList;

