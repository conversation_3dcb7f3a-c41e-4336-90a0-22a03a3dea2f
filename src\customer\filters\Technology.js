import { useEffect, useState } from 'react';
import AsyncSelect from 'react-select/async';
import { PostWithTokenNoCache } from '../common-files/ApiCalls.js';
import { ApiName } from '../common-files/ApiNames.js';
import UseTabStore from '../common-files/useGlobalState.js';

const Technology = () => {
	// State to store selected values
	const {
		selectedValues,
		setSelectedCompanyTechKeyWordsList,
		setCurrentPage,
		setSelectedValues,
		companyTechKeyWordsList,
		setIsFiltered
	} = UseTabStore();
	const [isTechnologyAutoFocus, setIsTechnologyAutoFocus] = useState(false); // Set one of them to autofocus initially

	useEffect(() => {
		if (Object.keys(companyTechKeyWordsList).length < 1) {
			setSelectedValues([]);
		} else {
			const selectedValues = Object.keys(companyTechKeyWordsList).map(key => {
				return { label: companyTechKeyWordsList[key], value: companyTechKeyWordsList[key] };
			});
			setSelectedValues(selectedValues);
		}
	}, [companyTechKeyWordsList])

	const convertToProperCase = async (data) => {
		let properCaseData = [];
		if (data) {
			properCaseData = data.map(val => {
				// Trim the input string to remove leading and trailing whitespace
				val = val.trim();

				// Match words while preserving non-alphanumeric characters in between
				const words = val.match(/\b\w+\b/g);
				let result = "";
				let lastIndex = 0;

				words.forEach(word => {
					// Find the position of each word in the original string
					const start = val.indexOf(word, lastIndex);

					// Append special characters or spaces before the current word
					result += val.slice(lastIndex, start);

					// Capitalize the word
					result += word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();

					// Update lastIndex to the end of this word
					lastIndex = start + word.length;
				});

				// Append any remaining special characters or spaces after the last word
				result += val.slice(lastIndex);

				return result;
			});
		}
		return properCaseData;
	};

	const loadOptions = (inputValue, callback) => {
		if (inputValue) {
			const dataPost = {
				company_tech_keywords_list: inputValue // Pass the inputValue to your API request
			};
			// Make an API call here to fetch suggestions based on the inputValue
			PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
				.then(async (res) => {
					if (res.data.status === 200) {
						const technology = JSON.parse(res.data.data);

						let properCaseTechnology = await convertToProperCase(technology);

						// Convert the array to a Set to remove duplicates and then convert it back to an array
						const uniqueTechnology = [...new Set(properCaseTechnology)];

						const options = uniqueTechnology.map((option) => ({
							label: option,
							value: option
						}));
						callback(options);
					}
				})
				.catch((error) => {
					console.error('Error fetching data:', error);
					callback([]); // Clear options on error
				});
		}
	};

	// Handle value change when an option is selected or deselected
	const handleValueChange = (selectedOptions) => {
		setCurrentPage(1);
		setIsFiltered(true);

		setSelectedValues(selectedOptions);
		let updatedTechnology = {};

		// Update the company names based on selected options
		selectedOptions.forEach((item, index) => {
			updatedTechnology[index] = item.value;
		});
		setSelectedCompanyTechKeyWordsList(updatedTechnology);
	};

	const SearchTechnology = ({ autoFocus, onFocus, onBlur }) => {
		return (
			<div className="mb-2 mt-3 d-flex flex-column">
				<div className="techno">
					<AsyncSelect
						cacheOptions
						defaultOptions
						loadOptions={loadOptions}
						isMulti
						placeholder="Type to search and select"
						onChange={handleValueChange} // Handle selected value changes
						value={selectedValues} // Pass selected values
						styles={SearchJobtitleStyles}
						autoFocus={autoFocus}
						onFocus={onFocus}
						onBlur={onBlur}
						isClearable

					/>
				</div>
				<div className="search-icon">
					<i className="fas fa-search"></i>
				</div>
			</div>
		)
	}


	const SearchJobtitleStyles = {
		control: (provided) => ({
			...provided,

			marginLeft: "26px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
			cursor: "text",

			":hover": {
				borderColor: "#000",
			},
			":focus": {
				border: "1px solid #000",
			},
		}),

		placeholder: (baseStyles) => ({
			...baseStyles,
			gridArea: "1/1/2/3",
			color: "#A3AEBB",
			marginLeft: "6px",
			fontSize: "14px",
			width: "187px"
		}),


		singleValue: (provided) => ({
			...provided,
			borderRadius: "10px",
			backgroudColor: "red",
		}),

		multiValue: (provided) => ({
			...provided,
			borderRadius: "10px",
			backgroundColor: "white",
			marginTop: "8px",
			border: "1px solid",
			fontSize: "70%",

		}),


		multiValueGeneric: (provided) => ({
			...provided,
			paddingLeft: "8",
			paddingTop: "10px",
			padding: "1px",
			marginTop: "12px",


		}),

		IndicatorsContainer: (provided) => ({
			...provided,
			visibility: "hidden",
			padding: "0"

		}),

		indicatorsContainer: (provided) => ({
			...provided,
			visibility: "hidden",
			padding: "0"
		}),

		loadingIndicator: (provided) => ({
			...provided,
			visibility: "hidden",
			width: "0",
			fontSize: "0"
		}),


		menuList: (provided) => ({
			...provided,
			"&:hover:": {
				color: "#55C2C3",
				marginTop: "-6px",
				marginBottom: "14px",
				backgroudColor: "transperent",
				zIndex: "1000",
			},

			"&:hover": {
				backgroundColor: "inherit",
			},
		}),

		menu: (provided) => ({
			...provided,

			border: "1px solid #093D54",
			marginLeft: "26px",
			marginRight: "2rem",
			fontSize: "14px",
			marginRight: "0px",
			position: "relative",
			width: "-webkit-fill-available",
			backgroudColor: "#fff",

			":hover": {
				color: "inherit",
				textDecoration: "none",
			},
		}),

		multiValueRemove: (base) => ({
			...base,
			backgroundColor: "#fff",
			setColor: "#000",
			borderRadius: "50%",
			borderRadius: "10px",
			paddingLeft: "-60",
			paddingRight: "0",
			innerHeight: "10px",
			border: "1px solid #08CEDC",
			marginTop: "-7px",
			marginBottom: "2px",
			font: "black !important",
			backgroudColor: "#ffffff",
			width: "11px",
			height: "11px",
			"&:hover": {
				backgroundColor: "#55C2C3",
				color: "black",
			},
		}),

		indicatorSeparator: (provided) => ({
			...provided,
			backgroundColor: "hsl(0deg 0% 100%)",
		}),

		MenuList: (provided) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#fff",
			outline: "none",
		}),



		valueContainer: (provided) => ({
			...provided,
			borderRadius: "10px",
			paddingLeft: "0px",
			color: "#000",
			paddingRight: "0",
			backgroudColor: "#000",
			outline: "none",
			padding: "0px 5px 0 0",
			width: "300px"
		}),
		container: (provided) => ({
			...provided,
			padding: "0px 11px 0 0",
		}),
		group: (provided) => ({
			...provided,
			width: "262px",
			// paddingLeft: '23px',
			marginLeft: "32px",
			backgroudColor: "#fff",
			outline: "none",
			border: "1px solid #093D54",
		}),

	};


	return (
		<div>
			<SearchTechnology
				autoFocus={isTechnologyAutoFocus}
				onFocus={() => setIsTechnologyAutoFocus(true)}
				onBlur={() => setIsTechnologyAutoFocus(false)}
			/>
		</div>
	)
}
export default Technology;