import React, { useEffect, useState } from "react";
import AdminHeader from "../../layouts/AdminHeader.js";
import S from "../../assets/css/layouts/admin-header.module.css";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { PostWithTokenNoCache } from "../../../customer/common-files/ApiCalls.js";
import LeftSideNav from "../../layouts/LeftSideNav.js";
import Pagination from "../../../customer/pagination/Pagination.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import Alert from "../../../customer/common-files/alert.js";
import loadingGif from "../../../customer/assests/waiting.gif";
import { useParams } from "react-router-dom";

const UserPaymentHistory = () => {
  const { id } = useParams();
  const [data, setData] = useState([]);
  const itemsPerPage = 10; // You can adjust the number of items per page here
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [totalPage, setTotalPage] = useState(0);
  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();

  useEffect(() => {
    orderTransactionDetails();
  }, [currentPage]);

  const orderTransactionDetails = async () => {
    try {
      let param = JSON.stringify({
        page: currentPage,
        pageSize: itemsPerPage,
        sortBy: "desc",
        searchParams: {
          user_id: parseInt(id),
        },
      });
      const res = await PostWithTokenNoCache(
        ApiName.orderTransactionDetails,
        param
      );
      if (res && "status" in res) {
        if (res.status == 200) {
          let record = JSON.parse(res.data.data);
          setTotalPage(record.totalPages);
          setData(record.items);
          console.log(record.items);
          setLoading(false);
        }
      }
    } catch (error) {
      setButtonType("error");
      setDefaultErrorMsg(error?.response?.data?.message);
      setDefaultAlert(true);
      setLoading(false);
    }
  };

  const dateFormat = (date) => {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = parsedDate.getMonth() + 1;
    const day = parsedDate.getDate();
    const hours = parsedDate.getHours();
    const minutes = parsedDate.getMinutes();
    const ampm = hours >= 12 ? "pm" : "am";
    const formattedHours = hours % 12 === 0 ? 12 : hours % 12;
    return `${year}/${month}/${day} - ${formattedHours}:${
      minutes < 10 ? "0" : ""
    }${minutes} ${ampm}`;
  };
  const convertCentstoDoller = (cents) => {
    return (cents / 100).toFixed(2);
  };

  return (
    <div className={S.app}>
      <AdminHeader />

      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div style={{ width: "100%", padding: "0 10px 0 0" }}>
              <h5 className={S.FreemiumUsers}>Payment History</h5>
              <br />
              <div className={S.content}>{/* Main content */}</div>
              <aside className={S.right_sidebar}>
                <table className="admin-table text-center">
                  <thead>
                    <tr className="table-active">
                      <th className={S.tableheaderspace}>Sl No</th>
                      <th className={S.tableheaderspace}>Subscription Id</th>
                      <th className={S.tableheaderspace}>Transaction Status</th>
                      <th className={S.tableheaderspaces}>
                        Transaction Amount
                      </th>
                      <th className={S.tableheaderspace}>Created At</th>
                      <th className={S.tableheaderspace}>Transaction Method</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((item, i) => (
                      <tr className="table-data-small" key={i}>
                        <td
                          style={{
                            fontSize: "13px",
                            textAlign: "center",
                            padding: "10px 0 10px 17px",
                          }}
                        >
                          {(currentPage - 1) * 10 + i + 1}
                        </td>
                        <td
                          style={{
                            fontSize: "13px",
                            padding: "10px 0 10px 17px",
                          }}
                        >
                          {item.subscription_id}
                        </td>
                        <td
                          style={{
                            fontSize: "13px",
                            padding: "10px 0 10px 17px",
                          }}
                        >
                          {item.transaction_status}
                        </td>
                        <td
                          style={{
                            fontSize: "13px",
                            padding: "10px 0 10px 17px",
                          }}
                        >
                          ${convertCentstoDoller(item.transaction_amount)}
                        </td>
                        <td
                          style={{
                            fontSize: "13px",
                            padding: "10px 0 10px 17px",
                          }}
                        >
                          {dateFormat(item.transaction_createdAt)}
                        </td>
                        <td
                          style={{
                            fontSize: "13px",
                            padding: "10px 0 10px 17px",
                          }}
                        >
                          {item.transaction_method}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </aside>
            </div>
          ) : (
            <div className="mx-auto mt-5" style={{ display: loading }}>
              <img
                src={loadingGif}
                alt="Loading"
                className="loader"
                width="400"
              />
            </div>
          )}
        </div>
      </div>
      <Pagination
        className="pagination-bar"
        currentPage={currentPage}
        totalCount={totalPage}
        pageSize={1}
        onPageChange={(page) => {
          setCurrentPage(page);
        }}
      />

      {defaultAlert && defaultErrorMsg ? (
        <Alert data={defaultErrorMsg} />
      ) : (
        <></>
      )}
    </div>
  );
};
export default UserPaymentHistory;
