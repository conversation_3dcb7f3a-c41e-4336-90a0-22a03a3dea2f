import React, { useState, useEffect, useRef } from "react";
import S from "../../assets/css/layouts/admin-header.module.css";
import AdminHeader from "../../layouts/AdminHeader.js";
import LeftSideNav from '../../layouts/LeftSideNav.js';
import {
  PostWithTokenNoCache
} from "../../../customer/common-files/ApiCalls.js";
import { ApiName } from "../../../customer/common-files/ApiNames.js";
import { useNavigate } from "react-router-dom";
import loadingGif from '../../../customer/assests/waiting.gif';
import Alert from "../../../customer/common-files/alert.js";
import UseTabStore from "../../../customer/common-files/useGlobalState.js";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";
import "../../../customer/assests/css/password-eye.css";

const AdminForm = () => {
  const invoice_popup = useRef();
  const close_invoice_popup = useRef();
  const navigate = useNavigate();
  const [fname, setFname] = useState("");
  const [fnameErr, setFnameErr] = useState("");
  const [validFname, setValidFname] = useState(false);
  const [lname, setLname] = useState("");
  const [lnameErr, setLnameErr] = useState("");
  const [validLname, setValidLname] = useState(false);
  const [email, setEmail] = useState("");
  const [emailErr, setEmailErr] = useState("");
  const [validEmail, setValidEmail] = useState(false);
  const [phone, setPhone] = useState("");
  const [phoneErr, setPhoneErr] = useState("");
  const [validPhone, setValidPhone] = useState(false);
  const [company, setCompany] = useState("");

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [password, setPassword] = useState("");
  const [validPassword, setValidPassword] = useState(false);
  const [cpassword, setCpassword] = useState("");
  const [validConfirmPassword, setValidConfirmPassword] = useState(false);
  const [validLength, setValidLength] = useState(false);
  const [validCase, setValidCase] = useState(false);
  const [validWord, setValidWord] = useState(false);
  const [fNameInvalidText, setFNameInvalidText] = useState(true);
  const [lNameInvalidText, setLNameInvalidText] = useState(true);
  const [emailInvalidText, setEmailInvalidText] = useState(true);
  const [phoneInvalidText, setPhoneInvalidText] = useState(true);
  const [verificationCodeInvalidText, setVerificationCodeInvalidText] = useState(true);
  const [passwordInvalidText, setPasswordInvalidText] = useState(true);
  const [confirmpasswordInvalidText, setConfirmPasswordInvalidText] =
    useState(true);

  const [selectedPlan, setSelectedPlan] = useState(0);
  const [loading, setLoading] = useState(true);
  const [formErrors, setFormErrors] = useState({});
  const [allFeilads, setAllFields] = useState(false);
  const [message, setMessage] = useState(false);
  const [userID, setUserID] = useState(null);
  const [btnVisible, setBtnVisible] = useState(false);
  const [promocodeMsg, setPromocodeMsg] = useState(false);
  const [promocodeInvalid, setPromocodeInvalid] = useState(true);
  const [promocode, setPromocode] = useState();
  const [inputDisable, setInputDisable] = useState(false);
  const [error, setError] = useState(false);
  const [validPrmocode, setValidPrmocode] = useState(false);
  const [blockedDomain, setBlockedDomain] = useState([]);

  const {
    defaultAlert,
    defaultErrorMsg,
    setButtonType,
    setDefaultAlert,
    setDefaultErrorMsg,
  } = UseTabStore();
  const priceData = {
    access_database: "",
    custom_filter: "",
    data_fields: "",
    export: "",
    no_of_contact_views: "",
    no_of_users: "",
    price: "",
    verified_email_phone: "",
  };
  const [pricePlan, setPricePlan] = useState(priceData);
  const [switchUserData, setSwitchuserData] = useState({
    amount_paid: "",
    invoice_id: "",
    status: "paid",
    paid: true,
    price_id: ""
  });

  const closeButton = async () => {
    setAllFields(false);
    setSwitchuserData({
      amount_paid: "",
      invoice_id: "",
      customer_name: "",
      status: "",
      paid: "",
      price_id: ""
    });

  }

  useEffect(() => {
    const params = {
      "method": "POST"
    }
    let result = PostWithTokenNoCache(ApiName.activeDomainBlockList, params)
      .then(function (response) {
        // const token = JSON.parse(response.data.data);
        if (response.data.status == 200) {
          let data = JSON.parse(response.data.data);
          // Extract the "domain" property from each object and store in an array
          const domainArray = data.map(item => item.domain);
          setBlockedDomain(domainArray);
        }
      })

  }, [])

  const onChangeHandler = (e) => {
    const { name, value } = e.target;

    // Validate the field based on its name
    let error = "";
    switch (name) {
      case "amount_paid":
        if (!value) {
          error = "Amount is required.";
        } else if (isNaN(value) || parseFloat(value) <= 0) {
          error = "Invalid amount.";
        }
        break;
      case "customer_id":
        if (!value) {
          error = "Customer id is required.";
        }
        break;
      case "invoice_id":
        if (!value) {
          error = "invoice id is required.";
        }
        break;
      case "price_id":
        if (!value) {
          error = "price id is required.";
        }
        break;

      default:
        break;
    }

    // Update the formErrors state
    setFormErrors({
      ...formErrors,
      [name]: error,
    });

    // Update the switchUserData state
    setSwitchuserData({
      ...switchUserData,
      [name]: value,
    });
  };

  const handleSelectChange = (event) => {

    setValidPrmocode(false);
    setError(false);
    setBtnVisible(false);
    setPromocode("");
    setPromocodeMsg("");
    setPromocodeInvalid(false);
    setInputDisable(false);

    const packageID = event.target.value;
    let selectPlan = pricePlan && pricePlan.filter((value) => value?.id == packageID);
    setSelectedPlan(selectPlan);
  };
  const onChangeCompany = (event) => {
    let company = event.target.value;
    setCompany(company);
  }
  const onChangeFname = (event) => {
    const specialChars = /[`!@#$%^&*()_+\=\[\]{};:"\\|,.<>\/?~*********]/;
    let fname = event.target.value;
    setFname(fname);
    if (specialChars.test(fname)) {
      setValidFname(false);
      setFNameInvalidText(false);
      setFnameErr("Enter a valid first name, avoid using 123@$!");
    } else {
      setValidFname(true);
      setFnameErr("");
      setFNameInvalidText(true);
    }
  };
  const onChangeLname = (event) => {
    let lname = event.target.value;
    setLname(lname);
    const specialChars = /[`!@#$%^&*()_+\=\[\]{};:"\\|,.<>\/?~*********]/;
    if (specialChars.test(lname)) {
      setValidLname(false);
      setLNameInvalidText(false);
      setLnameErr("Enter a valid last name, avoid using 123@$!");
    } else {
      setValidLname(true);
      setLnameErr("");
      setLNameInvalidText(true);
    }
  };
  const onChangeEmail = (event) => {
    let email = event.target.value;

    setEmail(email.toLowerCase());

    // Construct dynamic RegExp for disallowed domains
    const disallowedDomainsRegExp = new RegExp(`^.+@((?!${blockedDomain.join('|')}).)+$`);
    let result = disallowedDomainsRegExp.test(email);

    if (result) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      let checkDomain = emailRegex.test(email);

      if (!checkDomain) {
        setEmailErr("Please enter a valid business email address");
        setValidEmail(false);
        setEmailInvalidText(false);
      } else {
        setValidEmail(true);
        setEmailErr("");
        setEmailInvalidText(true);
      }
    } else {
      setEmailErr("Please enter a valid business email address");
      setValidEmail(false);
      setEmailInvalidText(false);
    }
  };
  function validatePhoneNumber(phoneNumber) {
    // Regular expression for a phone number with + sign and country code
    const phoneRegex = /^\+\d{1,}$/;
    // Test the phone number against the regular expression
    return phoneRegex.test(phoneNumber);
  }
  const onChangePhone = (event) => {
    let phone = event.target.value;
    // Remove non-numeric characters (except '+')
    phone = phone.replace(/[^0-9+]/g, '');
    setPhone(phone);
    const isValid = validatePhoneNumber(phone);
    if (isValid) {
      setValidPhone(true);
      setPhoneErr("");
      setPhoneInvalidText(true);
    } else {
      setValidPhone(false);
      setPhoneInvalidText(false);
      setPhoneErr("Enter a valid phone number with a country code");
    }
  };
  const onChangePassword = (event) => {
    let password = event.target.value.trim();
    setPassword(password);
    const minLength = 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /(?=.*[0-9]).*$/.test(password);
    const hasSymbol = /^(?=.*[~`!@#$%^&*()--+={}\[\]|\\:;"'<>,.?/_₹]).*$/.test(
      password
    );
    const isDictionaryWord =
      /password|123456|qwerty|letmein|monkey|football/.test(password);

    if (password.length < minLength) {
      setValidLength(false);
      setPasswordInvalidText(false);
    } else {
      setValidLength(true);
      setPasswordInvalidText(true);
    }

    if (
      password.length < minLength ||
      !hasUppercase ||
      !hasLowercase ||
      !hasNumber ||
      !hasSymbol
    ) {
      setValidCase(false);
      setPasswordInvalidText(false);
    } else {
      setValidCase(true);
      setPasswordInvalidText(true);
    }

    if (isDictionaryWord) {
      setValidWord(false);
      setPasswordInvalidText(false);
    } else {
      setValidWord(true);
      setPasswordInvalidText(true);
    }

    if (validLength && validCase && validWord) {
      setValidPassword(true);
      setPasswordInvalidText(true);
    }

    if (cpassword !== password) {
      setValidConfirmPassword(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidConfirmPassword(true);
      setConfirmPasswordInvalidText(true);
    }
  };
  const onChangeConfirmPassword = (event) => {
    let cPassword = event.target.value;
    setCpassword(cPassword);

    const minLength = 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSymbol = /[\W_]/.test(password);
    const isDictionaryWord =
      /password|123456|qwerty|letmein|monkey|football/.test(password);

    if (password.length < minLength) {
      setValidLength(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidLength(true);
      setConfirmPasswordInvalidText(true);
    }

    if (!hasUppercase || !hasLowercase || !hasNumber || !hasSymbol) {
      setValidCase(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidCase(true);
      setConfirmPasswordInvalidText(true);
    }

    if (isDictionaryWord) {
      setValidWord(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidWord(true);
      setConfirmPasswordInvalidText(true);
    }
    if (validLength && validCase && validWord) {
      setValidPassword(true);
      setConfirmPasswordInvalidText(true);
    }

    if (cPassword !== password) {
      setValidConfirmPassword(false);
      setConfirmPasswordInvalidText(false);
    } else {
      setValidConfirmPassword(true);
      setConfirmPasswordInvalidText(true);
    }
  };

  const onChangePromocodeHandler = (event) => {
    let promocode = event.target.value;
    setPromocode(promocode);

    const minLength = 2;
    if (promocode.length == 0) {
      setPromocodeInvalid(true);
    } else if (minLength <= promocode.length) {
      setError(true);
      setBtnVisible(true);
      setPromocodeInvalid(true);
    } else {
      setError(false);
      setBtnVisible(false);
      setPromocodeInvalid(false);
    }
  };

  const applyCoupon = async (user_id) => {
    if (promocode) {
      const params = {
        coupon_id: promocode,
        user_id,
        user_plan_name: selectedPlan && selectedPlan[0]?.package_name,
      };
      try {
        let response = await PostWithTokenNoCache(ApiName.oneTimeCoupon, params);
        if (response && "status" in response) {
          if (response.data.status == 200 && response.data.message == "Coupon Not Found") {
            setError("Invalid code");
            setPromocodeInvalid(false);
            setBtnVisible(false);
            setPromocodeMsg("Coupon Not Found");
            setLoading(false);
          } else if (response.data.status == 200 && response.data.message == "Valid Coupon") {
            return true;
          } else {
            setError(response.response.data.message);
            setPromocodeMsg(response.response.data.message);
            setPromocodeInvalid(false);
            setBtnVisible(false);
            setLoading(false);
          }
        }
      } catch (errors) {
        setError(errors.response.data.message);
        setPromocodeMsg(errors.response.data.message);
        setPromocodeInvalid(false);
        setBtnVisible(false);
        setLoading(false);
      }
    }
  }

  //create user plan
  const createUserPlan = async (userId, orderId) => {
    const dataPostUserPlan = {
      user_id: userId,
      user_plan_name: selectedPlan && selectedPlan[0]?.package_name,
      order_id: orderId,
      status: "active",
      total_assigned_credit: selectedPlan && selectedPlan[0]?.export,
      total_balance_credit: selectedPlan && selectedPlan[0]?.export,
      total_assigned_contact_view: selectedPlan && selectedPlan[0]?.no_of_contact_views,
      total_balance_contact_view: selectedPlan && selectedPlan[0]?.no_of_contact_views
    };

    let res = await PostWithTokenNoCache(ApiName.createUserPlan, { ...dataPostUserPlan });

    if (res && "status" in res) {
      if (res.data.status == 200) {
        return true;
      } else {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg("Sorry.! could not create the order");
        setDefaultAlert(true);
      }
    }
  }

  //create order
  async function createOrder(data) {
    try {
      const res = await PostWithTokenNoCache(ApiName.createOrder, { ...data });
      if (res && "status" in res) {
        if (res.data.status == 200) {
          //freemium user create plan
          let orderObj = JSON.parse(res.data.data);
          let orderId = orderObj?.orderId;
          let userId = orderObj?.userId;
          setUserID(userId);
          return { orderId: orderId, userId: userId }
        } else {
          setLoading(false);
          setButtonType("error");
          setDefaultErrorMsg("Sorry.! could not create the order");
          setDefaultAlert(true);
        }
      }
    } catch (errors) {
      setLoading(false);
      setButtonType("error");
      setDefaultErrorMsg(errors.response.data.message);
      setDefaultAlert(true);
    }
  }

  //create freemium account
  const createFreemiumAccount = (allData, sendEmail) => {

    let total_assigned_credit = null;
    let total_assigned_contact_view = null;
    if ("export" in selectedPlan) total_assigned_credit = selectedPlan && selectedPlan[0]?.export;
    if ("no_of_contact_views" in selectedPlan) total_assigned_contact_view = selectedPlan && selectedPlan[0]?.no_of_contact_views;

    //bypass email verification
    PostWithTokenNoCache(ApiName.userActivatedByAdmin, sendEmail)
      .then(async function (response) {
        if (response.data.status == 200) {
          PostWithTokenNoCache(ApiName.superSignUp, allData)
            .then(async function (response) {
              if (response.data.status == 200) {
                const dataPost = { email_id: email, package_id: selectedPlan && selectedPlan[0]?.id };
                let createOrderRes = await createOrder(dataPost);
                let createUserPlanRes = await createUserPlan(createOrderRes.userId, createOrderRes.orderId)
                if (createUserPlanRes) navigate('/admin/free-trial-users');
              }
            })
            .catch(function (errors) {
              setLoading(false);
              setButtonType("error");
              setDefaultErrorMsg(errors.response.data.message);
              setDefaultAlert(true);
            });
        }
      }).catch(function (errors) {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(errors.response.data.message);
        setDefaultAlert(true);
      });
  }

  //create appsumo account
  const createAppSumoAccount = (allData, sendEmail) => {

    let total_assigned_credit = null;
    let total_assigned_contact_view = null;
    if ("export" in selectedPlan) total_assigned_credit = selectedPlan && selectedPlan[0]?.export;
    if ("no_of_contact_views" in selectedPlan) total_assigned_contact_view = selectedPlan && selectedPlan[0]?.no_of_contact_views;

    //bypass email verification
    PostWithTokenNoCache(ApiName.userActivatedByAdmin, sendEmail)
      .then(async function (response) {
        if (response.data.status == 200) {
          PostWithTokenNoCache(ApiName.superSignUp, allData)
            .then(async function (response) {
              if (response.data.status == 200) {
                const dataPost = { email_id: email, package_id: selectedPlan && selectedPlan[0]?.id };
                let createOrderRes = await createOrder(dataPost);
                await createUserPlan(createOrderRes.userId, createOrderRes.orderId)
                let applyCouponRes = await applyCoupon(createOrderRes.userId);
                if (applyCouponRes) navigate('/admin/app-sumo');
              }
            })
            .catch(function (errors) {
              setLoading(false);
              setButtonType("error");
              setDefaultErrorMsg(errors.response.data.message);
              setDefaultAlert(true);
            });
        }
      }).catch(function (errors) {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(errors.response.data.message);
        setDefaultAlert(true);
      });
  }

  //super saver user plan creation
  const createManualPayment = async (userID) => {
    let params = {
      ...switchUserData,
      customer_email: email,
      customer_name: fname,
      customer_id: userID
    }
    let res = await PostWithTokenNoCache(ApiName.switchFreemiumToPreemium, { ...params });
    if (res && "status" in res) {
      if (res.data.status == 200) {
        return true;
      } else {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg("Sorry.! could not create user plan");
        setDefaultAlert(true);
      }
    }
  }

  //create super saver account
  const createSuperSaverAccount = (allData, sendEmail) => {

    let total_assigned_credit = null;
    let total_assigned_contact_view = null;
    if ("export" in selectedPlan) total_assigned_credit = selectedPlan && selectedPlan[0]?.export;
    if ("no_of_contact_views" in selectedPlan) total_assigned_contact_view = selectedPlan && selectedPlan[0]?.no_of_contact_views;

    // Bypass email verification
    PostWithTokenNoCache(ApiName.userActivatedByAdmin, sendEmail)
      .then(async function (response) {
        if (response.data.status == 200) {
          PostWithTokenNoCache(ApiName.superSignUp, allData)
            .then(async function (response) {
              if (response.data.status == 200) {
                const dataPost = { email_id: email, package_id: selectedPlan && selectedPlan[0]?.id };
                let createOrderRes = await createOrder(dataPost);
                let createUserPlanRes = await createManualPayment(createOrderRes.userId)
                if (createUserPlanRes) navigate('/admin/paid-users');
              }
            })
            .catch(function (errors) {
              setLoading(false);
              setButtonType("error");
              setDefaultErrorMsg(errors.response.data.message);
              setDefaultAlert(true);
            });
        }
      }).catch(function (errors) {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(errors.response.data.message);
        setDefaultAlert(true);
      });
  }

  const onSubmitInvoiceHandler = (event) => {
    event.preventDefault();

    if (!switchUserData.amount_paid ||
      !switchUserData.invoice_id ||
      !switchUserData.status ||
      !switchUserData.paid ||
      !switchUserData.price_id
    ) {
      setButtonType("error");
      setDefaultErrorMsg("Please fill all the fields");
      setDefaultAlert(true);
    }

    const allData = {
      firstName: fname.trim(),
      lastName: lname.trim(),
      email: email.trim(),
      password: password.trim(),
      confirmPassword: cpassword.trim(),
      userRole: "customer",
      phoneNumber: phone ? phone.trim() : null,
      packageId: selectedPlan && selectedPlan[0]?.id,
      agreement: "true",
    };

    const sendEmail = {
      email: email.trim(),
    }
    close_invoice_popup.current.click();
    if (
      selectedPlan && selectedPlan[0]?.package_name !== "freemium" &&
      selectedPlan && selectedPlan[0]?.package_name !== "APP_SUMO_59"
    ) {
      setLoading(true);
      createSuperSaverAccount(allData, sendEmail)
    }
  }

  const onSubmitCoupon = async (event) => {
    event.preventDefault();
    if (promocode) {
      setLoading(true);
      const params = {
        coupon_id: promocode,
        user_id: userID,
        user_plan_name: selectedPlan && selectedPlan[0]?.package_name,
      };
      try {
        let response = await PostWithTokenNoCache(ApiName.oneTimeCoupon, params);
        if (response && "status" in response) {
          if (response.data.status == 200 && response.data.message == "Coupon Not Found") {
            setError("Invalid code");
            setPromocodeInvalid(false);
            setBtnVisible(false);
            setPromocodeMsg("Coupon Not Found");
            setLoading(false);
          } else if (response.data.status == 200 && response.data.message == "Valid Coupon") {
            navigate('/admin/app-sumo');
          } else {
            setError(response.response.data.message);
            setPromocodeMsg(response.response.data.message);
            setPromocodeInvalid(false);
            setBtnVisible(false);
            setLoading(false);
          }
        }
      } catch (errors) {
        setError(errors.response.data.message);
        setPromocodeMsg(errors.response.data.message);
        setPromocodeInvalid(false);
        setBtnVisible(false);
        setLoading(false);
      }
    }
  }

  const onSubmitHandler = (event) => {
    event.preventDefault();
    if (
      validFname &&
      validLname &&
      validEmail &&
      validPassword &&
      validPassword &&
      validConfirmPassword
    ) {
      const allData = {
        firstName: fname.trim(),
        lastName: lname.trim(),
        email: email.trim(),
        password: password.trim(),
        confirmPassword: cpassword.trim(),
        userRole: "customer",
        phoneNumber: phone ? phone.trim() : null,
        packageId: selectedPlan && selectedPlan[0]?.id,
        agreement: "true",
      };
      const sendEmail = {
        email: email.trim(),
      }

      if (selectedPlan && selectedPlan[0]?.package_name == "freemium") {
        setLoading(true);
        createFreemiumAccount(allData, sendEmail);
      }
      if (selectedPlan && selectedPlan[0]?.package_name == "APP_SUMO_59") {
        setLoading(true);
        createAppSumoAccount(allData, sendEmail);
      }
      if (selectedPlan && selectedPlan[0]?.package_name !== "freemium" &&
        selectedPlan && selectedPlan[0]?.package_name !== "APP_SUMO_59"
      ) {
        invoice_popup.current.click();
        return;
      }
    }
  };
  useEffect(() => {
    const postData = async () => {
      try {
        let params = JSON.stringify({
          "method": "GET"
        });
        const res = await PostWithTokenNoCache(ApiName.getPackegeDetails, params);
        if (res && "status" in res) {
          if (res.status == 200) {
            let jsonArray = res.data.data ? JSON.parse(res.data.data) : [];
            setPricePlan(jsonArray);
            setLoading(false);
          }
        }
      } catch (error) {
        setLoading(false);
        setButtonType("error");
        setDefaultErrorMsg(error.response.data.message);
        setDefaultAlert(true);
      }
    };
    postData();
  }, [])


  return (
    <div className={S.app}>
      <AdminHeader />
      <div className={S.main_container}>
        <div className="d-flex flex-row">
          <div style={{ width: "350px" }}>
            <LeftSideNav />
          </div>
          {!loading ? (
            <div>
              <div className="row">
                <div className="col-md-1"></div>
                <div className="col-md-6 mx-auto">
                  <form className={S.adminform}>
                    <h3 className={S.adminsignup}>Create User</h3>
                    <div className="row">
                      <div className="col-md-6">
                        <div class="form-group">
                          <label >First Name</label>
                          <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp"
                            onChange={onChangeFname}
                            value={fname}
                            autoFocus
                            autoComplete="off"
                          />
                          {!validFname ? (
                            <span className="signup-errors">
                              {fnameErr && <p>{fnameErr}</p>}
                            </span>
                          ) : (
                            <></>
                          )}
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div class="form-group">
                          <label >Last Name</label>
                          <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp"
                            onChange={onChangeLname}
                            value={lname}
                            autoComplete="off"
                          />
                          {!validLname ? (
                            <span className="signup-errors">
                              {lnameErr && <p>{lnameErr}</p>}
                            </span>
                          ) : (
                            <></>
                          )}
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div class="form-group">
                          <label >Company Name</label>
                          <input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp"
                            autoComplete="off"
                            onChange={onChangeCompany}
                            value={company}
                          />
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div class="form-group">
                          <label >Phone Number</label>
                          <input type="phone" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp"
                            onChange={onChangePhone}
                            value={phone}
                            autoComplete="off"
                          />
                          {!validPhone ? (
                            <span className="signup-errors">
                              {phoneErr && <p>{phoneErr}</p>}
                            </span>
                          ) : (
                            <></>
                          )}
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div class="form-group">
                          <label >Business Email</label>
                          <input type="phone" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp"
                            onChange={onChangeEmail}
                            value={email}
                            autoComplete="off"
                          />
                          {!validEmail ? (
                            <span className="signup-errors">
                              {emailErr && <p>{emailErr}</p>}
                            </span>
                          ) : (
                            <></>
                          )}
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div class="form-group">
                          <label >Select plan</label>
                          <select className="browser-default custom-select" style={{ margin: "0px 0 0 0" }}
                            onChange={handleSelectChange} value={selectedPlan ? selectedPlan[0]?.id : ""}>
                            <option value="">Select Plan</option>
                            {pricePlan && pricePlan.map((item, index) => (
                              <option key={index} value={item.id}>
                                {item.package_name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="col-md-6" style={{ display: selectedPlan && selectedPlan[0]?.package_name === "APP_SUMO_59" ? 'block' : 'none' }}>
                        <div class="form-group">
                          <label >Coupon</label>
                          <div
                            className={
                              !promocodeInvalid
                                ? "input-with-icon-text"
                                : "input-with-icon"
                            }
                          >
                            {!promocodeInvalid ? (
                              <input
                                type="text"
                                className="invalid-input-text"
                                name="promocode"
                                onChange={onChangePromocodeHandler}
                                value={promocode}
                                disabled={inputDisable ? "disabled" : ""}
                                autoFocus
                              />
                            ) : (
                              <></>
                            )}
                          </div>

                          {promocodeInvalid ? (
                            <input
                              type="text"
                              className="form-control input-field"
                              name="promocode"
                              onChange={onChangePromocodeHandler}
                              value={promocode}
                              disabled={inputDisable ? "disabled" : ""}
                              autoFocus
                            />
                          ) : (
                            <></>
                          )}

                          <span className="email-error-message">
                            {error && <p>{error}</p>}
                          </span>
                        </div>
                      </div>

                      <div className="col-md-6" style={{ display: selectedPlan && selectedPlan[0]?.package_name === "APP_SUMO_59" ? 'block' : 'none' }}>
                        <div class="form-group">
                          <label ></label>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div class="form-group">
                          <label for="exampleInputPassword1">Password</label>
                          <div className="password-input">
                            <input type={showPassword ? "text" : "password"} class="form-control" id="exampleInputPassword1" placeholder="Password"
                              onChange={onChangePassword}
                              value={password}
                              readOnly={
                                selectedPlan && selectedPlan[0]?.id &&
                                  (selectedPlan[0]?.package_name === "APP_SUMO_59" ? promocode : !promocode)
                                  ? false
                                  : true
                              }
                              autoComplete="off"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              <FontAwesomeIcon
                                icon={showPassword ? faEye : faEyeSlash}
                                style={{ pointerEvents: "none" }}
                              />
                            </button>
                          </div>
                          {password && !validCase ? (
                            <span className="signup-message">
                              <p>
                                Password must be 8 characters long with at least one uppercase (A-Z), lowercase (a-z) & special character (@, #, &, $, etc). {" "}
                              </p>
                            </span>
                          ) : (
                            <></>
                          )}
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div class="form-group">
                          <label for="exampleInputPassword1">Confirm Password</label>
                          <div className="password-input">
                            <input type={showConfirmPassword ? "text" : "password"} class="form-control" id="exampleInputPassword1" placeholder="Password"
                              onChange={onChangeConfirmPassword}
                              value={cpassword}
                              readOnly={
                                selectedPlan && selectedPlan[0]?.id &&
                                  (selectedPlan[0]?.package_name === "APP_SUMO_59" ? promocode : !promocode)
                                  ? false
                                  : true
                              }
                              autoComplete="off"
                            />
                            <button
                              type="button"
                              onClick={() =>
                                setShowConfirmPassword(!showConfirmPassword)
                              }
                            >
                              <FontAwesomeIcon
                                icon={showConfirmPassword ? faEye : faEyeSlash}
                                style={{ pointerEvents: "none" }}
                              />
                            </button>
                          </div>
                          {validCase &&
                            validLength &&
                            validWord &&
                            !validConfirmPassword ? (
                            <span className="signup-errors">
                              <p>Passwords do not match</p>
                            </span>
                          ) : (
                            <></>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="d-flex flex-row justify-content-center mb-3">
                      <div className="mb-3 mt-2">
                        {!promocode ? (
                          <button
                            type="submit"
                            className={
                              selectedPlan &&
                                selectedPlan[0]?.id &&
                                fname &&
                                lname &&
                                email &&
                                validFname &&
                                validLname &&
                                validEmail &&
                                validPassword &&
                                validPassword &&
                                validConfirmPassword
                                ? `${S.adminsubmit}`
                                : `${S.adminsubmit} cust-disabled`
                            }
                            onClick={onSubmitHandler}
                          >
                            Submit
                          </button>
                        ) : promocode && promocodeMsg === "" ? (
                          <button
                            type="button"
                            className={
                              selectedPlan &&
                                selectedPlan[0]?.id &&
                                fname &&
                                lname &&
                                email &&
                                validFname &&
                                validLname &&
                                validEmail &&
                                validPassword &&
                                validPassword &&
                                validConfirmPassword
                                ? `${S.adminsubmit}`
                                : `${S.adminsubmit} cust-disabled`
                            }
                            onClick={onSubmitHandler}
                          >
                            Submit
                          </button>
                        ) : (
                          <button
                            type="button"
                            className={
                              selectedPlan &&
                                selectedPlan[0]?.id &&
                                fname &&
                                lname &&
                                email &&
                                validFname &&
                                validLname &&
                                validEmail &&
                                validPassword &&
                                validPassword &&
                                validConfirmPassword
                                ? `${S.adminsubmit}`
                                : `${S.adminsubmit} cust-disabled`
                            }
                            onClick={onSubmitCoupon}
                          >
                            Re-Submit
                          </button>
                        )}
                      </div>
                    </div>
                  </form>
                </div>
                <div className="col-md-1"></div>

              </div>


            </div>
          ) : (
            <>
              <div className="mx-auto mt-5" style={{ display: loading }}>
                <img src={loadingGif} alt="Loading" className="loader" width="400" />
              </div>
            </>
          )}

          <button
            className={S.swichtuser} data-toggle="modal"
            data-target="#exampleModal"
            ref={invoice_popup}
            style={{ "display": "none" }}
          >
            Switch Plan
          </button>
          <div
            className="modal fade"
            id="exampleModal"
            tabIndex="-1"
            aria-labelledby="exampleModalLabel"
            aria-hidden="true"
          >
            <div className="modal-dialog">
              <div className="modal-content">
                <div className="modalheaderswitch">

                  <button
                    ref={close_invoice_popup}
                    type="button"
                    className="close"
                    data-dismiss="modal"
                    aria-label="Close"
                    onClick={() => { closeButton() }}
                  >
                    <span aria-hidden="true">&times;</span>
                  </button>

                  <h5
                    className={S.modaltitleswitch}

                  >
                    Invoice Details
                  </h5>


                </div>

                {message == true ? (
                  <p className={S.successfullyy}>Successfully Added</p>
                ) : (
                  ""
                )}
                {allFeilads == true ? (
                  <p className={S.pleaseenter}>Please Enter All Fields</p>
                ) : (
                  ""
                )}
                <div className="modal-body">
                  <div className="row">
                    <div className=" col-md-6 ">
                      <div className="form-group">
                        <label className="label_for_form">
                          Customer Email
                        </label>
                        <input
                          type="email"
                          className="form-control"
                          aria-describedby="customerHelp"
                          name="customer_email"
                          onChange={onChangeHandler}
                          value={email}
                          readOnly={true}
                        />
                      </div>
                    </div>

                    <div className=" col-md-6 ">
                      <label className="label_for_form">
                        Amount Paid
                      </label>
                      <div className="form-group">
                        <input
                          type="text"
                          className="form-control"
                          aria-describedby="AmountHelp"
                          name="amount_paid"
                          onChange={onChangeHandler}
                          value={switchUserData.amount_paid}
                          autoComplete="off"
                        />

                        {/* here */}
                        {formErrors.amount_paid && (
                          <div className={S.error_message}>
                            {formErrors.amount_paid}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="col-md-6">
                      <label className="label_for_form">
                        Invoice no
                      </label>
                      <div className="form-group">
                        <input
                          type="text"
                          className="form-control"
                          aria-describedby="invoiceHelp"
                          name="invoice_id"
                          onChange={onChangeHandler}
                          value={switchUserData.invoice_id}
                          autoComplete="off"
                        />
                        {formErrors.invoice_id && (
                          <div className={S.error_message}>
                            {formErrors.invoice_id}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="col-md-6">
                      <label className="label_for_form">
                        Customer Name
                      </label>
                      <div className="form-group">
                        <input
                          type="text"
                          className="form-control"
                          aria-describedby="custoHelp"
                          name="customer_name"
                          onChange={onChangeHandler}
                          value={fname}
                        />
                      </div>
                    </div>

                    <div className="col-md-6">
                      <label className="label_for_form">
                        Status
                      </label>
                      <div className="form-group">
                        <input
                          type="text"
                          className="form-control"
                          aria-describedby="statusHelp"
                          name="customer_name"
                          onChange={onChangeHandler}
                          value="paid"
                          readOnly={true}
                        />
                      </div>
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="label_for_form">
                        Price Id
                      </label>
                      <div className="form-group">
                        <input
                          type="text"
                          className="form-control"
                          aria-describedby="priceHelp"
                          name="price_id"
                          onChange={onChangeHandler}
                          value={switchUserData.price_id}
                          autoComplete="off"
                        />
                        {formErrors.price_id && (
                          <div className={S.error_message}>
                            {formErrors.price_id}
                          </div>
                        )}
                      </div>
                      <input
                        type="hidden"
                        className="form-control"
                        aria-describedby="paidHelp"
                        name="paid"
                        value="true"
                      />
                    </div>
                  </div>
                  <div className="col-md-6 mb-3">
                    <button
                      type="button"
                      onClick={onSubmitInvoiceHandler}
                      className={`${S.paiduser} ${!Object.values(switchUserData).every(Boolean) ? 'cust-disabled' : ''}`}
                    >
                      Submit
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {defaultAlert && defaultErrorMsg ? (
            <Alert data={defaultErrorMsg} />
          ) : (<></>)}

        </div>
      </div>
    </div>
  )
}
export default AdminForm;