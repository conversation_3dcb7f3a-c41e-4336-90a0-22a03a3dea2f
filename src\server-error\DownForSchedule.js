import React from "react";
import "../customer/assests/css/servererrorcss/downforschedule.css";

const DownForSchedule = () => {
    return (

        <>
            <nav className="navbar navbar-expand-lg navbar-light d-flex justify-content-between">
                <a className="navbar-brand" href={window.location.href}><img src="../images/black-logo.png" className="img-fluid" width="150" />
                </a>
            </nav>

            <div className="container">
                <div className="gear-liver">
                    <img src="../images/icon-setting.png" className="img-fluid mx-auto d-block mt-5" width="300" />
                        </div>

                        <div className="down text-center">
                            <h1>Sorry, We're Down For Scheduled Maintenance.</h1>
                        </div>

                        <div className="expect text-center">
                            <h4>We expect to be back very soon. Thank you for your patience</h4>
                        </div>

                        <div className="input-group mb-3   email-input">
                            <input type="text" className="form-control email-input-feild" placeholder="Enter Your Email Address" aria-label="Recipient's username" aria-describedby="button-addon2" />
                            <div className="input-group-append">
                                <button className="notify" type="button" id="button-addon2">Notify me</button>
                            </div>
                        </div>
                </div>
            </>

            )
}

            export default DownForSchedule;