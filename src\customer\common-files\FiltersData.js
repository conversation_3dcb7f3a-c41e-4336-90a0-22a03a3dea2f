import React from "react";
import { useState } from "react";

// export const initialJobTitles = [
//   { value: "Doctor", label: "Doctor" },
//   { value: "Engineer", label: "Engineer" },
//   { value: "Executive Director", label: "Executive Director" },
//   { value: "Director", label: "Director" },
//   { value: "Electrical Engineer", label: "Electrical Engineer" },
//   { value: "Educator", label: "Educator" },
//   { value: "Software Engineer", label: "Software Engineer" },
// ];

export const SearchJobTitle = () => {
  return [
    { value: "Doctor", label: "Doctor" },
    { value: "Engineer", label: "Engineer" },
    { value: "Director", label: "Director" },
    { value: "ExicutiveDirector", label: "Exicutive Director" },
    { value: "ElectricalEngineer", label: "Electrical Engineer" },
    { value: "Educator", label: "Educator" },
    { value: "SoftwareEngineer", label: "Software Engineer" },
  ];
};
export const JobLevel = () => {
  return [
    { value: "Owner", label: "Owner" },
    { value: "Founder", label: "Founder" },
    { value: "C-Suite", label: "C-Suite" },
    { value: "Partner", label: "Partner" },
    { value: "VP", label: "VP" },
    { value: "Head", label: "Head" },
    { value: "Director", label: "Director" },
    { value: "Manager", label: "Manager" },
    { value: "Senior", label: "Senior" },
    { value: "Entry", label: "Entry" },
    { value: "Intern", label: "Intern" },
  ];
};
export const JobDepartmentsAndFuntions = () => {
  return [
    {
      id: "item1",
      name: "C-Suite",
      subitems: ["Executive", "Finance Executive", "Founder"],
    },
    {
      id: "item2",
      name: "Engineering & Technical",
      subitems: ["Sub Option 1", "Sub Option 2"],
    },
    /*{ id: 'item3', name: 'Meat', subitems: ['Beef', 'Pork', 'Chicken'] },*/
  ];
  /*return [
    {
      label: "C-Suite",
      value: "C-Suite",
      subOptions: [
        {
          label: "Exicutive",
          value: "Exicutive"
        },
        {
          label: "Finace Exicutive",
          value: "Finace Exicutive"
        },
        {
          label: "Founder",
          value: "Founder"
        }
      ]
    },
    {
      label: "Engineering & Technical",
      value: "Engineering",
      subOptions: [
        {
          label: "Sub Option 2",
          value: "suboption2.1"
        },
        {
          label: "Sub Option 2.2",
          value: "suboption2.2"
        }
      ]
    },
    {
      label: "Design",
      value: "Design",
      subOptions: [
        {
          label: "Sub Option 2",
          value: "suboption2.1"
        },
        {
          label: "Sub Option 2.2",
          value: "suboption2.2"
        }
      ]
    },
    {
      label: "Education",
      value: "Education",
      subOptions: [
        {
          label: "Sub Option 2",
          value: "suboption2.1"
        },
        {
          label: "Sub Option 2.2",
          value: "suboption2.2"
        }
      ]
    },
    {
      label: "Finace",
      value: "Finace",
      subOptions: [
        {
          label: "Sub Option ",
          value: "suboption"
        },
        {
          label: "Sub Option",
          value: "suboption"
        }
      ]
    },

  ];*/
};

export const IndustryList = () => {
  return [
    { value: "red", label: "Accounting" },
    { value: "green", label: "Real Estate" },
    { value: "yellow", label: "Finacial Services" },
    { value: "blue", label: "Agriculture" },
    { value: "white", label: "Machinery" },
    { value: "white", label: "Construction" },
    { value: "white", label: "Automotive" },
    { value: "white", label: "Store manager" },
    { value: "white", label: "Staff nurse" },
    { value: "white", label: "Staff doctor" },
  ];
};
export const IndustrySicList = () => {
  return [
    { value: "red", label: "1011" },
    { value: "green", label: "1021" },
    { value: "yellow", label: "1022" },
    { value: "blue", label: "1023" },
  ];
};
export const EmployesSize = () => {
  return [
    { value: "red", label: "1-10" },
    { value: "green", label: "21-50" },
    { value: "yellow", label: "51-100" },
    { value: "blue", label: "100-200" },
    { value: "blue", label: "200-500" },
    { value: "blue", label: "500-1000" },
    { value: "blue", label: "1000-2000" },
    { value: "blue", label: "2000-5000" },
    { value: "blue", label: "5000-10000" },
    { value: "blue", label: "10,000-1M" },
    { value: "blue", label: "1M-10M" },
  ];
};
export const RevenueSizeData = () => {
  return [
    { value: "red", label: "$1-10M" },
    { value: "green", label: "$10-100M" },
    { value: "green", label: "$100-1B" },
    { value: "green", label: "$10B-100B" },
  ];
};
export const LocationData = () => {
  return [
    { value: "red", label: "React Js" },
    { value: "green", label: "PHP" },
    { value: "yellow", label: "Java" },
    { value: "blue", label: "Ruby" },
    { value: "blue", label: "Mysql" },
    { value: "blue", label: ".Net" },
  ];
};
export const CompanyData = () => {
  return [
    { value: "red", label: "Insnap" },
    { value: "green", label: "Spokesly" },
    { value: "yellow", label: "Infosys" },
    { value: "blue", label: "IBM" },
    { value: "blue", label: "HCL" },
    { value: "blue", label: "Tech Mahindra" },
  ];
};

export const SearchJobtitleStyles = {
  control: (provided, state) => ({
    ...provided,
    // width: '262px',
    marginLeft: "32px",
    backgroudColor: "#fff",
    outline: "none",
    border: "1px solid #093D54",
    cursor: "text",

    ":hover": {
      borderColor: "#000",
    },
    ":focus": {
      border: "1px solid #000",
    },
  }),

  placeholder: (baseStyles, state) => ({
    ...baseStyles,
    gridArea: "1/1/2/3",
    color: "#A3AEBB",
    marginLeft: "15px",
  }),

  option: (base, state) => ({
    ...base,
    marginLeft: "0px",
    backgroudColor: "#fff",
  }),

  multiValue: (provided, state) => ({
    ...provided,
    border: "1px solid #000",
    borderRadius: "20px",
    backgroud: "red",
    fontSize: "10px",
    backgroundColor: "hsl(0deg 0% 100%)",
    cursor: "pointer",
    marginTop: "10px",
    
  }),

  singleValue: (provided, state) => ({
    ...provided,
    borderRadius: "10px",
    backgroudColor: "red",
  }),

  multiValueGeneric: (provided, state) => ({
    ...provided,
    fontSize: "16px",
    paddingLeft: "8",
    paddingTop: "0px",
    padding: "1px",
  }),

  indicatorsContainer: (provided, state) => ({
    ...provided,
    visibility: "hidden",
  }),

  menuList: (provided, state) => ({
    ...provided,
    "&:hover:": {
      color: "#55C2C3",
      marginTop: "-6px",
      marginBottom: "14px",
      backgroudColor: "transperent",
    },

    "&:hover": {
      backgroundColor: "inherit",
    },
  }),

  menu: (provided, state) => ({
    ...provided,

    border: "1px solid #093D54",
    marginLeft: "2rem",
    marginRight: "2rem",
    fontSize: "14px",
    marginRight: "0px",
    position: "relative",
    width: "-webkit-fill-available",
    backgroudColor: "#fff",

    ":hover": {
      color: "inherit",
      textDecoration: "none",
    },
  }),


  multiValueRemove: (provided, state) => ({
    ...provided,

    cursor: "pointer",
  }),

  multiValueRemove: (base, state) => ({
    ...base,
    backgroundColor: "#fff",
    setColor: "#000",
    cursor: "pointer",
    borderRadius: "50%",
    borderRadius: "10px",
    paddingLeft: "-60",
    paddingRight: "0",
    innerHeight: "10px",
    border: "1px solid #08CEDC",
    marginTop: "-7px",
    marginBottom: "2px",
    font: "black !important",
    backgroudColor: "#ffffff",
    width: "11px",
    height: "11px",
    "&:hover": {
      backgroundColor: "#55C2C3",
      color: "black",
    },
  }),

  indicatorSeparator: (provided, state) => ({
    ...provided,
    backgroundColor: "hsl(0deg 0% 100%)",
  }),

  MenuList: (provided, state) => ({
    ...provided,
    borderRadius: "10px",
    paddingLeft: "0px",
    color: "#000",
    paddingRight: "0",
    backgroudColor: "#fff",
    outline: "none",
  }),

  valueContainer: (provided, state) => ({
    ...provided,
    borderRadius: "10px",
    paddingLeft: "0px",
    color: "#000",
    paddingRight: "0",
    backgroudColor: "#000",
    outline: "none",
    padding: "0px 5px 0 0",
  }),
  container: (provided, state) => ({
    ...provided,
    padding: "0px 11px 0 0",
  }),
  group: (provided, state) => ({
    ...provided,
    width: "262px",
    // paddingLeft: '23px',
    marginLeft: "32px",
    backgroudColor: "#fff",
    outline: "none",
    border: "1px solid #093D54",
  }),
};
export const jobTitlesArr = [
  "Engineer",
  "Director",
  "Executive Director",
  "Electrical Engineer",
  "Educator",
  "Software Engineer",
  "Abstractor",
  "Account-Manager",
  "Accountant",
  "Activist",
  "Actor",
  "Actuary",
  "Acupuncturist",
  "Adjunct",
  "Adjuster",
  "Admin",
  "Administrative",
  "Administrator",
  "Advisor",
  "Advisory",
  "Advocate",
  "Aesthetician",
  "After-Sales-Manager",
  "Agent",
  "Agronomist",
  "Aide",
  "Allocator",
  "Ambassador",
  "Analyst",
  "Anchor",
  "Anesthesiologist",
  "Animator",
  "Announcer",
  "Anthropologist",
  "Appraiser",
  "Apprentice",
  "Arbitrator",
  "Arborist",
  "Archaeologist",
  "Archeologist",
  "Architect",
  "Archivist",
  "Army",
  "Art",
  "Artist",
  "Assembler",
  "Assessor",
  "Assistant",
  "Associate",
  "Astronomer",
  "Athlete",
  "Attendant",
  "Attorney",
  "Audiologist",
  "Audit",
  "Auditor",
  "Author",
  "Babysitter",
  "Bagger",
  "Bailiff",
  "Baker",
  "Bank",
  "Banker",
  "Banking",
  "Banquet",
  "Barber",
  "Barista",
  "Bartender",
  "Bellhop",
  "Bellman",
  "Biller",
  "Biochemist",
  "Bioinformatician",
  "Biologist",
  "Biophysicist",
  "Biostatistician",
  "Board",
  "Boilermaker",
  "Bookbinder",
  "Bookkeeper",
  "Boss",
  "Brazer",
  "Breeder",
  "Brewer",
  "Brickmason",
  "Broker",
  "Builder",
  "Business",
  "Busser",
  "Butcher",
  "Butler",
  "Buyer",
  "c/C++",
  "Cabinetmaker",
  "CAIO",
  "Candidate",
  "CAO",
  "Captain",
  "Cardiologist",
  "Caregiver",
  "Caretaker",
  "Carpenter",
  "Carrier",
  "Cartographer",
  "Cartoonist",
  "Carver",
  "Caseworker",
  "Cashier",
  "CBDO",
  "CBO",
  "CCO",
  "CDO",
  "CEO",
  "CFO",
  "CGO",
  "Chairman",
  "Chaplain",
  "Chauffeur",
  "Checker",
  "Chef",
  "Chemist",
  "Chief",
  "Chiropractor",
  "Choreographer",
  "CHRO",
  "CINO",
  "CIO",
  "CISO",
  "CITO",
  "City",
  "Civil",
  "CKO",
  "Cleaner",
  "Cleaning",
  "Clergy",
  "Clerical",
  "Clerk",
  "Clinician",
  "CLO",
  "Closer",
  "CMO",
  "Cna",
  "CNO",
  "Co-Chair",
  "Co-Chairman",
  "Co-Director",
  "Co-Founder",
  "Co-Leader",
  "Co-Owner",
  "Co-President",
  "Coach",
  "Coder",
  "Cofounder",
  "Collector",
  "Compliance-Manager",
  "Composer",
  "Computer",
  "Concierge",
  "Conductor",
  "Construction",
  "Consultant",
  "Contractor",
  "Controller",
  "COO",
  "Cook",
  "Coordinator",
  "Coowner",
  "Copilot",
  "Copyeditor",
  "Copywriter",
  "Coroner",
  "Correspondent",
  "Cosmetologist",
  "Counsel",
  "Counselor",
  "Counsil",
  "Courier",
  "CPO",
  "CPSO",
  "CQO",
  "Craftsman",
  "CRDO",
  "Creative",
  "Crew",
  "CRO",
  "CSCO",
  "CSMO",
  "CSO",
  "CTO",
  "CTPO",
  "CTSO",
  "CUO",
  "Curator",
  "Custodian",
  "Custom",
  "Cutter",
  "CVO",
  "CWO",
  "CXO",
  "Cytopathologist",
  "Cytotechnologist",
  "Dancer",
  "Dealer",
  "Dean",
  "Deckhand",
  "Decorator",
  "Demonstrator",
  "Dentist",
  "Deputy",
  "Dermatologist",
  "Dermatopathologist",
  "Designer",
  "Detailer",
  "Detective",
  "Developer",
  "Dietician",
  "Dietitian",
  // "Director",
  "Director;Prin",
  "Dishwasher",
  "Dispatcher",
  "Distributor",
  "Doctor",
  "Dosimetrist",
  "Drafter",
  "Draftsman",
  "Draftsperson",
  "Driller",
  "Driver",
  "Ecologist",
  "Economist",
  "Editor",
  "Editorial",
  // "Educator",
  "Electrician",
  "Electrotyper",
  "Embalmer",
  "Embryologist",
  "Employer",
  "Endocrinologist",
  "Endodontist",
  // "Engineer",
  "Engineering",
  "Engraver",
  "Enologist",
  "Entrepreneur",
  "Enumerator",
  "Epidemiologist",
  "Esthetician",
  "Estimator",
  "Etcher",
  "Evaluator",
  "Evangelist",
  "Event-Manager",
  "Examiner",
  "Executive",
  "Executive-Director",
  "Expeditor",
  "Fabricator",
  "Facilitator",
  "Faculty",
  "Farmer",
  "Farmworker",
  "Fellow",
  "Fellowship",
  "Filmmaker",
  "Finisher",
  "Firefighter",
  "Fitter",
  "Florist",
  "Foreman",
  "Forester",
  "Founder",
  "Framer",
  "Franchisee",
  "Fresher",
  "Fund",
  "Funder",
  "Fundraiser",
  "Gardener",
  "Gastroenterologist",
  "Gastroenterology",
  "Gauger",
  "Gemologist",
  "General",
  "Generalist",
  "Geneticist",
  "Geochemist",
  "Geographer",
  "Geologist",
  "Geophysicist",
  "Geoscientist",
  "Geoscientists",
  "Glazier",
  "Government",
  "Grader",
  "Graduate",
  "Greeter",
  "Groomer",
  "Groundskeeper",
  "Guard",
  "Guide",
  "Guru",
  "Gynecologist",
  "Gynecology",
  "Hacker",
  "Hairdresser",
  "Hairstylist",
  "Handler",
  "Handyman",
  "Head",
  "Hematologist",
  "Hematologist-Oncologist",
  "Hematology",
  "Hepatologist",
  "Historian",
  "Homemaker",
  "Horticulturist",
  "Hospital",
  "Hospitalist",
  "Host",
  "Hostess",
  "Housekeeper",
  "Hr-Manager",
  "Hunter",
  "Hydrogeologist",
  "Hydrologist",
  "Hygienist",
  "Illustrator",
  "Immunologist",
  "Implementor",
  "Indexer",
  "Infantry",
  "Innkeeper",
  "Innovator",
  "Inspector",
  "Installer",
  "Instructor",
  "Intensivist",
  "Intern",
  "Internist",
  "Internship",
  "Interpreter",
  "Interventionist",
  "Interviewer",
  "Inventor",
  "Investigator",
  "Investor",
  "Janitor",
  "Java",
  "Jeweler",
  "Jockey",
  "Journalist",
  "Journeyman",
  "Judge",
  "Keeper",
  "Key-Account-Manager",
  "Keyboardist",
  "Keyholder",
  "Kinesiologist",
  "Kinesiotherapist",
  "Laborer",
  "Landman",
  "Landscaper",
  "Lawyer",
  "Lead",
  "Lecturer",
  "Legislator",
  "Lender",
  "Librarian",
  "Lieutenant",
  "Lifeguard",
  "Lineman",
  "Linguist",
  "Loader",
  "Lobbyist",
  "Locator",
  "Locksmith",
  "Logistician",
  "Logistics",
  "Lyricist",
  "Machinist",
  "Magistrate",
  "Maid",
  "Mail",
  "Maintainer",
  "Major",
  "Management",
  "Manager",
  "Manicurists",
  "Marine",
  "Marketer",
  "Mason",
  "Master",
  "Mathematician",
  "Mathematics",
  "Mayor",
  "MBA",
  "Mechanic",
  "Medicaldirector",
  "Mentor",
  "Merchandiser",
  "Merchant",
  "Messenger",
  "Metallurgist",
  "Meteorologist",
  "Microbiologist",
  "Midwife",
  "Military",
  "Millwright",
  "Mixer",
  "Model",
  "Modeler",
  "Moderator",
  "Molder",
  "Monitor",
  "Mover",
  "Movers",
  "Musician",
  "Nanny",
  "Naturalist",
  "Navigator",
  "Navy",
  "Negotiator",
  "Neonatologist",
  "Nephrologist",
  "Neuro-oncologist",
  "Neuro-Oncology",
  "Neurologist",
  "Neuropathologist",
  "Neuropsychologist",
  "Neuroradiologist",
  "Neurosurgeon",
  "Neurotologist",
  "Nonprofit",
  "Nurse",
  "Nutritionist",
  "Obstetrician",
  "Obstetrics",
  "Office-Manager",
  "Officer",
  "Ombudsman",
  "Oncologic",
  "Oncologist",
  "Oncology",
  "Operator",
  "Ophthalmologist",
  "Optician",
  "Opticians",
  "Optometrist",
  "Organist",
  "Organizer",
  "Originator",
  "Orthodontist",
  "Orthopedics",
  "Orthotist",
  "Otolaryngologist",
  "Otologist",
  "Owner",
  "Packager",
  "Packer",
  "Painter",
  "Paperhanger",
  "Paraeducator",
  "Paralegal",
  "Paramedic",
  "Paraprofessional",
  "Partner",
  "Pathologist",
  "Patternmaker",
  "Pediatrician",
  "Pediatricians",
  "Pediatrics",
  "Pedodontist",
  "Perfusionist",
  "Pharmacist",
  "PHD",
  "Phlebologist",
  "Phlebotomist",
  "Photo",
  "Photoengraver",
  "Photographer",
  "php",
  "Physician",
  "Physicist",
  "Physiotherapist",
  "Picker",
  "Pilot",
  "Pipefitter",
  "Pipelayer",
  "Planner",
  "Platemaker",
  "Player",
  "Plumber",
  "Podiatrist",
  "Poet",
  "Police",
  "Porter",
  "Postdoc",
  "Postmasters",
  "Potter",
  "Pr-Manager",
  "Practitioner",
  "President",
  "Principal",
  "Printer",
  "Private",
  "Processor",
  "Proctologist",
  "Procurement",
  "Producer",
  "Professor",
  "Programmer",
  "Programming",
  "Promoter",
  "Proofreader",
  "Proprietor",
  "Prosthetist",
  "Prosthodontist",
  "Psychiatric",
  "Psychiatrist",
  "Psychiatry",
  "Psychologist",
  "Psychotherapist",
  "Publicist",
  "Publisher",
  "Pulmonologist",
  "Pulmonology",
  "Purchaser",
  "Purchasing",
  "Radiographer",
  "Radiologist",
  "Ranger",
  "Realtor",
  "Receiver",
  "Receptionist",
  "Records",
  "Recruiter",
  "Regional",
  "Registrar",
  "Repairer",
  "Repairman",
  "Reporter",
  "Reporters",
  "Representative",
  "Researcher",
  "Retailer",
  "Retired",
  "Retoucher",
  "Reviewer",
  "Rheumatologist",
  "Rider",
  "Rigger",
  "RN",
  "Roofer",
  "Roustabouts",
  "Runner",
  "Sailor",
  "Sales",
  "Salesman",
  "Salesperson",
  "Scheduler",
  "Science",
  "Scientist",
  "Screener",
  "Sculptor",
  "Secretary",
  "Selector",
  "Senior",
  "Server",
  "Shampooer",
  "Sheriff",
  "Shipper",
  "Shopper",
  "Silversmith",
  "Singer",
  "Sitter",
  "Sociologist",
  "Solderer",
  "Sonographer",
  "Sorter",
  "Sourcer",
  "Specialist",
  "Spotters",
  "Startup-Manager",
  "Statistician",
  "Stevedore",
  "Steward",
  "Stockbroker",
  "Stocker",
  "Stonemason",
  "Storyteller",
  "Strategist",
  "Stripper",
  "Student",
  "Students",
  "Stylist",
  "Subcontractor",
  "Superintendent",
  "Supervisor",
  "Surgeon",
  "Surveyor",
  "Tailor",
  "Talent",
  "Taper",
  "Teacher",
  "Technician",
  "Technologist",
  "Telemarketer",
  "Teller",
  "Tester",
  "Testing",
  "Therapist",
  "Toolmaker",
  "Toxicologist",
  "Trader",
  "Traffic-Manager",
  "Trainee",
  "Trainer",
  "Transcriptionist",
  "Translator",
  "Transporter",
  "Traveler",
  "Treasurer",
  "Tutor",
  "Typesetter",
  "Typist",
  "Ultrasonographer",
  "Umpire",
  "Undergraduate",
  "Underwriter",
  "Unemployed",
  "Unspecified",
  "Upholsterer",
  "Urogynecologist",
  "Urologist",
  "Urology",
  "Usher",
  "Valet",
  "Vendor",
  "Verifier",
  "Veterinarian",
  "Videographer",
  "Visitor",
  "Visual",
  "Vocalist",
  "Volunteer",
  "Vp",
  "Waiter",
  "Waitress",
  "Washer",
  "Webmaster",
  "Website-Manager",
  "Welder",
  "Welder-Fitter",
  "Wholesaler",
  "Woodworker",
  "Wrangler",
  "Writer",
  "Zoologists",
  ".net",
  "Vice President",
];
export const initialJobTitles = [
  { value: "Engineer", label: "Engineer" },
  { value: "Executive Director", label: "Executive Director" },
  { value: "Director", label: "Director" },
  { value: "Electrical Engineer", label: "Electrical Engineer" },
  { value: "Educator", label: "Educator" },
  { value: "Software Engineer", label: "Software Engineer" },
  {
    value: "Abstractor",
    label: "Abstractor",
  },
  {
    value: "Account-Manager",
    label: "Account-Manager",
  },
  {
    value: "Accountant",
    label: "Accountant",
  },
  {
    value: "Activist",
    label: "Activist",
  },
  {
    value: "Actor",
    label: "Actor",
  },
  {
    value: "Actuary",
    label: "Actuary",
  },
  {
    value: "Acupuncturist",
    label: "Acupuncturist",
  },
  {
    value: "Adjunct",
    label: "Adjunct",
  },
  {
    value: "Adjuster",
    label: "Adjuster",
  },
  {
    value: "Admin",
    label: "Admin",
  },
  {
    value: "Administrative",
    label: "Administrative",
  },
  {
    value: "Administrator",
    label: "Administrator",
  },
  {
    value: "Advisor",
    label: "Advisor",
  },
  {
    value: "Advisory",
    label: "Advisory",
  },
  {
    value: "Advocate",
    label: "Advocate",
  },
  {
    value: "Aesthetician",
    label: "Aesthetician",
  },
  {
    value: "After-Sales-Manager",
    label: "After-Sales-Manager",
  },
  {
    value: "Agent",
    label: "Agent",
  },
  {
    value: "Agronomist",
    label: "Agronomist",
  },
  {
    value: "Aide",
    label: "Aide",
  },
  {
    value: "Allocator",
    label: "Allocator",
  },
  {
    value: "Ambassador",
    label: "Ambassador",
  },
  {
    value: "Analyst",
    label: "Analyst",
  },
  {
    value: "Anchor",
    label: "Anchor",
  },
  {
    value: "Anesthesiologist",
    label: "Anesthesiologist",
  },
  {
    value: "Animator",
    label: "Animator",
  },
  {
    value: "Announcer",
    label: "Announcer",
  },
  {
    value: "Anthropologist",
    label: "Anthropologist",
  },
  {
    value: "Appraiser",
    label: "Appraiser",
  },
  {
    value: "Apprentice",
    label: "Apprentice",
  },
  {
    value: "Arbitrator",
    label: "Arbitrator",
  },
  {
    value: "Arborist",
    label: "Arborist",
  },
  {
    value: "Archaeologist",
    label: "Archaeologist",
  },
  {
    value: "Archeologist",
    label: "Archeologist",
  },
  {
    value: "Architect",
    label: "Architect",
  },
  {
    value: "Archivist",
    label: "Archivist",
  },
  {
    value: "Army",
    label: "Army",
  },
  {
    value: "Art",
    label: "Art",
  },
  {
    value: "Artist",
    label: "Artist",
  },
  {
    value: "Assembler",
    label: "Assembler",
  },
  {
    value: "Assessor",
    label: "Assessor",
  },
  {
    value: "Assistant",
    label: "Assistant",
  },
  {
    value: "Associate",
    label: "Associate",
  },
  {
    value: "Astronomer",
    label: "Astronomer",
  },
  {
    value: "Athlete",
    label: "Athlete",
  },
  {
    value: "Attendant",
    label: "Attendant",
  },
  {
    value: "Attorney",
    label: "Attorney",
  },
  {
    value: "Audiologist",
    label: "Audiologist",
  },
  {
    value: "Audit",
    label: "Audit",
  },
  {
    value: "Auditor",
    label: "Auditor",
  },
  {
    value: "Author",
    label: "Author",
  },
  {
    value: "Babysitter",
    label: "Babysitter",
  },
  {
    value: "Bagger",
    label: "Bagger",
  },
  {
    value: "Bailiff",
    label: "Bailiff",
  },
  {
    value: "Baker",
    label: "Baker",
  },
  {
    value: "Bank",
    label: "Bank",
  },
  {
    value: "Banker",
    label: "Banker",
  },
  {
    value: "Banking",
    label: "Banking",
  },
  {
    value: "Banquet",
    label: "Banquet",
  },
  {
    value: "Barber",
    label: "Barber",
  },
  {
    value: "Barista",
    label: "Barista",
  },
  {
    value: "Bartender",
    label: "Bartender",
  },
  {
    value: "Bellhop",
    label: "Bellhop",
  },
  {
    value: "Bellman",
    label: "Bellman",
  },
  {
    value: "Biller",
    label: "Biller",
  },
  {
    value: "Biochemist",
    label: "Biochemist",
  },
  {
    value: "Bioinformatician",
    label: "Bioinformatician",
  },
  {
    value: "Biologist",
    label: "Biologist",
  },
  {
    value: "Biophysicist",
    label: "Biophysicist",
  },
  {
    value: "Biostatistician",
    label: "Biostatistician",
  },
  {
    value: "Board",
    label: "Board",
  },
  {
    value: "Boilermaker",
    label: "Boilermaker",
  },
  {
    value: "Bookbinder",
    label: "Bookbinder",
  },
  {
    value: "Bookkeeper",
    label: "Bookkeeper",
  },
  {
    value: "Boss",
    label: "Boss",
  },
  {
    value: "Brazer",
    label: "Brazer",
  },
  {
    value: "Breeder",
    label: "Breeder",
  },
  {
    value: "Brewer",
    label: "Brewer",
  },
  {
    value: "Brickmason",
    label: "Brickmason",
  },
  {
    value: "Broker",
    label: "Broker",
  },
  {
    value: "Builder",
    label: "Builder",
  },
  {
    value: "Business",
    label: "Business",
  },
  {
    value: "Busser",
    label: "Busser",
  },
  {
    value: "Butcher",
    label: "Butcher",
  },
  {
    value: "Butler",
    label: "Butler",
  },
  {
    value: "Buyer",
    label: "Buyer",
  },
  {
    value: "c/C++",
    label: "c/C++",
  },
  {
    value: "Cabinetmaker",
    label: "Cabinetmaker",
  },
  {
    value: "CAIO",
    label: "CAIO",
  },
  {
    value: "Candidate",
    label: "Candidate",
  },
  {
    value: "CAO",
    label: "CAO",
  },
  {
    value: "Captain",
    label: "Captain",
  },
  {
    value: "Cardiologist",
    label: "Cardiologist",
  },
  {
    value: "Caregiver",
    label: "Caregiver",
  },
  {
    value: "Caretaker",
    label: "Caretaker",
  },
  {
    value: "Carpenter",
    label: "Carpenter",
  },
  {
    value: "Carrier",
    label: "Carrier",
  },
  {
    value: "Cartographer",
    label: "Cartographer",
  },
  {
    value: "Cartoonist",
    label: "Cartoonist",
  },
  {
    value: "Carver",
    label: "Carver",
  },
  {
    value: "Caseworker",
    label: "Caseworker",
  },
  {
    value: "Cashier",
    label: "Cashier",
  },
  {
    value: "CBDO",
    label: "CBDO",
  },
  {
    value: "CBO",
    label: "CBO",
  },
  {
    value: "CCO",
    label: "CCO",
  },
  {
    value: "CDO",
    label: "CDO",
  },
  {
    value: "CEO",
    label: "CEO",
  },
  {
    value: "CFO",
    label: "CFO",
  },
  {
    value: "CGO",
    label: "CGO",
  },
  {
    value: "Chairman",
    label: "Chairman",
  },
  {
    value: "Chaplain",
    label: "Chaplain",
  },
  {
    value: "Chauffeur",
    label: "Chauffeur",
  },
  {
    value: "Checker",
    label: "Checker",
  },
  {
    value: "Chef",
    label: "Chef",
  },
  {
    value: "Chemist",
    label: "Chemist",
  },
  {
    value: "Chief",
    label: "Chief",
  },
  {
    value: "Chiropractor",
    label: "Chiropractor",
  },
  {
    value: "Choreographer",
    label: "Choreographer",
  },
  {
    value: "CHRO",
    label: "CHRO",
  },
  {
    value: "CINO",
    label: "CINO",
  },
  {
    value: "CIO",
    label: "CIO",
  },
  {
    value: "CISO",
    label: "CISO",
  },
  {
    value: "CITO",
    label: "CITO",
  },
  {
    value: "City",
    label: "City",
  },
  {
    value: "Civil",
    label: "Civil",
  },
  {
    value: "CKO",
    label: "CKO",
  },
  {
    value: "Cleaner",
    label: "Cleaner",
  },
  {
    value: "Cleaning",
    label: "Cleaning",
  },
  {
    value: "Clergy",
    label: "Clergy",
  },
  {
    value: "Clerical",
    label: "Clerical",
  },
  {
    value: "Clerk",
    label: "Clerk",
  },
  {
    value: "Clinician",
    label: "Clinician",
  },
  {
    value: "CLO",
    label: "CLO",
  },
  {
    value: "Closer",
    label: "Closer",
  },
  {
    value: "CMO",
    label: "CMO",
  },
  {
    value: "Cna",
    label: "Cna",
  },
  {
    value: "CNO",
    label: "CNO",
  },
  {
    value: "Co-Chair",
    label: "Co-Chair",
  },
  {
    value: "Co-Chairman",
    label: "Co-Chairman",
  },
  {
    value: "Co-Director",
    label: "Co-Director",
  },
  {
    value: "Co-Founder",
    label: "Co-Founder",
  },
  {
    value: "Co-Leader",
    label: "Co-Leader",
  },
  {
    value: "Co-Owner",
    label: "Co-Owner",
  },
  {
    value: "Co-President",
    label: "Co-President",
  },
  {
    value: "Coach",
    label: "Coach",
  },
  {
    value: "Coder",
    label: "Coder",
  },
  {
    value: "Cofounder",
    label: "Cofounder",
  },
  {
    value: "Collector",
    label: "Collector",
  },
  {
    value: "Compliance-Manager",
    label: "Compliance-Manager",
  },
  {
    value: "Composer",
    label: "Composer",
  },
  {
    value: "Computer",
    label: "Computer",
  },
  {
    value: "Concierge",
    label: "Concierge",
  },
  {
    value: "Conductor",
    label: "Conductor",
  },
  {
    value: "Construction",
    label: "Construction",
  },
  {
    value: "Consultant",
    label: "Consultant",
  },
  {
    value: "Contractor",
    label: "Contractor",
  },
  {
    value: "Controller",
    label: "Controller",
  },
  {
    value: "COO",
    label: "COO",
  },
  {
    value: "Cook",
    label: "Cook",
  },
  {
    value: "Coordinator",
    label: "Coordinator",
  },
  {
    value: "Coowner",
    label: "Coowner",
  },
  {
    value: "Copilot",
    label: "Copilot",
  },
  {
    value: "Copyeditor",
    label: "Copyeditor",
  },
  {
    value: "Copywriter",
    label: "Copywriter",
  },
  {
    value: "Coroner",
    label: "Coroner",
  },
  {
    value: "Correspondent",
    label: "Correspondent",
  },
  {
    value: "Cosmetologist",
    label: "Cosmetologist",
  },
  {
    value: "Counsel",
    label: "Counsel",
  },
  {
    value: "Counselor",
    label: "Counselor",
  },
  {
    value: "Counsil",
    label: "Counsil",
  },
  {
    value: "Courier",
    label: "Courier",
  },
  {
    value: "CPO",
    label: "CPO",
  },
  {
    value: "CPSO",
    label: "CPSO",
  },
  {
    value: "CQO",
    label: "CQO",
  },
  {
    value: "Craftsman",
    label: "Craftsman",
  },
  {
    value: "CRDO",
    label: "CRDO",
  },
  {
    value: "Creative",
    label: "Creative",
  },
  {
    value: "Crew",
    label: "Crew",
  },
  {
    value: "CRO",
    label: "CRO",
  },
  {
    value: "CSCO",
    label: "CSCO",
  },
  {
    value: "CSMO",
    label: "CSMO",
  },
  {
    value: "CSO",
    label: "CSO",
  },
  {
    value: "CTO",
    label: "CTO",
  },
  {
    value: "CTPO",
    label: "CTPO",
  },
  {
    value: "CTSO",
    label: "CTSO",
  },
  {
    value: "CUO",
    label: "CUO",
  },
  {
    value: "Curator",
    label: "Curator",
  },
  {
    value: "Custodian",
    label: "Custodian",
  },
  {
    value: "Custom",
    label: "Custom",
  },
  {
    value: "Cutter",
    label: "Cutter",
  },
  {
    value: "CVO",
    label: "CVO",
  },
  {
    value: "CWO",
    label: "CWO",
  },
  {
    value: "CXO",
    label: "CXO",
  },
  {
    value: "Cytopathologist",
    label: "Cytopathologist",
  },
  {
    value: "Cytotechnologist",
    label: "Cytotechnologist",
  },
  {
    value: "Dancer",
    label: "Dancer",
  },
  {
    value: "Dealer",
    label: "Dealer",
  },
  {
    value: "Dean",
    label: "Dean",
  },
  {
    value: "Deckhand",
    label: "Deckhand",
  },
  {
    value: "Decorator",
    label: "Decorator",
  },
  {
    value: "Demonstrator",
    label: "Demonstrator",
  },
  {
    value: "Dentist",
    label: "Dentist",
  },
  {
    value: "Deputy",
    label: "Deputy",
  },
  {
    value: "Dermatologist",
    label: "Dermatologist",
  },
  {
    value: "Dermatopathologist",
    label: "Dermatopathologist",
  },
  {
    value: "Designer",
    label: "Designer",
  },
  {
    value: "Detailer",
    label: "Detailer",
  },
  {
    value: "Detective",
    label: "Detective",
  },
  {
    value: "Developer",
    label: "Developer",
  },
  {
    value: "Dietician",
    label: "Dietician",
  },
  {
    value: "Dietitian",
    label: "Dietitian",
  },
  // {
  //   value: "Director",
  //   label: "Director",
  // },
  {
    value: "Director;Prin",
    label: "Director;Prin",
  },
  {
    value: "Dishwasher",
    label: "Dishwasher",
  },
  {
    value: "Dispatcher",
    label: "Dispatcher",
  },
  {
    value: "Distributor",
    label: "Distributor",
  },
  {
    value: "Doctor",
    label: "Doctor",
  },
  {
    value: "Dosimetrist",
    label: "Dosimetrist",
  },
  {
    value: "Drafter",
    label: "Drafter",
  },
  {
    value: "Draftsman",
    label: "Draftsman",
  },
  {
    value: "Draftsperson",
    label: "Draftsperson",
  },
  {
    value: "Driller",
    label: "Driller",
  },
  {
    value: "Driver",
    label: "Driver",
  },
  {
    value: "Ecologist",
    label: "Ecologist",
  },
  {
    value: "Economist",
    label: "Economist",
  },
  {
    value: "Editor",
    label: "Editor",
  },
  {
    value: "Editorial",
    label: "Editorial",
  },
  // {
  //   value: "Educator",
  //   label: "Educator",
  // },
  {
    value: "Electrician",
    label: "Electrician",
  },
  {
    value: "Electrotyper",
    label: "Electrotyper",
  },
  {
    value: "Embalmer",
    label: "Embalmer",
  },
  {
    value: "Embryologist",
    label: "Embryologist",
  },
  {
    value: "Employer",
    label: "Employer",
  },
  {
    value: "Endocrinologist",
    label: "Endocrinologist",
  },
  {
    value: "Endodontist",
    label: "Endodontist",
  },
  // {
  //   value: "Engineer",
  //   label: "Engineer",
  // },
  {
    value: "Engineering",
    label: "Engineering",
  },
  {
    value: "Engraver",
    label: "Engraver",
  },
  {
    value: "Enologist",
    label: "Enologist",
  },
  {
    value: "Entrepreneur",
    label: "Entrepreneur",
  },
  {
    value: "Enumerator",
    label: "Enumerator",
  },
  {
    value: "Epidemiologist",
    label: "Epidemiologist",
  },
  {
    value: "Esthetician",
    label: "Esthetician",
  },
  {
    value: "Estimator",
    label: "Estimator",
  },
  {
    value: "Etcher",
    label: "Etcher",
  },
  {
    value: "Evaluator",
    label: "Evaluator",
  },
  {
    value: "Evangelist",
    label: "Evangelist",
  },
  {
    value: "Event-Manager",
    label: "Event-Manager",
  },
  {
    value: "Examiner",
    label: "Examiner",
  },
  {
    value: "Executive",
    label: "Executive",
  },
  {
    value: "Executive-Director",
    label: "Executive-Director",
  },
  {
    value: "Expeditor",
    label: "Expeditor",
  },
  {
    value: "Fabricator",
    label: "Fabricator",
  },
  {
    value: "Facilitator",
    label: "Facilitator",
  },
  {
    value: "Faculty",
    label: "Faculty",
  },
  {
    value: "Farmer",
    label: "Farmer",
  },
  {
    value: "Farmworker",
    label: "Farmworker",
  },
  {
    value: "Fellow",
    label: "Fellow",
  },
  {
    value: "Fellowship",
    label: "Fellowship",
  },
  {
    value: "Filmmaker",
    label: "Filmmaker",
  },
  {
    value: "Finisher",
    label: "Finisher",
  },
  {
    value: "Firefighter",
    label: "Firefighter",
  },
  {
    value: "Fitter",
    label: "Fitter",
  },
  {
    value: "Florist",
    label: "Florist",
  },
  {
    value: "Foreman",
    label: "Foreman",
  },
  {
    value: "Forester",
    label: "Forester",
  },
  {
    value: "Founder",
    label: "Founder",
  },
  {
    value: "Framer",
    label: "Framer",
  },
  {
    value: "Franchisee",
    label: "Franchisee",
  },
  {
    value: "Fresher",
    label: "Fresher",
  },
  {
    value: "Fund",
    label: "Fund",
  },
  {
    value: "Funder",
    label: "Funder",
  },
  {
    value: "Fundraiser",
    label: "Fundraiser",
  },
  {
    value: "Gardener",
    label: "Gardener",
  },
  {
    value: "Gastroenterologist",
    label: "Gastroenterologist",
  },
  {
    value: "Gastroenterology",
    label: "Gastroenterology",
  },
  {
    value: "Gauger",
    label: "Gauger",
  },
  {
    value: "Gemologist",
    label: "Gemologist",
  },
  {
    value: "General",
    label: "General",
  },
  {
    value: "Generalist",
    label: "Generalist",
  },
  {
    value: "Geneticist",
    label: "Geneticist",
  },
  {
    value: "Geochemist",
    label: "Geochemist",
  },
  {
    value: "Geographer",
    label: "Geographer",
  },
  {
    value: "Geologist",
    label: "Geologist",
  },
  {
    value: "Geophysicist",
    label: "Geophysicist",
  },
  {
    value: "Geoscientist",
    label: "Geoscientist",
  },
  {
    value: "Geoscientists",
    label: "Geoscientists",
  },
  {
    value: "Glazier",
    label: "Glazier",
  },
  {
    value: "Government",
    label: "Government",
  },
  {
    value: "Grader",
    label: "Grader",
  },
  {
    value: "Graduate",
    label: "Graduate",
  },
  {
    value: "Greeter",
    label: "Greeter",
  },
  {
    value: "Groomer",
    label: "Groomer",
  },
  {
    value: "Groundskeeper",
    label: "Groundskeeper",
  },
  {
    value: "Guard",
    label: "Guard",
  },
  {
    value: "Guide",
    label: "Guide",
  },
  {
    value: "Guru",
    label: "Guru",
  },
  {
    value: "Gynecologist",
    label: "Gynecologist",
  },
  {
    value: "Gynecology",
    label: "Gynecology",
  },
  {
    value: "Hacker",
    label: "Hacker",
  },
  {
    value: "Hairdresser",
    label: "Hairdresser",
  },
  {
    value: "Hairstylist",
    label: "Hairstylist",
  },
  {
    value: "Handler",
    label: "Handler",
  },
  {
    value: "Handyman",
    label: "Handyman",
  },
  {
    value: "Head",
    label: "Head",
  },
  {
    value: "Hematologist",
    label: "Hematologist",
  },
  {
    value: "Hematologist-Oncologist",
    label: "Hematologist-Oncologist",
  },
  {
    value: "Hematology",
    label: "Hematology",
  },
  {
    value: "Hepatologist",
    label: "Hepatologist",
  },
  {
    value: "Historian",
    label: "Historian",
  },
  {
    value: "Homemaker",
    label: "Homemaker",
  },
  {
    value: "Horticulturist",
    label: "Horticulturist",
  },
  {
    value: "Hospital",
    label: "Hospital",
  },
  {
    value: "Hospitalist",
    label: "Hospitalist",
  },
  {
    value: "Host",
    label: "Host",
  },
  {
    value: "Hostess",
    label: "Hostess",
  },
  {
    value: "Housekeeper",
    label: "Housekeeper",
  },
  {
    value: "Hr-Manager",
    label: "Hr-Manager",
  },
  {
    value: "Hunter",
    label: "Hunter",
  },
  {
    value: "Hydrogeologist",
    label: "Hydrogeologist",
  },
  {
    value: "Hydrologist",
    label: "Hydrologist",
  },
  {
    value: "Hygienist",
    label: "Hygienist",
  },
  {
    value: "Illustrator",
    label: "Illustrator",
  },
  {
    value: "Immunologist",
    label: "Immunologist",
  },
  {
    value: "Implementor",
    label: "Implementor",
  },
  {
    value: "Indexer",
    label: "Indexer",
  },
  {
    value: "Infantry",
    label: "Infantry",
  },
  {
    value: "Innkeeper",
    label: "Innkeeper",
  },
  {
    value: "Innovator",
    label: "Innovator",
  },
  {
    value: "Inspector",
    label: "Inspector",
  },
  {
    value: "Installer",
    label: "Installer",
  },
  {
    value: "Instructor",
    label: "Instructor",
  },
  {
    value: "Intensivist",
    label: "Intensivist",
  },
  {
    value: "Intern",
    label: "Intern",
  },
  {
    value: "Internist",
    label: "Internist",
  },
  {
    value: "Internship",
    label: "Internship",
  },
  {
    value: "Interpreter",
    label: "Interpreter",
  },
  {
    value: "Interventionist",
    label: "Interventionist",
  },
  {
    value: "Interviewer",
    label: "Interviewer",
  },
  {
    value: "Inventor",
    label: "Inventor",
  },
  {
    value: "Investigator",
    label: "Investigator",
  },
  {
    value: "Investor",
    label: "Investor",
  },
  {
    value: "Janitor",
    label: "Janitor",
  },
  {
    value: "Java",
    label: "Java",
  },
  {
    value: "Jeweler",
    label: "Jeweler",
  },
  {
    value: "Jockey",
    label: "Jockey",
  },
  {
    value: "Journalist",
    label: "Journalist",
  },
  {
    value: "Journeyman",
    label: "Journeyman",
  },
  {
    value: "Judge",
    label: "Judge",
  },
  {
    value: "Keeper",
    label: "Keeper",
  },
  {
    value: "Key-Account-Manager",
    label: "Key-Account-Manager",
  },
  {
    value: "Keyboardist",
    label: "Keyboardist",
  },
  {
    value: "Keyholder",
    label: "Keyholder",
  },
  {
    value: "Kinesiologist",
    label: "Kinesiologist",
  },
  {
    value: "Kinesiotherapist",
    label: "Kinesiotherapist",
  },
  {
    value: "Laborer",
    label: "Laborer",
  },
  {
    value: "Landman",
    label: "Landman",
  },
  {
    value: "Landscaper",
    label: "Landscaper",
  },
  {
    value: "Lawyer",
    label: "Lawyer",
  },
  {
    value: "Lead",
    label: "Lead",
  },
  {
    value: "Lecturer",
    label: "Lecturer",
  },
  {
    value: "Legislator",
    label: "Legislator",
  },
  {
    value: "Lender",
    label: "Lender",
  },
  {
    value: "Librarian",
    label: "Librarian",
  },
  {
    value: "Lieutenant",
    label: "Lieutenant",
  },
  {
    value: "Lifeguard",
    label: "Lifeguard",
  },
  {
    value: "Lineman",
    label: "Lineman",
  },
  {
    value: "Linguist",
    label: "Linguist",
  },
  {
    value: "Loader",
    label: "Loader",
  },
  {
    value: "Lobbyist",
    label: "Lobbyist",
  },
  {
    value: "Locator",
    label: "Locator",
  },
  {
    value: "Locksmith",
    label: "Locksmith",
  },
  {
    value: "Logistician",
    label: "Logistician",
  },
  {
    value: "Logistics",
    label: "Logistics",
  },
  {
    value: "Lyricist",
    label: "Lyricist",
  },
  {
    value: "Machinist",
    label: "Machinist",
  },
  {
    value: "Magistrate",
    label: "Magistrate",
  },
  {
    value: "Maid",
    label: "Maid",
  },
  {
    value: "Mail",
    label: "Mail",
  },
  {
    value: "Maintainer",
    label: "Maintainer",
  },
  {
    value: "Major",
    label: "Major",
  },
  {
    value: "Management",
    label: "Management",
  },
  {
    value: "Manager",
    label: "Manager",
  },
  {
    value: "Manicurists",
    label: "Manicurists",
  },
  {
    value: "Marine",
    label: "Marine",
  },
  {
    value: "Marketer",
    label: "Marketer",
  },
  {
    value: "Mason",
    label: "Mason",
  },
  {
    value: "Master",
    label: "Master",
  },
  {
    value: "Mathematician",
    label: "Mathematician",
  },
  {
    value: "Mathematics",
    label: "Mathematics",
  },
  {
    value: "Mayor",
    label: "Mayor",
  },
  {
    value: "MBA",
    label: "MBA",
  },
  {
    value: "Mechanic",
    label: "Mechanic",
  },
  {
    value: "Medicaldirector",
    label: "Medicaldirector",
  },
  {
    value: "Mentor",
    label: "Mentor",
  },
  {
    value: "Merchandiser",
    label: "Merchandiser",
  },
  {
    value: "Merchant",
    label: "Merchant",
  },
  {
    value: "Messenger",
    label: "Messenger",
  },
  {
    value: "Metallurgist",
    label: "Metallurgist",
  },
  {
    value: "Meteorologist",
    label: "Meteorologist",
  },
  {
    value: "Microbiologist",
    label: "Microbiologist",
  },
  {
    value: "Midwife",
    label: "Midwife",
  },
  {
    value: "Military",
    label: "Military",
  },
  {
    value: "Millwright",
    label: "Millwright",
  },
  {
    value: "Mixer",
    label: "Mixer",
  },
  {
    value: "Model",
    label: "Model",
  },
  {
    value: "Modeler",
    label: "Modeler",
  },
  {
    value: "Moderator",
    label: "Moderator",
  },
  {
    value: "Molder",
    label: "Molder",
  },
  {
    value: "Monitor",
    label: "Monitor",
  },
  {
    value: "Mover",
    label: "Mover",
  },
  {
    value: "Movers",
    label: "Movers",
  },
  {
    value: "Musician",
    label: "Musician",
  },
  {
    value: "Nanny",
    label: "Nanny",
  },
  {
    value: "Naturalist",
    label: "Naturalist",
  },
  {
    value: "Navigator",
    label: "Navigator",
  },
  {
    value: "Navy",
    label: "Navy",
  },
  {
    value: "Negotiator",
    label: "Negotiator",
  },
  {
    value: "Neonatologist",
    label: "Neonatologist",
  },
  {
    value: "Nephrologist",
    label: "Nephrologist",
  },
  {
    value: "Neuro-oncologist",
    label: "Neuro-oncologist",
  },
  {
    value: "Neuro-Oncology",
    label: "Neuro-Oncology",
  },
  {
    value: "Neurologist",
    label: "Neurologist",
  },
  {
    value: "Neuropathologist",
    label: "Neuropathologist",
  },
  {
    value: "Neuropsychologist",
    label: "Neuropsychologist",
  },
  {
    value: "Neuroradiologist",
    label: "Neuroradiologist",
  },
  {
    value: "Neurosurgeon",
    label: "Neurosurgeon",
  },
  {
    value: "Neurotologist",
    label: "Neurotologist",
  },
  {
    value: "Nonprofit",
    label: "Nonprofit",
  },
  {
    value: "Nurse",
    label: "Nurse",
  },
  {
    value: "Nutritionist",
    label: "Nutritionist",
  },
  {
    value: "Obstetrician",
    label: "Obstetrician",
  },
  {
    value: "Obstetrics",
    label: "Obstetrics",
  },
  {
    value: "Office-Manager",
    label: "Office-Manager",
  },
  {
    value: "Officer",
    label: "Officer",
  },
  {
    value: "Ombudsman",
    label: "Ombudsman",
  },
  {
    value: "Oncologic",
    label: "Oncologic",
  },
  {
    value: "Oncologist",
    label: "Oncologist",
  },
  {
    value: "Oncology",
    label: "Oncology",
  },
  {
    value: "Operator",
    label: "Operator",
  },
  {
    value: "Ophthalmologist",
    label: "Ophthalmologist",
  },
  {
    value: "Optician",
    label: "Optician",
  },
  {
    value: "Opticians",
    label: "Opticians",
  },
  {
    value: "Optometrist",
    label: "Optometrist",
  },
  {
    value: "Organist",
    label: "Organist",
  },
  {
    value: "Organizer",
    label: "Organizer",
  },
  {
    value: "Originator",
    label: "Originator",
  },
  {
    value: "Orthodontist",
    label: "Orthodontist",
  },
  {
    value: "Orthopedics",
    label: "Orthopedics",
  },
  {
    value: "Orthotist",
    label: "Orthotist",
  },
  {
    value: "Otolaryngologist",
    label: "Otolaryngologist",
  },
  {
    value: "Otologist",
    label: "Otologist",
  },
  {
    value: "Owner",
    label: "Owner",
  },
  {
    value: "Packager",
    label: "Packager",
  },
  {
    value: "Packer",
    label: "Packer",
  },
  {
    value: "Painter",
    label: "Painter",
  },
  {
    value: "Paperhanger",
    label: "Paperhanger",
  },
  {
    value: "Paraeducator",
    label: "Paraeducator",
  },
  {
    value: "Paralegal",
    label: "Paralegal",
  },
  {
    value: "Paramedic",
    label: "Paramedic",
  },
  {
    value: "Paraprofessional",
    label: "Paraprofessional",
  },
  {
    value: "Partner",
    label: "Partner",
  },
  {
    value: "Pathologist",
    label: "Pathologist",
  },
  {
    value: "Patternmaker",
    label: "Patternmaker",
  },
  {
    value: "Pediatrician",
    label: "Pediatrician",
  },
  {
    value: "Pediatricians",
    label: "Pediatricians",
  },
  {
    value: "Pediatrics",
    label: "Pediatrics",
  },
  {
    value: "Pedodontist",
    label: "Pedodontist",
  },
  {
    value: "Perfusionist",
    label: "Perfusionist",
  },
  {
    value: "Pharmacist",
    label: "Pharmacist",
  },
  {
    value: "PHD",
    label: "PHD",
  },
  {
    value: "Phlebologist",
    label: "Phlebologist",
  },
  {
    value: "Phlebotomist",
    label: "Phlebotomist",
  },
  {
    value: "Photo",
    label: "Photo",
  },
  {
    value: "Photoengraver",
    label: "Photoengraver",
  },
  {
    value: "Photographer",
    label: "Photographer",
  },
  {
    value: "php",
    label: "php",
  },
  {
    value: "Physician",
    label: "Physician",
  },
  {
    value: "Physicist",
    label: "Physicist",
  },
  {
    value: "Physiotherapist",
    label: "Physiotherapist",
  },
  {
    value: "Picker",
    label: "Picker",
  },
  {
    value: "Pilot",
    label: "Pilot",
  },
  {
    value: "Pipefitter",
    label: "Pipefitter",
  },
  {
    value: "Pipelayer",
    label: "Pipelayer",
  },
  {
    value: "Planner",
    label: "Planner",
  },
  {
    value: "Platemaker",
    label: "Platemaker",
  },
  {
    value: "Player",
    label: "Player",
  },
  {
    value: "Plumber",
    label: "Plumber",
  },
  {
    value: "Podiatrist",
    label: "Podiatrist",
  },
  {
    value: "Poet",
    label: "Poet",
  },
  {
    value: "Police",
    label: "Police",
  },
  {
    value: "Porter",
    label: "Porter",
  },
  {
    value: "Postdoc",
    label: "Postdoc",
  },
  {
    value: "Postmasters",
    label: "Postmasters",
  },
  {
    value: "Potter",
    label: "Potter",
  },
  {
    value: "Pr-Manager",
    label: "Pr-Manager",
  },
  {
    value: "Practitioner",
    label: "Practitioner",
  },
  {
    value: "President",
    label: "President",
  },
  {
    value: "Principal",
    label: "Principal",
  },
  {
    value: "Printer",
    label: "Printer",
  },
  {
    value: "Private",
    label: "Private",
  },
  {
    value: "Processor",
    label: "Processor",
  },
  {
    value: "Proctologist",
    label: "Proctologist",
  },
  {
    value: "Procurement",
    label: "Procurement",
  },
  {
    value: "Producer",
    label: "Producer",
  },
  {
    value: "Professor",
    label: "Professor",
  },
  {
    value: "Programmer",
    label: "Programmer",
  },
  {
    value: "Programming",
    label: "Programming",
  },
  {
    value: "Promoter",
    label: "Promoter",
  },
  {
    value: "Proofreader",
    label: "Proofreader",
  },
  {
    value: "Proprietor",
    label: "Proprietor",
  },
  {
    value: "Prosthetist",
    label: "Prosthetist",
  },
  {
    value: "Prosthodontist",
    label: "Prosthodontist",
  },
  {
    value: "Psychiatric",
    label: "Psychiatric",
  },
  {
    value: "Psychiatrist",
    label: "Psychiatrist",
  },
  {
    value: "Psychiatry",
    label: "Psychiatry",
  },
  {
    value: "Psychologist",
    label: "Psychologist",
  },
  {
    value: "Psychotherapist",
    label: "Psychotherapist",
  },
  {
    value: "Publicist",
    label: "Publicist",
  },
  {
    value: "Publisher",
    label: "Publisher",
  },
  {
    value: "Pulmonologist",
    label: "Pulmonologist",
  },
  {
    value: "Pulmonology",
    label: "Pulmonology",
  },
  {
    value: "Purchaser",
    label: "Purchaser",
  },
  {
    value: "Purchasing",
    label: "Purchasing",
  },
  {
    value: "Radiographer",
    label: "Radiographer",
  },
  {
    value: "Radiologist",
    label: "Radiologist",
  },
  {
    value: "Ranger",
    label: "Ranger",
  },
  {
    value: "Realtor",
    label: "Realtor",
  },
  {
    value: "Receiver",
    label: "Receiver",
  },
  {
    value: "Receptionist",
    label: "Receptionist",
  },
  {
    value: "Records",
    label: "Records",
  },
  {
    value: "Recruiter",
    label: "Recruiter",
  },
  {
    value: "Regional",
    label: "Regional",
  },
  {
    value: "Registrar",
    label: "Registrar",
  },
  {
    value: "Repairer",
    label: "Repairer",
  },
  {
    value: "Repairman",
    label: "Repairman",
  },
  {
    value: "Reporter",
    label: "Reporter",
  },
  {
    value: "Reporters",
    label: "Reporters",
  },
  {
    value: "Representative",
    label: "Representative",
  },
  {
    value: "Researcher",
    label: "Researcher",
  },
  {
    value: "Retailer",
    label: "Retailer",
  },
  {
    value: "Retired",
    label: "Retired",
  },
  {
    value: "Retoucher",
    label: "Retoucher",
  },
  {
    value: "Reviewer",
    label: "Reviewer",
  },
  {
    value: "Rheumatologist",
    label: "Rheumatologist",
  },
  {
    value: "Rider",
    label: "Rider",
  },
  {
    value: "Rigger",
    label: "Rigger",
  },
  {
    value: "RN",
    label: "RN",
  },
  {
    value: "Roofer",
    label: "Roofer",
  },
  {
    value: "Roustabouts",
    label: "Roustabouts",
  },
  {
    value: "Runner",
    label: "Runner",
  },
  {
    value: "Sailor",
    label: "Sailor",
  },
  {
    value: "Sales",
    label: "Sales",
  },
  {
    value: "Salesman",
    label: "Salesman",
  },
  {
    value: "Salesperson",
    label: "Salesperson",
  },
  {
    value: "Scheduler",
    label: "Scheduler",
  },
  {
    value: "Science",
    label: "Science",
  },
  {
    value: "Scientist",
    label: "Scientist",
  },
  {
    value: "Screener",
    label: "Screener",
  },
  {
    value: "Sculptor",
    label: "Sculptor",
  },
  {
    value: "Secretary",
    label: "Secretary",
  },
  {
    value: "Selector",
    label: "Selector",
  },
  {
    value: "Senior",
    label: "Senior",
  },
  {
    value: "Server",
    label: "Server",
  },
  {
    value: "Shampooer",
    label: "Shampooer",
  },
  {
    value: "Sheriff",
    label: "Sheriff",
  },
  {
    value: "Shipper",
    label: "Shipper",
  },
  {
    value: "Shopper",
    label: "Shopper",
  },
  {
    value: "Silversmith",
    label: "Silversmith",
  },
  {
    value: "Singer",
    label: "Singer",
  },
  {
    value: "Sitter",
    label: "Sitter",
  },
  {
    value: "Sociologist",
    label: "Sociologist",
  },
  {
    value: "Solderer",
    label: "Solderer",
  },
  {
    value: "Sonographer",
    label: "Sonographer",
  },
  {
    value: "Sorter",
    label: "Sorter",
  },
  {
    value: "Sourcer",
    label: "Sourcer",
  },
  {
    value: "Specialist",
    label: "Specialist",
  },
  {
    value: "Spotters",
    label: "Spotters",
  },
  {
    value: "Startup-Manager",
    label: "Startup-Manager",
  },
  {
    value: "Statistician",
    label: "Statistician",
  },
  {
    value: "Stevedore",
    label: "Stevedore",
  },
  {
    value: "Steward",
    label: "Steward",
  },
  {
    value: "Stockbroker",
    label: "Stockbroker",
  },
  {
    value: "Stocker",
    label: "Stocker",
  },
  {
    value: "Stonemason",
    label: "Stonemason",
  },
  {
    value: "Storyteller",
    label: "Storyteller",
  },
  {
    value: "Strategist",
    label: "Strategist",
  },
  {
    value: "Stripper",
    label: "Stripper",
  },
  {
    value: "Student",
    label: "Student",
  },
  {
    value: "Students",
    label: "Students",
  },
  {
    value: "Stylist",
    label: "Stylist",
  },
  {
    value: "Subcontractor",
    label: "Subcontractor",
  },
  {
    value: "Superintendent",
    label: "Superintendent",
  },
  {
    value: "Supervisor",
    label: "Supervisor",
  },
  {
    value: "Surgeon",
    label: "Surgeon",
  },
  {
    value: "Surveyor",
    label: "Surveyor",
  },
  {
    value: "Tailor",
    label: "Tailor",
  },
  {
    value: "Talent",
    label: "Talent",
  },
  {
    value: "Taper",
    label: "Taper",
  },
  {
    value: "Teacher",
    label: "Teacher",
  },
  {
    value: "Technician",
    label: "Technician",
  },
  {
    value: "Technologist",
    label: "Technologist",
  },
  {
    value: "Telemarketer",
    label: "Telemarketer",
  },
  {
    value: "Teller",
    label: "Teller",
  },
  {
    value: "Tester",
    label: "Tester",
  },
  {
    value: "Testing",
    label: "Testing",
  },
  {
    value: "Therapist",
    label: "Therapist",
  },
  {
    value: "Toolmaker",
    label: "Toolmaker",
  },
  {
    value: "Toxicologist",
    label: "Toxicologist",
  },
  {
    value: "Trader",
    label: "Trader",
  },
  {
    value: "Traffic-Manager",
    label: "Traffic-Manager",
  },
  {
    value: "Trainee",
    label: "Trainee",
  },
  {
    value: "Trainer",
    label: "Trainer",
  },
  {
    value: "Transcriptionist",
    label: "Transcriptionist",
  },
  {
    value: "Translator",
    label: "Translator",
  },
  {
    value: "Transporter",
    label: "Transporter",
  },
  {
    value: "Traveler",
    label: "Traveler",
  },
  {
    value: "Treasurer",
    label: "Treasurer",
  },
  {
    value: "Tutor",
    label: "Tutor",
  },
  {
    value: "Typesetter",
    label: "Typesetter",
  },
  {
    value: "Typist",
    label: "Typist",
  },
  {
    value: "Ultrasonographer",
    label: "Ultrasonographer",
  },
  {
    value: "Umpire",
    label: "Umpire",
  },
  {
    value: "Undergraduate",
    label: "Undergraduate",
  },
  {
    value: "Underwriter",
    label: "Underwriter",
  },
  {
    value: "Unemployed",
    label: "Unemployed",
  },
  {
    value: "Unspecified",
    label: "Unspecified",
  },
  {
    value: "Upholsterer",
    label: "Upholsterer",
  },
  {
    value: "Urogynecologist",
    label: "Urogynecologist",
  },
  {
    value: "Urologist",
    label: "Urologist",
  },
  {
    value: "Urology",
    label: "Urology",
  },
  {
    value: "Usher",
    label: "Usher",
  },
  {
    value: "Valet",
    label: "Valet",
  },
  {
    value: "Vendor",
    label: "Vendor",
  },
  {
    value: "Verifier",
    label: "Verifier",
  },
  {
    value: "Veterinarian",
    label: "Veterinarian",
  },
  {
    value: "Videographer",
    label: "Videographer",
  },
  {
    value: "Visitor",
    label: "Visitor",
  },
  {
    value: "Visual",
    label: "Visual",
  },
  {
    value: "Vocalist",
    label: "Vocalist",
  },
  {
    value: "Volunteer",
    label: "Volunteer",
  },
  {
    value: "Vp",
    label: "Vp",
  },
  {
    value: "Waiter",
    label: "Waiter",
  },
  {
    value: "Waitress",
    label: "Waitress",
  },
  {
    value: "Washer",
    label: "Washer",
  },
  {
    value: "Webmaster",
    label: "Webmaster",
  },
  {
    value: "Website-Manager",
    label: "Website-Manager",
  },
  {
    value: "Welder",
    label: "Welder",
  },
  {
    value: "Welder-Fitter",
    label: "Welder-Fitter",
  },
  {
    value: "Wholesaler",
    label: "Wholesaler",
  },
  {
    value: "Woodworker",
    label: "Woodworker",
  },
  {
    value: "Wrangler",
    label: "Wrangler",
  },
  {
    value: "Writer",
    label: "Writer",
  },
  {
    value: "Zoologists",
    label: "Zoologists",
  },
  {
    value: ".net",
    label: ".net",
  },
  {
    value: "Vice President",
    label: "Vice President",
  },
];
//export const SearchedFiltersData 
