import { useState, useContext, useEffect, useRef } from 'react';
import '../assests/css/layouts/header.css';
import { useNavigate } from 'react-router-dom';
import { ApiName } from '../../customer/common-files/ApiNames.js';
import { AxiosPostBearer, fetchProfilePicture, PostWithTokenNoCache } from '../../customer/common-files/ApiCalls.js';
import DashboardContext from "../common-files/ContextDashboard";
import UseTabStore from "../common-files/useGlobalState.js";
import { useMicrosoftLoginLogic } from '../common-files/SocialMediaAuth.js';
import {
	FaRocket
} from 'react-icons/fa';
import "../assests/css/filter/leftnavbarv2.css";

const Header = () => {

	const [isSecondDropDownOpen, setIsSecondDropDownOpen] = useState(false);
	const dropdownRef = useRef(null);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);

	const { dataDC } = useContext(DashboardContext);
	const [user] = useState(JSON.parse(localStorage.getItem('user')));
	const [contactData, setContactData] = useState({
		firstName: null,
		lastName: null,
		email: null,
		token: null,
	});
	const [profilePicture, setProfilePicture] = useState(null);
	const { Microsoftlogout } = useMicrosoftLoginLogic();
	const {
		profileUpdated,
		setSettingsActiveTab,
		setProfileUpdated
	} = UseTabStore();
	const navigate = useNavigate();

	// Fetch user data
	useEffect(() => {
		const user = JSON.parse(localStorage.getItem("user"));
		const postData = () => {
			const requestBody = { "method": "GET" };
			AxiosPostBearer(ApiName.userData, requestBody, user.token)
				.then(function (response) {
					const jsonData = JSON.parse(response.data.data);
					const total = { ...jsonData, ...user };
					const merge = { ...contactData, ...total };
					setContactData(merge);
					localStorage.setItem('user', JSON.stringify(merge));
				}).catch(function () { });
		};
		postData()
	}, []);

	// Get profile picture
	async function getUserProfilePicture(data) {
		const url = ApiName.getProfilePicture;
		const imageUrlTemp = await fetchProfilePicture(url, data);
		if (imageUrlTemp) {
			setProfilePicture(imageUrlTemp);
			setProfileUpdated("");
		} else {
			setProfilePicture("");
		}
	}

	useEffect(() => {
		getUserProfilePicture({});
	}, [profileUpdated])

	// Navigation functions
	const goToReachMax = () => {
		navigate('/refer-earn');
	}

	const account = () => {
		setSettingsActiveTab("profile");
		navigate('/settings', {
			state: { email: user.email, token: user.token }
		});
	}

	const openExtensionPage = () => {
		const extensionId = "nincdhaihcinfenbnmfacjkclcejomfi";
		window.open(`https://chromewebstore.google.com/detail/${extensionId}`, "_blank");
	};

	const viewPlans = () => {
		window.open('https://www.reachstream.com/pricing/', '_blank');
	}

	// Sign out function
	const signOutSubmit = async () => {
		// Clear data and navigate
		sessionStorage.clear();
		UseTabStore.getState().clearStorage();

		if (localStorage.getItem('signin-type') === 'microsoft') {
			localStorage.removeItem('signin-type');
			Microsoftlogout();
		}

		PostWithTokenNoCache(ApiName.deviceLogout, {}).then((res) => {
			if (res?.status === 200) {
				localStorage.clear();
				navigate('/');
			}
		});
	}

	// Click outside handler for dropdowns
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
				setIsSecondDropDownOpen(false);
			}
		};
		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	// Get display name
	const getDisplayName = () => {
		if (user?.firstName && user.firstName !== null) {
			return `${user.firstName} ${user.lastName || ''}`.trim();
		}
		if (contactData?.firstName && contactData.firstName !== null) {
			return `${contactData.firstName} ${contactData.lastName || ''}`.trim();
		}
		return "User";
	};

	// Get truncated email
	const getTruncatedEmail = () => {
		const email = contactData.email || user?.email || "";
		return email.length > 16 ? email.slice(0, 16) + "..." : email;
	};

	return (
		<>
			<div className="main container-fluid">
				{/* Header */}
				<div className="main-header d-flex flex-row justify-content-end align-items-center mb-3">
					<div className="d-flex flex-row justify-content-end align-items-center">
						<div className="d-flex align-items-center">
							<button className="animated-btn btn btn-sm" onClick={viewPlans}>
								Get Unlimited Leads
							</button>

							<div className={`unique-avatar-dropdown-wrapper ${isSecondDropDownOpen ? "show" : ""}`}
								ref={dropdownRef}>
								<div onClick={() => setIsSecondDropDownOpen(!isSecondDropDownOpen)}
									style={{ cursor: 'pointer' }}>
									<img src="images/wallet.png" className="img-fluid" alt="avatar" width="18" />
								</div>

								{isSecondDropDownOpen && (
									<div className="unique-avatar-dropdown-menu">
										<p className="v2-account-credits">Account credits</p>
										{/* Wallet content would go here */}
									</div>
								)}
							</div>
						</div>

						<div className="ml-3 mr-3">
							<div className="profile-dropdown-container position-relative">
								<div onClick={() => setIsDropdownOpen(!isDropdownOpen)}
									style={{ cursor: 'pointer' }}>
									<img
										src={profilePicture || "images/avetar.png"}
										className="img-fluid rounded-circle"
										alt="avatar"
										width="30"
										style={{ opacity: profilePicture ? null : "0.5" }}
									/>
								</div>

								{isDropdownOpen && (
									<div className="profile-menu shadow">
										<div className="d-flex flex-row">
											<img
												src={profilePicture || "images/upload-pp-v2.png"}
												className="upload-pp"
												alt=""
												style={{ opacity: profilePicture ? null : "0.5", width:"45px", margin:"5px 0 0 6px", height:"45px" }}
											/>
											<div className="profile-header d-flex flex-column">
												<p className="user-name">{getDisplayName()}</p>
												<small>{getTruncatedEmail()}</small>
											</div>
										</div>
										<hr className="dropdown-divider" />
										<ul className="list-unstyled mb-0">
											<li onClick={account}>
												<img src="images/new-profile.png" width="16" />
												My account
											</li>
											<li onClick={viewPlans}>
												<img src="images/new-view-plans.png" width="16" />
												View Plans
											</li>
											<li>
												<img src="images/new-feedback.png" width="16" />
												Feedback
											</li>
											<li onClick={goToReachMax}>
												<img src="images/r&f.png" width="16" />
												Refer & Earn
											</li>
											<li className="highlight" onClick={openExtensionPage}>
												<FaRocket /> Install RS extension
											</li>
										</ul>
										<hr className="dropdown-divider" />
										<button type='button' onClick={signOutSubmit} className="sign-out">
											<img src="images/sign-out.png" /> Sign Out
										</button>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		</>
	);
}

export default Header;