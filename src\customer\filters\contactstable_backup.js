import React, { useState, useContext, useEffect, useRef } from "react";
import '../assests/css/filter/contactstable.css';
import PaginationRender from '../pagination/PaginationRender.js';
import DashboardContext from '../common-files/ContextDashboard.js';
import { postWithToken, PostWithTokenNoCache } from '../common-files/ApiCalls.js';
import { ApiName } from '../common-files/ApiNames';
import ContactModal from './ContactModal';
import RS_Animation from '../layouts/RS_Animation.js';
import { getSessionItem } from '../common-files/LocalStorage';
import Search_Us from '../layouts/Search_us';
import UseTabStore from "../common-files/useGlobalState";
import loadingGif from '../assests/waiting.gif';
import { useNavigate } from "react-router-dom";
import Alert from "../common-files/alert.js";
const ContactstableBackup = ({ paginationDataCount }) => {

    const navigate = useNavigate();
    const alertRef = useRef();
    const closeAlertRef = useRef();
    const [loading, setLoading] = useState(true);
    const { dataDC, setDataDC } = useContext(DashboardContext);
    const [data, setData] = useState([]);
    const [showContactModal, setShowContactModal] = useState(true);
    const [isFilterApplied, setIsFilterApplied] = useState(false);
    const {
        unSelectedRows,
        selectedRows,
        advancedFilter,
        advancedFilterSearchPattern,
        foundCounts,
        currentSelectedPage,
        viewSelected,
        defaultAlert,
        defaultErrorMsg,
        contactModelId,
        selectedContact,
        sortingBy,
        isLowViewCredits,
        viewModal,
        pageSize,
        currentPage,
        selectedTab,
        companyAddressCountry,
        companyAddressState,
        companyAddressCity,
        companyZipCode,
        industryData,
        sicCode,
        companyTechKeyWordsList,
        companyName,
        contactJobTitle1,
        contactJobTitleLevel1,
        resetPage,
        companyURL,
        companyType,
        withDownloaded,
        applyFilters,
        setSelectedTab,
        setSearchPattern,
        setSelectedContactJobDeptName1,
        setSelectedContactJobFunctionName1,
        setIsLowViewCredits,
        setDefaultAlert,
        setDefaultErrorMsg,
        setViewModal,
        setSelectedContact,
        setButtonType,
        setIsFiltered,
        setFoundCounts,
        setSelectedRows,
        setOriginalFoundCounts
    } = UseTabStore();

    let contact_job_title_1 = null;
    let company_employee_size = null;
    let company_annual_revenue_amount = null;
    let dept = [];
    let func = [];
    let count = 1;
    let token = null;
    if ("token" in dataDC) token = dataDC.token;
    let user = getSessionItem("user");
    user = user ? JSON.parse(user) : null;
    if (user && "token" in user) token = user.token;

    useEffect(() => {
        // Add event listeners when the component mounts
        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('keydown', handleEscKeyPress);

        // Remove event listeners when the component unmounts
        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
            document.removeEventListener('keydown', handleEscKeyPress);
        };
    }, [viewModal, isLowViewCredits]);

    const handleOutsideClick = (e) => {
        // Check if the click is outside the modal
        if (viewModal && isLowViewCredits && !e.target.closest('.modal')) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        // Check if the pressed key is ESC
        if (viewModal && isLowViewCredits && e.key === 'Escape') {
            close();
        }
    };
    useEffect(() => {
        setSelectedTab("contact");
        const loaderElement = document.querySelector('#table-loader');
        const loaderContactTable = document.querySelector('#cust-contact-table');

        if (loaderElement) {
            loaderElement.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
        }
        if (loaderContactTable) {
            loaderContactTable.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
        }

        if (contactJobTitle1 && contactJobTitle1.length > 0) {
            contact_job_title_1 = contactJobTitle1.map((v) => v.value);
        } else {
            // Handle the case where jobTitle is not an array, e.g., set contact_job_title_1 to an empty array
            contact_job_title_1 = [];
        }

        if ("DeptFunc" in dataDC && dataDC.DeptFunc !== undefined) {
            let obj = dataDC.DeptFunc;
            for (const key in obj) {
                dept.push(key);
                for (const nestedKey in obj[key]) {
                    func.push(nestedKey);
                }
            }
            setSelectedContactJobDeptName1(dept);
            setSelectedContactJobFunctionName1(func);
        }

        if ("company_employee_size" in dataDC && dataDC.company_employee_size !== undefined) {
            company_employee_size = dataDC.company_employee_size.map((v) => v);
        }
        if ("company_annual_revenue_amount" in dataDC && dataDC.company_annual_revenue_amount !== undefined) {
            company_annual_revenue_amount = dataDC.company_annual_revenue_amount.map((v) => v);
        }

        const searchPatterns = {
            "company_address_country": !resetPage ? { ...companyAddressCountry } : {},
            "company_address_state": !resetPage ? { ...companyAddressState } : {},
            "company_address_city": !resetPage ? { ...companyAddressCity } : {},
            "company_address_zipcode": !resetPage ? { ...companyZipCode } : {},
            "contact_job_title_1": !resetPage ? { ...contact_job_title_1 } : {},
            "contact_job_title_level_1": !resetPage ? { ...contactJobTitleLevel1 } : {},
            "contact_job_dept_name_1": !resetPage ? { ...dept } : {},
            "contact_job_function_name_1": !resetPage ? { ...func } : {},
            "company_industries": !resetPage ? { ...industryData } : {},
            "company_sic_code": !resetPage ? { ...sicCode } : {},
            "company_employee_size": !resetPage ? { ...company_employee_size } : {},
            "company_annual_revenue_amount": !resetPage ? { ...company_annual_revenue_amount } : {},
            "company_tech_keywords_list": !resetPage ? { ...companyTechKeyWordsList } : {},
            "company_company_name": !resetPage ? { ...companyName } : {},
            "company_website": !resetPage ? { ...companyURL } : {},
            "company_type": !resetPage ? { ...companyType } : {}
        };
        // const url = ApiName.searchFilters;
        const url = ApiName.esContactFilter;
        let dataPost = {};
        if (!advancedFilter) {
            setSearchPattern(searchPatterns);
            searchPatterns["searchBy"] = "contact";
            searchPatterns["withDownloaded"] = withDownloaded;
            searchPatterns["sortBy"] = sortingBy;
            dataPost = {
                "page": viewSelected === true ? currentSelectedPage ? currentSelectedPage : 1 : currentPage ? currentPage : 1,
                "pageSize": dataDC.membership === "trail" ? 10 : 25,
                "searchPattern": searchPatterns
            }
        } else {
            setSearchPattern(advancedFilterSearchPattern.searchPattern);
            dataPost = {
                "page": advancedFilterSearchPattern.page,
                "pageSize": advancedFilterSearchPattern.pageSize,
                "searchPattern": advancedFilterSearchPattern.searchPattern,
                "maxContactPerCompany": advancedFilterSearchPattern.maxContactPerCompany
            }
        }

        const fetchAdvancedFilterCount = async () => {
            try {

                const updatedCompanyTechKeyWordsList = Object.keys(companyTechKeyWordsList).reduce((acc, key) => {
                    acc[key] = companyTechKeyWordsList[key].toLowerCase();
                    return acc;
                }, {})

                dataPost["page"] = 1;
                dataPost.searchPattern["countById"] = true;
                dataPost.searchPattern["company_tech_keywords_list"] = updatedCompanyTechKeyWordsList;

                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        const dataObj = JSON.parse(res.data.data);
                        if (dataObj.records.length > 0) {
                            // let resData = dataObj.records;
                            setData([]);
                            let dataObj = JSON.parse(res.data.data);
                            dataObj = dataObj.records.slice().sort((a, b) => a.id - b.id);
                            let id = dataObj.map(row => row.id);
                            setSelectedRows(id);
                            setFoundCounts(dataObj.length);
                            fetchAdvancedFilterData(id);
                        } else {
                            setFoundCounts(0);
                            setIsFilterApplied(false);
                            setLoading(false);
                            setData([]);
                            setSelectedRows([]);
                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }
                        }
                    }
                }
            } catch (error) {
                if (count == 1) {
                    const loaderElement = document.querySelector('#total_contacts');

                    if (loaderElement) {
                        loaderElement.textContent = 'Retrying...'; // Use textContent to set the text
                    }
                    fetchAdvancedFilterCount();
                    count++;
                } else {

                    const loaderElement = document.querySelector('#table-loader');
                    const loaderContactTable = document.querySelector('#cust-contact-table');

                    if (loaderElement) {
                        loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                    }
                    if (loaderContactTable) {
                        loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                    }
                    setButtonType("error");
                    setDefaultErrorMsg("Sorry.! Please try again");
                    setDefaultAlert(true);
                    setFoundCounts(-1);
                    setLoading(false);
                }
            }
        };

        const fetchAdvancedFilterData = async (filteredIds) => {
            try {
                setLoading(true);
                dataPost["pageSize"] = dataDC.membership === "trail" ? 10 : 25;
                dataPost["page"] = 1;
                delete dataPost.searchPattern["countById"];

                const itemPerPage = dataDC.membership === "trail" ? 10 : 25;
                // Calculate start index based on current page
                const startIndex = (currentPage - 1) * itemPerPage;
                // Use slice to extract data for the current page
                const id = filteredIds.slice(startIndex, startIndex + itemPerPage);

                dataPost.searchPattern["advanceFilterIds"] = [...id];
                // Do not change to postWithToken, selected data checkboxs logic get's wrong
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        let dataObj = JSON.parse(res.data.data);
                        // let dataObj = dataObj1.records.slice().sort((a, b) => a.id - b.id);
                        if (dataObj.records.length < 1) {
                            setFoundCounts(-1);
                            setIsFilterApplied(false);
                        } else {
                            let currentSelectedRows = selectedRows.length > 0 ? selectedRows : filteredIds;
                            let filteredNewIds = dataObj.records.filter(row => !currentSelectedRows.includes(row.id)).map(row => row.id);
                            // Filter newSelectedRows to exclude unselected IDs
                            let finalSelectedRows = filteredNewIds.filter(id => !unSelectedRows.includes(id));
                            let newSelectedRows = [...currentSelectedRows, ...finalSelectedRows].sort((a, b) => a - b);

                            setSelectedRows(newSelectedRows);
                            setIsFilterApplied(true);
                            setData(dataObj.records);
                            setLoading(false);
                        }
                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                    }
                }
            } catch (error) {
                setLoading(false);
                setData([]);

                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                if (loaderElement) {
                    loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                }
                setButtonType("error");
                setDefaultErrorMsg("Sorry.! Please try again");
                setDefaultAlert(true);
                setFoundCounts(-1);
                setLoading(false);
            }
        };

        const fetchAdvancedFilterESData = async () => {
            try {
                let url = ApiName.esAdvancedSearchFilter;
                setLoading(true);
                // Do not change to postWithToken, selected data checkboxs logic get's wrong
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {

                        let dataObj = JSON.parse(res.data.data);
                        var filteredIds = dataObj.records.map(row => row.id);
                        if (selectedRows.length < 1) {
                            setSelectedRows(filteredIds);
                        }
                        let itemsPerPage = dataDC.membership === "trail" ? 10 : 25;
                        let startIndex = (currentPage - 1) * itemsPerPage;
                        let endIndex = startIndex + itemsPerPage;

                        let slicedData = dataObj.records.slice(startIndex, endIndex);
                        //  debugger;
                        if (dataObj.length < 1) {
                            setFoundCounts(0);
                            setIsFilterApplied(false);
                            setLoading(false);
                            setData([]);
                            setSelectedRows([]);
                        } else {
                            setIsFilterApplied(true);
                            setData(slicedData);
                            setLoading(false);
                        }
                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                    }
                }
            } catch (error) {
                setLoading(false);
                setData([]);

                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                if (loaderElement) {
                    loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                }
                setButtonType("error");
                setDefaultErrorMsg("Sorry.! Please try again");
                setDefaultAlert(true);
                setFoundCounts(-1);
                setLoading(false);
            }
        };

        const fetchData = async () => {
            try {
                setLoading(true);
                // Do not change to postWithToken, selected data checkboxs logic get's wrong
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        const dataObj = JSON.parse(res.data.data);
                        if (dataObj.records.length < 1) {
                            setFoundCounts(-1);
                            setIsFilterApplied(false);
                        } else {
                            setIsFilterApplied(true);
                            setData(dataObj.records);
                            setLoading(false);
                        }
                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                    }
                }
            } catch (error) {
                setLoading(false);
                setData([]);

                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                if (loaderElement) {
                    loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                }
                setButtonType("error");
                setDefaultErrorMsg("Sorry.! Please try again");
                setDefaultAlert(true);
                setFoundCounts(-1);
                setLoading(false);
            }
        };

        const fetchESData = async () => {
            try {
                setLoading(true);
                // Do not change to postWithToken, selected data checkboxs logic get's wrong
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        const dataObj = JSON.parse(res.data.data);


                        if (dataObj.totalCount > 0) {
                            setFoundCounts(dataObj.totalCount);
                            setOriginalFoundCounts(dataObj.totalCount);
                            setIsFilterApplied(true);
                            setData(dataObj.records);
                            setLoading(false);

                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }

                        } else {
                            setFoundCounts(-1);
                            setIsFilterApplied(true);
                            setLoading(false);
                            setData([]);
                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }
                        }
                    }
                }
            } catch (error) {
                if (error.response.status == 403) {
                    setButtonType("error");
                    setDefaultErrorMsg("Sorry.! Please contact admin");
                } else {
                    setButtonType("error");
                    setDefaultErrorMsg("Sorry.! Please try again");
                }
                setFoundCounts(-1);
                setIsFilterApplied(true);
                setLoading(false);
                setData([]);

                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                if (loaderElement) {
                    loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                }
                setDefaultAlert(true);
                setFoundCounts(-1);
                setLoading(false);
            }
        };

        const fetchTotalCount = async () => {
            let totalCount = {
                "searchPattern": dataPost.searchPattern
            };

            const updatedCompanyTechKeyWordsList = Object.keys(companyTechKeyWordsList).reduce((acc, key) => {
                acc[key] = companyTechKeyWordsList[key].toLowerCase();
                return acc;
            }, {})
            dataPost.searchPattern["company_tech_keywords_list"] = updatedCompanyTechKeyWordsList;

            await PostWithTokenNoCache(ApiName.totalContactCounts, totalCount)
                .then(function (response) {
                    if (response.data.status === 200) {
                        const dataObj = JSON.parse(response.data.data);
                        if (dataObj.counts > 0) {
                            setFoundCounts(dataObj.counts);
                            setOriginalFoundCounts(dataObj.counts);
                            fetchData();
                        } else {
                            setFoundCounts(0);
                            setIsFilterApplied(false);
                            setLoading(false);
                            setData([]);
                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }
                        }
                    }
                }).catch(function (error) {
                    if (count == 1) {
                        const loaderElement = document.querySelector('#total_contacts');

                        if (loaderElement) {
                            loaderElement.textContent = 'Retrying...'; // Use textContent to set the text
                        }
                        fetchTotalCount();
                        count++;
                    } else {

                        const loaderElement = document.querySelector('#table-loader');
                        const loaderContactTable = document.querySelector('#cust-contact-table');

                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                        setButtonType("error");
                        setDefaultErrorMsg("Sorry.! Please try again");
                        setDefaultAlert(true);
                        setFoundCounts(-1);
                        setLoading(false);
                    }
                });
        }

        const keysToCheck = [
            "company_address_country",
            "company_address_state",
            "company_address_city",
            "company_address_zipcode",
            "contact_job_title_1",
            "contact_job_title_level_1",
            "contact_job_dept_name_1",
            "contact_job_function_name_1",
            "company_industries",
            "company_sic_code",
            "company_employee_size",
            "company_annual_revenue_amount",
            "company_tech_keywords_list",
            "company_company_name",
            "company_website",
            "company_type"
        ];

        const hasValues = keysToCheck.some(key => Object.keys(dataPost.searchPattern[key]).length > 0);

        if (hasValues && !advancedFilter) {
            //fetchTotalCount();
            fetchESData();
        } else if (hasValues && advancedFilter) {
            if (selectedRows.length <= 0) {
                //fetchAdvancedFilterCount();
                fetchAdvancedFilterESData();
            } else {
                setData([]);
                // let newSelectedRows = [...selectedRows, ...unSelectedRows].sort((a, b) => a - b);
                fetchAdvancedFilterESData();
            }
        } else {
            setIsFiltered(false);
            setFoundCounts(-1);
            setIsFilterApplied(false);
            setLoading(false);
            setData([]);
            if (loaderElement) {
                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
            }
            if (loaderContactTable) {
                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
            }
        }
    }, [
        resetPage,
        applyFilters,
        currentPage,
        currentSelectedPage,
        advancedFilter
    ]);

    useEffect(() => {
        fetchSingleContact(contactModelId)
    }, [contactModelId]);

    const urlSingleFetch = ApiName.fetchSingleRecord;
    async function fetchSingleContactRecord(data, total_balance_contact_view, total_balance_credit) {
        const addActionKey = {
            ...data,
            action: "view",
            searchBy: selectedTab,

        }
        const res = await postWithToken(urlSingleFetch, addActionKey);
        if (res && "status" in res) {
            if (res.data.status === 200) {
                let dataObj = JSON.parse(res.data.data);
                if (dataObj[0] === undefined || dataObj[0] === null) return;
                if (showContactModal) {
                    setIsLowViewCredits(false);
                    setSelectedContact(dataObj[0]);
                    if (total_balance_contact_view != null) {
                        let totalBalanceContactView = total_balance_contact_view;
                        if ("membership" in dataDC && dataDC.membership === "prime") {
                            totalBalanceContactView += 1;
                        } else {
                            totalBalanceContactView -= 1;
                        }
                        const requestBody = {
                            total_balance_contact_view: totalBalanceContactView,
                            total_balance_credit: total_balance_credit
                        };
                    }
                }
            }
        }
    }

    const fetchSingleContact = async (dataId) => {
        if (dataId) {
            const data = { "id": dataId };
            if ("membership" in dataDC && dataDC.membership === "prime") {
                // fetchSingleContactRecord(data,null,null);
                try {
                    const activeCreaditsFetchRes = await PostWithTokenNoCache(ApiName.activeCredit, {});
                    if (activeCreaditsFetchRes && "status" in activeCreaditsFetchRes) {
                        if (activeCreaditsFetchRes.data.status === 200) {
                            let dataObj = JSON.parse(activeCreaditsFetchRes.data.data);
                            let total_balance_contact_view = "total_balance_contact_view" in dataObj ? dataObj.total_balance_contact_view : null;
                            if (total_balance_contact_view == "Unlimited") total_balance_contact_view = 0;
                            let total_balance_credit = "total_balance_credit" in dataObj ? dataObj.total_balance_credit : null;
                            if (total_balance_contact_view !== undefined && total_balance_contact_view !== null && Number(total_balance_contact_view) != NaN) {
                                fetchSingleContactRecord(data, Number(total_balance_contact_view), Number(total_balance_credit));
                            } else {
                                setSelectedContact(null);
                                setIsLowViewCredits(true);
                            }
                        }
                    } else {
                        setSelectedContact(null);
                        setIsLowViewCredits(true);
                    }
                } catch (error) {
                    setButtonType("error");
                    setDefaultErrorMsg(error.response.data.message);
                    setDefaultAlert(true);
                }
            } else {
                try {
                    const activeCreaditsFetchRes = await PostWithTokenNoCache(ApiName.activeCredit, {});
                    if (activeCreaditsFetchRes && "status" in activeCreaditsFetchRes) {
                        if (activeCreaditsFetchRes.data.status === 200) {
                            let dataObj = JSON.parse(activeCreaditsFetchRes.data.data);
                            let total_balance_contact_view = "total_balance_contact_view" in dataObj ? dataObj.total_balance_contact_view : null;
                            let total_balance_credit = "total_balance_credit" in dataObj ? dataObj.total_balance_credit : null;
                            if (total_balance_contact_view && Number(total_balance_contact_view) != NaN && Number(total_balance_contact_view) > 0 || total_balance_contact_view == "Unlimited") {
                                fetchSingleContactRecord(data, Number(total_balance_contact_view), Number(total_balance_credit));
                            } else {
                                setSelectedContact(null);
                                setIsLowViewCredits(true);
                            }
                        }
                    } else {
                        setSelectedContact(null);
                        setIsLowViewCredits(true);
                    }
                } catch (error) {
                    setButtonType("error");
                    setDefaultErrorMsg(error.response.data.message);
                    setDefaultAlert(true);
                }
            }
        }
    };

    useEffect(() => {
        if (viewModal && isLowViewCredits) {
            setDefaultAlert("error");
            setDefaultErrorMsg("You have exceeded1 your view credits");
            alertRef.current.click();
        }
    }, [viewModal, isLowViewCredits])

    const close = () => {
        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");
        setViewModal(false);
        closeAlertRef.current.click();
    }

    const upgradeNow = () => {
        closeAlertRef.current.click();
        navigate("/payment-details");
    }

    return (
        <div id="cust-contact-table" style={{ display: 'block' }}>
            {/* {console.log("foundCounts", foundCounts)}
            {console.log("loading", loading ? "True" : "False")}
            {console.log("isFilterApplied", isFilterApplied ? "True" : "False")} */}
            {
                foundCounts == -1 && !loading && !isFilterApplied ? (
                    <RS_Animation />
                ) : !foundCounts && foundCounts !== -1 && isFilterApplied && !loading ? (
                    <RS_Animation />
                ) : !foundCounts &&  !isFilterApplied && !loading ? (
                    <RS_Animation />
                ) : foundCounts == -1 && !loading && isFilterApplied ? (
                    <Search_Us />
                ) : foundCounts == 0 && !isFilterApplied && !loading ? (
                    <Search_Us />
                ) : foundCounts && !isFilterApplied && !loading ? (
                    <Search_Us />
                ) : !loading && foundCounts >= 0 && foundCounts != -1 && isFilterApplied ? (
                    <PaginationRender
                        key={foundCounts}
                        foundCounts={foundCounts}
                        pageNumber={currentPage}
                        pageCount={pageSize}
                        data={data}
                        loadingCount={loading}
                        paginationDataCount={paginationDataCount}
                    />
                ) : (
                    <div className="loader-container" style={{ display: 'none', margin: "9rem 0 0 0" }}>
                        <img src={loadingGif} width="300" alt="Loading" className="m-auto d-block" />
                    </div>
                )}
            {viewModal && isLowViewCredits ? (
                <>
                    <button
                        type="button"
                        className="btn btn-info btn-md"
                        data-toggle="modal"
                        data-target="#alertModal1"
                        ref={alertRef}
                        style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
                        data-backdrop="true"
                    ></button>
                    <div className="modal fade bd-example-modal-sm show" tabIndex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" id="alertModal1">
                        <div className="modal-dialog modal-sm" >
                            <div className="modal-content" style={{ width: "376px", borderRadius: "16px", position: "absolute", top: "14rem" }} >
                                <div className="d-flex flex-row justify-content-end pt-2 pr-3 pb-2">
                                    <button
                                        ref={closeAlertRef}
                                        type="button"
                                        className="close"
                                        data-dismiss="modal"
                                        aria-label="Close"
                                        onClick={close}
                                        style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
                                    />
                                    <div onClick={close}>
                                        <img src="../images/cross.png" width="20" style={{ cursor: "pointer" }} />
                                    </div>
                                </div>
                                <div className="d-flex flex-row justify-content-center">
                                    <div>
                                        <img src="../images/grey-avetar.png" width="50" />
                                    </div>
                                    <div>
                                        <p className="grey-avetar">!</p>
                                    </div>
                                    <div>
                                        <p className="anerror">An Error Occurred</p>
                                    </div>
                                </div>

                                <div className="lorem">
                                    <p>You have exceeded your view credits</p>
                                </div>

                                <div className="upgrdbutton">
                                    <button type="button" onClick={upgradeNow}>Upgrade</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            ) : selectedContact && viewModal ? (
                <ContactModal isLowViewCredits={isLowViewCredits} />
            ) : !loading && defaultAlert ? (
                <Alert data={defaultErrorMsg} />
            ) : (<></>)}
        </div >
    )
}

export default ContactstableBackup;