// import React from 'react';
import { useContext, useEffect, useState } from "react";
import EmployeeSize from "../../customer/filters/EmployeeSize.js";
import Industry from "../../customer/filters/Industry.js";
import JobTitles from "../../customer/filters/JobTitles.js";
import Location from "../../customer/filters/Location.js";
import RevenueSize from "../../customer/filters/RevenueSize.js";
// import { useState} from "react";
import { useNavigate } from "react-router-dom";
import "../assests/css/filter/job_title.css";
import DashboardContext from "../common-files/ContextDashboard.js";
import UseTabStore from "../common-files/useGlobalState.js";
import Technology from "./Technology.js";



const LeftCompanySidebar = () => {

	const [data, setData] = useState("");
	const { dataDC, setDataDC } = useContext(DashboardContext);
	const [accordionOpen, setAccordionOpen] = useState(false);
	const [otherState, setOtherState] = useState(null);
	const [classNameArrow, setClassNameArrow] = useState("fa fa-caret-right");

	const [accordionOpenTwo, setAccordionOpenTwo] = useState(false);
	const [otherStateTwo, setOtherStateTwo] = useState(null);
	const [classNameArrow2, setClassNameArrow2] = useState("fa fa-caret-right");

	const [accordionOpenThird, setAccordionOpenThird] = useState(false);
	const [otherStateThird, setOtherStateThird] = useState(null);
	const [classNameArrow3, setClassNameArrow3] = useState("fa fa-caret-right");

	const [accordionOpenFourth, setAccordionOpenFourth] = useState(false);
	const [otherStateFourth, setOtherStateFourth] = useState(null);
	const [classNameArrow4, setClassNameArrow4] = useState("fa fa-caret-right");

	const [accordionOpenFifth, setAccordionOpenFifth] = useState(false);
	const [otherStateFifth, setOtherStateFifth] = useState(null);
	const [classNameArrow5, setClassNameArrow5] = useState("fa fa-caret-right");

	let contact_job_title_1 = null;
	let contact_job_title_level_1 = null;
	let company_industry_categories_list = null;
	let company_employee_size = null;
	let company_annual_revenue_amount = null;
	let company_address_country = null;
	let company_address_state = null;
	let company_address_city = null;
	let sic_code = null;
	let dept = [];
	let func = [];
	useEffect(() => {
		if (!accordionOpen) {
			// console.log(" dataDC up");
			// console.log(dataDC);
			if ("jobTitle" in dataDC && dataDC.jobTitle !== undefined) {
				contact_job_title_1 = dataDC.jobTitle.map((v) => v.value);
			}
			if ("jobLevel" in dataDC && dataDC.jobLevel !== undefined) {
				contact_job_title_level_1 = dataDC.jobLevel.map((v) => v);
			}
			if ("DeptFunc" in dataDC && dataDC.DeptFunc !== undefined) {
				let obj = dataDC.DeptFunc;
				for (const key in obj) {
					dept.push(key);
					for (const nestedKey in obj[key]) {
						func.push(nestedKey);
					}
				}
			}
			if (
				contact_job_title_1 ||
				contact_job_title_level_1 ||
				dept.length > 0 ||
				func.length > 0
			) {
				setOtherState({
					contact_job_title_1,
					contact_job_title_level_1,
					dept,
					func,
				});
			}
		}
		// console.log(!accordionOpen);
		// console.log(func, dept, contact_job_title_level_1, contact_job_title_1);
		// console.log(dataDC);
	}, [accordionOpen, dataDC]);
	useEffect(() => {
		if (!accordionOpenTwo) {
			if ("industry" in dataDC && dataDC.industry !== undefined) {
				company_industry_categories_list = dataDC.industry.map((v) => v.value);
			}
			if ("sic_code" in dataDC && dataDC.sic_code !== undefined) {
				sic_code = dataDC.sic_code.map((v) => v.value);
			}
			if (company_industry_categories_list || sic_code) {
				setOtherStateTwo({ company_industry_categories_list, sic_code });
			}
		}
		// console.log(!accordionOpenTwo);
		// console.log(func, dept, contact_job_title_level_1, contact_job_title_1);
		// console.log(dataDC);
	}, [accordionOpenTwo, dataDC]);
	useEffect(() => {
		if (!accordionOpenThird) {
			if (
				"company_employee_size" in dataDC &&
				dataDC.company_employee_size !== undefined
			) {
				company_employee_size = dataDC.company_employee_size.map((v) => v);
			}
			if (company_employee_size) {
				setOtherStateThird({ company_employee_size });
			}
		}
		// console.log(!accordionOpenThird);
		// console.log(func, dept, contact_job_title_level_1, contact_job_title_1);
		// console.log(dataDC);
	}, [accordionOpenThird, dataDC]);
	useEffect(() => {
		if (!accordionOpenFourth) {
			if (
				"company_annual_revenue_amount" in dataDC &&
				dataDC.company_annual_revenue_amount !== undefined
			) {
				company_annual_revenue_amount =
					dataDC.company_annual_revenue_amount.map((v) => v);
			}
			if (company_annual_revenue_amount) {
				setOtherStateFourth({ company_annual_revenue_amount });
			}
		}
		// console.log(!accordionOpenFourth);
		// console.log(func, dept, contact_job_title_level_1, contact_job_title_1);
		// console.log(dataDC);
	}, [accordionOpenFourth, dataDC]);
	useEffect(() => {
		if (!accordionOpenFifth) {
			if ("location" in dataDC && dataDC.location !== undefined) {
				company_address_country = dataDC.location.map((v) => {
					if (v.dataGroup == "country") {
						return v.value;
					}
				});
				company_address_state = dataDC.location.map((v) => {
					if (v.dataGroup == "state") {
						return v.value;
					}
				});
				company_address_city = dataDC.location.map((v) => {
					if (v.dataGroup == "city") {
						return v.value;
					}
				});
			}
			if (
				company_address_country ||
				company_address_state ||
				company_address_city
			) {
				setOtherStateFifth({
					company_address_country,
					company_address_state,
					company_address_city,
				});
			}
		}
		// console.log(!accordionOpenFifth);
		// console.log(func, dept, contact_job_title_level_1, contact_job_title_1);
		// console.log(dataDC);
	}, [accordionOpenFifth, dataDC]);

	const handleAccordionClickTwo = () => {
		if (!accordionOpenTwo) {
			setClassNameArrow2("fa fa-caret-down");
		} else {
			setClassNameArrow2("fa fa-caret-right");
		}
		setAccordionOpenTwo(!accordionOpenTwo);
	};
	const handleAccordionClickThird = () => {
		if (!accordionOpenThird) {
			setClassNameArrow3("fa fa-caret-down");
		} else {
			setClassNameArrow3("fa fa-caret-right");
		}
		setAccordionOpenThird(!accordionOpenThird);
	};
	const handleAccordionClickFourth = () => {
		if (!accordionOpenFourth) {
			setClassNameArrow4("fa fa-caret-down");
		} else {
			setClassNameArrow4("fa fa-caret-right");
		}
		setAccordionOpenFourth(!accordionOpenFourth);
	};
	const handleAccordionClickFifth = () => {
		if (!accordionOpenFifth) {
			setClassNameArrow5("fa fa-caret-down");
		} else {
			setClassNameArrow5("fa fa-caret-right");
		}
		setAccordionOpenFifth(!accordionOpenFifth);
	};
	const removeValueEmpSize = (value) => {
		// console.log(value);
		// console.log(dataDC);
		let prevList =
			"company_employee_size" in dataDC ? dataDC.company_employee_size : null;
		let updateList = prevList.filter((item) => item !== value);
		// console.log("updateList");
		// console.log(updateList);
		setDataDC({ ...dataDC, company_employee_size: updateList });
		// setList((prevList) => prevList.filter((item) => item !== value));
	};
	const removeValueRevenueAmt = (value) => {
		// console.log(value);
		// console.log(dataDC);
		// console.log(value);
		// console.log(dataDC);
		let prevList =
			"company_annual_revenue_amount" in dataDC
				? dataDC.company_annual_revenue_amount
				: null;
		let updateList = prevList.filter((item) => item !== value);
		// console.log("updateList");
		// console.log(updateList);
		setDataDC({ ...dataDC, company_annual_revenue_amount: updateList });
	};
	const removeValueSic = (value) => {
		// console.log(value);
		// console.log(dataDC);
		let prevList = "sic_code" in dataDC ? dataDC.sic_code : null;
		let updateList = prevList.filter((item) => item.value !== value);
		// console.log(updateList);
		setDataDC({ ...dataDC, sic_code: updateList });
		// setList((prevList) => prevList.filter((item) => item !== value));
	};
	const removeValueIndustry = (value) => {
		// console.log(value);
		// console.log(dataDC);
		let prevList = "industry" in dataDC ? dataDC.industry : null;
		let updateList = prevList.filter((item) => item.value !== value);
		// console.log(updateList);
		setDataDC({ ...dataDC, industry: updateList });
		// setList((prevList) => prevList.filter((item) => item !== value));
	};
	const removeValueLocation = (value) => {

		let prevList = "location" in dataDC ? dataDC.location : null;
		let updateList = prevList.filter((item) => item.value !== value);
		setDataDC({ ...dataDC, location: updateList });
	};
	const resetButton = () => {
		sessionStorage.removeItem("jobDepartments");
		sessionStorage.removeItem("jobFunction");
	
		dataDC.jobTitle = {}; // Set jobTitle to an empty object
	
		dataDC.jobLevel = {}; // Set jobTitle to an empty object
	
		dataDC.DeptFunc = {}; // Set jobTitle to an empty object
	
		if ("company_employee_size" in dataDC && dataDC.company_employee_size !== undefined) {
		  dataDC.company_employee_size = []; // Set company_employee_size to an empty object
		}
	
		if ("company_annual_revenue_amount" in dataDC && dataDC.company_annual_revenue_amount !== undefined) {
		  dataDC.company_annual_revenue_amount = []; // Set company_annual_revenue_amount to an empty object
		}
		getAllPlanDetails();
		UseTabStore.getState().clearStorage();
		clearStore("AdvancedFilterData","Company")
		setSelectedTab("company");
		setTimeout(() => {
		  // Your state update code here
		  setResetPage(null);
		}, 1); // 1000 milliseconds = 1 second
	  };



	return (
		<div>
			<div className="sidenave" style={{ width: "320px" }}>
				<div className="filter-section">
					<div className="d-flex justify-content-between">
						<div className="Filter">
							<p>
								<img src="../images/filter.png" className="img-fluid" />
								&nbsp;Filters
							</p>
						</div>
						<div className="d-flex align-items-end">
							<p>
								<button className="reset" onClick={resetButton}>
									Reset
								</button>
							</p>
						</div>
					</div>
				</div>
				<div>

					{/* <div>
			  <ul class="nav nav-tabs justify-content-center" id="myTab" role="tablist">
				<li class="nav-item">
				  <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Contact</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Company</a>
				</li>
  
			  </ul>
			  <div class="tab-content" id="myTabContent">
				<div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">Zuber</div>
				<div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">Ahmed</div>
			  </div>
			</div> */}

					<div>


					</div>



					<div className="collapse" id="collapseExample">
						<div className="card card-body-container">
							<JobTitles />
						</div>
					</div>

					<p className="drop-button">
						<button
							onClick={handleAccordionClickTwo}
							className="Job-Titles"
							type="button"
							data-toggle="collapse"
							data-target="#industry"
							aria-expanded="false"
							aria-controls="collapseExample"
						>
							<span className="suitcase">
								<img src="./images/industry.png" />
							</span>
							Industry <i className={classNameArrow2}></i>
						</button>
						{/* {console.log(otherStateTwo)} */}
						{!accordionOpenTwo &&
							otherStateTwo &&
							(otherStateTwo.sic_code !== null ||
								otherStateTwo.company_industry_categories_list !== null) &&
							(otherStateTwo.company_industry_categories_list
								? otherStateTwo.company_industry_categories_list.length > 0
								: otherStateTwo.sic_code
									? otherStateTwo.sic_code.length > 0
									: false) && (
								<div className="title-2">
									<div className="d-flex flex-row">
										<div className="d-flex flex-column">
											<p className="focused">
												Industry: <span className="cancel">×</span>
											</p>
										</div>
										<div className="d-flex flex-column">
											<p>
												{otherStateTwo.company_industry_categories_list &&
													otherStateTwo.company_industry_categories_list.map(
														(tag) => (
															// <span key={tag} className={` roundedTag `}>
															// 	<span className="" >{tag}<span className="cancel">×</span></span>
															// </span>
															<>
																<span key={tag} className={` roundedTag `}>
																	<span key={"s_" + tag} className="">
																		{tag}
																	</span>
																	<span
																		key={"s_rm_" + tag}
																		className="cancelled"
																		onClick={() => removeValueIndustry(tag)}
																	>
																		<span
																			key={"s_rounded_" + tag}
																			className="roundedX"
																		>
																			{/* &nbsp;×&nbsp; */}
																			<svg
																				height="9"
																				width="10"
																				viewBox="0 0 20 20"
																				aria-hidden="true"
																				focusable="false"
																				className="css-tj5bde-Svg"
																			>
																				<path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
																			</svg>
																		</span>
																	</span>
																</span>
															</>
														)
													)}
												{otherStateTwo.sic_code &&
													otherStateTwo.sic_code.map((tag) => (
														// <span key={tag} className={` roundedTag `}>
														// 	<span className="" >{tag}<span className="cancel">×</span></span>
														// </span>
														<>
															<span key={tag} className={` roundedTag `}>
																<span key={"s_" + tag} className="">
																	{tag}
																</span>
																<span
																	key={"s_rm_" + tag}
																	className="cancelled"
																	onClick={() => removeValueSic(tag)}
																>
																	<span
																		key={"s_rounded_" + tag}
																		className="roundedX"
																	>
																		{/* &nbsp;×&nbsp; */}
																		<svg
																			height="9"
																			width="10"
																			viewBox="0 0 20 20"
																			aria-hidden="true"
																			focusable="false"
																			className="css-tj5bde-Svg"
																		>
																			<path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
																		</svg>
																	</span>
																</span>
															</span>
														</>
													))}
											</p>
										</div>
									</div>
								</div>
							)}
					</p>
					<div className="collapse" id="industry">
						<div className="card card-body-container">
							<Industry />
						</div>
					</div>

					{/*<p className="drop-button">
			  <button className="Job-Titles" type="button" data-toggle="collapse" data-target="#Employee_size" aria-expanded="false" aria-controls="collapseExample">
			  <span className="suitcase">
			  <img src="./images/employee.png" /></span>
			  Employees Size  <i className="fa fa-caret-down"></i>
  
				  <div className="collapse" id="Employee_size">
				  <div className="card card-body-container" >
									  <p className="paragraph mt-0 ml-1 mb-0">
			  <button onClick={EmployeeSizeDropdown} className="Job-Titles-1" type="button" data-toggle="collapse" data-target="#EmployeeSize" aria-expanded="false" aria-controls="collapseExample">
											  Select<i className="fa fa-caret-down"></i>
											  
											  <div className="collapse" id="EmployeeSize">
											   <EmployeeSize />
											  </div>
											  
										  </button>
  
									  </p>
  
								  </div>
							  </div>
						  </button>
					  </p>
  
  */}


					<p className="drop-button">
						<button
							onClick={handleAccordionClickTwo}
							className="Job-Titles"
							type="button"
							data-toggle="collapse"
							data-target="#technology"
							aria-expanded="false"
							aria-controls="collapseExample"
						>
							<span className="suitcase">
								<img src="./images/technology.png" />
							</span>
							Technology <i className={classNameArrow2}></i>
						</button>
						{/* {console.log(otherStateTwo)} */}
						{!accordionOpenTwo &&
							otherStateTwo &&
							(otherStateTwo.sic_code !== null ||
								otherStateTwo.company_industry_categories_list !== null) &&
							(otherStateTwo.company_industry_categories_list
								? otherStateTwo.company_industry_categories_list.length > 0
								: otherStateTwo.sic_code
									? otherStateTwo.sic_code.length > 0
									: false) && (
								<div className="title-2">
									<div className="d-flex flex-row">
										<div className="d-flex flex-column">
											<p className="focused">
												Industry: <span className="cancel">×</span>
											</p>
										</div>
										<div className="d-flex flex-column">
											<p>
												{otherStateTwo.company_industry_categories_list &&
													otherStateTwo.company_industry_categories_list.map(
														(tag) => (
															// <span key={tag} className={` roundedTag `}>
															// 	<span className="" >{tag}<span className="cancel">×</span></span>
															// </span>
															<>
																<span key={tag} className={` roundedTag `}>
																	<span key={"s_" + tag} className="">
																		{tag}
																	</span>
																	<span
																		key={"s_rm_" + tag}
																		className="cancelled"
																		onClick={() => removeValueIndustry(tag)}
																	>
																		<span
																			key={"s_rounded_" + tag}
																			className="roundedX"
																		>
																			{/* &nbsp;×&nbsp; */}
																			<svg
																				height="9"
																				width="10"
																				viewBox="0 0 20 20"
																				aria-hidden="true"
																				focusable="false"
																				className="css-tj5bde-Svg"
																			>
																				<path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
																			</svg>
																		</span>
																	</span>
																</span>
															</>
														)
													)}
												{otherStateTwo.sic_code &&
													otherStateTwo.sic_code.map((tag) => (
														// <span key={tag} className={` roundedTag `}>
														// 	<span className="" >{tag}<span className="cancel">×</span></span>
														// </span>
														<>
															<span key={tag} className={` roundedTag `}>
																<span key={"s_" + tag} className="">
																	{tag}
																</span>
																<span
																	key={"s_rm_" + tag}
																	className="cancelled"
																	onClick={() => removeValueSic(tag)}
																>
																	<span
																		key={"s_rounded_" + tag}
																		className="roundedX"
																	>
																		{/* &nbsp;×&nbsp; */}
																		<svg
																			height="9"
																			width="10"
																			viewBox="0 0 20 20"
																			aria-hidden="true"
																			focusable="false"
																			className="css-tj5bde-Svg"
																		>
																			<path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
																		</svg>
																	</span>
																</span>
															</span>
														</>
													))}
											</p>
										</div>
									</div>
								</div>
							)}
					</p>
					<div className="collapse" id="technology">
						<div className="card card-body-container">
							<Technology />
						</div>
					</div>


					<p className="drop-button">
						<button
							onClick={handleAccordionClickThird}
							className="Job-Titles"
							type="button"
							data-toggle="collapse"
							data-target="#collapseExampleEmpSize"
							aria-expanded="false"
							aria-controls="collapseExampleEmpSize"
						>
							<span className="suitcase">
								<img src="./images/employee.png" />
							</span>
							Employee Size <i className={classNameArrow3}></i>
						</button>
						{!accordionOpenThird &&
							otherStateThird &&
							otherStateThird.company_employee_size.length > 0 && (
								<div>
									<div className="title-2">
										{" "}
										{/* {console.log(otherStateThird)} */}
										<p>
											{otherStateThird.company_employee_size &&
												otherStateThird.company_employee_size.map((tag) => (
													<>
														<span key={tag} className={` roundedTag `}>
															<span key={"s_" + tag} className="">
																{tag}
															</span>
															<span
																key={"s_rm_" + tag}
																className="cancelled"
																onClick={() => removeValueEmpSize(tag)}
															>
																<span
																	key={"s_rounded_" + tag}
																	className="roundedX"
																>
																	{/* &nbsp;×&nbsp; */}
																	<svg
																		height="9"
																		width="10"
																		viewBox="0 0 20 20"
																		aria-hidden="true"
																		focusable="false"
																		className="css-tj5bde-Svg"
																	>
																		<path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
																	</svg>
																</span>
															</span>
														</span>
													</>
												))}
										</p>
									</div>
								</div>
							)}
					</p>
					<div className="collapse" id="collapseExampleEmpSize">
						<div className="card card-body-container">
							<EmployeeSize />
						</div>
					</div>

					<p className=" drop-button">
						<button
							onClick={handleAccordionClickFourth}
							className="Job-Titles"
							type="button"
							data-toggle="collapse"
							data-target="#Revenue"
							aria-expanded="false"
							aria-controls="collapseExample"
						>
							<span className="suitcase">
								<img src="./images/doller.png" />
							</span>
							Revenue Size <i className={classNameArrow4}></i>
						</button>
						{!accordionOpenFourth &&
							otherStateFourth &&
							otherStateFourth.company_annual_revenue_amount.length > 0 && (
								<div>
									<div className="title-2">
										{" "}
										{/* {console.log(otherStateFourth)} */}
										<p>
											{otherStateFourth.company_annual_revenue_amount &&
												otherStateFourth.company_annual_revenue_amount.map(
													(tag) => (
														<>
															<span key={tag} className={` roundedTag `}>
																<span key={"s_" + tag} className="">
																	{tag}
																</span>
																<span
																	key={"s_rm_" + tag}
																	className="cancelled"
																	onClick={() => removeValueRevenueAmt(tag)}
																>
																	<span
																		key={"s_rounded_" + tag}
																		className="roundedX"
																	>
																		{/* &nbsp;×&nbsp; */}
																		<svg
																			height="9"
																			width="10"
																			viewBox="0 0 20 20"
																			aria-hidden="true"
																			focusable="false"
																			className="css-tj5bde-Svg"
																		>
																			<path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
																		</svg>
																	</span>
																</span>
															</span>
														</>
													)
												)}
										</p>
									</div>
								</div>
							)}
					</p>
					<div className="collapse" id="Revenue">
						<div className="card card-body-container">
							<RevenueSize />
						</div>
					</div>

					<p className=" drop-button mb-4">
						<button
							onClick={handleAccordionClickFifth}
							className="Job-Titles"
							type="button"
							data-toggle="collapse"
							data-target="#Location"
							aria-expanded="false"
							aria-controls="collapseExample"
						>
							<span className="suitcase">
								<img src="./images/pin.png" />
							</span>
							Location <i className={classNameArrow5}></i>
						</button>
						{!accordionOpenFifth && otherStateFifth && (
							<div >
								<div className="title-2 ">
									{" "}
									{/* {console.log(otherStateFifth)} */}
									<p>
										{otherStateFifth.company_address_country &&
											otherStateFifth.company_address_country.map(
												(tag) =>
													tag && (
														<>
															<span key={tag} className={` roundedTag `}>
																<span key={"s_" + tag} className="">
																	{tag}
																</span>
																<span
																	key={"s_rm_" + tag}
																	className="cancelled"
																	onClick={() => removeValueLocation(tag)}
																>
																	<span
																		key={"s_rounded_" + tag}
																		className="roundedX"
																	>
																		{/* &nbsp;×&nbsp; */}
																		<svg
																			height="9"
																			width="10"
																			viewBox="0 0 20 20"
																			aria-hidden="true"
																			focusable="false"
																			className="css-tj5bde-Svg"
																		>
																			<path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
																		</svg>
																	</span>
																</span>
															</span>
														</>
													)
											)}
										{otherStateFifth.company_address_state &&
											otherStateFifth.company_address_state.map(
												(tag) =>
													tag && (
														<>
															<span key={tag} className={` roundedTag `}>
																<span key={"s_" + tag} className="">
																	{tag}
																</span>
																<span
																	key={"s_rm_" + tag}
																	className="cancelled"
																	onClick={() => removeValueLocation(tag)}
																>
																	<span
																		key={"s_rounded_" + tag}
																		className="roundedX"
																	>
																		{/* &nbsp;×&nbsp; */}
																		<svg
																			height="9"
																			width="10"
																			viewBox="0 0 20 20"
																			aria-hidden="true"
																			focusable="false"
																			className="css-tj5bde-Svg"
																		>
																			<path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
																		</svg>
																	</span>
																</span>
															</span>
														</>
													)
											)}
										{otherStateFifth.company_address_city &&
											otherStateFifth.company_address_city.map(
												(tag) =>
													tag && (
														<>
															<span key={tag} className={` roundedTag `}>
																<span key={"s_" + tag} className="">
																	{tag}
																</span>
																<span
																	key={"s_rm_" + tag}
																	className="cancelled"
																	onClick={() => removeValueLocation(tag)}
																>
																	<span
																		key={"s_rounded_" + tag}
																		className="roundedX"
																	>
																		{/* &nbsp;×&nbsp; */}
																		<svg
																			height="9"
																			width="10"
																			viewBox="0 0 20 20"
																			aria-hidden="true"
																			focusable="false"
																			className="css-tj5bde-Svg"
																		>
																			<path d="M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"></path>
																		</svg>
																	</span>
																</span>
															</span>
														</>
													)
											)}
									</p>
								</div>
							</div>
						)}
					</p>
					<div className="collapse" id="Location">
						<div className="card card-body-container">
							<Location />
						</div>
						<div ></div>
					</div>
				</div>

				

				{/* <div className="dropdown">
					  <button onClick={SearchJobTitles} className="secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						  Job Titles
					  </button>
					  <div className="dropdown-menu" aria-labelledby="dropdownMenuButton">
					  <div className="dropdown-item iduse" href="#"><Industry /></div>
						  <div className="dropdown-item iduse" Link to="/jobtitle"><Industry /></div>
						  <div className="dropdown-item iduse" href="#"><EmployeeSize /></div>
						  <div className="dropdown-item iduse" href="#"><RevenueSize /></div>
						  <div className="dropdown-item iduse" href="#"><Location /></div>
					  </div>
				  </div>
	   */}
			</div>

			{/*<Sidenave/>*/}
		</div>
	)
};

export default LeftCompanySidebar;