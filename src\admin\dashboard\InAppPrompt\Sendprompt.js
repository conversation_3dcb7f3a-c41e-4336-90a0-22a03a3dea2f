import React from "react";
import S from "../../assets/css/layouts/admin-header.module.css";

const SendPrompt = () => {
    return (
        <>

            <button type="button" className="btn btn-primary" data-toggle="modal" data-target="#exampleModalCenter">
                Launch demo modal
            </button>


            <div className="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
                <div className="modal-dialog modal-dialog-centered" role="document" >
                    <div className="modal-content">
                        {/* <div className="modal-header"> */}
                        <div className="row">
                            <div className="col-md-10 text-center">
                            <h5 className={S.SentPrompt} >Send Prompt</h5>

                            </div>
                            <div className="col-md-2">
                            <button type="button" className="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            </div>
                        </div>
                           
                        {/* </div> */}
                        <div className="modal-body">
                            <div className="row">
                                <div className="col-md-6">
                                <label className={S.promptlabel}>User Type</label>
                                    <div className={S.dropdown}>
                                        <button className={S.dropbtn}>Select</button>
                                        <div className={S.dropdowncontent}>
                                            <a href="#">Free Trial Users</a>
                                            <a href="#">Paid Users</a>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-md-6">
                                <label className={S.promptlabel}>User Name</label>
                                    <div class="form-group">
                                        <input type="text" className={S.enterusername} aria-describedby="emailHelp" placeholder="Enter user name here" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="text-right mr-3 mb-4">
                            <button type="button" className={S.savechanges}>Send</button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );

}

export default SendPrompt