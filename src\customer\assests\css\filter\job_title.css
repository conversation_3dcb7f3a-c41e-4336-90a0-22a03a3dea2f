@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  /* Regular */
}

.no-horizontal-scroll-container {
  margin-left: 53px;
  flex-grow: 1;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 2px;
  background: #EDF0F4;
  background: linear-gradient(241deg, rgba(237, 240, 244, 1) 0%, rgba(244, 243, 244, 1) 45%);
  min-width: 0;
  font-family: 'Poppins', sans-serif;
}

.sidenave {
  width: auto;
  background-color: #f9f9f9;
  height: 100vh;
  position: relative;
  overflow: auto;
  overflow-y: inherit;
}

/* width */
::-webkit-scrollbar {
  width: 5px;
  border-radius: 4px;
  padding: 0 5px 0 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #55C2C3;
  border-radius: 5px;
}

.css-p8t2xb {
  -webkit-box-align: center;
  align-items: center;
  display: flex;
  border-radius: 10px;
  background-color: rgb(255, 255, 255);
  padding-right: 0px;
  box-sizing: border-box;
  border: 1px solid rgb(8, 206, 220);
  margin-top: -7px;
  margin-bottom: 2px;
  width: 11px;
  height: 11px;
  cursor: pointer;
}


.sub_item_padding {

  padding: 0 0 0 15px;

}

button.Job-Titles {
  width: -webkit-fill-available;
  border: 0;
  height: 38px;
  text-align: inherit;
  padding: 0 0 0 12px;
  background-color: #f9f9f9;
  cursor: pointer;
  outline: none;
  color: #093d54;
  font-family: "Lato", Regular;
  font-weight: 600;
  font-size: 14px;
  margin: 0 0 0 0;
}

.css-1xc3v61-indicatorContainer {
  display: -ms-flexbox;
  display: flex;
  transition: color 150ms;
  color: hsl(0deg 0% 100%);
  padding: 8px;
  box-sizing: border-box;
}

.css-1xc3v61-indicatorContainer:hover {
  color: hsl(0, 0%, 100%);
}

.css-tj5bde-Svg {
  display: inline-block;
  fill: currentColor;
  line-height: 1;
  stroke: currentColor;
  stroke-width: 0;
  color: #fff;
}

.css-1u9des2-indicatorSeparator {
  align-self: stretch;
  margin-bottom: 8px;
  margin-top: 8px;
  box-sizing: border-box;
}

.search-icon {
  position: absolute;
  transform: translateY(28%);
  left: 89%;
  margin-top: 0px;
}

.search-icon2 {
  position: absolute;
  transform: translateY(28%);
  left: 89%;
  margin-top: 45px;
}

i.fas.fa-search {
  font-size: 12px;
  color: #093d54;
}

.card-body-container {
  max-width: 30rem;
  border-radius: 0px;
  border: 0;
  margin: auto;
}

button.Job-Titles-1 {
  width: -webkit-fill-available;
  width: -moz-available;
  padding: 5px 0 5px 8px;
  text-align: inherit;
  font-weight: 600;
  font-size: 14px;
  background-color: #fff;
  /* margin-left: 28px; */
  /* margin-right: 12px; */
  outline: none;
  /* border: 1px solid #093d54; */
  border-radius: 3px;
  border: 0;
  /* margin-top: 3px; */
  /* margin-bottom: 13px; */
  color: #093d54;
  /* margin-top: 16px; */
  cursor: pointer;
}

button.Job-Titles-2 {
  /* width: -webkit-fill-available; */
  width: -webkit-fill-available;
  padding: 2px 0 2px 8px;
  text-align: inherit;
  background-color: #fff;
  margin-left: 0px;
  margin-right: 12px;
  outline: none;
  border: 0;
  border-radius: 3px;
  margin-top: 3px;
  margin-bottom: 5px;
  color: #093d54;
  margin-top: 3px;
  cursor: pointer;
  font-family: Lato, Regular;
  font-weight: 600;
  font-size: 14px;
}

.paragraph {
  border: 1px solid #093d54;
  width: -webkit-fill-available;
  margin: 14px 11px 10px 16px;
  border-radius: 3px;
}

.css-14u7tma-control:hover {
  border-color: hsl(0deg 0% 0%);
}

p.paragraph.mt-1.ml-1 {
  padding: 0;
  margin: 0;
}

.card.card-body-containerr {
  border-radius: 0;
  border-top: 0;
  background-color: transparent;
  margin-left: 21px;
  margin-top: 6px;
}

.card.card-body-container {
  border-radius: 0;
  box-shadow: none;
  box-shadow: rgb(12 22 24 / 0%) 0px 25px 37px -20px, rgb(0 0 0 / 9%) 0px 2px 2px 2px inset;
  /* margin: 0 0 60px 0px; */
  padding: 0 0 4px 0;
}

.madespace {
  margin: 0 0 -30px 0px;
}



select#dynamic-dropdown {
  width: 269px;
  padding: 6px 0 6px 0px;
}

.d-flex.flex-column label {
  padding: 0px 0 0 16px;
  font-size: 14px;
  /* width: 200px; */
}

.card-body-containerr {
  margin-top: 7px;
}

span.suitcase {
  padding: 0 8px 0 0px;
}

.css-1p3m7a8-multiValue {
  border-radius: 20px;
  background-color: #fff;
  border: qpx solid #7e8c9c;
  border: 1px solid #7e8c9c;
}

p.ml-2.mb-0.drop-button {
  padding: 7px 0 7px 0px;
  background-color: #f9f9f9;
  border-top: 1px solid #dbdbdb;
}

.Filter {
  padding: 0;
}


.Filter p {
  font-size: 14px;
  padding: 10px 0 0 0;
  font-weight: 600;
  color: #093d54;
  /* font-family: "Lato", Regular; */
  margin: 0;
}

.Filter img {
  width: 20px;
  /* height: 19px; */
  padding: 0 0 5px 4px;
}

/* .d-flex.align-items-end p {
  padding: 0 13px 0 0;
  font-size: 16px;
  color: #55c2c3;
  cursor: pointer;
  margin: 0px 0 12px 0;
  font-weight: 300;
} */

i.fa.fa-caret-down {
  float: right;
  padding: 5px 20px 0 0;
  position: absolute;
  right: 0;
}

.css-wsp0cs-MultiValueGeneric {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: hsl(0, 0%, 20%);
  font-size: 12px;
  padding: 3px;
  padding-left: 6px;
  box-sizing: border-box;
}

.filter-section {
  margin: 0px;
  padding: 0px 0 0px 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: #f9f9f9;
}

.my-select {
  font-size: 1.2rem;
  border: 2px solid #ccc;
  border-radius: 4px;
  padding: 0.5rem;
  width: 100%;
  background-color: #fff;
  color: #333;
}

.my-select option {
  font-size: 1rem;
  background-color: #fff;
  color: #333;
}

button.reset {
  /* margin-top: 0; */
  outline: none;
  border: 0;
  cursor: pointer;
  background-color: #f9f9f9;
  font-size: 14px;
  color: #55c2c3;
  -webkit-text-stroke-width: thin;
  padding: 10px 18px 0px 0px;
}

.drop-button-1:focus {
  border-left: 3px solid #55c2c3;
  /* background-color: #fff; */
}

.drop-button-1 {
  border-left: 3px solid #55c2c3;
  background-color: #fff;
}

button.Job-Titles:focus {
  /* background-color: #fff; */
  font-family: "Lato", Regular;
}

p.drop-button {
  margin: 0;
  padding: 5px 0 5px 8px;
  border-top: 1px solid #e9e9e9;
  border-bottom: 1px solid #e9e9e9;
  background-color: #f9f9f9;
}

p.drop-button-1 {
  margin-top: 10px;
  padding: 5px 0 5px 10px;
  margin: 0;
  background-color: #f9f9f9;
}

.dynamic {
  padding: 6px 0px 1px 6px;
}

.btn-link:hover {
  color: #093d54;
  text-decoration: underline;
  background-color: transparent;
  border-color: transparent;
}

button.btn.btn-link {
  border: 0;
  width: -webkit-fill-available;
  text-align: inherit;
  outline: none;
  text-decoration: none;
  color: #093d54;
  padding: 4px 0 6px 8px;
  font-weight: 600;
  font-size: 14px;
}

div#headingThree {
  margin-left: 20px;
}

.collaps-bar {
  width: 270px;
  border: 1px solid #093d54;
  border-radius: 3px;
}

.border- {
  border-radius: 0;
  margin: auto;
  border: 1px solid #093d54;
  padding: 0 0 0 0px;
  border-radius: 3px;
  margin: 12px 11px 0 16px;

}

.card-body-1 {
  padding: 9px 0 0 14px;
}

div#accordion {
  margin-top: 5px;
  margin-bottom: 5px;
}

.css-gqbcc4-MultiValueRemove:hover {
  background-color: #55c2c3;
  color: #55c2c3;
  height: 14px;
  margin-top: -6px;
}

.bohiiux {
  display: flex;
  justify-content: space-between;
}

.css-tj5bde-Svg {
  display: inline-block;
  fill: currentColor;
  line-height: 1;
  stroke: currentColor;
  stroke-width: 0;
  color: rgb(184, 243, 243);
}

span#example-five {
  float: right;
  padding: 0 5px 0 0px;
  color: #55c2c3;
  cursor: pointer;
}

label.job-checkbox {
  width: -webkit-fill-available;
  padding: 0 9px 0 0px;
  width: -moz-available;
}

span.job-checkbox {
  padding: 0px 0 0px 0px;
  margin: 0;
  /* border: 1px solid #000; */
  width: 243px;
  cursor: pointer;
}

#example-four {
  position: relative;
}

#example-four.on:after {
  content: "Hide";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  text-decoration: underline;
}

#example-five {
  position: relative;
}

#example-five-checkbox:checked+#example-five:after {
  content: "-";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  transition: 2s;
}

.css-b62m3t-container {
  position: relative;
  box-sizing: border-box;
  padding: 0 12px 0 1px;
}

p.focused {
  width: max-content;
}

.title-2 p {
  font-size: 12px;
  line-height: 31px;
  margin: 0px 0px 0px 11px;
  color: #000;
}

span.cancel {
  color: #f9f9f9;
}

span.cancelled {
  margin-top: -16px;
  padding: 0px 0 -5px 4px;
  margin-bottom: -7px;
  text-align: end;
}


.roundedX:hover {
  background-color: #55c2c3;
  color: #fff;
}


.css-tj5bde-Svg {
  /* background-color: #55c2c3; */
  color: #55c2c3;
}

.css-tj5bde-Svg:hover {
  color: white;
}

.roundedX {
  border-radius: 50% !important;
  border: 1px solid #55c2c3;
  padding: 0;
  font-size: 6px;
  background-color: #fff;
  cursor: pointer;
  color: #55c2c3;
  position: absolute;
  top: -13px;
  line-height: 0px;
  margin: 5px 0px 0 -7px;
}

span.cancel:hover {
  color: red;
  cursor: pointer;
  padding: 0 3px 0 1px;
}

i.fa.fa-caret-right {
  float: right;
  padding: 5px 20px 0 0;
  position: absolute;
  right: 0;
}


.roundedTag {
  border: 1px solid;
  border-radius: 30px;
  padding: 3px 6px 3px 6px;
  position: relative;
  line-break: 18px;
  font-size: 10px;
  width: auto;
  white-space: normal;
  vertical-align: top;
  display: inline-flex;
  line-height: 11px;
  background-color: #fff;
  margin: 4px 10px 0px 0px;
  display: inline-table;
}

label.job-checkbox1 {
  width: 160px;
}

.job-checkbox2 {
  width: 145px;
}

.job-checkbox3 {
  width: 160px;
}


/* 21-08-2023 */

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 3px;
}


.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  border-color: transparent;
}

a#profile-tab {
  font-size: 12px;
  font-weight: 600;
  color: #093d54;
  padding: 2px 0px 2px 0px;
  border: 0;
  border-radius: 30px;
  margin: 2px 8px 0 0px;
  width: 90px;
  text-align: center;
}

a#home-tab {
  font-size: 12px;
  font-weight: 600;
  color: #093d54;
  padding: 2px 0px 2px 0px;
  border: 0;
  border-radius: 30px;
  margin: 2px 0 0 8px;
  width: 90px;
  text-align: center;
}

a#home-tab .active {
  box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 3px;
}

ul#myTab {
  background-color: #e8f7f7;
  border-radius: 20px;
  box-shadow: inset 0px 2px 3px #c7c7c7;
  /* margin: auto 15px auto 18px; */
  padding: 2px 0 4px 0;
}



/* new Modal  */

.modaldialognew {
  width: 45rem;
  margin: auto;
}

/* 4-09-2023 */

img.bimage {
  box-shadow: rgb(100 100 111 / 20%) 0px 7px 29px 0px;
  border-radius: 20px;
}


button.downldbtn {
  border: 0;
  padding: 5px 11px 5px 11px;
  font-size: 14px;
  color: #fff;
  background-color: #093D54;
  border-radius: 5px;
  margin-top: 16px;
  cursor: pointer;
}

.bottom-section {
  /* background-color: #F6FAFF; */
  border-top: #E1EDF5;
  border-bottom: #E1EDF5;
}

.tabuler {
  border-top: 1px solid #dfdfdf;
  padding: 10px 8px 10px 8px;

}

label.requiredalue {
  margin-left: 33px;
  margin: 8px 0px 2px 26px;
  font-size: 14px;
  color: #000;
}

.locks {
  box-shadow: rgb(12 22 24 / 0%) 0px 25px 37px -20px, rgb(0 0 0 / 9%) 0px 0px 2px 2px inset;
  margin: 0 0 3rem 0;
}


.Apply-Filters {
  background-color: #f9f9f9;
  /* border-top: 1px solid rgb(214, 212, 212); */
  padding: 10px 0 20px 0;
}


.element-smooth {
  scroll-behavior: smooth;
}

.element-smooth {
  scroll-behavior: smooth;
}


button.Apply-Filters-button {
  margin: 20px auto 0px auto;
  display: block;
  background-color: #55C2C3;
  font-weight: 400;
  border: 0;
  font-size: 14px;
  padding: 8px 28px 8px 28px;
  outline: none;
  color: #fff;
  border-radius: 5px;
  cursor: pointer;
}

button.Apply-Filters-button:active {
  transform: scale(0.98);
  /* Scaling button to 0.98 to its original size */
  box-shadow: 3px 2px 22px 1px rgba(3, 65, 70, 0.24);
  /* Lowering the shadow */
}

button.Apply-Filters-button-disable {
  /* position: sticky; */
  /* bottom: 15px; */
  padding: 10px 30px 10px 30px;
  background-color: #c2c8ce;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  /* text-align: center; */
  /* margin: auto; */
  /* width: 170px; */
  /* z-index: 1000; */
}

.apply-back {
  position: sticky;
  bottom: 0;
  margin: 0 auto 0 auto;
  padding: 5px 0 20px 0;
  z-index: 1000;
  text-align: center;
  background-color: #f9f9f9;
}


.techno::placeholder {
  font-size: 14px;
}

span.suitcase img {
  width: 19px;
}

.drop-button-1:focus {
  background-color: #fff;
  border-left: 3px solid #55c2c3;
}


@media screen and (min-width: 1680px) {
  .sidenave {
    height: 88vh !important;
  }
}

@media screen and (min-width: 1600px) {
  .sidenave {
    height: 86vh;
  }
}

@media screen and (min-width: 1440px) {
  .sidenave {
    height: 88vh;
  }
}



@media screen and (min-width: 1400px) {
  .sidenave {
    height: 87vh;
  }
}























.sidebar {
  background: #f9f9f9;
  width: 240px;
  max-width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0px;

  position: relative;
  overflow: auto;
}

.header {
  /* display: flex; */
  /* justify-content: space-between; */
  align-items: center;
  margin-bottom: 5px;
}

.header h2 {
  font-size: 1.2rem;
  color: #333;
}

/* .header .reset {
  font-size: 0.9rem;
  color: #55c2c3;
  text-decoration: none;
  cursor: pointer;
  font-size: 16px;
} */

.toggle-buttons {
  /* display: flex
; */
  justify-content: space-between;
  margin-bottom: 15px;
  height: 100vh;
  overflow: auto;
}

/* .toggle-buttons button {
  flex: 1;
  padding: 10px 0;
  margin-right: 10px;
  font-size: 0.9rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  background-color: #f0f0f0;
  color: #333;
} */

/* .toggle-buttons button:last-child {
  margin-right: 0;
} */

/* .toggle-buttons button.active {
  background-color: #007BFF;
  color: #fff;
} */

.menu {
  flex: 1;
  list-style: none;
  overflow-y: auto;
  margin-bottom: 15px;
}

.menu li {
  margin-bottom: 10px;
}

.menu li a {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 5px;
  color: #333;
  text-decoration: none;
  font-size: 0.9rem;
  background: #f0f0f0;
  transition: background-color 0.3s ease;
}

.menu li a:hover {
  background-color: #007BFF;
  color: #fff;
}

.menu li a .icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.apply-filters {
  position: sticky;
  bottom: 15px;
  padding: 12px;
  background-color: #007BFF;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  text-align: center;
}

.apply-filters:hover {
  background-color: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    border-radius: 0;
    width: 100%;
  }
}

