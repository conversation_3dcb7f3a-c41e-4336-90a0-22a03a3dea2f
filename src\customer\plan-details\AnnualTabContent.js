import React, { useEffect, useState } from "react";
import UseTabStore from "../common-files/useGlobalState";
import Popup from "./Popup";
import { useNavigate } from "react-router-dom";
import { envConfig } from "../../config";

const AnnualTabContent = ({ isOpen, togglePopup }) => {
    const { allPlanDetails, setSelectedPlanDetails, setSelectedPlanType, setButtonType, setDefaultAlert, setDefaultErrorMsg } = UseTabStore();
    const [filteredPlans, setFilteredPlans] = useState([]);
    const navigate = useNavigate();
    const [user, setUser] = useState(JSON.parse(localStorage.getItem('user')));

    let selected_plan_name;
    let userCredits = localStorage.getItem("userCredits");
    if (userCredits) {
        const parsedData = JSON.parse(userCredits);
        selected_plan_name = parsedData.user_plan_name || null;
    }

    useEffect(() => {

        const desiredIds = envConfig.yearlyPlanIds;
        if (allPlanDetails && allPlanDetails.length > 0) {
            const specificPlans = allPlanDetails.filter((plan) => desiredIds.includes(plan.id));
            setFilteredPlans(specificPlans);
        }
    }, [allPlanDetails]);

    const closeModal = () => {
        const modal = document.getElementById('pricePlanModal');
        if (modal) {
            modal.classList.remove('show');
            modal.style.display = 'none';
        }

        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }

        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';

        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");

    };


    const upgradePlan = (selectedPlan) => {
        closeModal();
        setSelectedPlanType("upgrade");
        setSelectedPlanDetails(selectedPlan);
        navigate('/upgrade-plan', {
            state: {
                email: user.email,
                planDetails: selectedPlan
            }
        });
    };


    return (
        <div>
            {filteredPlans && filteredPlans.length > 0 &&
                <div className="container">
                    {/* Annual Pricing Table Content */}
                    <div className="row">
                        <div className="col p-0">
                            <div className="top-penguin">
                                <img src="../images/top-penguin.png" width="220" />
                            </div>
                        </div>
                        {filteredPlans.map((plan) => (
                            (envConfig.environment === "production" ? plan.id === 6688279 : plan.id === 374575) && (
                                <div className="col p-0" key={plan.id}>
                                    <div className="pricing-card-1 position-relative">
                                        <div className="glaciar-parts">
                                            <img src="../images/glaciar.png" alt="Glacier" />
                                        </div>
                                        <h4 className="Glacier-of-month">Glacier</h4>

                                        <span className="original-price">$39</span>
                                        <span className="current-price">$29</span>
                                        <span className="per-month">/Month</span>

                                        <p className="text-muted-italic">Billed Annually</p>
                                        {selected_plan_name === plan.package_name ? (
                                            <button class="btn-block-current-plan"><i className="fa fa-check checked-plan"></i> Current Plan</button>
                                        ) : (
                                            <button className="btn-block" onClick={() => upgradePlan(plan)}>Select</button>
                                        )}
                                        <p className="text-success">Unlock annual savings of $120!</p>
                                        <div className="features">
                                            <p>{plan?.no_of_contact_views ? plan?.no_of_contact_views : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <p>{plan?.export ? (plan.export * 12).toLocaleString() : ""}/Year</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <img src="../images/checks.png" alt="Feature check" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <img src="../images/checks.png" alt="Feature check" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <img src="../images/checks.png" alt="Feature check" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <img src="../images/checks.png" alt="Feature check" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <img src="../images/checks.png" alt="Feature check" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <img src="../images/checks.png" alt="Feature check" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <p>{plan?.no_of_users ? plan?.no_of_users : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features-cross">
                                            <img src="../images/cancell.png" alt="Feature cross" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features-Freelancers">
                                            <p>
                                                Freelancers & <br /> Solopreneurs
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        ))}
                        {filteredPlans.map((plan) => (
                            (envConfig.environment === "production" ? plan.id === 6688275 : plan.id === 374579) && (
                                <div class="col  p-0">
                                    <div class="pricing-card-2 position-relative">
                                        <div class="recommended-badge">Recommended</div>
                                        <div className="ice-flow-img">
                                            <img alt="checks" src="../images/ice-floe.png" />
                                        </div>
                                        <h4 className="ice-floe-top">Ice Floe</h4>

                                        <span class="original-price">$59</span>
                                        <span className="current-price">$49</span>
                                        <span class="per-month">/Month</span>
                                        <p class="text-muted-italic">Billed Annually</p>
                                        {selected_plan_name === plan.package_name ? (
                                            <button class="btn-block-current-plan"><i className="fa fa-check checked-plan"></i> Current Plan</button>
                                        ) : (
                                            <button className="btn-block" onClick={() => upgradePlan(plan)}>Select</button>
                                        )}
                                        <p class="Unlock-annual">Unlock annual savings of $120!</p>

                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_contact_views ? plan?.no_of_contact_views : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <p>{plan?.export ? (plan.export * 12).toLocaleString() : ""}/Year</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_users ? plan?.no_of_users : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features-cross">
                                            <img alt="checks" src="../images/cancell.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features-Freelancers">
                                            <p>Growing Teams & <br /> Startups</p>
                                        </div>
                                    </div>
                                </div>
                            )
                        ))}
                        {filteredPlans.map((plan) => (
                            (envConfig.environment === "production" ? plan.id === 6688271 : plan.id === 374583) && (
                                <div class="col  p-0">
                                    <div class="pricing-card-3 position-relative">
                                        <div className="polar-peak-img">
                                            <img src="../images/ice-mountain.png" width="110" />
                                        </div>
                                        <h4 className="ice-floe">Polar Peak</h4>
                                        <span class="original-price">$99</span>
                                        <span class="current-price">$79</span>
                                        <span class="per-month">/Month</span>
                                        <p class="text-muted-italic">Billed Annually</p>
                                        {selected_plan_name === plan.package_name ? (
                                            <button class="btn-block-current-plan" onClick={() => closeModal()}><i className="fa fa-check checked-plan"></i> Current Plan</button>
                                        ) : (
                                            <button className="btn-block" onClick={() => upgradePlan(plan)}>Select</button>
                                        )}
                                        <p class="unlock-success">Unlock annual savings of $240!</p>

                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_contact_views ? plan?.no_of_contact_views : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div className="features">
                                            <p>{plan?.export ? (plan.export * 12).toLocaleString() : ""}/Year</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_users ? plan?.no_of_users : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features-cross">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features-Freelancers">
                                            <p> Mid-Size Businesses & <br /> Emerging Enterprises</p>
                                        </div>
                                    </div>
                                </div>
                            )
                        ))}
                        <div class="col  p-0">
                            <div class="pricing-card-4 position-relative">
                                <div className="frozen-fortune-img">
                                    <img src="../images/frozen-fortune.png" width="213" />
                                </div>
                                <h4 className="ice-floe">Frozen Fortune</h4>
                                <p class="Playing-it">Playing it big?</p>
                                <p class="text-Fully">Fully Customized For Any Team Size</p>
                                <button onClick={togglePopup} class="btn-block">Contact Sales</button>


                                <p class="unlock-success-blank"></p>
                                <hr className="horizontal-hr-3" />

                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features-cross">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features-Enterprises-annually">
                                    <p>Enterprises & <br /> Large Teams</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            }

            {isOpen && (
                <Popup togglePopup={togglePopup} />
            )}
        </div>
    )
};

export default AnnualTabContent;